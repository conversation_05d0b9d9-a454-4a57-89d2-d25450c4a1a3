package game

import (
	"context"
	"fmt"
	"testing"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

func TestWorldService_CreateWorld_Enhanced(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewWorldService(testDB.DB, logger)

	user := testDB.CreateTestUser(t)
	ctx := testutil.SetupTestContext()

	tests := []struct {
		name        string
		creatorID   uuid.UUID
		worldName   string
		description string
		config      map[string]interface{}
		expectError bool
		description_test string
	}{
		{
			name:        "成功创建基础世界",
			creatorID:   user.ID,
			worldName:   "测试世界",
			description: "这是一个测试世界",
			config:      map[string]interface{}{},
			expectError: false,
			description_test: "使用基础参数成功创建世界",
		},
		{
			name:        "成功创建带配置的世界",
			creatorID:   user.ID,
			worldName:   "高级测试世界",
			description: "这是一个带有自定义配置的测试世界",
			config: map[string]interface{}{
				"theme":      "sci-fi",
				"difficulty": "hard",
				"time_rate":  2.0,
			},
			expectError: false,
			description_test: "使用自定义配置成功创建世界",
		},
		{
			name:        "空名称创建失败",
			creatorID:   user.ID,
			worldName:   "",
			description: "测试描述",
			config:      map[string]interface{}{},
			expectError: true,
			description_test: "空名称应该导致创建失败",
		},
		{
			name:        "无效创建者ID创建失败",
			creatorID:   uuid.Nil,
			worldName:   "测试世界",
			description: "测试描述",
			config:      map[string]interface{}{},
			expectError: true,
			description_test: "无效的创建者ID应该导致创建失败",
		},
		{
			name:        "长名称创建成功",
			creatorID:   user.ID,
			worldName:   "这是一个非常长的世界名称用来测试系统对长名称的处理能力",
			description: "测试长名称的处理",
			config:      map[string]interface{}{},
			expectError: false,
			description_test: "长名称应该能够正常处理",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行创建世界操作
			world, err := service.CreateWorld(ctx, tt.creatorID, tt.worldName, tt.description, tt.config)

			if tt.expectError {
				// 验证错误情况
				assert.Error(t, err, tt.description_test)
				assert.Nil(t, world, "错误情况下世界对象应该为nil")
			} else {
				// 验证成功情况
				require.NoError(t, err, tt.description_test)
				require.NotNil(t, world, "成功情况下世界对象不应该为nil")

				// 验证世界基本属性
				assert.NotEqual(t, uuid.Nil, world.ID, "世界ID应该被正确生成")
				assert.Equal(t, tt.worldName, world.Name, "世界名称应该匹配")
				assert.Equal(t, tt.creatorID, world.CreatorID, "创建者ID应该匹配")
				assert.Equal(t, "active", world.Status, "世界状态应该为active")
				assert.False(t, world.IsPublic, "世界默认应该为私有")
				assert.Equal(t, 10, world.MaxPlayers, "默认最大玩家数应该为10")

				// 验证描述
				require.NotNil(t, world.Description, "世界描述不应该为nil")
				assert.Equal(t, tt.description, *world.Description, "世界描述应该匹配")

				// 验证配置
				assert.NotNil(t, world.WorldConfig, "世界配置不应该为nil")
				if len(tt.config) > 0 {
					// 验证自定义配置是否被正确设置
					for key, expectedValue := range tt.config {
						actualValue, exists := world.WorldConfig[key]
						assert.True(t, exists, "配置键 %s 应该存在", key)
						assert.Equal(t, expectedValue, actualValue, "配置值 %s 应该匹配", key)
					}
				}

				// 验证默认配置是否被设置
				defaultKeys := []string{"time_rate", "tick_interval", "max_memory_per_char", "rules", "theme", "difficulty", "language"}
				for _, key := range defaultKeys {
					_, exists := world.WorldConfig[key]
					assert.True(t, exists, "默认配置键 %s 应该存在", key)
				}

				// 验证世界状态
				assert.NotNil(t, world.WorldState, "世界状态不应该为nil")
				stateKeys := []string{"current_tick", "last_tick_at", "active_events", "global_variables", "weather", "season", "world_goals"}
				for _, key := range stateKeys {
					_, exists := world.WorldState[key]
					assert.True(t, exists, "世界状态键 %s 应该存在", key)
				}

				// 验证时间戳
				assert.False(t, world.CreatedAt.IsZero(), "创建时间应该被设置")
				assert.False(t, world.UpdatedAt.IsZero(), "更新时间应该被设置")

				// 验证数据库中的记录
				var dbWorld models.World
				err = testDB.DB.First(&dbWorld, world.ID).Error
				require.NoError(t, err, "应该能从数据库中查询到创建的世界")
				assert.Equal(t, world.ID, dbWorld.ID, "数据库中的世界ID应该匹配")
				assert.Equal(t, world.Name, dbWorld.Name, "数据库中的世界名称应该匹配")
			}
		})
	}
}

func TestWorldService_CreateWorld_Transaction(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewWorldService(testDB.DB, logger)

	user := testDB.CreateTestUser(t)
	ctx := testutil.SetupTestContext()

	t.Run("事务回滚测试", func(t *testing.T) {
		// 使用事务测试
		testDB.WithTestTransaction(t, func(tx *gorm.DB) {
			// 在事务中创建世界
			world, err := service.CreateWorld(ctx, user.ID, "事务测试世界", "事务测试描述", map[string]interface{}{})
			require.NoError(t, err, "在事务中创建世界应该成功")
			require.NotNil(t, world, "世界对象不应该为nil")

			// 验证在事务中可以查询到
			var txWorld models.World
			err = tx.First(&txWorld, world.ID).Error
			require.NoError(t, err, "在事务中应该能查询到世界")

			// 事务会在函数结束时回滚
		})

		// 验证事务回滚后数据不存在
		var count int64
		err := testDB.DB.Model(&models.World{}).Where("name = ?", "事务测试世界").Count(&count).Error
		require.NoError(t, err, "查询世界数量不应该出错")
		assert.Equal(t, int64(0), count, "事务回滚后世界应该不存在")
	})
}

func TestWorldService_CreateWorld_Concurrency(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewWorldService(testDB.DB, logger)

	user := testDB.CreateTestUser(t)
	ctx := testutil.SetupTestContext()

	t.Run("并发创建世界测试", func(t *testing.T) {
		const goroutineCount = 5
		results := make(chan *models.World, goroutineCount)
		errors := make(chan error, goroutineCount)

		// 启动多个goroutine并发创建世界
		for i := 0; i < goroutineCount; i++ {
			go func(index int) {
				worldName := fmt.Sprintf("并发测试世界_%d", index)
				world, err := service.CreateWorld(ctx, user.ID, worldName, "并发测试", map[string]interface{}{})
				if err != nil {
					errors <- err
				} else {
					results <- world
				}
			}(i)
		}

		// 收集结果
		var createdWorlds []*models.World
		var createErrors []error

		for i := 0; i < goroutineCount; i++ {
			select {
			case world := <-results:
				createdWorlds = append(createdWorlds, world)
			case err := <-errors:
				createErrors = append(createErrors, err)
			}
		}

		// 验证结果
		assert.Empty(t, createErrors, "并发创建不应该产生错误")
		assert.Len(t, createdWorlds, goroutineCount, "应该成功创建所有世界")

		// 验证每个世界都有唯一的ID
		worldIDs := make(map[uuid.UUID]bool)
		for _, world := range createdWorlds {
			assert.False(t, worldIDs[world.ID], "世界ID应该是唯一的")
			worldIDs[world.ID] = true
		}
	})
}
