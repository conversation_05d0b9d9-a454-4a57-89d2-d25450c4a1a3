package migration

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/database"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestSmartMigrator_TriggerPrimaryKeyFix 测试触发器主键字段修复
// 这个测试专门验证 user_stats 表触发器使用正确的主键字段（user_id 而不是 id）
func TestSmartMigrator_TriggerPrimaryKeyFix(t *testing.T) {
	// 创建临时数据库文件
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_trigger_fix.db")

	// 配置数据库连接
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 静默模式，减少测试输出
	})
	require.NoError(t, err, "创建测试数据库失败")

	// 创建数据库配置
	cfg := &config.DatabaseConfig{
		Host:     "",
		Port:     0,
		DBName:   dbPath,
		SSLMode:  "development",
	}

	// 创建智能迁移器
	migrator := NewSmartMigrator(db, cfg, "")

	// 测试模型
	testModels := []interface{}{
		&models.User{},
		&models.UserStats{},
	}

	// 执行开发环境模式迁移
	err = migrator.CreateDevelopmentSchema(testModels...)
	assert.NoError(t, err, "开发环境数据库迁移应该成功")

	// 验证表是否创建成功
	assert.True(t, db.Migrator().HasTable(&models.User{}), "users 表应该存在")
	assert.True(t, db.Migrator().HasTable(&models.UserStats{}), "user_stats 表应该存在")

	// 验证触发器是否正确创建
	// 通过查询 sqlite_master 表来检查触发器
	var triggerCount int64
	err = db.Raw("SELECT COUNT(*) FROM sqlite_master WHERE type='trigger' AND name='update_user_stats_updated_at'").Scan(&triggerCount).Error
	assert.NoError(t, err, "查询触发器应该成功")
	assert.Equal(t, int64(1), triggerCount, "update_user_stats_updated_at 触发器应该存在")

	// 测试触发器功能：创建用户和用户统计
	user := &models.User{
		ExternalID:       "test_user_001",
		ExternalProvider: "test",
		Email:           "<EMAIL>",
	}
	err = db.Create(user).Error
	assert.NoError(t, err, "创建用户应该成功")

	// 获取用户统计（应该通过 AfterCreate 钩子自动创建）
	var userStats models.UserStats
	err = db.Where("user_id = ?", user.ID).First(&userStats).Error
	assert.NoError(t, err, "用户统计应该自动创建")

	// 记录原始更新时间
	originalUpdatedAt := userStats.UpdatedAt

	// 更新用户统计，触发器应该自动更新 updated_at
	err = db.Model(&userStats).Update("level", 2).Error
	assert.NoError(t, err, "更新用户统计应该成功")

	// 重新获取用户统计，验证 updated_at 是否被触发器更新
	var updatedUserStats models.UserStats
	err = db.Where("user_id = ?", user.ID).First(&updatedUserStats).Error
	assert.NoError(t, err, "重新获取用户统计应该成功")

	// 验证触发器是否正常工作（updated_at 应该被更新）
	assert.True(t, updatedUserStats.UpdatedAt.After(originalUpdatedAt), 
		"触发器应该自动更新 updated_at 字段")
	assert.Equal(t, 2, updatedUserStats.Level, "用户等级应该被正确更新")
}

// TestCompatibilityConfig_GetPrimaryKeyField 测试主键字段获取逻辑
func TestCompatibilityConfig_GetPrimaryKeyField(t *testing.T) {
	// 创建临时数据库用于测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	compatibility := database.NewCompatibilityConfig(db)

	// 测试各种表名的主键字段映射
	testCases := []struct {
		tableName    string
		expectedKey  string
		description  string
	}{
		{"user_stats", "user_id", "user_stats 表应该使用 user_id 作为主键"},
		{"users", "id", "users 表应该使用 id 作为主键"},
		{"worlds", "id", "worlds 表应该使用 id 作为主键"},
		{"characters", "id", "characters 表应该使用 id 作为主键"},
		{"scenes", "id", "scenes 表应该使用 id 作为主键"},
		{"entities", "id", "entities 表应该使用 id 作为主键"},
		{"game_events", "id", "game_events 表应该使用 id 作为主键"},
		{"unknown_table", "id", "未知表应该默认使用 id 作为主键"},
	}

	for _, tc := range testCases {
		t.Run(tc.tableName, func(t *testing.T) {
			// 使用反射调用私有方法进行测试
			// 注意：这里我们通过生成触发器SQL来间接测试主键字段逻辑
			triggerSQL := compatibility.GetTriggerSQL(tc.tableName)
			
			// 验证生成的SQL包含正确的主键字段
			expectedPattern := tc.expectedKey + " = NEW." + tc.expectedKey
			assert.Contains(t, triggerSQL, expectedPattern, tc.description)
		})
	}
}

// TestSmartMigrator_CleanupTriggersBeforeMigration 测试迁移前触发器清理
func TestSmartMigrator_CleanupTriggersBeforeMigration(t *testing.T) {
	// 创建临时数据库
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_cleanup.db")

	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	cfg := &config.DatabaseConfig{
		Host:     "",
		DBName:   dbPath,
		SSLMode:  "development",
	}

	migrator := NewSmartMigrator(db, cfg, "")

	// 先创建一些表和触发器
	err = db.Exec(`
		CREATE TABLE test_table (
			id TEXT PRIMARY KEY,
			updated_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	// 创建一个测试触发器
	err = db.Exec(`
		CREATE TRIGGER update_test_table_updated_at 
		AFTER UPDATE ON test_table 
		FOR EACH ROW 
		BEGIN 
			UPDATE test_table SET updated_at = datetime('now') WHERE id = NEW.id;
		END
	`).Error
	require.NoError(t, err)

	// 验证触发器存在
	var count int64
	err = db.Raw("SELECT COUNT(*) FROM sqlite_master WHERE type='trigger' AND name='update_test_table_updated_at'").Scan(&count).Error
	require.NoError(t, err)
	assert.Equal(t, int64(1), count, "测试触发器应该存在")

	// 执行清理
	err = migrator.cleanupTriggersBeforeMigration()
	assert.NoError(t, err, "清理触发器应该成功")

	// 注意：我们的清理方法只清理特定的触发器，不会清理测试触发器
	// 这是正确的行为，因为我们只想清理可能冲突的系统触发器
}

// TestSmartMigrator_MultipleTableMigration 测试多表迁移场景
func TestSmartMigrator_MultipleTableMigration(t *testing.T) {
	// 创建临时数据库
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_multiple_tables.db")

	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	cfg := &config.DatabaseConfig{
		Host:    "",
		DBName:  dbPath,
		SSLMode: "development",
	}

	migrator := NewSmartMigrator(db, cfg, "")

	// 测试所有主要模型的迁移
	allModels := []interface{}{
		&models.User{},
		&models.UserStats{},
		&models.World{},
		&models.Character{},
		&models.Scene{},
		&models.Entity{},
	}

	// 执行迁移
	err = migrator.CreateDevelopmentSchema(allModels...)
	assert.NoError(t, err, "多表迁移应该成功")

	// 验证所有表都已创建
	for _, model := range allModels {
		assert.True(t, db.Migrator().HasTable(model), "表应该存在: %T", model)
	}

	// 验证所有相关触发器都已创建
	expectedTriggers := []string{
		"update_users_updated_at",
		"update_user_stats_updated_at",
		"update_worlds_updated_at",
		"update_characters_updated_at",
		"update_scenes_updated_at",
	}

	for _, triggerName := range expectedTriggers {
		var count int64
		err = db.Raw("SELECT COUNT(*) FROM sqlite_master WHERE type='trigger' AND name=?", triggerName).Scan(&count).Error
		assert.NoError(t, err, "查询触发器应该成功: %s", triggerName)
		assert.Equal(t, int64(1), count, "触发器应该存在: %s", triggerName)
	}
}

// TestSmartMigrator_TriggerFunctionality 测试触发器功能
func TestSmartMigrator_TriggerFunctionality(t *testing.T) {
	// 创建临时数据库
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_trigger_functionality.db")

	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	cfg := &config.DatabaseConfig{
		Host:    "",
		DBName:  dbPath,
		SSLMode: "development",
	}

	migrator := NewSmartMigrator(db, cfg, "")

	// 执行迁移
	testModels := []interface{}{
		&models.User{},
		&models.UserStats{},
	}
	err = migrator.CreateDevelopmentSchema(testModels...)
	require.NoError(t, err)

	// 创建测试用户
	user := &models.User{
		ExternalID:       "trigger_test_user",
		ExternalProvider: "test",
		Email:           "<EMAIL>",
	}
	err = db.Create(user).Error
	require.NoError(t, err)

	// 获取用户统计（应该通过钩子自动创建）
	var userStats models.UserStats
	err = db.Where("user_id = ?", user.ID).First(&userStats).Error
	require.NoError(t, err)

	// 记录原始时间
	originalTime := userStats.UpdatedAt

	// 等待一小段时间确保时间戳不同
	time.Sleep(10 * time.Millisecond)

	// 更新用户统计，但不改变 updated_at（触发器应该自动更新）
	err = db.Model(&userStats).Update("level", 5).Error
	assert.NoError(t, err, "更新用户统计应该成功")

	// 重新获取用户统计
	var updatedStats models.UserStats
	err = db.Where("user_id = ?", user.ID).First(&updatedStats).Error
	require.NoError(t, err)

	// 验证触发器是否正常工作
	assert.True(t, updatedStats.UpdatedAt.After(originalTime),
		"触发器应该自动更新 updated_at 字段")
	assert.Equal(t, 5, updatedStats.Level, "用户等级应该被正确更新")
}

// TestSmartMigrator_ErrorHandling 测试错误处理
func TestSmartMigrator_ErrorHandling(t *testing.T) {
	// 测试无效数据库路径
	cfg := &config.DatabaseConfig{
		Host:    "",
		DBName:  "/invalid/path/test.db",
		SSLMode: "development",
	}

	// 这应该在创建数据库连接时失败，而不是在迁移器创建时失败
	db, err := gorm.Open(sqlite.Open(cfg.DBName), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})

	if err != nil {
		// 如果数据库连接失败，这是预期的
		t.Logf("数据库连接失败（预期行为）: %v", err)
		return
	}

	migrator := NewSmartMigrator(db, cfg, "")

	// 尝试迁移，可能会因为权限问题失败
	err = migrator.CreateDevelopmentSchema(&models.User{})
	// 不强制要求失败，因为某些环境可能允许创建文件
	t.Logf("迁移结果: %v", err)
}

// 清理测试文件
func TestMain(m *testing.M) {
	// 运行测试
	code := m.Run()

	// 清理可能残留的测试文件
	os.RemoveAll("test_*.db")

	os.Exit(code)
}
