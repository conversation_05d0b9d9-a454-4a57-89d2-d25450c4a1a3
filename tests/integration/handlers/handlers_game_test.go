package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/game"
	"ai-text-game-iam-npc/internal/testutil"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestRouter(t *testing.T) (*gin.Engine, *testutil.TestDB, *GameHandler) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 设置测试数据库
	testDB := testutil.SetupTestDB(t)

	// 创建服务
	logger := testutil.CreateTestLogger()
	worldService := game.NewWorldService(testDB.DB, logger)
	characterService := game.NewCharacterService(testDB.DB, logger)
	sceneService := game.NewSceneService(testDB.DB, logger)
	eventService := game.NewEventService(testDB.DB, logger)

	// 创建处理器
	gameHandler := NewGameHandler(worldService, characterService, sceneService, eventService)

	// 创建路由
	router := gin.New()

	// 添加测试中间件来模拟认证
	router.Use(func(c *gin.Context) {
		// 从请求头获取用户ID（测试用）
		userIDStr := c.GetHeader("X-Test-User-ID")
		if userIDStr != "" {
			if userID, err := uuid.Parse(userIDStr); err == nil {
				c.Set("user_id", userID)
			}
		}
		c.Next()
	})

	// 设置路由
	api := router.Group("/api/v1")
	gameGroup := api.Group("/game")
	{
		// 世界相关路由
		gameGroup.POST("/worlds", gameHandler.CreateWorld)
		gameGroup.GET("/worlds/:id", gameHandler.GetWorld)
		gameGroup.PUT("/worlds/:id", gameHandler.UpdateWorld)
		gameGroup.DELETE("/worlds/:id", gameHandler.DeleteWorld)
		gameGroup.GET("/worlds", gameHandler.ListWorlds)
		gameGroup.POST("/worlds/:id/join", gameHandler.JoinWorld)
		gameGroup.POST("/worlds/:id/leave", gameHandler.LeaveWorld)

		// 角色相关路由
		gameGroup.POST("/characters", gameHandler.CreateCharacter)
		gameGroup.GET("/characters/:id", gameHandler.GetCharacter)
		gameGroup.PUT("/characters/:id", gameHandler.UpdateCharacter)
		gameGroup.DELETE("/characters/:id", gameHandler.DeleteCharacter)
		gameGroup.GET("/worlds/:world_id/characters", gameHandler.ListCharacters)

		// 场景相关路由
		gameGroup.POST("/scenes", gameHandler.CreateScene)
		gameGroup.GET("/scenes/:id", gameHandler.GetScene)
		gameGroup.PUT("/scenes/:id", gameHandler.UpdateScene)
		gameGroup.DELETE("/scenes/:id", gameHandler.DeleteScene)
		gameGroup.GET("/worlds/:world_id/scenes", gameHandler.ListScenes)

		// 事件相关路由
		gameGroup.POST("/events", gameHandler.ProcessEvent)
		gameGroup.GET("/events/:id", gameHandler.GetEvent)
		gameGroup.GET("/worlds/:world_id/events", gameHandler.ListEvents)
	}

	return router, testDB, gameHandler
}

func createTestJWT(userID uuid.UUID) string {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, &auth.Claims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer: "ai-text-game-test",
		},
	})

	tokenString, _ := token.SignedString([]byte("test-secret-key-for-testing-only"))
	return tokenString
}

func TestGameHandler_CreateWorld(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	router, testDB, _ := setupTestRouter(t)
	defer testDB.CleanupTestDB(t)

	user := testDB.CreateTestUser(t)

	tests := []struct {
		name           string
		requestBody    interface{}
		userID         uuid.UUID
		expectedStatus int
		description    string
	}{
		{
			name: "创建正常世界",
			requestBody: CreateWorldRequest{
				Name:        "测试世界",
				Description: "这是一个测试世界",
				IsPublic:    true,
				MaxPlayers:  10,
				Config:      map[string]interface{}{},
			},
			userID:         user.ID,
			expectedStatus: http.StatusCreated,
			description:    "正常的世界创建请求应该成功",
		},
		{
			name: "世界名称为空",
			requestBody: CreateWorldRequest{
				Name:        "",
				Description: "这是一个测试世界",
				IsPublic:    true,
				MaxPlayers:  10,
				Config:      map[string]interface{}{},
			},
			userID:         user.ID,
			expectedStatus: http.StatusBadRequest,
			description:    "世界名称为空应该返回400错误",
		},
		{
			name:           "无效的JSON",
			requestBody:    "invalid json",
			userID:         user.ID,
			expectedStatus: http.StatusBadRequest,
			description:    "无效的JSON应该返回400错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备请求体
			var body []byte
			var err error
			if str, ok := tt.requestBody.(string); ok {
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				require.NoError(t, err)
			}

			// 创建请求
			req, err := http.NewRequest("POST", "/api/v1/game/worlds", bytes.NewBuffer(body))
			require.NoError(t, err)

			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("X-Test-User-ID", tt.userID.String())

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			if tt.expectedStatus == http.StatusCreated {
				var response Response
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				assert.True(t, response.Success, "响应应该表示成功")
				assert.NotNil(t, response.Data, "响应数据不应该为nil")
			}
		})
	}
}

func TestGameHandler_GetWorld(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	router, testDB, _ := setupTestRouter(t)
	defer testDB.CleanupTestDB(t)

	user := testDB.CreateTestUser(t)
	world := testDB.CreateTestWorld(t, user.ID)

	tests := []struct {
		name           string
		worldID        string
		userID         uuid.UUID
		expectedStatus int
		description    string
	}{
		{
			name:           "获取存在的世界",
			worldID:        world.ID.String(),
			userID:         user.ID,
			expectedStatus: http.StatusOK,
			description:    "获取存在的世界应该成功",
		},
		{
			name:           "获取不存在的世界",
			worldID:        uuid.New().String(),
			userID:         user.ID,
			expectedStatus: http.StatusNotFound,
			description:    "获取不存在的世界应该返回404",
		},
		{
			name:           "无效的世界ID",
			worldID:        "invalid-uuid",
			userID:         user.ID,
			expectedStatus: http.StatusBadRequest,
			description:    "无效的世界ID应该返回400",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建请求
			req, err := http.NewRequest("GET", fmt.Sprintf("/api/v1/game/worlds/%s", tt.worldID), nil)
			require.NoError(t, err)

			req.Header.Set("X-Test-User-ID", tt.userID.String())

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			if tt.expectedStatus == http.StatusOK {
				var response Response
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				assert.True(t, response.Success, "响应应该表示成功")
				assert.NotNil(t, response.Data, "响应数据不应该为nil")
			}
		})
	}
}

func TestGameHandler_ListWorlds(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	router, testDB, _ := setupTestRouter(t)
	defer testDB.CleanupTestDB(t)

	user := testDB.CreateTestUser(t)

	// 创建多个世界
	for i := 0; i < 3; i++ {
		testDB.CreateTestWorld(t, user.ID)
	}

	tests := []struct {
		name           string
		queryParams    string
		userID         uuid.UUID
		expectedStatus int
		description    string
	}{
		{
			name:           "列出所有世界",
			queryParams:    "",
			userID:         user.ID,
			expectedStatus: http.StatusOK,
			description:    "列出所有世界应该成功",
		},
		{
			name:           "分页查询",
			queryParams:    "?page=1&page_size=2",
			userID:         user.ID,
			expectedStatus: http.StatusOK,
			description:    "分页查询应该成功",
		},
		{
			name:           "只查询公开世界",
			queryParams:    "?is_public=true",
			userID:         user.ID,
			expectedStatus: http.StatusOK,
			description:    "查询公开世界应该成功",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建请求
			url := "/api/v1/game/worlds" + tt.queryParams
			req, err := http.NewRequest("GET", url, nil)
			require.NoError(t, err)

			req.Header.Set("X-Test-User-ID", tt.userID.String())

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			if tt.expectedStatus == http.StatusOK {
				var response Response
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				assert.True(t, response.Success, "响应应该表示成功")
				assert.NotNil(t, response.Data, "响应数据不应该为nil")
			}
		})
	}
}
