package ai

import (
	"testing"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"
	"github.com/google/uuid"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

/**
 * AI服务集成测试
 * @description 测试整个AI数据生成流程的集成功能
 */

// setupIntegrationTestDB 设置集成测试数据库
func setupIntegrationTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		t.Fatalf("创建测试数据库失败: %v", err)
	}
	
	// 自动迁移
	err = db.AutoMigrate(
		&models.World{},
		&models.Scene{},
		&models.Character{},
		&models.Event{},
		&models.AIInteraction{},
	)
	if err != nil {
		t.Fatalf("数据库迁移失败: %v", err)
	}
	
	return db
}

// setupTestConfig 设置测试配置
func setupTestConfig() *config.Config {
	return &config.Config{
		AI: config.AIConfig{
			BaseURL:    "http://localhost:8080",
			Token:      "test-token",
			Timeout:    30 * time.Second,
			MaxRetries: 3,
			RetryDelay: 1 * time.Second,
			Windmill: config.WindmillConfig{
				Workspace:    "test-workspace",
				DefaultModel: "gemini-1.5-pro",
			},
		},
	}
}

func TestAIServiceIntegration(t *testing.T) {
	// 跳过集成测试，除非设置了环境变量
	if testing.Short() {
		t.Skip("跳过集成测试")
	}
	
	// 设置测试环境
	db := setupIntegrationTestDB(t)
	cfg := setupTestConfig()
	log := newTestLogger()
	
	// 创建AI服务
	service := NewService(cfg, db, log)
	
	// 创建测试世界
	description := "用于集成测试的世界"
	world := &models.World{
		Name:        "测试世界",
		Description: &description,
		CreatorID:   uuid.New(),
		WorldConfig: models.JSON(map[string]interface{}{"theme": "奇幻"}),
		WorldState:  models.JSON(map[string]interface{}{}),
		Status:      "active",
	}
	if err := db.Create(world).Error; err != nil {
		t.Fatalf("创建测试世界失败: %v", err)
	}
	
	t.Run("测试Schema注册表集成", func(t *testing.T) {
		// 测试获取所有支持的内容类型
		types := service.GetAvailableContentTypes()
		if len(types) == 0 {
			t.Error("期望至少有一个支持的内容类型")
		}
		
		// 测试验证内容类型
		if !service.ValidateContentType("scene") {
			t.Error("期望场景类型验证通过")
		}
		
		if service.ValidateContentType("invalid_type") {
			t.Error("期望无效类型验证失败")
		}
		
		// 测试获取Schema
		schema, err := service.GetSchemaForContentType("character")
		if err != nil {
			t.Errorf("获取角色Schema失败: %v", err)
		}
		
		if schema["type"] != "object" {
			t.Errorf("期望角色Schema类型为 object，实际为 %v", schema["type"])
		}
	})
	
	t.Run("测试配置管理集成", func(t *testing.T) {
		// 测试获取配置摘要
		summary := service.GetAIConfiguration()
		if summary["total_models"].(int) == 0 {
			t.Error("期望至少有一个模型配置")
		}
		
		// 测试获取支持的模型
		models := service.GetSupportedModels()
		if len(models) == 0 {
			t.Error("期望至少有一个支持的模型")
		}
		
		// 测试获取模型配置
		modelConfig, err := service.GetModelConfiguration("gemini-1.5-pro")
		if err != nil {
			t.Errorf("获取模型配置失败: %v", err)
		}
		
		if modelConfig.Name != "gemini-1.5-pro" {
			t.Errorf("期望模型名称为 'gemini-1.5-pro'，实际为 '%s'", modelConfig.Name)
		}
		
		// 测试获取内容类型配置
		contentConfig, err := service.GetContentTypeConfiguration("scene")
		if err != nil {
			t.Errorf("获取内容类型配置失败: %v", err)
		}
		
		if contentConfig.Model == "" {
			t.Error("期望内容类型配置包含模型信息")
		}
	})
	
	t.Run("测试异步任务管理集成", func(t *testing.T) {
		// 创建测试请求
		req := &GenerateRequest{
			Type:    "scene",
			Prompt:  "生成一个神秘的森林场景",
			WorldID: &world.ID,
			Context: map[string]interface{}{
				"world_theme": "奇幻",
				"target_atmosphere": "神秘",
			},
		}
		
		// 提交异步任务
		task, err := service.GenerateContentAsync(req)
		if err != nil {
			t.Errorf("提交异步任务失败: %v", err)
		}
		
		if task.ID == "" {
			t.Error("期望任务ID不为空")
		}
		
		if task.Status != TaskStatusPending {
			t.Errorf("期望任务状态为 pending，实际为 %s", task.Status)
		}
		
		// 获取任务状态
		retrievedTask, err := service.GetAsyncTask(task.ID)
		if err != nil {
			t.Errorf("获取任务状态失败: %v", err)
		}
		
		if retrievedTask.ID != task.ID {
			t.Errorf("期望任务ID为 %s，实际为 %s", task.ID, retrievedTask.ID)
		}
		
		// 等待一段时间让任务处理
		time.Sleep(100 * time.Millisecond)
		
		// 获取任务统计
		stats := service.GetAsyncTaskStatistics()
		if stats["total_tasks"].(int64) == 0 {
			t.Error("期望至少有一个任务")
		}
		
		// 取消任务（如果还在运行）
		if err := service.CancelAsyncTask(task.ID); err != nil {
			// 任务可能已经完成，这是正常的
			t.Logf("取消任务失败（可能已完成）: %v", err)
		}
	})
	
	t.Run("测试数据流处理集成", func(t *testing.T) {
		// 获取数据流处理器
		dataFlow := service.GetDataFlowProcessor()
		if dataFlow == nil {
			t.Error("期望数据流处理器不为空")
		}
		
		// 订阅事件
		eventChan := service.SubscribeToDataFlowEvents("scene_created")
		
		// 发布测试事件
		testEvent := &DataFlowEvent{
			ID:        uuid.New().String(),
			Type:      "scene_created",
			Source:    "test",
			Target:    "test",
			Timestamp: time.Now(),
			Data: map[string]interface{}{
				"scene_id": uuid.New().String(),
				"world_id": world.ID.String(),
			},
		}
		
		service.PublishDataFlowEvent(testEvent)
		
		// 等待事件处理
		select {
		case receivedEvent := <-eventChan:
			if receivedEvent.Type != "scene_created" {
				t.Errorf("期望事件类型为 'scene_created'，实际为 '%s'", receivedEvent.Type)
			}
		case <-time.After(1 * time.Second):
			t.Error("等待事件超时")
		}
	})
	
	t.Run("测试前端同步集成", func(t *testing.T) {
		// 获取前端同步管理器
		frontendSync := service.GetFrontendSyncManager()
		if frontendSync == nil {
			t.Error("期望前端同步管理器不为空")
		}
		
		// 创建测试连接
		userID := uuid.New().String()
		conn := frontendSync.AddConnection(userID, world.ID.String())
		if conn == nil {
			t.Error("期望创建连接成功")
		}
		
		// 发送测试消息
		message := frontendSync.CreateSystemMessage("test", "测试消息", map[string]interface{}{
			"test_data": "hello",
		})
		
		service.SendToUser(userID, message)
		
		// 检查消息是否发送到连接
		select {
		case receivedMessage := <-conn.Channel:
			if receivedMessage.Type != "system" {
				t.Errorf("期望消息类型为 'system'，实际为 '%s'", receivedMessage.Type)
			}
		case <-time.After(1 * time.Second):
			t.Error("等待消息超时")
		}
		
		// 获取连接统计
		stats := service.GetConnectionStatistics()
		if stats["total_connections"].(int) == 0 {
			t.Error("期望至少有一个连接")
		}
		
		// 移除连接
		frontendSync.RemoveConnection(conn.ID)
	})
	
	t.Run("测试全局设置管理", func(t *testing.T) {
		// 设置全局设置
		service.SetGlobalAISetting("test_setting", "test_value")
		
		// 获取全局设置
		value, exists := service.GetGlobalAISetting("test_setting")
		if !exists {
			t.Error("期望全局设置存在")
		}
		
		if value != "test_value" {
			t.Errorf("期望全局设置值为 'test_value'，实际为 %v", value)
		}
	})
}

func TestDataTransformationIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过集成测试")
	}
	
	// 设置测试环境
	db := setupIntegrationTestDB(t)
	log := newTestLogger()
	transformer := NewDataTransformer(log)
	
	// 创建测试世界
	description := "用于数据转换测试的世界"
	world := &models.World{
		Name:        "测试世界",
		Description: &description,
		CreatorID:   uuid.New(),
		WorldConfig: models.JSON(map[string]interface{}{"theme": "奇幻"}),
		WorldState:  models.JSON(map[string]interface{}{}),
		Status:      "active",
	}
	if err := db.Create(world).Error; err != nil {
		t.Fatalf("创建测试世界失败: %v", err)
	}
	
	t.Run("测试完整的场景数据转换和保存", func(t *testing.T) {
		// 模拟完整的AI生成场景数据
		sceneData := map[string]interface{}{
			"name":        "魔法图书馆",
			"description": "一座古老的魔法图书馆，书架高耸入云，充满了神秘的魔法书籍。",
			"atmosphere":  "神秘",
			"scene_type":  "室内",
			"key_features": []interface{}{
				"浮动的书籍",
				"魔法水晶球",
				"古老的卷轴",
			},
			"possible_actions": []interface{}{
				"阅读书籍",
				"研究魔法",
				"寻找线索",
			},
			"connections": []interface{}{
				map[string]interface{}{
					"direction":    "南",
					"description":  "通往大厅的门",
					"scene_name":   "魔法学院大厅",
					"is_locked":    false,
				},
			},
			"hidden_elements": []interface{}{
				"秘密通道",
				"隐藏的魔法书",
			},
			"danger_level": 2,
			"lighting":     "明亮",
			"sounds": []interface{}{
				"翻书声",
				"魔法嗡鸣",
			},
			"smells": []interface{}{
				"羊皮纸香",
				"魔法气息",
			},
		}
		
		// 转换数据
		scene, err := transformer.TransformToScene(sceneData, world.ID)
		if err != nil {
			t.Fatalf("场景数据转换失败: %v", err)
		}
		
		// 保存到数据库
		if err := db.Create(scene).Error; err != nil {
			t.Fatalf("保存场景到数据库失败: %v", err)
		}
		
		// 验证保存的数据
		var savedScene models.Scene
		if err := db.First(&savedScene, scene.ID).Error; err != nil {
			t.Fatalf("从数据库获取场景失败: %v", err)
		}
		
		if savedScene.Name != "魔法图书馆" {
			t.Errorf("期望场景名称为 '魔法图书馆'，实际为 '%s'", savedScene.Name)
		}
		
		if savedScene.WorldID != world.ID {
			t.Errorf("期望世界ID为 %s，实际为 %s", world.ID, savedScene.WorldID)
		}
	})
	
	t.Run("测试完整的角色数据转换和保存", func(t *testing.T) {
		userID := uuid.New()
		
		// 模拟完整的AI生成角色数据
		characterData := map[string]interface{}{
			"name":           "图书管理员莫里斯",
			"description":    "一位年老的人类学者，专门管理魔法图书馆的珍贵藏书。",
			"character_type": "NPC",
			"personality": []interface{}{
				"博学",
				"严谨",
				"友善",
			},
			"background": "莫里斯在魔法学院工作了四十年，对每一本书都了如指掌。",
			"skills": []interface{}{
				"古文字学",
				"魔法理论",
				"书籍修复",
			},
			"dialogue_style": "学者风范，喜欢引用古籍",
			"motivations": []interface{}{
				"保护知识",
				"培养学者",
			},
			"fears": []interface{}{
				"书籍损坏",
				"知识失传",
			},
			"goals": []interface{}{
				"完成魔法百科全书",
				"培养继承人",
			},
			"relationships": []interface{}{
				"学院院长的顾问",
				"年轻学者的导师",
			},
			"attributes": map[string]interface{}{
				"strength":     40,
				"intelligence": 98,
				"agility":      30,
				"charisma":     75,
				"health":       80,
				"mana":         150,
			},
			"appearance": map[string]interface{}{
				"height":               "中等身高",
				"build":                "瘦弱",
				"hair_color":           "灰白",
				"eye_color":            "棕色",
				"skin_tone":            "苍白",
				"distinctive_features": []interface{}{"厚眼镜", "墨迹斑点"},
				"clothing_style":       "学者长袍",
			},
			"age_range":  "老年",
			"occupation": "图书管理员",
			"alignment":  "善良",
		}
		
		// 转换数据
		character, err := transformer.TransformToCharacter(characterData, world.ID, &userID)
		if err != nil {
			t.Fatalf("角色数据转换失败: %v", err)
		}
		
		// 保存到数据库
		if err := db.Create(character).Error; err != nil {
			t.Fatalf("保存角色到数据库失败: %v", err)
		}
		
		// 验证保存的数据
		var savedCharacter models.Character
		if err := db.First(&savedCharacter, character.ID).Error; err != nil {
			t.Fatalf("从数据库获取角色失败: %v", err)
		}
		
		if savedCharacter.Name != "图书管理员莫里斯" {
			t.Errorf("期望角色名称为 '图书管理员莫里斯'，实际为 '%s'", savedCharacter.Name)
		}
		
		if savedCharacter.CharacterType != "npc" {
			t.Errorf("期望角色类型为 'npc'，实际为 '%s'", savedCharacter.CharacterType)
		}
	})
}
