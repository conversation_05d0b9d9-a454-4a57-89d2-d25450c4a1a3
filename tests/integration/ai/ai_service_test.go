package ai

import (
	"context"
	"testing"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB 创建测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// 创建AI交互表
	err = db.Exec(`
		CREATE TABLE ai_interactions (
			id TEXT PRIMARY KEY,
			world_id TEXT,
			user_id TEXT,
			interaction_type TEXT NOT NULL,
			prompt TEXT NOT NULL,
			response TEXT,
			response_schema TEXT DEFAULT '{}',
			token_usage INTEGER DEFAULT 0,
			response_time INTEGER DEFAULT 0,
			status TEXT DEFAULT 'pending',
			error_message TEXT,
			created_at DATETIME,
			updated_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	return db
}

// setupTestAIService 创建测试AI服务
func setupTestAIService(t *testing.T) *Service {
	cfg := &config.Config{
		AI: config.AIConfig{
			MockEnabled: true, // 使用mock模式进行测试
			BaseURL:     "https://test.windmill.com",
			Token:       "test-token",
			Timeout:     30,
			MaxRetries:  3,
			RetryDelay:  1,
			Windmill: config.WindmillConfig{
				Workspace:    "test-workspace",
				DefaultModel: "gemini-1.5-pro",
			},
		},
	}
	db := setupTestDB(t)
	log := logger.New("debug")

	service := NewService(cfg, db, log)
	return service
}

// setupTestWindmillClient 创建测试Windmill客户端
func setupTestWindmillClient(t *testing.T) *WindmillClient {
	cfg := &config.Config{
		AI: config.AIConfig{
			BaseURL:    "https://test.windmill.com",
			Token:      "test-token",
			Timeout:    30,
			MaxRetries: 3,
			RetryDelay: 1,
			Windmill: config.WindmillConfig{
				Workspace:    "test-workspace",
				DefaultModel: "gemini-1.5-pro",
			},
		},
	}
	log := logger.New("debug")

	client := NewWindmillClient(cfg, log)
	return client
}

func TestNewAIService(t *testing.T) {
	cfg := &config.Config{
		AI: config.AIConfig{
			MockEnabled: true,
			Windmill: config.WindmillConfig{
				Workspace:    "test-workspace",
				DefaultModel: "gemini-1.5-pro",
			},
		},
	}
	db := setupTestDB(t)
	log := logger.New("debug")

	service := NewService(cfg, db, log)
	assert.NotNil(t, service)
	assert.NotNil(t, service.windmillClient)
}

func TestNewWindmillClient(t *testing.T) {
	client := setupTestWindmillClient(t)
	assert.NotNil(t, client)
	assert.Equal(t, "https://test.windmill.com", client.baseURL)
	assert.Equal(t, "test-workspace", client.workspace)
	assert.Equal(t, "test-token", client.token)
}

func TestWindmillClient_BuildRequest(t *testing.T) {
	service := setupTestAIService(t)

	// 测试场景生成的响应结构构建
	schema := service.buildResponseSchema("scene", nil)
	assert.NotNil(t, schema)
	assert.Equal(t, "object", schema["type"])

	properties, ok := schema["properties"].(map[string]interface{})
	assert.True(t, ok)
	assert.Contains(t, properties, "name")
	assert.Contains(t, properties, "description")
	assert.Contains(t, properties, "atmosphere")
	assert.Contains(t, properties, "key_features")
	assert.Contains(t, properties, "possible_actions")
	assert.Contains(t, properties, "connections")
}

func TestWindmillClient_GetModelForType(t *testing.T) {
	service := setupTestAIService(t)

	// 测试不同内容类型的模型选择
	sceneModel := service.getModelForType("scene")
	assert.Equal(t, "gemini-1.5-pro", sceneModel)

	characterModel := service.getModelForType("character")
	assert.Equal(t, "gemini-1.5-pro", characterModel)

	eventModel := service.getModelForType("event")
	assert.Equal(t, "gemini-1.5-pro", eventModel)

	defaultModel := service.getModelForType("unknown")
	assert.Equal(t, "gemini-1.5-pro", defaultModel)
}

func TestWindmillClient_ExtractContentFromResult(t *testing.T) {
	service := setupTestAIService(t)

	// 测试从结构化结果中提取内容
	result1 := map[string]interface{}{
		"description": "这是一个测试描述",
		"name":        "测试名称",
	}
	content, ok := service.extractContentFromResult(result1)
	assert.True(t, ok)
	assert.Equal(t, "这是一个测试描述", content)

	result2 := map[string]interface{}{
		"name": "只有名称",
	}
	content, ok = service.extractContentFromResult(result2)
	assert.True(t, ok)
	assert.Equal(t, "只有名称", content)

	result3 := map[string]interface{}{
		"content": "内容字段",
	}
	content, ok = service.extractContentFromResult(result3)
	assert.True(t, ok)
	assert.Equal(t, "内容字段", content)

	result4 := map[string]interface{}{
		"other": "其他字段",
	}
	content, ok = service.extractContentFromResult(result4)
	assert.False(t, ok)
	assert.Empty(t, content)
}

func TestWindmillClient_EstimateTokenUsage(t *testing.T) {
	service := setupTestAIService(t)

	prompt := "这是一个测试提示词"
	result := map[string]interface{}{
		"description": "这是一个测试结果",
		"name":        "测试",
	}

	tokenUsage := service.estimateTokenUsage(prompt, result)
	assert.Greater(t, tokenUsage, 0)
	assert.Greater(t, tokenUsage, 50) // 应该包含系统开销
}

func TestGenerateContent_WithWindmillInterface(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()

	worldID := uuid.New()
	userID := uuid.New()

	// 测试使用新的Windmill结构化文本接口生成场景
	req := &GenerateRequest{
		Type:        "scene",
		Prompt:      "生成一个神秘的魔法森林场景，包含古老的遗迹和神秘的生物",
		Context:     map[string]interface{}{"theme": "fantasy", "mood": "mysterious"},
		MaxTokens:   500,
		Temperature: 0.7,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema: map[string]interface{}{
			"name":             "string",
			"description":      "string",
			"atmosphere":       "string",
			"key_features":     "array",
			"possible_actions": "array",
			"connections":      "array",
		},
	}

	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)

	// 验证结构化数据包含期望的字段
	structuredData := response.StructuredData
	assert.Contains(t, structuredData, "name")
	assert.Contains(t, structuredData, "description")
	assert.Contains(t, structuredData, "atmosphere")

	// 验证字段类型
	if name, ok := structuredData["name"].(string); ok {
		assert.NotEmpty(t, name)
	}
	if description, ok := structuredData["description"].(string); ok {
		assert.NotEmpty(t, description)
	}
}

func TestGenerateContent_WithWindmillInterface_Character(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()

	worldID := uuid.New()
	userID := uuid.New()

	// 测试使用新的Windmill结构化文本接口生成角色
	req := &GenerateRequest{
		Type:        "character",
		Prompt:      "生成一个智慧的精灵法师，擅长自然魔法",
		Context:     map[string]interface{}{"theme": "fantasy", "role": "mage"},
		MaxTokens:   400,
		Temperature: 0.8,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema: map[string]interface{}{
			"name":           "string",
			"description":    "string",
			"personality":    "array",
			"background":     "string",
			"skills":         "array",
			"dialogue_style": "string",
			"motivations":    "array",
		},
	}

	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)

	// 验证结构化数据包含期望的字段
	structuredData := response.StructuredData
	assert.Contains(t, structuredData, "name")
	assert.Contains(t, structuredData, "description")
	assert.Contains(t, structuredData, "personality")
	assert.Contains(t, structuredData, "background")
	assert.Contains(t, structuredData, "skills")
}

func TestGenerateContent_Scene(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()
	
	worldID := uuid.New()
	userID := uuid.New()
	
	req := &GenerateRequest{
		Type:        "scene",
		Prompt:      "生成一个神秘的森林场景",
		Context:     map[string]interface{}{"theme": "fantasy"},
		MaxTokens:   500,
		Temperature: 0.7,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
		},
	}
	
	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)
}

func TestGenerateContent_Character(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()
	
	worldID := uuid.New()
	userID := uuid.New()
	
	req := &GenerateRequest{
		Type:        "character",
		Prompt:      "生成一个精灵法师角色",
		Context:     map[string]interface{}{"theme": "fantasy"},
		MaxTokens:   400,
		Temperature: 0.8,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
		},
	}
	
	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)
}

func TestGenerateContent_Event(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()
	
	worldID := uuid.New()
	userID := uuid.New()
	
	req := &GenerateRequest{
		Type:        "event",
		Prompt:      "生成一个冒险事件",
		Context:     map[string]interface{}{"theme": "fantasy"},
		MaxTokens:   300,
		Temperature: 0.6,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
		},
	}
	
	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)
}

func TestGenerateContent_Dialogue(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()
	
	worldID := uuid.New()
	userID := uuid.New()
	
	req := &GenerateRequest{
		Type:        "dialogue",
		Prompt:      "生成村民的友好问候",
		Context:     map[string]interface{}{"theme": "fantasy"},
		MaxTokens:   200,
		Temperature: 0.9,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema: map[string]interface{}{
			"content": "string",
			"tone":    "string",
		},
	}
	
	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)
}

func TestGenerateContent_General(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()
	
	worldID := uuid.New()
	userID := uuid.New()
	
	req := &GenerateRequest{
		Type:        "unknown_type",
		Prompt:      "生成通用内容",
		Context:     map[string]interface{}{},
		MaxTokens:   100,
		Temperature: 0.5,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema:      map[string]interface{}{},
	}
	
	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)
}
