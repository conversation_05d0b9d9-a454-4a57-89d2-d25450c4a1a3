package test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"ai-text-game-iam-npc/internal/handlers"
	"ai-text-game-iam-npc/internal/ai"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// MockAIService AI服务的模拟实现
type MockAIService struct {
	mock.Mock
}

func (m *MockAIService) GenerateContent(ctx context.Context, req *ai.GenerateRequest) (*ai.GenerateResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*ai.GenerateResponse), args.Error(1)
}

func (m *MockAIService) GenerateImage(ctx context.Context, req *ai.ImageRequest) (*ai.ImageResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*ai.ImageResponse), args.Error(1)
}

func (m *MockAIService) GetUsage(ctx context.Context, userID string) (*ai.UsageResponse, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*ai.UsageResponse), args.Error(1)
}

// TestGenerateWorlds 测试AI生成世界配置接口
func TestGenerateWorlds(t *testing.T) {
	// 设置测试环境
	gin.SetMode(gin.TestMode)
	
	// 创建模拟的AI服务
	mockAI := new(MockAIService)
	
	// 创建测试用的世界配置响应
	mockWorldConfig := map[string]interface{}{
		"worldDescription": "这是一个测试生成的魔法世界，充满了神秘和冒险。",
		"worldRules": []map[string]interface{}{
			{
				"name":        "魔法消耗",
				"description": "使用魔法会消耗魔力值",
				"category":    "magic",
				"severity":    "medium",
				"enforcement": "automatic",
			},
		},
		"environment": map[string]interface{}{
			"climate": map[string]interface{}{
				"type":            "temperate",
				"seasons":         []string{"spring", "summer", "autumn", "winter"},
				"weatherPatterns": []string{"sunny", "cloudy", "rainy"},
			},
		},
	}

	// 设置AI服务的期望调用
	mockAI.On("GenerateContent", mock.Anything, mock.MatchedBy(func(req *ai.GenerateRequest) bool {
		return req.Type == "world"
	})).Return(&ai.GenerateResponse{
		Content:        "",
		StructuredData: mockWorldConfig,
		TokensUsed:     500,
		Model:          "test-model",
	}, nil)

	// 创建游戏处理器
	gameHandler := &handlers.GameHandler{
		// 这里需要根据实际的GameHandler构造函数来设置
		// 由于我们没有完整的依赖注入设置，这里简化处理
	}

	// 创建测试路由
	router := gin.New()
	router.POST("/api/worlds/generate", gameHandler.GenerateWorlds)

	// 准备测试请求
	requestBody := handlers.GenerateWorldsRequest{
		WorldName: "测试魔法世界",
		WorldSettings: map[string]interface{}{
			"theme":      "fantasy",
			"difficulty": "medium",
		},
	}

	jsonBody, _ := json.Marshal(requestBody)

	// 创建HTTP请求
	req, _ := http.NewRequest("POST", "/api/worlds/generate", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	
	// 添加认证头（在实际测试中需要有效的JWT token）
	req.Header.Set("Authorization", "Bearer test-token")

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response handlers.Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)

	// 验证响应数据结构
	data, ok := response.Data.(map[string]interface{})
	assert.True(t, ok)

	candidates, ok := data["candidates"].([]interface{})
	assert.True(t, ok)
	assert.Greater(t, len(candidates), 0)

	// 验证AI服务被正确调用
	mockAI.AssertExpectations(t)
}

// TestWorldConfigurationValidation 测试世界配置数据验证
func TestWorldConfigurationValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      handlers.WorldConfiguration
		expectValid bool
	}{
		{
			name: "有效的基本配置",
			config: handlers.WorldConfiguration{
				WorldDescription: "这是一个有效的世界描述，包含足够的详细信息来描述这个游戏世界的基本特征和背景设定。",
			},
			expectValid: true,
		},
		{
			name: "描述太短",
			config: handlers.WorldConfiguration{
				WorldDescription: "太短",
			},
			expectValid: false,
		},
		{
			name: "包含规则的完整配置",
			config: handlers.WorldConfiguration{
				WorldDescription: "这是一个包含规则的完整世界配置，描述了一个复杂的游戏世界。",
				WorldRules: []handlers.WorldRule{
					{
						Name:        "测试规则",
						Description: "这是一个测试规则的描述",
						Category:    "magic",
						Severity:    "medium",
						Enforcement: "automatic",
					},
				},
			},
			expectValid: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 这里可以添加实际的验证逻辑
			// 例如使用JSON Schema验证器
			isValid := validateWorldConfiguration(tt.config)
			assert.Equal(t, tt.expectValid, isValid)
		})
	}
}

// validateWorldConfiguration 验证世界配置的辅助函数
func validateWorldConfiguration(config handlers.WorldConfiguration) bool {
	// 基本验证：世界描述必须存在且长度合适
	if len(config.WorldDescription) < 50 {
		return false
	}
	
	if len(config.WorldDescription) > 2000 {
		return false
	}

	// 验证规则
	if len(config.WorldRules) > 20 {
		return false
	}

	for _, rule := range config.WorldRules {
		if rule.Name == "" || rule.Description == "" {
			return false
		}
		
		// 验证类别枚举值
		validCategories := []string{"magic", "combat", "social", "economic", "environmental", "character", "other"}
		if !contains(validCategories, rule.Category) {
			return false
		}
		
		// 验证严重性枚举值
		validSeverities := []string{"low", "medium", "high", "critical"}
		if !contains(validSeverities, rule.Severity) {
			return false
		}
		
		// 验证执行方式枚举值
		validEnforcements := []string{"automatic", "manual", "community", "none"}
		if !contains(validEnforcements, rule.Enforcement) {
			return false
		}
	}

	return true
}

// contains 检查字符串切片是否包含指定值的辅助函数
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// TestWorldPreviewGeneration 测试世界预览信息生成
func TestWorldPreviewGeneration(t *testing.T) {
	// 创建测试配置
	config := handlers.WorldConfiguration{
		WorldDescription: "这是一个充满魔法的幻想世界，古老的龙族统治着天空，各种族为了争夺魔法水晶而竞争。",
		WorldRules: []handlers.WorldRule{
			{Name: "魔法消耗", Description: "使用魔法消耗魔力", Category: "magic"},
			{Name: "死亡惩罚", Description: "死亡有惩罚", Category: "character"},
		},
		Environment: &handlers.EnvironmentConfig{
			Climate: &handlers.ClimateConfig{
				Type: "temperate",
			},
			Terrain: &handlers.TerrainConfig{
				PrimaryTerrain: "forests",
			},
		},
	}

	// 这里需要实际的GameHandler实例来测试generateWorldPreview方法
	// 由于依赖注入的复杂性，这里简化为直接测试逻辑

	// 验证预览生成逻辑
	worldName := "测试世界"
	
	// 模拟预览生成
	shortDesc := config.WorldDescription
	if len(shortDesc) > 100 {
		shortDesc = shortDesc[:100] + "..."
	}

	features := []string{}
	if config.Environment != nil && config.Environment.Climate != nil {
		features = append(features, config.Environment.Climate.Type+"气候")
	}
	if config.Environment != nil && config.Environment.Terrain != nil {
		features = append(features, config.Environment.Terrain.PrimaryTerrain+"地形")
	}

	// 验证结果
	assert.NotEmpty(t, shortDesc)
	assert.Greater(t, len(features), 0)
	assert.Contains(t, features, "temperate气候")
	assert.Contains(t, features, "forests地形")
}
