// +build ignore

package main

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

// 测试用的响应结构
type TestAPIResponse struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data,omitempty"`
	Error     *APIError   `json:"error,omitempty"`
	Timestamp string      `json:"timestamp"`
	RequestID string      `json:"request_id"`
}

type TestWorld struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	Description    string `json:"description"`
	Theme          string `json:"theme"`
	IsPublic       bool   `json:"is_public"`
	MaxPlayers     int    `json:"max_players"`
	CurrentPlayers int    `json:"current_players"`
	CreatorID      string `json:"creator_id"`
	CreatedAt      string `json:"created_at"`
	UpdatedAt      string `json:"updated_at"`
}

type TestWorldListResponse struct {
	Worlds []TestWorld `json:"worlds"`
	Total  int         `json:"total"`
	Page   int         `json:"page"`
	Limit  int         `json:"limit"`
}

// 设置测试路由
func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	
	// 初始化示例数据
	initSampleWorlds()
	
	r := gin.New()
	r.Use(corsMiddleware())
	setupAPIRoutes(r)
	
	return r
}

// 测试世界创建功能 - 验证不再返回硬编码的"world-new"
func TestCreateWorld_NoHardcodedID(t *testing.T) {
	router := setupTestRouter()
	
	// 准备请求数据
	worldData := map[string]interface{}{
		"name":        "测试世界",
		"description": "这是一个测试世界",
		"theme":       "fantasy",
		"is_public":   true,
		"max_players": 10,
	}
	
	jsonData, _ := json.Marshal(worldData)
	
	// 发送POST请求
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/game/worlds", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	
	router.ServeHTTP(w, req)
	
	// 验证响应
	assert.Equal(t, http.StatusCreated, w.Code)
	
	var response TestAPIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	
	// 验证返回的世界数据
	worldDataBytes, _ := json.Marshal(response.Data)
	var world TestWorld
	err = json.Unmarshal(worldDataBytes, &world)
	assert.NoError(t, err)
	
	// 关键测试：验证ID不是硬编码的"world-new"
	assert.NotEqual(t, "world-new", world.ID, "世界ID不应该是硬编码的'world-new'")
	
	// 验证ID是有效的UUID格式
	_, err = uuid.Parse(world.ID)
	assert.NoError(t, err, "世界ID应该是有效的UUID格式")
	
	// 验证其他字段
	assert.Equal(t, "测试世界", world.Name)
	assert.Equal(t, "这是一个测试世界", world.Description)
	assert.Equal(t, "fantasy", world.Theme)
	assert.True(t, world.IsPublic)
	assert.Equal(t, 10, world.MaxPlayers)
	assert.Equal(t, 0, world.CurrentPlayers)
}

// 测试获取世界列表 - 验证使用内存存储而不是硬编码数据
func TestGetWorlds_UsesMemoryStore(t *testing.T) {
	router := setupTestRouter()
	
	// 先创建一个世界
	worldData := map[string]interface{}{
		"name":        "动态世界",
		"description": "这是一个动态创建的世界",
		"theme":       "sci-fi",
		"is_public":   true,
		"max_players": 5,
	}
	
	jsonData, _ := json.Marshal(worldData)
	
	// 创建世界
	w1 := httptest.NewRecorder()
	req1, _ := http.NewRequest("POST", "/api/v1/game/worlds", bytes.NewBuffer(jsonData))
	req1.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w1, req1)
	
	// 获取世界列表
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("GET", "/api/v1/worlds", nil)
	router.ServeHTTP(w2, req2)
	
	// 验证响应
	assert.Equal(t, http.StatusOK, w2.Code)
	
	var response TestAPIResponse
	err := json.Unmarshal(w2.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	
	// 验证返回的世界列表
	worldsDataBytes, _ := json.Marshal(response.Data)
	var worlds []TestWorld
	err = json.Unmarshal(worldsDataBytes, &worlds)
	assert.NoError(t, err)
	
	// 验证列表中包含我们刚创建的世界
	found := false
	for _, world := range worlds {
		if world.Name == "动态世界" {
			found = true
			assert.Equal(t, "sci-fi", world.Theme)
			assert.Equal(t, 5, world.MaxPlayers)
			break
		}
	}
	assert.True(t, found, "应该能在世界列表中找到新创建的世界")
}

// 测试获取我的世界列表
func TestGetMyWorlds_WithPagination(t *testing.T) {
	router := setupTestRouter()
	
	// 测试分页参数
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/game/my-worlds?page=1&limit=5", nil)
	router.ServeHTTP(w, req)
	
	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)
	
	var response TestAPIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	
	// 验证分页数据结构
	responseDataBytes, _ := json.Marshal(response.Data)
	var listResponse TestWorldListResponse
	err = json.Unmarshal(responseDataBytes, &listResponse)
	assert.NoError(t, err)
	
	assert.Equal(t, 1, listResponse.Page)
	assert.Equal(t, 5, listResponse.Limit)
	assert.GreaterOrEqual(t, listResponse.Total, 0)
}

// 测试获取公开世界列表
func TestGetPublicWorlds_FiltersByPublic(t *testing.T) {
	router := setupTestRouter()
	
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/game/public-worlds", nil)
	router.ServeHTTP(w, req)
	
	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)
	
	var response TestAPIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	
	// 验证只返回公开的世界
	responseDataBytes, _ := json.Marshal(response.Data)
	var listResponse TestWorldListResponse
	err = json.Unmarshal(responseDataBytes, &listResponse)
	assert.NoError(t, err)
	
	// 验证所有返回的世界都是公开的
	for _, world := range listResponse.Worlds {
		assert.True(t, world.IsPublic, "公开世界列表中的所有世界都应该是公开的")
	}
}

// 测试获取特定世界详情
func TestGetWorld_ReturnsCorrectWorld(t *testing.T) {
	router := setupTestRouter()
	
	// 先创建一个世界
	worldData := map[string]interface{}{
		"name":        "特定测试世界",
		"description": "用于测试获取特定世界的世界",
		"theme":       "medieval",
		"is_public":   false,
		"max_players": 12,
	}
	
	jsonData, _ := json.Marshal(worldData)
	
	// 创建世界
	w1 := httptest.NewRecorder()
	req1, _ := http.NewRequest("POST", "/api/v1/game/worlds", bytes.NewBuffer(jsonData))
	req1.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w1, req1)
	
	// 获取创建的世界ID
	var createResponse TestAPIResponse
	json.Unmarshal(w1.Body.Bytes(), &createResponse)
	worldDataBytes, _ := json.Marshal(createResponse.Data)
	var createdWorld TestWorld
	json.Unmarshal(worldDataBytes, &createdWorld)
	
	// 获取特定世界详情
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("GET", "/api/v1/worlds/"+createdWorld.ID, nil)
	router.ServeHTTP(w2, req2)
	
	// 验证响应
	assert.Equal(t, http.StatusOK, w2.Code)
	
	var getResponse TestAPIResponse
	err := json.Unmarshal(w2.Body.Bytes(), &getResponse)
	assert.NoError(t, err)
	assert.True(t, getResponse.Success)
	
	// 验证返回的世界数据
	getWorldDataBytes, _ := json.Marshal(getResponse.Data)
	var retrievedWorld TestWorld
	err = json.Unmarshal(getWorldDataBytes, &retrievedWorld)
	assert.NoError(t, err)
	
	assert.Equal(t, createdWorld.ID, retrievedWorld.ID)
	assert.Equal(t, "特定测试世界", retrievedWorld.Name)
	assert.Equal(t, "medieval", retrievedWorld.Theme)
	assert.False(t, retrievedWorld.IsPublic)
	assert.Equal(t, 12, retrievedWorld.MaxPlayers)
}

// 测试获取不存在的世界
func TestGetWorld_NotFound(t *testing.T) {
	router := setupTestRouter()
	
	// 使用一个不存在的UUID
	nonExistentID := uuid.New().String()
	
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/worlds/"+nonExistentID, nil)
	router.ServeHTTP(w, req)
	
	// 验证返回404
	assert.Equal(t, http.StatusNotFound, w.Code)
	
	var response TestAPIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Success)
	assert.NotNil(t, response.Error)
	assert.Equal(t, "WORLD_NOT_FOUND", response.Error.Code)
}
