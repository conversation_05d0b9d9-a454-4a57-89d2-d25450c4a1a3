package database

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestCompatibilityConfig_GetTriggerSQL_SQLite 测试SQLite触发器SQL生成
func TestCompatibilityConfig_GetTriggerSQL_SQLite(t *testing.T) {
	// 创建SQLite数据库连接
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	compatibility := NewCompatibilityConfig(db)
	assert.Equal(t, SQLite, compatibility.DBType, "应该识别为SQLite数据库")

	// 测试不同表的触发器生成
	testCases := []struct {
		tableName       string
		expectedKeyword string
		description     string
	}{
		{
			tableName:       "users",
			expectedKeyword: "WHERE id = NEW.id",
			description:     "users表应该使用id字段",
		},
		{
			tableName:       "user_stats",
			expectedKeyword: "WHERE user_id = NEW.user_id",
			description:     "user_stats表应该使用user_id字段",
		},
		{
			tableName:       "worlds",
			expectedKeyword: "WHERE id = NEW.id",
			description:     "worlds表应该使用id字段",
		},
		{
			tableName:       "characters",
			expectedKeyword: "WHERE id = NEW.id",
			description:     "characters表应该使用id字段",
		},
		{
			tableName:       "scenes",
			expectedKeyword: "WHERE id = NEW.id",
			description:     "scenes表应该使用id字段",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.tableName, func(t *testing.T) {
			triggerSQL := compatibility.GetTriggerSQL(tc.tableName)
			
			// 验证基本结构
			assert.Contains(t, triggerSQL, "CREATE TRIGGER", "应该包含CREATE TRIGGER")
			assert.Contains(t, triggerSQL, "update_"+tc.tableName+"_updated_at", "应该包含正确的触发器名称")
			assert.Contains(t, triggerSQL, "AFTER UPDATE ON "+tc.tableName, "应该包含正确的表名")
			assert.Contains(t, triggerSQL, "FOR EACH ROW", "应该包含FOR EACH ROW")
			assert.Contains(t, triggerSQL, "WHEN NEW.updated_at = OLD.updated_at", "应该包含WHEN条件")
			assert.Contains(t, triggerSQL, "datetime('now')", "应该使用SQLite的datetime函数")
			
			// 验证主键字段
			assert.Contains(t, triggerSQL, tc.expectedKeyword, tc.description)
		})
	}
}

// TestCompatibilityConfig_GetPrimaryKeyField 测试主键字段获取
func TestCompatibilityConfig_GetPrimaryKeyField(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	compatibility := NewCompatibilityConfig(db)

	// 测试各种表名的主键字段映射
	testCases := []struct {
		tableName    string
		expectedKey  string
		description  string
	}{
		{"user_stats", "user_id", "user_stats表应该使用user_id作为主键"},
		{"users", "id", "users表应该使用id作为主键"},
		{"worlds", "id", "worlds表应该使用id作为主键"},
		{"characters", "id", "characters表应该使用id作为主键"},
		{"scenes", "id", "scenes表应该使用id作为主键"},
		{"entities", "id", "entities表应该使用id作为主键"},
		{"game_events", "id", "game_events表应该使用id作为主键"},
		{"unknown_table", "id", "未知表应该默认使用id作为主键"},
		{"", "id", "空表名应该默认使用id作为主键"},
	}

	for _, tc := range testCases {
		t.Run(tc.tableName+"_primary_key", func(t *testing.T) {
			// 通过生成触发器SQL来间接测试主键字段逻辑
			triggerSQL := compatibility.GetTriggerSQL(tc.tableName)
			expectedPattern := tc.expectedKey + " = NEW." + tc.expectedKey
			assert.Contains(t, triggerSQL, expectedPattern, tc.description)
		})
	}
}

// TestCompatibilityConfig_TransformSQL 测试SQL转换功能
func TestCompatibilityConfig_TransformSQL(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	compatibility := NewCompatibilityConfig(db)

	// 测试PostgreSQL到SQLite的SQL转换
	testCases := []struct {
		input       string
		expected    string
		description string
	}{
		{
			input:       "SELECT gen_random_uuid()",
			expected:    "SELECT lower(hex(randomblob(16)))",
			description: "应该转换UUID生成函数",
		},
		{
			input:       "CREATE TABLE test (data JSONB)",
			expected:    "CREATE TABLE test (data TEXT)",
			description: "应该转换JSONB类型为TEXT",
		},
		{
			input:       "CREATE TABLE test (created_at TIMESTAMP WITH TIME ZONE)",
			expected:    "CREATE TABLE test (created_at DATETIME)",
			description: "应该转换时间戳类型",
		},
		{
			input:       "SELECT NOW()",
			expected:    "SELECT datetime('now')",
			description: "应该转换NOW()函数",
		},
		{
			input:       "CREATE TABLE test (id UUID PRIMARY KEY)",
			expected:    "CREATE TABLE test (id TEXT PRIMARY KEY)",
			description: "应该转换UUID类型为TEXT",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			result := compatibility.TransformSQL(tc.input)
			assert.Equal(t, tc.expected, result, tc.description)
		})
	}
}

// TestCompatibilityConfig_SupportFeatures 测试功能支持检查
func TestCompatibilityConfig_SupportFeatures(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	compatibility := NewCompatibilityConfig(db)

	// 测试SQLite的功能支持
	assert.False(t, compatibility.SupportsJSONB(), "SQLite不应该支持JSONB")
	assert.False(t, compatibility.SupportsUUIDType(), "SQLite不应该支持原生UUID类型")
	assert.True(t, compatibility.SupportsTriggers(), "SQLite应该支持触发器")
}

// TestCompatibilityConfig_GetUpdateTimestampFunctionSQL 测试时间戳函数SQL
func TestCompatibilityConfig_GetUpdateTimestampFunctionSQL(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	compatibility := NewCompatibilityConfig(db)

	functionSQL := compatibility.GetUpdateTimestampFunctionSQL()
	
	// SQLite不需要单独的函数，应该返回注释
	assert.Contains(t, functionSQL, "SQLite使用触发器直接更新", "SQLite应该返回说明注释")
}

// TestCompatibilityConfig_ValidateCompatibility 测试兼容性验证
func TestCompatibilityConfig_ValidateCompatibility(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	compatibility := NewCompatibilityConfig(db)

	// SQLite应该通过兼容性验证
	err = compatibility.ValidateCompatibility()
	assert.NoError(t, err, "SQLite应该通过兼容性验证")
}

// TestCompatibilityConfig_TriggerSQLStructure 测试触发器SQL结构完整性
func TestCompatibilityConfig_TriggerSQLStructure(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	compatibility := NewCompatibilityConfig(db)

	// 测试生成的触发器SQL是否包含所有必要的组件
	triggerSQL := compatibility.GetTriggerSQL("test_table")

	requiredComponents := []string{
		"CREATE TRIGGER",
		"update_test_table_updated_at",
		"AFTER UPDATE ON test_table",
		"FOR EACH ROW",
		"WHEN NEW.updated_at = OLD.updated_at",
		"BEGIN",
		"UPDATE test_table SET updated_at = datetime('now')",
		"WHERE id = NEW.id",
		"END;",
	}

	for _, component := range requiredComponents {
		assert.Contains(t, triggerSQL, component, 
			"触发器SQL应该包含必要组件: %s", component)
	}

	// 验证SQL格式正确（没有语法错误的基本检查）
	assert.True(t, strings.Count(triggerSQL, "BEGIN") == strings.Count(triggerSQL, "END;"),
		"BEGIN和END应该配对")
}

// TestCompatibilityConfig_EdgeCases 测试边界情况
func TestCompatibilityConfig_EdgeCases(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	compatibility := NewCompatibilityConfig(db)

	// 测试空表名
	triggerSQL := compatibility.GetTriggerSQL("")
	assert.Contains(t, triggerSQL, "CREATE TRIGGER", "空表名也应该生成基本的触发器结构")

	// 测试特殊字符表名（虽然不推荐，但应该能处理）
	specialTableName := "test_table_123"
	triggerSQL = compatibility.GetTriggerSQL(specialTableName)
	assert.Contains(t, triggerSQL, specialTableName, "应该能处理包含数字的表名")

	// 测试SQL转换的边界情况
	emptySQL := compatibility.TransformSQL("")
	assert.Equal(t, "", emptySQL, "空SQL应该返回空字符串")

	// 测试不需要转换的SQL
	normalSQL := "SELECT * FROM users"
	transformedSQL := compatibility.TransformSQL(normalSQL)
	assert.Equal(t, normalSQL, transformedSQL, "不需要转换的SQL应该保持不变")
}
