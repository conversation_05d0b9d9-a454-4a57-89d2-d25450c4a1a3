#!/bin/bash
# 运行集成测试脚本

echo "🔗 运行集成测试..."
echo "⚠️  注意: 集成测试需要数据库和其他外部服务"

# 设置测试环境变量
export ENVIRONMENT=test
export DATABASE_MODE=sqlite
export TEST_DB_PATH=":memory:"

go test -v ./tests/integration/... -cover -coverprofile=tests/integration_coverage.out

if [ $? -eq 0 ]; then
    echo "✅ 集成测试通过"
    echo "📊 生成覆盖率报告..."
    go tool cover -html=tests/integration_coverage.out -o tests/integration_coverage.html
    echo "📋 覆盖率报告已生成: tests/integration_coverage.html"
else
    echo "❌ 集成测试失败"
    exit 1
fi
