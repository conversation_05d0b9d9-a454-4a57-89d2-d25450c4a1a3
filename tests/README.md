# AI文本游戏测试目录说明

## 概述

本目录包含了AI文本游戏项目的所有Go测试文件，按照测试类型和模块进行了重新组织，提供了清晰的目录结构和便捷的测试运行方式。

## 目录结构

```
tests/
├── unit/                    # 单元测试
│   ├── ai/                 # AI模块单元测试
│   ├── handlers/           # 处理器单元测试
│   ├── models/             # 数据模型单元测试
│   ├── services/           # 服务层单元测试
│   └── ...
├── integration/            # 集成测试
│   ├── ai/                 # AI模块集成测试
│   ├── apidoc/             # API文档集成测试
│   ├── debug/              # 调试服务集成测试
│   ├── game/               # 游戏逻辑集成测试
│   ├── handlers/           # 处理器集成测试
│   ├── migration/          # 数据库迁移集成测试
│   ├── models/             # 数据模型集成测试
│   ├── pkg/                # 公共包集成测试
│   ├── services/           # 服务层集成测试
│   ├── validation/         # 验证服务集成测试
│   └── misc/               # 其他集成测试
├── e2e/                    # 端到端测试（目录结构同上）
├── fixtures/               # 测试数据文件
├── helpers/                # 测试辅助函数
├── mocks/                  # 模拟对象
├── testdata/               # 测试数据
├── run_unit_tests.sh       # 单元测试运行脚本
├── run_integration_tests.sh # 集成测试运行脚本
├── run_e2e_tests.sh        # 端到端测试运行脚本
├── run_all_tests.sh        # 全部测试运行脚本
└── file_moves.log          # 文件移动记录
```

## 测试分类

### 🧪 单元测试 (Unit Tests)
- **位置**: `tests/unit/`
- **特点**: 测试单个函数或方法的功能
- **依赖**: 最小化外部依赖，使用模拟对象
- **运行速度**: 快速
- **文件数量**: 8个

**包含的测试文件**:
- `ai_game_schemas_test.go` - AI游戏模式测试
- `ai_schema_test.go` - AI模式定义测试
- `ai_transformer_test.go` - AI数据转换测试
- `handlers_ai_test.go` - AI处理器测试
- `models_character_memory_test.go` - 角色记忆模型测试
- `models_time_schedule_test.go` - 时间调度模型测试
- `services_character_test.go` - 角色服务测试
- `services_world_test.go` - 世界服务测试

### 🔗 集成测试 (Integration Tests)
- **位置**: `tests/integration/`
- **特点**: 测试多个组件之间的交互
- **依赖**: 需要数据库、外部服务等
- **运行速度**: 中等
- **文件数量**: 18个

**主要模块**:
- **AI模块**: AI服务集成、AI处理流程
- **游戏模块**: 世界服务、时间管理、角色交互
- **处理器模块**: HTTP处理器、游戏角色处理
- **数据模块**: 用户会话、数据库迁移
- **服务模块**: 认证服务、验证服务
- **其他**: API调试、世界生成等

### 🌐 端到端测试 (E2E Tests)
- **位置**: `tests/e2e/`
- **特点**: 测试完整的用户场景
- **依赖**: 需要完整的应用环境
- **运行速度**: 较慢
- **文件数量**: 0个（待添加）

## 测试运行

### 快速开始

```bash
# 运行所有测试
./tests/run_all_tests.sh

# 仅运行单元测试
./tests/run_unit_tests.sh

# 仅运行集成测试
./tests/run_integration_tests.sh

# 仅运行端到端测试
./tests/run_e2e_tests.sh
```

### 手动运行

```bash
# 运行特定模块的单元测试
go test -v ./tests/unit/ai/...

# 运行特定模块的集成测试
go test -v ./tests/integration/game/...

# 运行所有单元测试并生成覆盖率报告
go test -v ./tests/unit/... -cover -coverprofile=unit_coverage.out

# 运行所有集成测试
go test -v ./tests/integration/...
```

### 环境变量

测试运行时可能需要设置以下环境变量：

```bash
# 基本环境变量
export ENVIRONMENT=test
export DATABASE_MODE=sqlite
export TEST_DB_PATH=":memory:"

# 集成测试环境变量
export SKIP_AUTH=true
export DEV_ENABLE_DEBUG_LOGS=false

# 端到端测试环境变量
export FRONTEND_PORT=3001
export BACKEND_PORT=8081
```

## 测试数据管理

### 测试数据目录
- `fixtures/` - 静态测试数据文件
- `testdata/` - Go测试约定的测试数据目录
- `mocks/` - 模拟对象和接口

### 测试数据库
- 单元测试：使用内存SQLite数据库 (`:memory:`)
- 集成测试：使用临时SQLite文件或测试PostgreSQL实例
- 端到端测试：使用独立的测试数据库

## 覆盖率报告

测试运行后会生成覆盖率报告：

- `unit_coverage.out` - 单元测试覆盖率数据
- `unit_coverage.html` - 单元测试覆盖率HTML报告
- `integration_coverage.out` - 集成测试覆盖率数据
- `integration_coverage.html` - 集成测试覆盖率HTML报告

查看覆盖率报告：
```bash
# 在浏览器中打开覆盖率报告
open tests/unit_coverage.html
open tests/integration_coverage.html
```

## 注意事项

### ⚠️ Import路径
由于测试文件已移动到新位置，部分测试文件中的import路径可能需要手动调整：

1. 检查相对路径引用
2. 确保包名正确
3. 验证测试辅助函数的引用

### 🔧 测试环境
- 集成测试需要数据库连接
- 某些测试可能需要Redis服务
- 端到端测试需要完整的应用环境

### 📝 最佳实践
1. 单元测试应该快速且独立
2. 集成测试应该测试真实的组件交互
3. 使用表驱动测试提高测试覆盖率
4. 为复杂的测试场景编写辅助函数
5. 保持测试代码的可读性和可维护性

## 文件移动记录

详细的文件移动记录请查看 `file_moves.log` 文件，其中记录了每个测试文件的原始位置和新位置。

## 贡献指南

### 添加新测试
1. 根据测试类型选择合适的目录（unit/integration/e2e）
2. 根据模块选择合适的子目录
3. 使用一致的命名规范：`{模块}_{功能}_test.go`
4. 添加适当的测试文档和注释

### 测试命名规范
- 单元测试：`Test{功能名称}`
- 集成测试：`TestIntegration{功能名称}`
- 端到端测试：`TestE2E{场景名称}`

### 测试文档
每个测试文件应包含：
- 文件头部注释说明测试目的
- 测试函数的详细注释
- 复杂测试逻辑的解释说明

这个重新组织的测试结构提供了更好的可维护性和可扩展性，便于团队协作和持续集成。
