# 测试文件修复建议

## 常见问题和解决方案

### 1. Package声明问题
**问题**: 测试文件的package声明与目录结构不匹配
**解决方案**:
```go
// 原来的声明
package ai

// 修改为
package ai_test

// 并添加import
import "ai-text-game-iam-npc/internal/ai"
```

### 2. Import路径问题
**问题**: 相对路径引用失效
**解决方案**:
```go
import (
    "testing"
    "ai-text-game-iam-npc/internal/ai"
    "ai-text-game-iam-npc/internal/models"
    // 其他必要的import
)
```

### 3. 函数调用问题
**问题**: 移动后无法直接调用原包的函数
**解决方案**:
```go
// 原来的调用
schema := NewSchemaBuilder()

// 修改为
schema := ai.NewSchemaBuilder()
```

### 4. 测试辅助函数问题
**问题**: 测试辅助函数无法访问
**解决方案**:
- 将测试辅助函数移动到 `tests/helpers/` 目录
- 或者在原包中导出这些函数

## 自动修复脚本

可以使用以下命令进行批量修复：

```bash
# 修复package声明
find tests -name "*.go" -exec sed -i 's/^package \([a-z]*\)$/package \1_test/' {} \;

# 添加import语句（需要手动调整）
# 这个比较复杂，建议手动处理
```

## 手动修复步骤

1. 检查每个测试文件的package声明
2. 添加必要的import语句
3. 更新函数调用，添加包前缀
4. 验证编译和运行结果

## 验证修复结果

```bash
# 检查编译
go test -c ./tests/unit/...
go test -c ./tests/integration/...

# 运行测试
go test ./tests/unit/...
go test ./tests/integration/...
```
