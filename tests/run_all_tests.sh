#!/bin/bash
# 运行所有测试脚本

echo "🧪 运行所有测试..."

# 运行单元测试
echo "1️⃣ 运行单元测试..."
./tests/run_unit_tests.sh
if [ $? -ne 0 ]; then
    echo "❌ 单元测试失败，停止执行"
    exit 1
fi

# 运行集成测试
echo "2️⃣ 运行集成测试..."
./tests/run_integration_tests.sh
if [ $? -ne 0 ]; then
    echo "❌ 集成测试失败，停止执行"
    exit 1
fi

# 运行端到端测试
echo "3️⃣ 运行端到端测试..."
./tests/run_e2e_tests.sh
if [ $? -ne 0 ]; then
    echo "❌ 端到端测试失败"
    exit 1
fi

echo "🎉 所有测试通过！"
