package ai_test

import (
	"testing"

	"ai-text-game-iam-npc/internal/ai"
)

/**
 * JSON Schema模块单元测试
 * @description 测试JSON Schema构建器和验证器的功能
 */

func TestSchemaBuilder(t *testing.T) {
	t.Run("测试基础Schema构建", func(t *testing.T) {
		schema := ai.NewSchemaBuilder().
			Object().
			Description("测试对象").
			Property("name", ai.StringSchema("名称")).
			Property("age", ai.IntegerSchema("年龄", floatPtr(0), floatPtr(150))).
			Required("name").
			Build()
		
		if schema.Type != "object" {
			t.<PERSON><PERSON>("期望类型为 object，实际为 %s", schema.Type)
		}
		
		if schema.Description != "测试对象" {
			t.Errorf("期望描述为 '测试对象'，实际为 '%s'", schema.Description)
		}
		
		if len(schema.Properties) != 2 {
			t.<PERSON><PERSON>("期望属性数量为 2，实际为 %d", len(schema.Properties))
		}
		
		if len(schema.Required) != 1 || schema.Required[0] != "name" {
			t.Errorf("期望必需字段为 ['name']，实际为 %v", schema.Required)
		}
	})
	
	t.Run("测试数组Schema构建", func(t *testing.T) {
		schema := NewSchemaBuilder().
			Array().
			Description("字符串数组").
			Items(StringSchema("数组项")).
			MinItems(1).
			MaxItems(10).
			Build()
		
		if schema.Type != "array" {
			t.Errorf("期望类型为 array，实际为 %s", schema.Type)
		}
		
		if schema.Items == nil {
			t.Error("期望数组项Schema不为空")
		}
		
		if schema.MinItems == nil || *schema.MinItems != 1 {
			t.Errorf("期望最小项数为 1，实际为 %v", schema.MinItems)
		}
		
		if schema.MaxItems == nil || *schema.MaxItems != 10 {
			t.Errorf("期望最大项数为 10，实际为 %v", schema.MaxItems)
		}
	})
	
	t.Run("测试枚举Schema构建", func(t *testing.T) {
		schema := EnumSchema("状态", "active", "inactive", "pending")
		
		if schema.Type != "string" {
			t.Errorf("期望类型为 string，实际为 %s", schema.Type)
		}
		
		if len(schema.Enum) != 3 {
			t.Errorf("期望枚举值数量为 3，实际为 %d", len(schema.Enum))
		}
		
		expectedEnums := []string{"active", "inactive", "pending"}
		for i, expected := range expectedEnums {
			if i >= len(schema.Enum) || schema.Enum[i] != expected {
				t.Errorf("期望枚举值[%d]为 '%s'，实际为 '%s'", i, expected, schema.Enum[i])
			}
		}
	})
}

func TestSchemaToMap(t *testing.T) {
	t.Run("测试Schema转换为Map", func(t *testing.T) {
		schema := NewSchemaBuilder().
			Object().
			Description("测试对象").
			Property("name", NewSchemaBuilder().String().MinLength(1).MaxLength(50).Build()).
			Property("age", NewSchemaBuilder().Integer().Minimum(0).Maximum(150).Build()).
			Required("name", "age").
			Build()
		
		schemaMap := schema.ToMap()
		
		// 检查基本属性
		if schemaMap["type"] != "object" {
			t.Errorf("期望类型为 object，实际为 %v", schemaMap["type"])
		}
		
		if schemaMap["description"] != "测试对象" {
			t.Errorf("期望描述为 '测试对象'，实际为 %v", schemaMap["description"])
		}
		
		// 检查必需字段
		required, ok := schemaMap["required"].([]string)
		if !ok {
			t.Error("期望required字段为字符串数组")
		} else if len(required) != 2 {
			t.Errorf("期望必需字段数量为 2，实际为 %d", len(required))
		}
		
		// 检查属性
		properties, ok := schemaMap["properties"].(map[string]interface{})
		if !ok {
			t.Error("期望properties字段为map")
		} else {
			if len(properties) != 2 {
				t.Errorf("期望属性数量为 2，实际为 %d", len(properties))
			}
			
			// 检查name属性
			nameSchema, ok := properties["name"].(map[string]interface{})
			if !ok {
				t.Error("期望name属性为map")
			} else {
				if nameSchema["type"] != "string" {
					t.Errorf("期望name类型为 string，实际为 %v", nameSchema["type"])
				}
				if nameSchema["minLength"] != 1 {
					t.Errorf("期望name最小长度为 1，实际为 %v", nameSchema["minLength"])
				}
			}
		}
	})
}

func TestGameSchemaRegistry(t *testing.T) {
	t.Run("测试Schema注册表", func(t *testing.T) {
		registry := NewGameSchemaRegistry()
		
		// 测试获取场景Schema
		sceneSchema, err := registry.GetSchema("scene")
		if err != nil {
			t.Errorf("获取场景Schema失败: %v", err)
		}
		
		if sceneSchema.Type != "object" {
			t.Errorf("期望场景Schema类型为 object，实际为 %s", sceneSchema.Type)
		}
		
		// 测试获取不存在的Schema
		_, err = registry.GetSchema("nonexistent")
		if err == nil {
			t.Error("期望获取不存在的Schema时返回错误")
		}
		
		// 测试列出所有类型
		types := registry.ListAvailableTypes()
		if len(types) == 0 {
			t.Error("期望至少有一个可用类型")
		}
		
		// 检查是否包含预期的类型
		expectedTypes := []string{"scene", "character", "event", "dialogue", "item"}
		for _, expectedType := range expectedTypes {
			found := false
			for _, actualType := range types {
				if actualType == expectedType {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("期望类型列表包含 '%s'", expectedType)
			}
		}
	})
	
	t.Run("测试Schema转换为Map", func(t *testing.T) {
		registry := NewGameSchemaRegistry()
		
		schemaMap, err := registry.GetSchemaAsMap("character")
		if err != nil {
			t.Errorf("获取角色Schema Map失败: %v", err)
		}
		
		if schemaMap["type"] != "object" {
			t.Errorf("期望角色Schema类型为 object，实际为 %v", schemaMap["type"])
		}
		
		// 检查是否有properties字段
		if _, exists := schemaMap["properties"]; !exists {
			t.Error("期望角色Schema包含properties字段")
		}
		
		// 检查是否有required字段
		if _, exists := schemaMap["required"]; !exists {
			t.Error("期望角色Schema包含required字段")
		}
	})
}

func TestSchemaValidator(t *testing.T) {
	t.Run("测试对象验证", func(t *testing.T) {
		schema := NewSchemaBuilder().
			Object().
			Property("name", NewSchemaBuilder().String().MinLength(1).MaxLength(50).Build()).
			Property("age", NewSchemaBuilder().Integer().Minimum(0).Maximum(150).Build()).
			Required("name", "age").
			Build()
		
		validator := NewSchemaValidator(schema)
		
		// 测试有效数据
		validData := map[string]interface{}{
			"name": "张三",
			"age":  25,
		}
		
		result := validator.Validate(validData)
		if !result.Valid {
			t.Errorf("期望数据验证通过，但失败了: %v", result.Errors)
		}
		
		// 测试缺少必需字段
		invalidData := map[string]interface{}{
			"name": "李四",
			// 缺少age字段
		}
		
		result = validator.Validate(invalidData)
		if result.Valid {
			t.Error("期望数据验证失败，但通过了")
		}
		
		if len(result.Errors) == 0 {
			t.Error("期望有验证错误")
		}
	})
	
	t.Run("测试字符串验证", func(t *testing.T) {
		schema := NewSchemaBuilder().
			String().
			MinLength(2).
			MaxLength(10).
			Pattern("^[a-zA-Z]+$").
			Build()
		
		validator := NewSchemaValidator(schema)
		
		// 测试有效字符串
		result := validator.Validate("hello")
		if !result.Valid {
			t.Errorf("期望字符串验证通过，但失败了: %v", result.Errors)
		}
		
		// 测试长度不足
		result = validator.Validate("a")
		if result.Valid {
			t.Error("期望长度不足的字符串验证失败")
		}
		
		// 测试长度超限
		result = validator.Validate("verylongstring")
		if result.Valid {
			t.Error("期望长度超限的字符串验证失败")
		}
		
		// 测试模式不匹配
		result = validator.Validate("hello123")
		if result.Valid {
			t.Error("期望模式不匹配的字符串验证失败")
		}
	})
	
	t.Run("测试数组验证", func(t *testing.T) {
		schema := NewSchemaBuilder().
			Array().
			Items(StringSchema("数组项")).
			MinItems(1).
			MaxItems(3).
			Build()
		
		validator := NewSchemaValidator(schema)
		
		// 测试有效数组
		validArray := []interface{}{"item1", "item2"}
		result := validator.Validate(validArray)
		if !result.Valid {
			t.Errorf("期望数组验证通过，但失败了: %v", result.Errors)
		}
		
		// 测试空数组
		emptyArray := []interface{}{}
		result = validator.Validate(emptyArray)
		if result.Valid {
			t.Error("期望空数组验证失败")
		}
		
		// 测试数组过长
		longArray := []interface{}{"item1", "item2", "item3", "item4"}
		result = validator.Validate(longArray)
		if result.Valid {
			t.Error("期望过长数组验证失败")
		}
	})
	
	t.Run("测试枚举验证", func(t *testing.T) {
		schema := EnumSchema("状态", "active", "inactive", "pending")
		validator := NewSchemaValidator(schema)
		
		// 测试有效枚举值
		result := validator.Validate("active")
		if !result.Valid {
			t.Errorf("期望枚举验证通过，但失败了: %v", result.Errors)
		}
		
		// 测试无效枚举值
		result = validator.Validate("unknown")
		if result.Valid {
			t.Error("期望无效枚举值验证失败")
		}
	})
}
