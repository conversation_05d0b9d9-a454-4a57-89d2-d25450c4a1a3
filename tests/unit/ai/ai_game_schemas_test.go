package ai

import (
	"testing"
)

/**
 * 六个核心场景JSON Schema单元测试
 * @description 测试新设计的六个核心场景Schema是否符合需求文档规范
 */

func TestGameSchemaRegistry_CoreScenarios(t *testing.T) {
	registry := NewGameSchemaRegistry()

	// 测试六个核心场景Schema是否都已注册
	coreScenarios := []string{
		"world_creation",
		"world_entry", 
		"world_exploration",
		"world_heartbeat",
		"letter_communication",
		"event_interaction",
	}

	for _, scenario := range coreScenarios {
		t.Run("测试核心场景Schema注册_"+scenario, func(t *testing.T) {
			schema, err := registry.GetSchema(scenario)
			if err != nil {
				t.<PERSON><PERSON>("获取场景 '%s' 的Schema失败: %v", scenario, err)
				return
			}

			if schema == nil {
				t.<PERSON>rrorf("场景 '%s' 的Schema为空", scenario)
				return
			}

			if schema.Type != "object" {
				t.<PERSON><PERSON><PERSON>("场景 '%s' 的Schema类型应为 'object'，实际为 '%s'", scenario, schema.Type)
			}

			if schema.Description == "" {
				t.<PERSON><PERSON>("场景 '%s' 的Schema缺少描述", scenario)
			}

			if len(schema.Properties) == 0 {
				t.Errorf("场景 '%s' 的Schema缺少属性定义", scenario)
			}

			if len(schema.Required) == 0 {
				t.Errorf("场景 '%s' 的Schema缺少必需字段定义", scenario)
			}
		})
	}
}

func TestWorldCreationSchema(t *testing.T) {
	registry := NewGameSchemaRegistry()
	schema, err := registry.GetSchema("world_creation")
	
	if err != nil {
		t.Fatalf("获取世界创建Schema失败: %v", err)
	}

	// 验证必需字段
	expectedRequired := []string{"world_options", "creation_timestamp"}
	for _, field := range expectedRequired {
		found := false
		for _, required := range schema.Required {
			if required == field {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("世界创建Schema缺少必需字段: %s", field)
		}
	}

	// 验证world_options是数组类型
	worldOptionsProperty := schema.Properties["world_options"]
	if worldOptionsProperty == nil {
		t.Error("世界创建Schema缺少world_options属性")
	} else if worldOptionsProperty.Type != "array" {
		t.Errorf("world_options应为数组类型，实际为: %s", worldOptionsProperty.Type)
	}

	// 验证world_options的items结构
	if worldOptionsProperty.Items == nil {
		t.Error("world_options缺少items定义")
	} else {
		itemSchema := worldOptionsProperty.Items
		if itemSchema.Type != "object" {
			t.Errorf("world_options的items应为对象类型，实际为: %s", itemSchema.Type)
		}

		// 验证世界选项的必需字段
		expectedItemRequired := []string{
			"world_name", "environment_description", "cultural_background", 
			"historical_background", "geographical_info", "theme", 
			"difficulty_level", "estimated_scale",
		}
		for _, field := range expectedItemRequired {
			found := false
			for _, required := range itemSchema.Required {
				if required == field {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("世界选项Schema缺少必需字段: %s", field)
			}
		}
	}
}

func TestWorldEntrySchema(t *testing.T) {
	registry := NewGameSchemaRegistry()
	schema, err := registry.GetSchema("world_entry")
	
	if err != nil {
		t.Fatalf("获取世界进入Schema失败: %v", err)
	}

	// 验证必需字段
	expectedRequired := []string{
		"initial_scene", "connected_scenes", "game_time", 
		"world_status", "player_spawn_info",
	}
	for _, field := range expectedRequired {
		found := false
		for _, required := range schema.Required {
			if required == field {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("世界进入Schema缺少必需字段: %s", field)
		}
	}

	// 验证场景信息结构
	initialSceneProperty := schema.Properties["initial_scene"]
	if initialSceneProperty == nil {
		t.Error("世界进入Schema缺少initial_scene属性")
	} else if initialSceneProperty.Type != "object" {
		t.Errorf("initial_scene应为对象类型，实际为: %s", initialSceneProperty.Type)
	}

	// 验证connected_scenes是数组类型
	connectedScenesProperty := schema.Properties["connected_scenes"]
	if connectedScenesProperty == nil {
		t.Error("世界进入Schema缺少connected_scenes属性")
	} else if connectedScenesProperty.Type != "array" {
		t.Errorf("connected_scenes应为数组类型，实际为: %s", connectedScenesProperty.Type)
	}
}

func TestWorldExplorationSchema(t *testing.T) {
	registry := NewGameSchemaRegistry()
	schema, err := registry.GetSchema("world_exploration")
	
	if err != nil {
		t.Fatalf("获取世界探索Schema失败: %v", err)
	}

	// 验证必需字段
	expectedRequired := []string{
		"explorable_scenes", "current_game_time", 
		"time_based_recommendations", "world_state_summary",
	}
	for _, field := range expectedRequired {
		found := false
		for _, required := range schema.Required {
			if required == field {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("世界探索Schema缺少必需字段: %s", field)
		}
	}

	// 验证explorable_scenes是数组类型
	explorableScenesProperty := schema.Properties["explorable_scenes"]
	if explorableScenesProperty == nil {
		t.Error("世界探索Schema缺少explorable_scenes属性")
	} else if explorableScenesProperty.Type != "array" {
		t.Errorf("explorable_scenes应为数组类型，实际为: %s", explorableScenesProperty.Type)
	}

	// 验证时间推荐是数组类型
	timeRecommendationsProperty := schema.Properties["time_based_recommendations"]
	if timeRecommendationsProperty == nil {
		t.Error("世界探索Schema缺少time_based_recommendations属性")
	} else if timeRecommendationsProperty.Type != "array" {
		t.Errorf("time_based_recommendations应为数组类型，实际为: %s", timeRecommendationsProperty.Type)
	}
}

func TestWorldHeartbeatSchema(t *testing.T) {
	registry := NewGameSchemaRegistry()
	schema, err := registry.GetSchema("world_heartbeat")
	
	if err != nil {
		t.Fatalf("获取世界心跳Schema失败: %v", err)
	}

	// 验证必需字段
	expectedRequired := []string{
		"scene_changes", "global_changes", "update_summary", 
		"next_heartbeat_time", "affected_players",
	}
	for _, field := range expectedRequired {
		found := false
		for _, required := range schema.Required {
			if required == field {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("世界心跳Schema缺少必需字段: %s", field)
		}
	}

	// 验证scene_changes是数组类型
	sceneChangesProperty := schema.Properties["scene_changes"]
	if sceneChangesProperty == nil {
		t.Error("世界心跳Schema缺少scene_changes属性")
	} else if sceneChangesProperty.Type != "array" {
		t.Errorf("scene_changes应为数组类型，实际为: %s", sceneChangesProperty.Type)
	}
}

func TestLetterCommunicationSchema(t *testing.T) {
	registry := NewGameSchemaRegistry()
	schema, err := registry.GetSchema("letter_communication")
	
	if err != nil {
		t.Fatalf("获取书信对话Schema失败: %v", err)
	}

	// 验证必需字段
	expectedRequired := []string{
		"letter_replies", "communication_summary", "follow_up_actions",
	}
	for _, field := range expectedRequired {
		found := false
		for _, required := range schema.Required {
			if required == field {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("书信对话Schema缺少必需字段: %s", field)
		}
	}

	// 验证letter_replies是数组类型
	letterRepliesProperty := schema.Properties["letter_replies"]
	if letterRepliesProperty == nil {
		t.Error("书信对话Schema缺少letter_replies属性")
	} else if letterRepliesProperty.Type != "array" {
		t.Errorf("letter_replies应为数组类型，实际为: %s", letterRepliesProperty.Type)
	}
}

func TestEventInteractionSchema(t *testing.T) {
	registry := NewGameSchemaRegistry()
	schema, err := registry.GetSchema("event_interaction")
	
	if err != nil {
		t.Fatalf("获取事件交互Schema失败: %v", err)
	}

	// 验证必需字段
	expectedRequired := []string{
		"interaction_results", "event_summary", 
		"world_impact_assessment", "follow_up_events",
	}
	for _, field := range expectedRequired {
		found := false
		for _, required := range schema.Required {
			if required == field {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("事件交互Schema缺少必需字段: %s", field)
		}
	}

	// 验证interaction_results是数组类型
	interactionResultsProperty := schema.Properties["interaction_results"]
	if interactionResultsProperty == nil {
		t.Error("事件交互Schema缺少interaction_results属性")
	} else if interactionResultsProperty.Type != "array" {
		t.Errorf("interaction_results应为数组类型，实际为: %s", interactionResultsProperty.Type)
	}
}

func TestSchemaToMapConversion(t *testing.T) {
	registry := NewGameSchemaRegistry()
	
	// 测试所有核心场景Schema的ToMap转换
	coreScenarios := []string{
		"world_creation", "world_entry", "world_exploration",
		"world_heartbeat", "letter_communication", "event_interaction",
	}

	for _, scenario := range coreScenarios {
		t.Run("测试Schema转Map_"+scenario, func(t *testing.T) {
			schemaMap, err := registry.GetSchemaAsMap(scenario)
			if err != nil {
				t.Errorf("获取场景 '%s' 的Schema Map失败: %v", scenario, err)
				return
			}

			if schemaMap == nil {
				t.Errorf("场景 '%s' 的Schema Map为空", scenario)
				return
			}

			// 验证基本字段
			if schemaMap["type"] != "object" {
				t.Errorf("场景 '%s' 的Schema Map类型应为 'object'", scenario)
			}

			if schemaMap["description"] == nil || schemaMap["description"] == "" {
				t.Errorf("场景 '%s' 的Schema Map缺少描述", scenario)
			}

			if schemaMap["properties"] == nil {
				t.Errorf("场景 '%s' 的Schema Map缺少属性定义", scenario)
			}

			if schemaMap["required"] == nil {
				t.Errorf("场景 '%s' 的Schema Map缺少必需字段定义", scenario)
			}
		})
	}
}
