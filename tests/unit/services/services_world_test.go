package game

import (
	"context"
	"testing"

	"ai-text-game-iam-npc/internal/game"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB 创建测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// 手动创建简化的用户表（SQLite兼容）
	err = db.Exec(`
		CREATE TABLE users (
			id TEXT PRIMARY KEY,
			external_id TEXT NOT NULL,
			external_provider TEXT NOT NULL,
			email TEXT NOT NULL,
			display_name TEXT,
			avatar_url TEXT,
			game_roles TEXT DEFAULT '["user"]',
			status TEXT DEFAULT 'active',
			preferences TEXT DEFAULT '{}',
			id_p_claims TEXT DEFAULT '{}',
			created_at DATETIME,
			updated_at DATETIME,
			last_login_at DATETIME,
			deleted_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	// 创建世界表
	err = db.Exec(`
		CREATE TABLE worlds (
			id TEXT PRIMARY KEY,
			name TEXT NOT NULL,
			description TEXT,
			creator_id TEXT NOT NULL,
			is_public BOOLEAN DEFAULT false,
			max_players INTEGER DEFAULT 10,
			current_players INTEGER DEFAULT 0,
			status TEXT DEFAULT 'active',
			world_config TEXT DEFAULT '{}',
			world_state TEXT DEFAULT '{}',
			game_time INTEGER DEFAULT 0,
			tick_count INTEGER DEFAULT 0,
			created_at DATETIME,
			updated_at DATETIME,
			deleted_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	// 创建用户统计表
	err = db.Exec(`
		CREATE TABLE user_stats (
			user_id TEXT PRIMARY KEY,
			total_play_time INTEGER DEFAULT 0,
			worlds_created INTEGER DEFAULT 0,
			worlds_joined INTEGER DEFAULT 0,
			achievements TEXT DEFAULT '[]',
			level INTEGER DEFAULT 1,
			experience INTEGER DEFAULT 0,
			updated_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	// 创建角色表
	err = db.Exec(`
		CREATE TABLE characters (
			id TEXT PRIMARY KEY,
			world_id TEXT NOT NULL,
			user_id TEXT,
			name TEXT NOT NULL,
			description TEXT,
			character_type TEXT DEFAULT 'player',
			traits TEXT DEFAULT '[]',
			memories TEXT DEFAULT '[]',
			experiences TEXT DEFAULT '[]',
			relationships TEXT DEFAULT '{}',
			status TEXT DEFAULT 'active',
			created_at DATETIME,
			updated_at DATETIME,
			deleted_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	// 创建场景表
	err = db.Exec(`
		CREATE TABLE scenes (
			id TEXT PRIMARY KEY,
			world_id TEXT NOT NULL,
			name TEXT NOT NULL,
			description TEXT,
			scene_type TEXT DEFAULT 'location',
			properties TEXT DEFAULT '{}',
			connections TEXT DEFAULT '[]',
			created_at DATETIME,
			updated_at DATETIME,
			deleted_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	return db
}

// setupTestLogger 创建测试日志器
func setupTestLogger() logger.Logger {
	return logger.New("debug")
}

func TestNewWorldService(t *testing.T) {
	db := setupTestDB(t)
	log := setupTestLogger()

	service := game.NewWorldService(db, log)

	assert.NotNil(t, service)
}

func TestCreateWorld(t *testing.T) {
	db := setupTestDB(t)
	log := setupTestLogger()
	service := game.NewWorldService(db, log)

	// 创建测试用户
	displayName := "测试用户"
	user := &models.User{
		ExternalID:       "google-123",
		ExternalProvider: "google",
		Email:            "<EMAIL>",
		DisplayName:      &displayName,
		GameRoles:        models.StringArray{"user"},
		Status:           "active",
		Preferences:      models.JSON{},
		IDPClaims:        models.JSON{},
	}
	err := db.Create(user).Error
	require.NoError(t, err)

	// 创建世界
	ctx := context.Background()
	description := "这是一个测试世界"
	config := map[string]interface{}{
		"difficulty": "normal",
		"pvp":        false,
	}

	world, err := service.CreateWorld(ctx, user.ID, "测试世界", description, config)
	if err != nil {
		t.Fatalf("CreateWorld failed: %v", err)
	}
	if world == nil {
		t.Fatal("CreateWorld returned nil world")
	}
	assert.Equal(t, "测试世界", world.Name)
	assert.Equal(t, user.ID, world.CreatorID)
	assert.Equal(t, "active", world.Status)
}

func TestGetWorld(t *testing.T) {
	db := setupTestDB(t)
	log := setupTestLogger()
	service := game.NewWorldService(db, log)

	// 创建测试用户
	displayName := "测试用户"
	user := &models.User{
		ExternalID:       "google-123",
		ExternalProvider: "google",
		Email:            "<EMAIL>",
		DisplayName:      &displayName,
		GameRoles:        models.StringArray{"user"},
		Status:           "active",
		Preferences:      models.JSON{},
		IDPClaims:        models.JSON{},
	}
	err := db.Create(user).Error
	require.NoError(t, err)

	// 创建测试世界
	description := "这是一个测试世界"
	world := &models.World{
		Name:        "测试世界",
		Description: &description,
		IsPublic:    true,
		MaxPlayers:  10,
		CreatorID:   user.ID,
		Status:      "active",
		WorldConfig: models.JSON{},
	}
	err = db.Create(world).Error
	require.NoError(t, err)

	// 测试获取存在的世界
	ctx := context.Background()
	foundWorld, err := service.GetWorld(ctx, world.ID)
	if err != nil {
		t.Fatalf("GetWorld failed: %v", err)
	}
	if foundWorld == nil {
		t.Fatal("GetWorld returned nil world")
	}
	assert.Equal(t, world.ID, foundWorld.ID)
	assert.Equal(t, world.Name, foundWorld.Name)

	// 测试获取不存在的世界
	notFoundWorld, err := service.GetWorld(ctx, uuid.New())
	assert.Error(t, err)
	assert.Nil(t, notFoundWorld)
}

func TestGetPublicWorlds(t *testing.T) {
	db := setupTestDB(t)
	log := setupTestLogger()
	service := game.NewWorldService(db, log)

	// 创建测试用户
	displayName := "测试用户"
	user := &models.User{
		ExternalID:       "google-123",
		ExternalProvider: "google",
		Email:            "<EMAIL>",
		DisplayName:      &displayName,
		GameRoles:        models.StringArray{"user"},
		Status:           "active",
		Preferences:      models.JSON{},
		IDPClaims:        models.JSON{},
	}
	err := db.Create(user).Error
	require.NoError(t, err)

	// 创建公开世界
	description := "这是一个公开的测试世界"
	publicWorld := &models.World{
		Name:        "公开世界",
		Description: &description,
		IsPublic:    true,
		MaxPlayers:  10,
		CreatorID:   user.ID,
		Status:      "active",
		WorldConfig: models.JSON{},
	}
	err = db.Create(publicWorld).Error
	require.NoError(t, err)

	// 获取公开世界列表
	ctx := context.Background()
	worlds, total, err := service.GetPublicWorlds(ctx, 10, 0)
	assert.NoError(t, err)
	assert.Len(t, worlds, 1)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, publicWorld.Name, worlds[0].Name)
}



