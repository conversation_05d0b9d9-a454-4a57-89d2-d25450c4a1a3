#!/bin/bash

# 游戏世界管理修复验证测试脚本
# 测试修复的三个主要问题：
# 1. 替换模拟数据为真实API调用
# 2. 修复游戏进入逻辑
# 3. 修复世界创建功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
SERVER_URL="http://localhost:8080"
API_BASE="${SERVER_URL}/api/v1"

# 打印函数
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查服务器是否运行
check_server() {
    print_header "检查服务器状态"
    
    if curl -s "${SERVER_URL}/health" > /dev/null; then
        print_success "服务器运行正常"
    else
        print_error "服务器未运行，请先启动服务器: go run cmd/simple-server/main.go"
        exit 1
    fi
}

# 测试世界列表API（修复问题1：替换模拟数据）
test_world_list_apis() {
    print_header "测试世界列表API（验证模拟数据修复）"
    
    # 测试获取所有世界
    print_info "测试 GET /api/v1/worlds"
    response=$(curl -s "${API_BASE}/worlds")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        world_count=$(echo "$response" | jq '.data | length')
        print_success "获取世界列表成功，共 $world_count 个世界"
        
        # 检查世界是否有真实的UUID而不是硬编码ID
        first_world_id=$(echo "$response" | jq -r '.data[0].id // empty')
        if [[ "$first_world_id" =~ ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$ ]]; then
            print_success "世界ID格式正确（UUID）: $first_world_id"
        else
            print_warning "世界ID可能不是UUID格式: $first_world_id"
        fi
    else
        print_error "获取世界列表失败"
        echo "$response" | jq '.'
    fi
    
    # 测试获取我的世界
    print_info "测试 GET /api/v1/game/my-worlds"
    response=$(curl -s "${API_BASE}/game/my-worlds?page=1&limit=10")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        my_world_count=$(echo "$response" | jq '.data.total')
        print_success "获取我的世界列表成功，共 $my_world_count 个世界"
    else
        print_error "获取我的世界列表失败"
        echo "$response" | jq '.'
    fi
    
    # 测试获取公开世界
    print_info "测试 GET /api/v1/game/public-worlds"
    response=$(curl -s "${API_BASE}/game/public-worlds?page=1&limit=20")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        public_world_count=$(echo "$response" | jq '.data.total')
        print_success "获取公开世界列表成功，共 $public_world_count 个世界"
    else
        print_error "获取公开世界列表失败"
        echo "$response" | jq '.'
    fi
}

# 测试世界创建功能（修复问题2和3：硬编码ID和世界创建）
test_world_creation() {
    print_header "测试世界创建功能（验证ID生成修复）"
    
    # 创建测试世界
    print_info "创建新的测试世界"
    world_data='{
        "name": "测试世界-'$(date +%s)'",
        "description": "这是一个用于测试的世界",
        "theme": "fantasy",
        "is_public": true,
        "max_players": 8
    }'
    
    response=$(curl -s -X POST "${API_BASE}/game/worlds" \
        -H "Content-Type: application/json" \
        -d "$world_data")
    
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        created_world_id=$(echo "$response" | jq -r '.data.id')
        created_world_name=$(echo "$response" | jq -r '.data.name')
        
        print_success "世界创建成功"
        print_info "世界ID: $created_world_id"
        print_info "世界名称: $created_world_name"
        
        # 验证ID不是硬编码的"world-new"
        if [ "$created_world_id" != "world-new" ]; then
            print_success "世界ID不是硬编码的'world-new'"
        else
            print_error "世界ID仍然是硬编码的'world-new'"
        fi
        
        # 验证ID是UUID格式
        if [[ "$created_world_id" =~ ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$ ]]; then
            print_success "世界ID是有效的UUID格式"
        else
            print_warning "世界ID不是标准UUID格式: $created_world_id"
        fi
        
        # 测试获取刚创建的世界
        print_info "验证新创建的世界可以被查询"
        get_response=$(curl -s "${API_BASE}/worlds/${created_world_id}")
        if echo "$get_response" | jq -e '.success' > /dev/null 2>&1; then
            print_success "新创建的世界可以正常查询"
        else
            print_error "无法查询新创建的世界"
            echo "$get_response" | jq '.'
        fi
        
        # 保存世界ID供后续测试使用
        echo "$created_world_id" > /tmp/test_world_id
        
    else
        print_error "世界创建失败"
        echo "$response" | jq '.'
    fi
}

# 测试游戏进入逻辑（验证问题2：进入逻辑修复）
test_game_entry_logic() {
    print_header "测试游戏进入逻辑（验证跳转修复）"
    
    if [ -f /tmp/test_world_id ]; then
        world_id=$(cat /tmp/test_world_id)
        print_info "使用世界ID进行测试: $world_id"
        
        # 测试获取世界详情（模拟前端进入游戏时的API调用）
        print_info "测试获取世界详情 GET /api/v1/worlds/$world_id"
        response=$(curl -s "${API_BASE}/worlds/${world_id}")
        if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
            world_name=$(echo "$response" | jq -r '.data.name')
            print_success "可以正确获取世界详情: $world_name"
        else
            print_error "无法获取世界详情"
            echo "$response" | jq '.'
        fi
        
        # 测试获取世界角色列表（这是进入游戏后的常见API调用）
        print_info "测试获取世界角色列表 GET /api/v1/game/world/$world_id/characters"
        response=$(curl -s "${API_BASE}/game/world/${world_id}/characters?page=1&limit=10")
        if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
            character_count=$(echo "$response" | jq '.data.total // 0')
            print_success "可以正确获取世界角色列表，共 $character_count 个角色"
        else
            print_warning "获取世界角色列表失败（可能是正常的，如果世界刚创建）"
            # 不显示错误详情，因为新世界可能没有角色
        fi
        
    else
        print_warning "没有找到测试世界ID，跳过游戏进入逻辑测试"
    fi
}

# 测试数据持久性
test_data_persistence() {
    print_header "测试数据持久性"
    
    # 再次获取世界列表，验证新创建的世界是否存在
    print_info "验证新创建的世界是否在世界列表中"
    response=$(curl -s "${API_BASE}/worlds")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        if [ -f /tmp/test_world_id ]; then
            test_world_id=$(cat /tmp/test_world_id)
            if echo "$response" | jq -e ".data[] | select(.id == \"$test_world_id\")" > /dev/null 2>&1; then
                print_success "新创建的世界存在于世界列表中"
            else
                print_error "新创建的世界不在世界列表中"
            fi
        fi
    else
        print_error "无法获取世界列表进行验证"
    fi
}

# 清理测试数据
cleanup() {
    print_header "清理测试数据"
    
    if [ -f /tmp/test_world_id ]; then
        rm /tmp/test_world_id
        print_info "清理临时文件"
    fi
}

# 主测试流程
main() {
    print_header "游戏世界管理修复验证测试"
    print_info "测试目标："
    print_info "1. 验证模拟数据已替换为真实API调用"
    print_info "2. 验证世界创建不再返回硬编码的'world-new'"
    print_info "3. 验证游戏进入逻辑使用正确的世界ID"
    
    # 检查依赖
    if ! command -v jq &> /dev/null; then
        print_error "需要安装 jq 工具来解析JSON响应"
        print_info "Ubuntu/Debian: sudo apt-get install jq"
        print_info "macOS: brew install jq"
        exit 1
    fi
    
    # 执行测试
    check_server
    test_world_list_apis
    test_world_creation
    test_game_entry_logic
    test_data_persistence
    cleanup
    
    print_header "测试完成"
    print_success "所有测试已完成，请查看上述结果"
}

# 运行主函数
main "$@"
