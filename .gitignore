# =============================================================================
# AI文本游戏项目 .gitignore 文件
# =============================================================================
# 技术栈: Go + React + TypeScript + Vite + PostgreSQL/SQLite + Redis
# 生成时间: $(date '+%Y-%m-%d %H:%M:%S')
# =============================================================================

# =============================================================================
# Go 语言相关文件
# =============================================================================

# Go 编译产物
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
go.work.sum

# Go 构建缓存
.cache/

# Go 模块代理缓存
/pkg/mod/

# Go 编译器生成的文件
*.6
*.8
*.a

# Go 覆盖率测试文件
*.coverprofile
coverage.out
coverage.html
profile.out

# Go 二进制文件
/bin/
/dist/
/build/
main
server
ai-text-game-*

# Go 调试文件
debug
debug.test
__debug_bin

# =============================================================================
# Node.js 和前端相关文件
# =============================================================================

# Node.js 依赖目录
node_modules/
jspm_packages/

# npm 相关文件
npm-debug.log*
npm-error.log*
.npm

# Yarn 相关文件
yarn-debug.log*
yarn-error.log*
yarn.lock

# pnpm 相关文件
pnpm-debug.log*
.pnpm-store/

# 包管理器锁文件（根据项目需要选择保留）
# package-lock.json  # 如果团队使用 npm，建议保留此文件
# yarn.lock          # 如果团队使用 yarn，建议保留此文件

# =============================================================================
# 前端构建和开发文件
# =============================================================================

# Vite 构建产物
web/frontend/dist/
web/frontend/build/
web/static/dist/

# Vite 开发缓存
web/frontend/.vite/
web/frontend/vite.config.js.timestamp-*
web/frontend/vite.config.ts.timestamp-*

# 前端环境配置文件
web/frontend/.env.local
web/frontend/.env.development.local
web/frontend/.env.test.local
web/frontend/.env.production.local

# 前端测试覆盖率报告
web/frontend/coverage/
web/frontend/.nyc_output/

# 前端构建分析报告
web/frontend/bundle-analyzer-report.html
web/frontend/stats.json

# TypeScript 编译缓存
web/frontend/.tsbuildinfo
web/frontend/tsconfig.tsbuildinfo

# ESLint 缓存
web/frontend/.eslintcache

# =============================================================================
# 数据库相关文件
# =============================================================================

# SQLite 数据库文件
*.db
*.sqlite
*.sqlite3
dev.db
test.db
data/*.db
data/*.sqlite
data/*.sqlite3

# PostgreSQL 备份文件
*.sql.gz
*.dump
*.backup

# 数据库迁移临时文件
migrations/tmp/
migrations/*.tmp

# =============================================================================
# Redis 和缓存文件
# =============================================================================

# Redis 数据文件
dump.rdb
appendonly.aof
redis.conf.bak

# 缓存目录
cache/
.cache/
tmp/cache/

# =============================================================================
# 日志文件
# =============================================================================

# 应用日志
logs/
*.log
log/
*.log.*

# 系统日志
/var/log/
syslog
auth.log

# 开发服务器日志
server.log
server_*.log
backend.log
frontend.log
/tmp/backend.log
/tmp/frontend.log

# 错误日志
error.log
error_*.log
crash.log

# 调试日志
debug.log
debug_*.log

# =============================================================================
# 临时文件和系统文件
# =============================================================================

# 临时目录
tmp/
temp/
.tmp/
.temp/

# 系统临时文件
*.tmp
*.temp
*.swp
*.swo
*~
.DS_Store
Thumbs.db
desktop.ini

# 进程ID文件
*.pid
/tmp/*.pid

# 锁文件
*.lock
.lock

# =============================================================================
# IDE 和编辑器配置文件
# =============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json.example
!.vscode/launch.json.example
!.vscode/extensions.json.example

# JetBrains IDEs (IntelliJ IDEA, GoLand, WebStorm 等)
.idea/
*.iml
*.ipr
*.iws
.idea_modules/

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
.vimrc.local

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Atom
.atom/

# Eclipse
.metadata
.project
.classpath
.settings/

# =============================================================================
# 操作系统相关文件
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# 安全和配置文件
# =============================================================================

# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production
.env.staging
.env.*.local

# 密钥和证书文件
*.key
*.pem
*.crt
*.cert
*.p12
*.pfx
id_rsa
id_dsa
id_ecdsa
id_ed25519
*.pub

# 配置文件（包含敏感信息）
config.json
config.yaml
config.yml
secrets.json
secrets.yaml
secrets.yml

# JWT 密钥
jwt.key
jwt_*.key

# OAuth 配置
oauth.json
oauth_*.json

# =============================================================================
# 项目特定文件
# =============================================================================

# AI文本游戏世界数据文件
worlds/*.json
worlds/generated/
worlds/backup/

# 用户上传文件
uploads/
user_data/
avatars/

# 游戏存档文件
saves/
*.save
game_data/

# AI 模型和训练数据
models/
training_data/
*.model
*.weights

# 测试数据
test_data/
fixtures/
mock_data/

# 性能分析文件
*.prof
*.pprof
cpu.prof
mem.prof
trace.out

# 基准测试结果
benchmark_*.txt
bench.out

# =============================================================================
# 开发和调试文件
# =============================================================================

# 调试数据库
debug.db
debug_*.db

# 开发脚本生成的文件
dev_output/
dev_*.txt

# API 调试文件
api_test.http
*.http
rest-client.env.json

# 文档生成临时文件
docs/generated/
docs/build/
docs/.vuepress/dist/

# 代码生成文件
generated/
auto_generated/
*.generated.go
*.generated.ts

# =============================================================================
# 部署和运维文件
# =============================================================================

# Docker 相关
.dockerignore.bak
docker-compose.override.yml
docker-compose.local.yml
.docker/

# Kubernetes 配置
k8s/secrets/
*.secret.yaml
*.secret.yml

# 部署脚本生成的文件
deploy/
deployment/
release/

# 监控和日志收集
prometheus/
grafana/
elasticsearch/
kibana/

# 备份文件
backup/
*.backup
*.bak
*.old

# =============================================================================
# 第三方工具和服务
# =============================================================================

# Git 相关
.git/
.gitconfig.local

# GitHub 相关
.github/workflows/secrets/

# 代码质量工具
.sonarqube/
sonar-project.properties.local

# 依赖检查工具
.snyk
snyk_report/

# 性能监控
newrelic.ini
.newrelic/

# 错误追踪
sentry.properties
.sentry/

# =============================================================================
# 测试相关文件
# =============================================================================

# Go 测试文件
*.test
test_output/
testdata/tmp/

# 前端测试文件
web/frontend/test-results/
web/frontend/playwright-report/
web/frontend/test-results.xml

# 集成测试
integration_test_output/
e2e_test_output/

# 测试覆盖率
coverage/
.coverage
htmlcov/

# =============================================================================
# 构建和打包文件
# =============================================================================

# 构建产物
build/
dist/
out/

# 打包文件
*.tar.gz
*.zip
*.rar
*.7z

# 安装包
*.deb
*.rpm
*.pkg
*.dmg

# =============================================================================
# 其他忽略文件
# =============================================================================

# 编辑器备份文件
*.orig
*.rej

# 补丁文件
*.patch
*.diff

# 压缩文件
*.gz
*.bz2
*.xz

# 媒体文件（如果不需要版本控制）
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.mp4
# *.mp3
# *.wav

# 大文件（根据项目需要调整）
# *.iso
# *.bin

# =============================================================================
# 保留的重要文件（使用 ! 前缀取消忽略）
# =============================================================================

# 保留示例配置文件
!config.example.json
!config.example.yaml
!.env.example

# 保留重要的构建脚本
!build.sh
!deploy.sh

# 保留文档中的图片
!docs/**/*.png
!docs/**/*.jpg
!docs/**/*.gif

# 保留前端公共资源
!web/frontend/public/**

# =============================================================================
# 结束标记
# =============================================================================
