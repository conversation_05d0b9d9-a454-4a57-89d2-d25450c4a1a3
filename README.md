# AI文本游戏 - I am NPC

一个基于AI驱动的沉浸式文本冒险游戏平台，玩家可以创建和探索无限可能的虚拟世界。

## 🎮 项目概述

这是一个现代化的AI文本游戏平台，结合了先进的技术栈和创新的游戏设计：

- **🤖 AI驱动**: 智能内容生成，创造无限可能的游戏体验
- **🌍 开放世界**: 玩家可以创建和分享自己的游戏世界
- **👥 多人协作**: 支持多玩家在同一世界中互动
- **🎭 角色扮演**: 丰富的角色系统和NPC交互
- **📱 现代界面**: 响应式设计，支持多设备访问

## 🛠 技术栈

### 后端技术
- **语言**: Go 1.21+ (高性能并发处理)
- **Web框架**: Gin (轻量级HTTP路由)
- **数据库**: PostgreSQL (主数据库) + Redis (缓存)
- **认证**: OAuth2 (支持Google、GitHub登录)
- **AI集成**: 支持多种AI模型和提供商
- **实时通信**: WebSocket (游戏实时交互)

### 前端技术
- **框架**: React 18 + TypeScript (类型安全)
- **构建工具**: Vite (快速构建和热重载)
- **状态管理**: Redux Toolkit + RTK Query (数据管理)
- **UI组件**: Ant Design (企业级UI组件)
- **样式方案**: Styled Components (CSS-in-JS)
- **动画效果**: Framer Motion (流畅动画)
- **国际化**: 完整的中文本地化支持

## 📁 项目结构

```
ai-text-game-iam-npc/
├── cmd/
│   └── server/                 # 主服务器入口
├── internal/                   # 内部包（不对外暴露）
│   ├── auth/                  # 认证服务
│   ├── ai/                    # AI集成服务
│   ├── config/                # 配置管理
│   ├── game/                  # 游戏核心逻辑
│   ├── handlers/              # HTTP处理器
│   ├── models/                # 数据模型
│   ├── routes/                # 路由配置
│   ├── validation/            # 内容校验
│   ├── migration/             # 数据库迁移
│   └── testutil/              # 测试工具
├── pkg/                       # 公共包
│   ├── database/              # 数据库连接
│   └── logger/                # 日志系统
├── web/                       # 前端项目
│   ├── frontend/              # React 前端应用
│   │   ├── src/               # 源代码
│   │   ├── public/            # 静态资源
│   │   └── package.json       # 前端依赖配置
│   ├── static/                # 静态文件服务
│   └── templates/             # Go 模板文件
├── migrations/                # 数据库迁移文件
├── scripts/                   # 脚本文件
└── docs/                      # 文档
```

## 🚀 快速开始

### 环境要求
- Go 1.19+
- PostgreSQL 13+
- Redis 6+
- Node.js 16+ (前端开发)

### 🔧 开发环境快速启动（推荐）

**无需配置认证，一键启动开发环境：**

```bash
# 1. 克隆项目
git clone <repository-url>
cd ai-text-game-iam-npc

# 2. 启动开发环境（跳过认证）
./scripts/dev_no_auth.sh

# 3. 测试开发环境
./scripts/test_dev_mode.sh
```

**开发环境特性：**
- ✅ **跳过身份认证** - 无需OAuth配置
- ✅ **自动测试用户** - 预设开发用户信息
- ✅ **AI Mock模式** - 无需真实AI服务
- ✅ **放宽安全策略** - 便于调试
- ✅ **详细日志** - 完整的调试信息

**访问地址：**
- 应用服务器: http://localhost:8080
- 健康检查: http://localhost:8080/health
- API测试: http://localhost:8080/api/v1

### 📋 完整安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ai-text-game-iam-npc
```

2. **安装后端依赖**
```bash
go mod download
```

3. **安装前端依赖**
```bash
cd web/frontend
npm install
cd ../..
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和其他服务
```

5. **数据库迁移**
```bash
# 创建数据库
createdb ai_text_game

# 运行迁移
go run cmd/migrate/main.go up
```

6. **启动服务**

**开发环境（完整功能）：**
```bash
# 终端1：启动后端服务
go run cmd/server/main.go

# 终端2：启动前端开发服务器
cd web/frontend
npm run dev
```

**生产环境：**
```bash
# 构建前端
cd web/frontend
npm run build
cd ../..

# 启动后端服务（会自动服务前端静态文件）
go run cmd/server/main.go
```

- 后端服务：`http://localhost:8080`
- 前端开发服务器：`http://localhost:3000`（开发环境）

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `SERVER_PORT` | 服务器端口 | 8080 |
| `DB_HOST` | 数据库主机 | localhost |
| `DB_PORT` | 数据库端口 | 5432 |
| `DB_USER` | 数据库用户 | postgres |
| `DB_PASSWORD` | 数据库密码 | |
| `DB_NAME` | 数据库名称 | ai_text_game |
| `REDIS_HOST` | Redis主机 | localhost |
| `REDIS_PORT` | Redis端口 | 6379 |
| `JWT_SECRET` | JWT密钥 | |
| `AI_PROVIDER` | AI提供商 | mock |

### Windmill AI配置

| 环境变量 | 说明 | 默认值 |
|---------|------|--------|
| `AI_BASE_URL` | Windmill服务器地址 | https://wm.atjog.com |
| `AI_TOKEN` | Windmill API Token | |
| `AI_TIMEOUT` | 请求超时时间 | 30s |
| `AI_MAX_RETRIES` | 最大重试次数 | 3 |
| `AI_RETRY_DELAY` | 重试延迟 | 1s |
| `WINDMILL_WORKSPACE` | Windmill工作空间 | my-workspace |
| `WINDMILL_DEFAULT_MODEL` | 默认AI模型 | gemini-1.5-pro |

### OAuth配置

支持多种OAuth提供商：

```bash
# Google OAuth
OAUTH_GOOGLE_CLIENT_ID=your_client_id
OAUTH_GOOGLE_CLIENT_SECRET=your_client_secret

# GitHub OAuth
OAUTH_GITHUB_CLIENT_ID=your_client_id
OAUTH_GITHUB_CLIENT_SECRET=your_client_secret
```

## 📚 API文档

### 认证相关
- `GET /api/v1/auth/login/:provider` - OAuth登录
- `GET /api/v1/auth/callback/:provider` - OAuth回调
- `POST /api/v1/auth/refresh` - 刷新Token

### 游戏世界
- `POST /api/v1/game/worlds` - 创建世界
- `GET /api/v1/game/worlds` - 列出世界
- `GET /api/v1/game/worlds/:id` - 获取世界详情
- `PUT /api/v1/game/worlds/:id` - 更新世界
- `POST /api/v1/game/worlds/:id/join` - 加入世界
- `POST /api/v1/game/worlds/:id/leave` - 离开世界

### 角色管理
- `POST /api/v1/game/characters` - 创建角色
- `GET /api/v1/game/characters/:id` - 获取角色
- `PUT /api/v1/game/characters/:id` - 更新角色
- `GET /api/v1/game/worlds/:world_id/characters` - 列出世界中的角色

### 场景系统
- `POST /api/v1/game/scenes` - 创建场景
- `GET /api/v1/game/scenes/:id` - 获取场景
- `PUT /api/v1/game/scenes/:id` - 更新场景
- `GET /api/v1/game/worlds/:world_id/scenes` - 列出世界中的场景

### AI生成（基于Windmill结构化文本接口）
- `POST /api/v1/ai/generate` - 生成内容（通用接口）
- `POST /api/v1/ai/generate/scene` - 生成场景（结构化输出）
- `POST /api/v1/ai/generate/character` - 生成角色（结构化输出）
- `POST /api/v1/ai/generate/event` - 生成事件（结构化输出）
- `POST /api/v1/ai/generate/dialogue` - 生成对话（结构化输出）
- `GET /api/v1/ai/history` - 获取AI交互历史

**Windmill接口特性：**
- 🔄 **异步处理**: 任务提交+轮询模式，支持长时间生成
- 📊 **结构化输出**: 使用JSON Schema定义输出格式
- 🤖 **Gemini模型**: 支持Google Gemini 1.5 Pro等先进模型
- ⚡ **智能重试**: 自动错误恢复和重试机制
- 📈 **性能监控**: 实时监控生成性能和token使用

详细文档：[Windmill接口实现说明](docs/windmill-interface.md)

### 内容校验
- `POST /api/v1/validation/validate` - 校验内容
- `POST /api/v1/validation/batch-validate` - 批量校验
- `GET /api/v1/validation/stats` - 获取校验统计
- `GET /api/v1/validation/config` - 获取校验配置

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
./scripts/run_tests.sh

# 只运行单元测试
./scripts/run_tests.sh --unit

# 跳过数据库测试
./scripts/run_tests.sh --skip-db

# 生成覆盖率报告
./scripts/run_tests.sh --coverage
```

### 测试环境配置
```bash
# 测试数据库配置
export TEST_DB_HOST=localhost
export TEST_DB_PORT=5432
export TEST_DB_USER=postgres
export TEST_DB_PASSWORD=
export TEST_DB_NAME=ai_text_game_test
```

## 🔒 安全特性

### 内容校验系统
- **敏感词过滤**: 自动检测和过滤不当内容
- **SQL注入防护**: 检测和阻止SQL注入攻击
- **XSS防护**: 过滤恶意脚本内容
- **频率限制**: 防止API滥用
- **AI内容审核**: 智能内容审核（可选）

### 认证授权
- **OAuth2集成**: 支持主流OAuth提供商
- **JWT Token**: 安全的会话管理
- **角色权限**: 基于角色的访问控制
- **API限流**: 防止恶意请求

## 🚀 部署

### Docker部署
```bash
# 构建镜像
docker build -t ai-text-game .

# 运行容器
docker run -p 8080:8080 ai-text-game
```

### 生产环境配置
- 使用环境变量管理敏感配置
- 配置反向代理（Nginx）
- 设置SSL证书
- 配置监控和日志收集

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目链接: [GitHub Repository](https://github.com/your-username/ai-text-game-iam-npc)
- 问题反馈: [Issues](https://github.com/your-username/ai-text-game-iam-npc/issues)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和测试者！

## 📋 开发状态

### 已完成功能

#### 后端功能
- ✅ 项目初始化和配置管理
- ✅ 数据库设计和迁移系统
- ✅ 用户认证和OAuth集成
- ✅ 游戏核心服务（世界、角色、场景、事件）
- ✅ AI集成服务（支持模拟模式）
- ✅ 内容校验和安全系统
- ✅ RESTful API接口
- ✅ 单元测试和集成测试

#### 前端功能
- ✅ React + TypeScript 项目架构
- ✅ Redux Toolkit 状态管理
- ✅ Ant Design UI 组件库
- ✅ 响应式设计和主题系统
- ✅ 路由配置和页面结构
- ✅ API 集成和错误处理
- ✅ 用户认证流程
- ✅ 游戏大厅和世界管理界面

### 技术亮点
- **全栈架构**: Go 后端 + React 前端的现代化技术栈
- **模块化设计**: 清晰的代码组织和依赖管理
- **安全优先**: 多层次的内容校验和安全防护
- **类型安全**: TypeScript 提供完整的类型检查
- **状态管理**: Redux Toolkit 提供可预测的状态管理
- **用户体验**: 现代化的 UI 设计和流畅的交互动画
- **可扩展性**: 支持多种AI提供商和认证方式
- **生产就绪**: 完整的构建流程和部署支持