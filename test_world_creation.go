package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 测试世界创建请求
type CreateWorldRequest struct {
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	Theme         string                 `json:"theme"`
	IsPublic      bool                   `json:"is_public"`
	MaxPlayers    int                    `json:"max_players"`
	WorldSettings map[string]interface{} `json:"world_settings"`
}

// 测试AI生成场景请求
type GenerateSceneRequest struct {
	WorldID              string   `json:"world_id"`
	SceneName            string   `json:"scene_name,omitempty"`
	SceneType            string   `json:"scene_type,omitempty"`
	Theme                string   `json:"theme,omitempty"`
	Mood                 string   `json:"mood,omitempty"`
	ConnectedScenes      []string `json:"connected_scenes,omitempty"`
	SpecialRequirements  string   `json:"special_requirements,omitempty"`
}

// 响应结构
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	Error   string      `json:"error,omitempty"`
}

func main() {
	fmt.Println("🧪 开始测试世界创建和AI生成功能...")

	// 测试1: AI生成场景描述
	fmt.Println("\n1️⃣ 测试AI生成场景描述...")
	testAIGeneration()

	// 等待一秒
	time.Sleep(1 * time.Second)

	// 测试2: 创建世界
	fmt.Println("\n2️⃣ 测试世界创建...")
	worldID := testWorldCreation()

	if worldID != "" {
		// 等待一秒
		time.Sleep(1 * time.Second)

		// 测试3: 获取世界信息
		fmt.Println("\n3️⃣ 测试获取世界信息...")
		testGetWorld(worldID)
	}

	fmt.Println("\n🎉 测试完成！")
}

func testAIGeneration() {
	req := GenerateSceneRequest{
		WorldID:             "temp",
		SceneName:           "测试世界",
		SceneType:           "main",
		Theme:               "fantasy",
		Mood:                "mysterious",
		SpecialRequirements: "为名为'测试世界'的fantasy类型世界生成详细描述",
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post("http://localhost:8080/api/v1/ai/generate/scene", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ AI生成请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("📊 AI生成响应状态: %d\n", resp.StatusCode)
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		fmt.Printf("❌ 解析响应失败: %v\n", err)
		fmt.Printf("原始响应: %s\n", string(body))
		return
	}

	if apiResp.Success {
		fmt.Printf("✅ AI生成成功: %s\n", apiResp.Message)
		
		// 检查数据结构
		if data, ok := apiResp.Data.(map[string]interface{}); ok {
			if content, exists := data["content"]; exists {
				fmt.Printf("📝 生成内容: %s\n", content)
			}
			if structuredData, exists := data["structured_data"]; exists {
				if sd, ok := structuredData.(map[string]interface{}); ok {
					if desc, exists := sd["description"]; exists {
						fmt.Printf("📋 结构化描述: %s\n", desc)
					}
				}
			}
		}
	} else {
		fmt.Printf("❌ AI生成失败: %s\n", apiResp.Message)
		if apiResp.Error != "" {
			fmt.Printf("错误详情: %s\n", apiResp.Error)
		}
	}
}

func testWorldCreation() string {
	req := CreateWorldRequest{
		Name:        "测试世界",
		Description: "这是一个用于测试的幻想世界",
		Theme:       "fantasy",
		IsPublic:    true,
		MaxPlayers:  10,
		WorldSettings: map[string]interface{}{
			"difficulty": "normal",
		},
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post("http://localhost:8080/api/v1/game/worlds", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ 世界创建请求失败: %v\n", err)
		return ""
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("📊 世界创建响应状态: %d\n", resp.StatusCode)

	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		fmt.Printf("❌ 解析响应失败: %v\n", err)
		fmt.Printf("原始响应: %s\n", string(body))
		return ""
	}

	if apiResp.Success {
		fmt.Printf("✅ 世界创建成功: %s\n", apiResp.Message)
		
		// 提取世界ID
		if data, ok := apiResp.Data.(map[string]interface{}); ok {
			if id, exists := data["id"]; exists {
				worldID := fmt.Sprintf("%v", id)
				fmt.Printf("🌍 世界ID: %s\n", worldID)
				return worldID
			}
		}
	} else {
		fmt.Printf("❌ 世界创建失败: %s\n", apiResp.Message)
		if apiResp.Error != "" {
			fmt.Printf("错误详情: %s\n", apiResp.Error)
		}
	}

	return ""
}

func testGetWorld(worldID string) {
	url := fmt.Sprintf("http://localhost:8080/api/v1/game/worlds/%s", worldID)
	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("❌ 获取世界请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("📊 获取世界响应状态: %d\n", resp.StatusCode)

	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		fmt.Printf("❌ 解析响应失败: %v\n", err)
		fmt.Printf("原始响应: %s\n", string(body))
		return
	}

	if apiResp.Success {
		fmt.Printf("✅ 获取世界成功: %s\n", apiResp.Message)
	} else {
		fmt.Printf("❌ 获取世界失败: %s\n", apiResp.Message)
		if apiResp.Error != "" {
			fmt.Printf("错误详情: %s\n", apiResp.Error)
		}
	}
}
