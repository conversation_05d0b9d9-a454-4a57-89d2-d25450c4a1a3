version: '3.8'

services:
  # 增强的AI文本游戏应用
  ai-text-game-enhanced:
    build:
      context: ..
      dockerfile: deployments/Dockerfile.enhanced
    ports:
      - "${SERVER_PORT:-8080}:8080"
    environment:
      # 基础配置
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      
      # 数据库配置（根据环境自动选择）
      - DB_TYPE=${DB_TYPE:-sqlite}
      - DB_DSN=${DB_DSN:-game.db}
      - DB_MAX_OPEN_CONNS=${DB_MAX_OPEN_CONNS:-25}
      - DB_MAX_IDLE_CONNS=${DB_MAX_IDLE_CONNS:-5}
      - DB_CONN_MAX_LIFETIME=${DB_CONN_MAX_LIFETIME:-1h}
      
      # 兼容性配置
      - DB_ENABLE_PARTITIONING=${DB_ENABLE_PARTITIONING:-false}
      - DB_ENABLE_MATERIALIZED_VIEWS=${DB_ENABLE_MATERIALIZED_VIEWS:-false}
      - DB_JSON_INDEX_PATHS=ui.theme,ui.language,game.ai_speed,theme.genre,theme.mood
      
      # 性能配置
      - DB_ENABLE_QUERY_LOGGING=${DB_ENABLE_QUERY_LOGGING:-true}
      - DB_SLOW_QUERY_THRESHOLD=${DB_SLOW_QUERY_THRESHOLD:-200ms}
      - DB_ENABLE_PERFORMANCE_MONITOR=${DB_ENABLE_PERFORMANCE_MONITOR:-true}
      
      # 缓存配置
      - CACHE_TYPE=redis
      - CACHE_HOST=redis
      - CACHE_PORT=6379
      - CACHE_PASSWORD=${REDIS_PASSWORD:-}
      - CACHE_DB=0
      
      # AI配置
      - AI_PROVIDER=${AI_PROVIDER:-windmill}
      - AI_API_KEY=${AI_API_KEY:-}
      - AI_BASE_URL=${AI_BASE_URL:-}
      - AI_DEFAULT_MODEL=${AI_DEFAULT_MODEL:-gpt-3.5-turbo}
      
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FORMAT=json
      - LOG_OUTPUT=stdout
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
    networks:
      - ai-game-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库（生产环境）
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-ai_text_game}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - ai-game-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - ai-game-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    command: redis-server /usr/local/etc/redis/redis.conf

  # 数据库管理工具（可选）
  adminer:
    image: adminer:4.8.1
    ports:
      - "${ADMINER_PORT:-8081}:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=postgres
    networks:
      - ai-game-network
    restart: unless-stopped
    profiles:
      - tools

  # Redis管理工具（可选）
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "${REDIS_COMMANDER_PORT:-8082}:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    networks:
      - ai-game-network
    restart: unless-stopped
    profiles:
      - tools

  # 监控工具（可选）
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - ai-game-network
    restart: unless-stopped
    profiles:
      - monitoring
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ai-game-network
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  ai-game-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
