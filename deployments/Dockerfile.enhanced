# 多阶段构建的增强Dockerfile
# 阶段1: 构建阶段
FROM golang:1.21-alpine AS builder

# 安装必要的工具
RUN apk add --no-cache git ca-certificates tzdata gcc musl-dev sqlite-dev

# 设置工作目录
WORKDIR /app

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建增强服务器
RUN CGO_ENABLED=1 GOOS=linux go build \
    -ldflags="-w -s -extldflags '-static'" \
    -a -installsuffix cgo \
    -o enhanced-server \
    ./cmd/enhanced-server

# 构建测试工具
RUN CGO_ENABLED=1 GOOS=linux go build \
    -ldflags="-w -s -extldflags '-static'" \
    -a -installsuffix cgo \
    -o test-compatibility \
    ./scripts/test_enhanced_compatibility.go

# 阶段2: 运行阶段
FROM alpine:3.18

# 安装运行时依赖
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    curl \
    sqlite \
    postgresql-client \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/enhanced-server /app/enhanced-server
COPY --from=builder /app/test-compatibility /app/test-compatibility

# 复制配置文件和脚本
COPY --from=builder /app/deployments/scripts/ /app/scripts/
COPY --from=builder /app/migrations/ /app/migrations/

# 创建必要的目录
RUN mkdir -p /app/data /app/logs /app/config && \
    chown -R appuser:appgroup /app

# 设置时区
ENV TZ=Asia/Shanghai

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["/app/enhanced-server"]

# 元数据标签
LABEL maintainer="AI Text Game Team" \
      version="1.0.0" \
      description="Enhanced AI Text Game Server with Database Compatibility" \
      org.opencontainers.image.title="AI Text Game Enhanced Server" \
      org.opencontainers.image.description="Enhanced server with PostgreSQL/SQLite compatibility" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.created="2024-01-08" \
      org.opencontainers.image.source="https://github.com/your-org/ai-text-game" \
      org.opencontainers.image.licenses="MIT"
