#!/bin/bash

# AI文本游戏增强版部署脚本
# 支持开发环境（SQLite）和生产环境（PostgreSQL）的自动部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AI文本游戏增强版部署脚本

用法: $0 [选项] <环境>

环境:
  dev         开发环境（使用SQLite）
  prod        生产环境（使用PostgreSQL）
  test        测试环境

选项:
  -h, --help              显示此帮助信息
  -v, --verbose           详细输出
  -c, --clean             清理现有容器和数据
  -t, --test              运行兼容性测试
  -m, --monitoring        启用监控工具
  --tools                 启用管理工具
  --no-cache              构建时不使用缓存
  --pull                  强制拉取最新镜像

示例:
  $0 dev                  部署开发环境
  $0 prod -m              部署生产环境并启用监控
  $0 test -t              部署测试环境并运行测试
  $0 dev --clean          清理并重新部署开发环境

EOF
}

# 默认参数
ENVIRONMENT=""
VERBOSE=false
CLEAN=false
RUN_TEST=false
ENABLE_MONITORING=false
ENABLE_TOOLS=false
NO_CACHE=false
PULL_IMAGES=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -t|--test)
            RUN_TEST=true
            shift
            ;;
        -m|--monitoring)
            ENABLE_MONITORING=true
            shift
            ;;
        --tools)
            ENABLE_TOOLS=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --pull)
            PULL_IMAGES=true
            shift
            ;;
        dev|prod|test)
            ENVIRONMENT=$1
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查环境参数
if [[ -z "$ENVIRONMENT" ]]; then
    log_error "请指定部署环境: dev, prod, 或 test"
    show_help
    exit 1
fi

# 设置详细输出
if [[ "$VERBOSE" == true ]]; then
    set -x
fi

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 设置环境变量
setup_environment() {
    log_info "设置 $ENVIRONMENT 环境变量..."
    
    # 基础环境变量
    export ENVIRONMENT=$ENVIRONMENT
    export COMPOSE_PROJECT_NAME="ai-text-game-${ENVIRONMENT}"
    
    case $ENVIRONMENT in
        dev)
            export DB_TYPE=sqlite
            export DB_DSN=./data/game_dev.db
            export DB_MAX_OPEN_CONNS=10
            export DB_MAX_IDLE_CONNS=2
            export DB_ENABLE_QUERY_LOGGING=true
            export LOG_LEVEL=debug
            export SERVER_PORT=8080
            ;;
        prod)
            export DB_TYPE=postgresql
            export DB_DSN="host=postgres port=5432 user=postgres password=postgres dbname=ai_text_game sslmode=disable"
            export DB_MAX_OPEN_CONNS=100
            export DB_MAX_IDLE_CONNS=20
            export DB_ENABLE_PARTITIONING=true
            export DB_ENABLE_MATERIALIZED_VIEWS=true
            export DB_ENABLE_QUERY_LOGGING=false
            export LOG_LEVEL=info
            export SERVER_PORT=8080
            ;;
        test)
            export DB_TYPE=sqlite
            export DB_DSN=./data/game_test.db
            export DB_MAX_OPEN_CONNS=5
            export DB_MAX_IDLE_CONNS=1
            export DB_ENABLE_QUERY_LOGGING=true
            export LOG_LEVEL=warn
            export SERVER_PORT=8080
            ;;
    esac
    
    # 设置Docker Compose配置文件
    export COMPOSE_FILE="deployments/docker-compose.enhanced.yml"
    
    # 设置profiles
    PROFILES=""
    if [[ "$ENABLE_MONITORING" == true ]]; then
        PROFILES="${PROFILES},monitoring"
    fi
    if [[ "$ENABLE_TOOLS" == true ]]; then
        PROFILES="${PROFILES},tools"
    fi
    
    if [[ -n "$PROFILES" ]]; then
        export COMPOSE_PROFILES="${PROFILES#,}"
    fi
    
    log_info "环境变量设置完成"
}

# 清理现有部署
cleanup_deployment() {
    if [[ "$CLEAN" == true ]]; then
        log_info "清理现有部署..."
        
        docker-compose down -v --remove-orphans || true
        
        # 清理数据卷（谨慎操作）
        if [[ "$ENVIRONMENT" == "dev" || "$ENVIRONMENT" == "test" ]]; then
            docker volume prune -f || true
        fi
        
        log_info "清理完成"
    fi
}

# 拉取镜像
pull_images() {
    if [[ "$PULL_IMAGES" == true ]]; then
        log_info "拉取最新镜像..."
        docker-compose pull
        log_info "镜像拉取完成"
    fi
}

# 构建应用
build_application() {
    log_info "构建应用..."
    
    BUILD_ARGS=""
    if [[ "$NO_CACHE" == true ]]; then
        BUILD_ARGS="--no-cache"
    fi
    
    docker-compose build $BUILD_ARGS ai-text-game-enhanced
    
    log_info "应用构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 首先启动依赖服务
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        log_info "启动PostgreSQL..."
        docker-compose up -d postgres
        
        # 等待PostgreSQL启动
        log_info "等待PostgreSQL启动..."
        sleep 10
        
        # 检查PostgreSQL健康状态
        for i in {1..30}; do
            if docker-compose exec postgres pg_isready -U postgres; then
                log_info "PostgreSQL已就绪"
                break
            fi
            if [[ $i -eq 30 ]]; then
                log_error "PostgreSQL启动超时"
                exit 1
            fi
            sleep 2
        done
    fi
    
    # 启动Redis
    log_info "启动Redis..."
    docker-compose up -d redis
    
    # 启动主应用
    log_info "启动AI文本游戏应用..."
    docker-compose up -d ai-text-game-enhanced
    
    # 启动其他服务
    if [[ -n "$COMPOSE_PROFILES" ]]; then
        log_info "启动额外服务..."
        docker-compose up -d
    fi
    
    log_info "所有服务启动完成"
}

# 运行兼容性测试
run_compatibility_test() {
    if [[ "$RUN_TEST" == true ]]; then
        log_info "运行兼容性测试..."
        
        # 等待应用启动
        sleep 15
        
        # 运行测试
        docker-compose exec ai-text-game-enhanced /app/test-compatibility
        
        if [[ $? -eq 0 ]]; then
            log_info "兼容性测试通过"
        else
            log_error "兼容性测试失败"
            exit 1
        fi
    fi
}

# 显示部署信息
show_deployment_info() {
    log_info "部署完成！"
    echo
    echo "=== 部署信息 ==="
    echo "环境: $ENVIRONMENT"
    echo "数据库类型: $DB_TYPE"
    echo "应用端口: $SERVER_PORT"
    echo
    echo "=== 访问地址 ==="
    echo "应用: http://localhost:$SERVER_PORT"
    echo "健康检查: http://localhost:$SERVER_PORT/health"
    echo "数据库信息: http://localhost:$SERVER_PORT/api/v1/database/info"
    
    if [[ "$ENABLE_TOOLS" == true ]]; then
        echo
        echo "=== 管理工具 ==="
        echo "Adminer (数据库管理): http://localhost:8081"
        echo "Redis Commander: http://localhost:8082"
    fi
    
    if [[ "$ENABLE_MONITORING" == true ]]; then
        echo
        echo "=== 监控工具 ==="
        echo "Prometheus: http://localhost:9090"
        echo "Grafana: http://localhost:3000 (admin/admin)"
    fi
    
    echo
    echo "=== 常用命令 ==="
    echo "查看日志: docker-compose logs -f ai-text-game-enhanced"
    echo "停止服务: docker-compose down"
    echo "重启服务: docker-compose restart ai-text-game-enhanced"
    echo
}

# 主函数
main() {
    log_info "开始部署AI文本游戏增强版 ($ENVIRONMENT 环境)"
    
    check_dependencies
    setup_environment
    cleanup_deployment
    pull_images
    build_application
    start_services
    run_compatibility_test
    show_deployment_info
    
    log_info "部署完成！"
}

# 执行主函数
main "$@"
