# AI文本游戏接口设计文档

## 1. 接口设计概述

### 1.1. 设计原则
- **RESTful设计**: 遵循REST架构风格，资源导向的URL设计
- **统一响应格式**: 所有接口使用统一的响应数据格式
- **版本控制**: 支持API版本管理，确保向后兼容
- **安全优先**: 完善的认证授权和数据验证机制
- **性能优化**: 支持分页、缓存和批量操作

### 1.2. 基础信息
- **Base URL**: `https://api.ai-text-game.com/v1`
- **认证方式**: Bearer Token (JWT) + 外部IDP集成
- **内容类型**: `application/json`
- **字符编码**: UTF-8

### 1.3. 统一响应格式设计原则

为了简化客户端处理逻辑和提供更好的错误处理体验，本系统采用统一的JSON响应格式，不直接依赖HTTP状态码来表示业务状态。

#### 设计原因
- **简化客户端逻辑**: 客户端只需要解析JSON响应，无需同时处理HTTP状态码和响应体
- **更丰富的错误信息**: 可以在响应体中提供详细的错误代码、消息和上下文信息
- **统一的异常处理**: 所有业务异常都通过统一的错误格式返回，便于前端统一处理
- **更好的调试体验**: 错误信息更加详细，包含请求ID和时间戳，便于问题追踪
- **API网关兼容**: 通过API网关统一处理，避免不同服务返回不同的状态码格式

#### HTTP状态码使用策略
- **200 OK**: 所有成功的API调用都返回200状态码，业务状态通过响应体中的success字段表示
- **500 Internal Server Error**: 仅在服务器无法处理请求时返回（如服务崩溃、网络异常）
- **其他状态码**: 仅在网络层面或基础设施层面使用（如API网关、负载均衡器的路由错误）

### 1.4. 统一响应格式

#### 成功响应 (HTTP 200)
```json
{
    "success": true,
    "data": {},
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-************"
}
```

#### 业务错误响应 (HTTP 200)
```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "请求参数验证失败",
        "details": "用户名格式不正确",
        "field": "username"
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-************"
}
```

#### 系统错误响应 (HTTP 500)
```json
{
    "success": false,
    "error": {
        "code": "INTERNAL_ERROR",
        "message": "服务器内部错误",
        "details": "请稍后重试或联系技术支持"
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-************"
}
```

## 2. 外部IDP认证接口

### 2.1. 获取认证URL
```http
GET /auth/providers/{provider}/authorize?redirect_uri={redirect_uri}&state={state}
```

**路径参数:**
- `provider`: IDP提供商 (auth0, keycloak, google, github等)

**查询参数:**
- `redirect_uri`: 认证成功后的回调URL
- `state`: 防CSRF攻击的状态参数

**响应示例 (HTTP 200):**
```json
{
    "success": true,
    "data": {
        "authorize_url": "https://idp.example.com/oauth/authorize?client_id=xxx&redirect_uri=xxx&response_type=code&scope=openid+profile+email&state=xxx"
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-************"
}
```

### 2.2. 处理认证回调
```http
POST /auth/callback
Content-Type: application/json

{
    "provider": "auth0",
    "code": "authorization_code_from_idp",
    "state": "csrf_protection_state"
}
```

**响应示例 (HTTP 200):**
```json
{
    "success": true,
    "data": {
        "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 3600,
        "user": {
            "id": "uuid",
            "external_id": "auth0|123456789",
            "external_provider": "auth0",
            "email": "<EMAIL>",
            "display_name": "玩家123",
            "game_roles": ["user"],
            "created_at": "2024-01-01T00:00:00Z"
        }
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-************"
}
```

### 2.3. 刷新访问令牌
```http
POST /auth/refresh
Content-Type: application/json
Authorization: Bearer {refresh_token}

{
    "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应示例 (HTTP 200):**
```json
{
    "success": true,
    "data": {
        "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 3600
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-************"
}
```

### 2.4. 用户登出
```http
POST /auth/logout
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应示例 (HTTP 204):**
```
(无响应体)
```

### 2.5. 获取支持的IDP列表
```http
GET /auth/providers
```

**响应示例 (HTTP 200):**
```json
{
    "success": true,
    "data": [
        {
            "name": "auth0",
            "display_name": "Auth0",
            "icon_url": "https://cdn.auth0.com/styleguide/components/1.0.8/media/logos/img/badge.png",
            "enabled": true
        },
        {
            "name": "google",
            "display_name": "Google",
            "icon_url": "https://developers.google.com/identity/images/g-logo.png",
            "enabled": true
        }
    ],
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-************"
}

## 3. 用户管理接口

### 3.1. 获取用户信息
```http
GET /users/profile
Authorization: Bearer {access_token}
```

**响应示例 (HTTP 200):**
```json
{
    "success": true,
    "data": {
        "id": "uuid",
        "external_id": "auth0|123456789",
        "external_provider": "auth0",
        "email": "<EMAIL>",
        "display_name": "玩家123",
        "avatar_url": "https://example.com/avatar.jpg",
        "game_roles": ["user"],
        "status": "active",
        "preferences": {
            "theme": "dark",
            "language": "zh-CN",
            "notifications": true
        },
        "created_at": "2024-01-01T00:00:00Z",
        "last_login_at": "2024-01-01T12:00:00Z"
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-************"
}
```

### 3.2. 更新用户信息
```http
PUT /users/profile
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "display_name": "新昵称",
    "avatar_url": "https://example.com/avatar.jpg",
    "preferences": {
        "theme": "dark",
        "language": "zh-CN",
        "notifications": true
    }
}
```

### 3.3. 获取用户统计
```http
GET /users/stats
Authorization: Bearer {access_token}
```

## 4. 游戏世界接口

### 4.1. 创建世界
```http
POST /worlds
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "魔法王国",
    "description": "一个充满魔法和冒险的中世纪世界",
    "world_prompt": "创建一个中世纪魔法世界，有巨龙、骑士和法师",
    "config": {
        "time_multiplier": 60,
        "max_players": 10,
        "is_public": false,
        "enable_multiplayer": true,
        "content_safety_level": "standard"
    }
}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "world_id": "uuid",
        "name": "魔法王国",
        "description": "一个充满魔法和冒险的中世纪世界",
        "creator_id": "uuid",
        "status": "generating",
        "framework": {
            "regions": ["北方山脉", "中央平原", "南方森林"],
            "landmarks": ["王都", "魔法学院", "古老遗迹"],
            "estimated_size": "large",
            "player_capacity": 10
        },
        "created_at": "2024-01-01T00:00:00Z"
    },
    "message": "世界创建中，请稍候..."
}
```

### 4.1.1. 获取世界框架
```http
GET /worlds/{world_id}/framework
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "world_id": "uuid",
        "regions": [
            {
                "name": "北方山脉",
                "description": "寒冷的山区，有矮人城市和龙穴",
                "player_slots": 3,
                "difficulty": "hard"
            }
        ],
        "landmarks": [
            {
                "name": "王都",
                "region": "中央平原",
                "type": "convergence_point",
                "description": "王国的政治和商业中心"
            }
        ],
        "distances": {
            "北方山脉-中央平原": "2天旅程",
            "中央平原-南方森林": "1天旅程"
        },
        "convergence_points": ["王都", "魔法学院"]
    }
}
```

### 4.2. 获取世界列表
```http
GET /worlds?page=1&limit=20&filter=my|public|joined
Authorization: Bearer {access_token}
```

### 4.3. 获取世界详情
```http
GET /worlds/{world_id}
Authorization: Bearer {access_token}
```

### 4.4. 加入世界
```http
POST /worlds/{world_id}/join
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "character_name": "艾莉亚",
    "character_description": "一个年轻的精灵法师",
    "initial_traits": ["魔法天赋", "好奇心强", "善良"]
}
```

### 4.5. 离开世界
```http
POST /worlds/{world_id}/leave
Authorization: Bearer {access_token}
```

## 5. 游戏交互接口

### 5.1. 获取当前游戏状态
```http
GET /worlds/{world_id}/state
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "world": {
            "id": "uuid",
            "name": "魔法王国",
            "current_time": "第3天 上午10:30"
        },
        "character": {
            "id": "uuid",
            "name": "艾莉亚",
            "current_scene": "森林小径",
            "traits": ["魔法天赋", "好奇心强"],
            "inventory": ["法师袍", "魔法杖", "治疗药水"]
        },
        "scene": {
            "id": "uuid",
            "name": "森林小径",
            "description": "阳光透过茂密的树叶洒在小径上...",
            "entities": ["古老的橡树", "小溪", "野花"],
            "characters": ["艾莉亚", "神秘商人"]
        },
        "recent_events": [
            {
                "narrative": "你沿着小径向前走，听到了潺潺的流水声。",
                "timestamp": "2024-01-01T10:25:00Z"
            }
        ]
    }
}
```

### 5.2. 执行游戏行动
```http
POST /worlds/{world_id}/actions
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "action": "与神秘商人交谈",
    "details": "询问关于附近危险的信息"
}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "action_id": "uuid",
        "status": "processing",
        "estimated_time": 3
    },
    "message": "行动正在处理中..."
}
```

### 5.3. 获取行动结果
```http
GET /worlds/{world_id}/actions/{action_id}
Authorization: Bearer {access_token}
```

### 5.4. 获取游戏历史
```http
GET /worlds/{world_id}/history?page=1&limit=50&from_time=timestamp
Authorization: Bearer {access_token}
```

## 6. 实时通信接口 (WebSocket)

### 6.1. 连接建立
```
WebSocket URL: wss://api.ai-text-game.com/v1/ws
Authorization: Bearer {access_token}
```

### 6.2. 消息格式

#### 客户端发送消息
```json
{
    "type": "join_world",
    "world_id": "uuid",
    "data": {}
}
```

#### 服务端推送消息
```json
{
    "type": "game_update",
    "world_id": "uuid",
    "data": {
        "narrative": "神秘商人微笑着说：'年轻的法师，前方的森林深处有古老的魔法遗迹...'",
        "scene_changes": {},
        "character_changes": {
            "memories": ["与神秘商人的对话"]
        },
        "new_entities": []
    },
    "timestamp": "2024-01-01T10:30:00Z"
}
```

## 7. 内容安全接口

### 7.1. 内容验证
```http
POST /content/validate
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "content": "用户输入的文本内容",
    "content_type": "user_input",
    "context": {
        "world_id": "uuid",
        "character_id": "uuid"
    }
}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "is_valid": true,
        "confidence": 0.95,
        "safety_score": 0.98,
        "violations": [],
        "processed_content": "处理后的内容",
        "suggestions": []
    }
}
```

### 7.2. AI内容审核
```http
POST /content/audit
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "content": "AI生成的内容",
    "content_type": "ai_output",
    "source": "world_generation",
    "context": {
        "world_id": "uuid",
        "prompt": "原始提示词"
    }
}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "audit_id": "uuid",
        "safety_score": 0.96,
        "consistency_score": 0.92,
        "quality_score": 0.88,
        "action_taken": "approve",
        "issues": [],
        "recommendations": ["建议增加更多细节描述"]
    }
}
```

### 7.3. 内容审核历史
```http
GET /content/audits?page=1&limit=20&content_type=user_input&from_date=2024-01-01
Authorization: Bearer {access_token}
```

### 7.4. 举报内容
```http
POST /content/report
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "content_id": "uuid",
    "content_type": "ai_output",
    "reason": "inappropriate_content",
    "description": "详细举报说明",
    "evidence": {
        "screenshot_url": "https://example.com/screenshot.png"
    }
}
```

## 8. 管理后台接口

### 7.1. 系统统计
```http
GET /admin/stats
Authorization: Bearer {admin_token}
```

### 7.2. 用户管理
```http
GET /admin/users?page=1&limit=50&search=keyword
PUT /admin/users/{user_id}/status
DELETE /admin/users/{user_id}
```

### 7.3. 世界管理
```http
GET /admin/worlds?status=active|paused|archived
PUT /admin/worlds/{world_id}/status
GET /admin/worlds/{world_id}/logs
```

### 7.4. AI交互日志
```http
GET /admin/ai-logs?page=1&limit=100&from_date=2024-01-01
GET /admin/ai-logs/{interaction_id}
```

### 7.5. 内容安全管理
```http
GET /admin/content/audits?page=1&limit=50&status=pending|approved|rejected
GET /admin/content/audits/{audit_id}
PUT /admin/content/audits/{audit_id}/review
POST /admin/content/keywords
DELETE /admin/content/keywords/{keyword_id}
```

### 7.6. 世界框架管理
```http
GET /admin/worlds/{world_id}/framework
PUT /admin/worlds/{world_id}/framework
GET /admin/worlds/{world_id}/player-allocations
```

## 8. 错误码定义

### 8.1. 通用错误码
- `INVALID_REQUEST`: 请求格式错误
- `UNAUTHORIZED`: 未授权访问
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `RATE_LIMITED`: 请求频率超限

### 8.2. 业务错误码
- `USER_EXISTS`: 用户已存在
- `INVALID_CREDENTIALS`: 登录凭据无效
- `WORLD_FULL`: 世界人数已满
- `CHARACTER_EXISTS`: 角色已存在
- `AI_SERVICE_ERROR`: AI服务异常
- `WORLD_GENERATING`: 世界生成中

## 9. 接口安全

### 9.1. 认证机制
- JWT Token认证，包含用户ID和权限信息
- Token过期时间设置为1小时，支持刷新
- 敏感操作需要二次验证

### 9.2. 权限控制
- 基于角色的访问控制(RBAC)
- 资源级权限验证
- API调用频率限制

### 9.3. 数据验证
- 请求参数严格验证
- SQL注入防护
- XSS攻击防护

## 10. 性能优化

### 10.1. 缓存策略
- 用户信息缓存30分钟
- 世界状态缓存5分钟
- 静态数据长期缓存

### 10.2. 分页机制
- 默认分页大小20条
- 最大分页大小100条
- 支持游标分页

### 10.3. 批量操作
- 支持批量获取世界信息
- 支持批量执行游戏行动
- 异步处理耗时操作

## 11. 错误码定义

### 11.1. 认证相关错误
- **AUTH_001**: 认证失败 (HTTP 401)
- **AUTH_002**: 访问令牌已过期 (HTTP 401)
- **AUTH_003**: 刷新令牌无效 (HTTP 401)
- **AUTH_004**: 权限不足 (HTTP 403)
- **AUTH_005**: IDP认证失败 (HTTP 401)
- **AUTH_006**: 不支持的IDP提供商 (HTTP 400)

### 11.2. 用户相关错误
- **USER_001**: 用户不存在 (HTTP 404)
- **USER_002**: 用户已被禁用 (HTTP 403)
- **USER_003**: 用户信息更新失败 (HTTP 400)
- **USER_004**: 外部用户ID已存在 (HTTP 409)

### 11.3. 游戏相关错误
- **GAME_001**: 世界不存在 (HTTP 404)
- **GAME_002**: 世界访问权限不足 (HTTP 403)
- **GAME_003**: 世界已满员 (HTTP 409)
- **GAME_004**: 角色不存在 (HTTP 404)
- **GAME_005**: 游戏行动无效 (HTTP 400)
- **GAME_006**: AI接口调用失败 (HTTP 502)
- **GAME_007**: 世界状态异常 (HTTP 500)

### 11.4. 系统相关错误
- **SYS_001**: 请求频率过高 (HTTP 429)
- **SYS_002**: 服务暂时不可用 (HTTP 503)
- **SYS_003**: 数据库连接失败 (HTTP 500)
- **SYS_004**: 缓存服务异常 (HTTP 500)
- **SYS_005**: 参数验证失败 (HTTP 400)

### 11.5. 业务相关错误
- **BIZ_001**: 资源配额不足 (HTTP 403)
- **BIZ_002**: 操作冲突 (HTTP 409)
- **BIZ_003**: 业务规则违反 (HTTP 422)
- **BIZ_004**: 数据完整性错误 (HTTP 422)

## 12. Go语言接口实现示例

### 12.1. 认证中间件
```go
func AuthMiddleware(idpClient *oidc.Client) gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": gin.H{
                    "code":    "AUTH_001",
                    "message": "认证失败",
                    "details": "缺少Authorization头",
                },
                "timestamp":  time.Now(),
                "request_id": c.GetString("request_id"),
            })
            c.Abort()
            return
        }

        token := strings.TrimPrefix(authHeader, "Bearer ")
        claims, err := validateJWT(token, idpClient)
        if err != nil {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": gin.H{
                    "code":    "AUTH_002",
                    "message": "访问令牌已过期",
                    "details": err.Error(),
                },
                "timestamp":  time.Now(),
                "request_id": c.GetString("request_id"),
            })
            c.Abort()
            return
        }

        c.Set("user_claims", claims)
        c.Next()
    }
}
```

### 12.2. 统一响应处理
```go
type APIResponse struct {
    Data      interface{} `json:"data,omitempty"`
    Error     *APIError   `json:"error,omitempty"`
    Timestamp time.Time   `json:"timestamp"`
    RequestID string      `json:"request_id"`
}

type APIError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func SuccessResponse(c *gin.Context, data interface{}) {
    c.JSON(http.StatusOK, APIResponse{
        Data:      data,
        Timestamp: time.Now(),
        RequestID: c.GetString("request_id"),
    })
}

func ErrorResponse(c *gin.Context, statusCode int, code, message, details string) {
    c.JSON(statusCode, APIResponse{
        Error: &APIError{
            Code:    code,
            Message: message,
            Details: details,
        },
        Timestamp: time.Now(),
        RequestID: c.GetString("request_id"),
    })
}
```

本接口设计确保了系统的安全性、性能和可扩展性，通过外部IDP集成和标准HTTP状态码提供了完整的API支持。
