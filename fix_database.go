package main

import (
	"fmt"
	"log"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/migration"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/database"
)

func main() {
	fmt.Println("🔧 开始修复数据库...")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	fmt.Printf("📊 数据库配置: %s\n", cfg.Database.DBName)

	// 连接数据库
	db, err := database.New(&cfg.Database)
	if err != nil {
		log.Fatalf("❌ 连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 创建智能迁移器
	smartMigrator := migration.NewSmartMigrator(db, &cfg.Database, "migrations")

	// 获取数据库信息
	info := smartMigrator.GetDatabaseInfo()
	fmt.Printf("📋 数据库类型: %s\n", info["database_type"])
	fmt.Printf("📋 支持JSONB: %v\n", info["supports_jsonb"])
	fmt.Printf("📋 支持UUID: %v\n", info["supports_uuid"])

	// 定义所有需要迁移的模型
	modelsList := []interface{}{
		&models.User{},
		&models.UserStats{},
		&models.World{},
		&models.Scene{},
		&models.Character{},
		&models.Entity{},
		&models.Event{},
		&models.AIInteraction{},
	}

	fmt.Println("🚀 开始执行数据库迁移...")

	// 使用简单的自动迁移，避免触发器问题
	if err := smartMigrator.AutoMigrate(modelsList...); err != nil {
		log.Fatalf("❌ 数据库迁移失败: %v", err)
	}

	fmt.Println("✅ 数据库迁移成功")

	// 验证表是否创建成功
	fmt.Println("🔍 验证表创建情况...")

	tableNames := []string{"users", "user_stats", "worlds", "scenes", "characters", "entities", "events", "ai_interactions"}
	for _, tableName := range tableNames {
		if db.Migrator().HasTable(tableName) {
			fmt.Printf("  ✅ %s 表存在\n", tableName)
		} else {
			fmt.Printf("  ❌ %s 表不存在\n", tableName)
		}
	}

	// 创建测试用户（如果不存在）
	fmt.Println("👤 检查测试用户...")
	var userCount int64
	db.Model(&models.User{}).Count(&userCount)
	
	if userCount == 0 {
		fmt.Println("📝 创建测试用户...")
		testUser := &models.User{
			ExternalID:       "dev_user_001",
			ExternalProvider: "development",
			Email:           "<EMAIL>",
			Status:          "active",
		}
		
		if err := db.Create(testUser).Error; err != nil {
			log.Printf("⚠️ 创建测试用户失败: %v", err)
		} else {
			fmt.Printf("✅ 测试用户创建成功，ID: %s\n", testUser.ID)
		}
	} else {
		fmt.Printf("✅ 已存在 %d 个用户\n", userCount)
	}

	// 检查世界表
	var worldCount int64
	db.Model(&models.World{}).Count(&worldCount)
	fmt.Printf("🌍 数据库中有 %d 个世界\n", worldCount)

	fmt.Println("🎉 数据库修复完成！")
}
