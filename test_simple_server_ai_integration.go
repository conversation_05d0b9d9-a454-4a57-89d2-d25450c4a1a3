package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"syscall"
	"time"
)

// 测试simple-server的统一架构
func main() {
	fmt.Println("🧪 开始测试 simple-server 的统一架构")
	fmt.Println("📋 验证：开发和生产环境使用完全相同的代码路径")

	// 测试1：Mock模式（开发环境）
	fmt.Println("\n🔧 测试1：Mock模式（开发环境配置）")
	if !testMockMode() {
		fmt.Println("❌ Mock模式测试失败")
		return
	}

	// 测试2：真实AI模式（如果配置了的话）
	fmt.Println("\n🌐 测试2：真实AI模式（生产环境配置）")
	if !testRealAIMode() {
		fmt.Println("⚠️  真实AI模式测试跳过（未配置AI服务）")
	}

	fmt.Println("\n✅ 统一架构验证完成！")
}

// testMockMode 测试Mock模式
func testMockMode() bool {
	fmt.Println("🚀 启动 simple-server (Mock模式)...")
	cmd := exec.Command("go", "run", "cmd/simple-server/main.go")
	cmd.Env = append(os.Environ(),
		"PORT=8081",
		"ENVIRONMENT=development",
		"DEV_ENABLE_DEBUG_LOGS=true",
		// 不设置AI配置，使用Mock模式
	)

	if err := cmd.Start(); err != nil {
		fmt.Printf("❌ 启动服务器失败: %v\n", err)
		return false
	}

	// 确保服务器关闭
	defer func() {
		if cmd.Process != nil {
			cmd.Process.Signal(syscall.SIGTERM)
			cmd.Wait()
		}
	}()

	// 等待服务器启动
	fmt.Println("⏳ 等待服务器启动...")
	time.Sleep(3 * time.Second)

	// 测试健康检查
	fmt.Println("🔍 测试健康检查...")
	if !testHealthCheck() {
		fmt.Println("❌ 健康检查失败")
		return false
	}

	// 测试AI场景生成（Mock模式）
	fmt.Println("🎭 测试AI场景生成（Mock模式）...")
	if !testSceneGenerationMock() {
		fmt.Println("❌ AI场景生成测试失败")
		return false
	}

	// 测试角色生成（新增）
	fmt.Println("👤 测试AI角色生成（Mock模式）...")
	if !testCharacterGeneration() {
		fmt.Println("❌ AI角色生成测试失败")
		return false
	}

	fmt.Println("✅ Mock模式测试通过！")
	return true
}

// testRealAIMode 测试真实AI模式
func testRealAIMode() bool {
	// 检查是否配置了真实AI服务
	if os.Getenv("AI_BASE_URL") == "" || os.Getenv("AI_TOKEN") == "" {
		fmt.Println("⚠️  未配置AI服务，跳过真实AI模式测试")
		return false
	}

	fmt.Println("🚀 启动 simple-server (真实AI模式)...")
	cmd := exec.Command("go", "run", "cmd/simple-server/main.go")
	cmd.Env = append(os.Environ(),
		"PORT=8082",
		"ENVIRONMENT=production",
		// 使用环境变量中的真实AI配置
	)

	if err := cmd.Start(); err != nil {
		fmt.Printf("❌ 启动服务器失败: %v\n", err)
		return false
	}

	defer func() {
		if cmd.Process != nil {
			cmd.Process.Signal(syscall.SIGTERM)
			cmd.Wait()
		}
	}()

	time.Sleep(3 * time.Second)

	// 测试真实AI场景生成
	fmt.Println("🎭 测试AI场景生成（真实AI模式）...")
	if !testSceneGenerationReal() {
		fmt.Println("❌ 真实AI场景生成测试失败")
		return false
	}

	fmt.Println("✅ 真实AI模式测试通过！")
	return true
}

// testHealthCheck 测试健康检查
func testHealthCheck() bool {
	resp, err := http.Get("http://localhost:8081/health")
	if err != nil {
		fmt.Printf("❌ 健康检查请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ 健康检查状态码错误: %d\n", resp.StatusCode)
		return false
	}

	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		fmt.Printf("❌ 健康检查响应解析失败: %v\n", err)
		return false
	}

	fmt.Printf("✅ 健康检查通过: %s\n", result["status"])
	return true
}

// testSceneGenerationMock 测试AI场景生成（Mock模式）
func testSceneGenerationMock() bool {
	requestData := map[string]interface{}{
		"prompt":   "生成一个神秘的魔法森林场景",
		"world_id": "550e8400-e29b-41d4-a716-446655440000",
		"context": map[string]interface{}{
			"theme": "fantasy",
			"mood":  "mysterious",
		},
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		fmt.Printf("❌ 请求数据序列化失败: %v\n", err)
		return false
	}

	resp, err := http.Post(
		"http://localhost:8081/api/v1/ai/generate/scene",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		fmt.Printf("❌ AI场景生成请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return false
	}

	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ AI场景生成状态码错误: %d, 响应: %s\n", resp.StatusCode, string(body))
		return false
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		fmt.Printf("❌ AI场景生成响应解析失败: %v\n", err)
		return false
	}

	// 检查响应结构
	if !result["success"].(bool) {
		fmt.Printf("❌ AI场景生成失败: %v\n", result["error"])
		return false
	}

	data := result["data"].(map[string]interface{})
	if content, ok := data["content"].(string); ok && content != "" {
		fmt.Printf("✅ AI场景生成成功，内容长度: %d 字符\n", len(content))
		fmt.Printf("📝 生成内容预览: %.100s...\n", content)
	} else {
		fmt.Println("❌ AI场景生成响应缺少内容")
		return false
	}

	if structuredData, ok := data["structured_data"].(map[string]interface{}); ok {
		if name, ok := structuredData["name"].(string); ok {
			fmt.Printf("🏷️  场景名称: %s\n", name)
		}
		if sceneType, ok := structuredData["type"].(string); ok {
			fmt.Printf("🎭 场景类型: %s\n", sceneType)
		}
	}

	if tokenUsage, ok := data["token_usage"].(float64); ok {
		fmt.Printf("🔢 Token使用量: %.0f\n", tokenUsage)
	}

	return true
}

// testSceneGenerationReal 测试AI场景生成（真实AI模式）
func testSceneGenerationReal() bool {
	requestData := map[string]interface{}{
		"prompt":   "生成一个赛博朋克风格的城市场景",
		"world_id": "550e8400-e29b-41d4-a716-446655440000",
		"context": map[string]interface{}{
			"theme": "cyberpunk",
			"mood":  "futuristic",
		},
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		fmt.Printf("❌ 请求数据序列化失败: %v\n", err)
		return false
	}

	resp, err := http.Post(
		"http://localhost:8082/api/v1/ai/generate/scene",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		fmt.Printf("❌ AI场景生成请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return false
	}

	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ AI场景生成状态码错误: %d, 响应: %s\n", resp.StatusCode, string(body))
		return false
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		fmt.Printf("❌ AI场景生成响应解析失败: %v\n", err)
		return false
	}

	if !result["success"].(bool) {
		fmt.Printf("❌ AI场景生成失败: %v\n", result["error"])
		return false
	}

	data := result["data"].(map[string]interface{})
	if content, ok := data["content"].(string); ok && content != "" {
		fmt.Printf("✅ 真实AI场景生成成功，内容长度: %d 字符\n", len(content))
		fmt.Printf("📝 生成内容预览: %.100s...\n", content)
	} else {
		fmt.Println("❌ AI场景生成响应缺少内容")
		return false
	}

	return true
}

// testCharacterGeneration 测试AI角色生成
func testCharacterGeneration() bool {
	requestData := map[string]interface{}{
		"prompt":   "生成一个精灵法师角色",
		"world_id": "550e8400-e29b-41d4-a716-446655440000",
		"context": map[string]interface{}{
			"race":  "elf",
			"class": "mage",
		},
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		fmt.Printf("❌ 请求数据序列化失败: %v\n", err)
		return false
	}

	resp, err := http.Post(
		"http://localhost:8081/api/v1/ai/generate/character",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		fmt.Printf("❌ AI角色生成请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return false
	}

	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ AI角色生成状态码错误: %d, 响应: %s\n", resp.StatusCode, string(body))
		return false
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		fmt.Printf("❌ AI角色生成响应解析失败: %v\n", err)
		return false
	}

	if !result["success"].(bool) {
		fmt.Printf("❌ AI角色生成失败: %v\n", result["error"])
		return false
	}

	data := result["data"].(map[string]interface{})
	if content, ok := data["content"].(string); ok && content != "" {
		fmt.Printf("✅ AI角色生成成功，内容长度: %d 字符\n", len(content))
		fmt.Printf("👤 角色描述预览: %.100s...\n", content)
	} else {
		fmt.Println("❌ AI角色生成响应缺少内容")
		return false
	}

	if structuredData, ok := data["structured_data"].(map[string]interface{}); ok {
		if name, ok := structuredData["name"].(string); ok {
			fmt.Printf("🏷️  角色名称: %s\n", name)
		}
		if charType, ok := structuredData["type"].(string); ok {
			fmt.Printf("🎭 角色类型: %s\n", charType)
		}
	}

	return true
}

// testWorldList 测试世界列表
func testWorldList() bool {
	resp, err := http.Get("http://localhost:8081/api/v1/worlds")
	if err != nil {
		fmt.Printf("❌ 世界列表请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ 世界列表状态码错误: %d\n", resp.StatusCode)
		return false
	}

	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		fmt.Printf("❌ 世界列表响应解析失败: %v\n", err)
		return false
	}

	if !result["success"].(bool) {
		fmt.Printf("❌ 世界列表获取失败: %v\n", result["error"])
		return false
	}

	worlds := result["data"].([]interface{})
	fmt.Printf("✅ 世界列表获取成功，共 %d 个世界\n", len(worlds))

	// 显示第一个世界的信息
	if len(worlds) > 0 {
		world := worlds[0].(map[string]interface{})
		fmt.Printf("🌍 示例世界: %s - %s\n", world["name"], world["description"])
	}

	return true
}
