#!/bin/bash

# 简化的游戏世界管理修复验证测试脚本
# 不依赖 jq，使用基本的 curl 和 grep 进行测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
SERVER_URL="http://localhost:8080"
API_BASE="${SERVER_URL}/api/v1"

# 打印函数
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查服务器是否运行
check_server() {
    print_header "检查服务器状态"
    
    if curl -s "${SERVER_URL}/health" > /dev/null; then
        print_success "服务器运行正常"
        curl -s "${SERVER_URL}/health"
    else
        print_error "服务器未运行，请先启动服务器: go run cmd/simple-server/main.go"
        exit 1
    fi
}

# 测试世界列表API
test_world_list_apis() {
    print_header "测试世界列表API"
    
    # 测试获取所有世界
    print_info "测试 GET /api/v1/worlds"
    response=$(curl -s "${API_BASE}/worlds")
    if echo "$response" | grep -q '"success":true'; then
        print_success "获取世界列表成功"
        echo "响应示例: $(echo "$response" | head -c 200)..."
    else
        print_error "获取世界列表失败"
        echo "响应: $response"
    fi
    
    # 测试获取我的世界
    print_info "测试 GET /api/v1/game/my-worlds"
    response=$(curl -s "${API_BASE}/game/my-worlds?page=1&limit=10")
    if echo "$response" | grep -q '"success":true'; then
        print_success "获取我的世界列表成功"
    else
        print_error "获取我的世界列表失败"
        echo "响应: $response"
    fi
    
    # 测试获取公开世界
    print_info "测试 GET /api/v1/game/public-worlds"
    response=$(curl -s "${API_BASE}/game/public-worlds?page=1&limit=20")
    if echo "$response" | grep -q '"success":true'; then
        print_success "获取公开世界列表成功"
    else
        print_error "获取公开世界列表失败"
        echo "响应: $response"
    fi
}

# 测试世界创建功能
test_world_creation() {
    print_header "测试世界创建功能"
    
    # 创建测试世界
    print_info "创建新的测试世界"
    world_data='{
        "name": "测试世界-'$(date +%s)'",
        "description": "这是一个用于测试的世界",
        "theme": "fantasy",
        "is_public": true,
        "max_players": 8
    }'
    
    response=$(curl -s -X POST "${API_BASE}/game/worlds" \
        -H "Content-Type: application/json" \
        -d "$world_data")
    
    if echo "$response" | grep -q '"success":true'; then
        print_success "世界创建成功"
        
        # 检查是否包含硬编码的"world-new"
        if echo "$response" | grep -q '"id":"world-new"'; then
            print_error "世界ID仍然是硬编码的'world-new'"
        else
            print_success "世界ID不是硬编码的'world-new'"
        fi
        
        # 提取世界ID（简单方法）
        world_id=$(echo "$response" | sed -n 's/.*"id":"\([^"]*\)".*/\1/p')
        if [ -n "$world_id" ] && [ "$world_id" != "world-new" ]; then
            print_success "生成的世界ID: $world_id"
            echo "$world_id" > /tmp/test_world_id
        fi
        
        echo "创建响应: $(echo "$response" | head -c 300)..."
        
    else
        print_error "世界创建失败"
        echo "响应: $response"
    fi
}

# 测试游戏进入逻辑
test_game_entry_logic() {
    print_header "测试游戏进入逻辑"
    
    if [ -f /tmp/test_world_id ]; then
        world_id=$(cat /tmp/test_world_id)
        print_info "使用世界ID进行测试: $world_id"
        
        # 测试获取世界详情
        print_info "测试获取世界详情 GET /api/v1/worlds/$world_id"
        response=$(curl -s "${API_BASE}/worlds/${world_id}")
        if echo "$response" | grep -q '"success":true'; then
            print_success "可以正确获取世界详情"
        else
            print_error "无法获取世界详情"
            echo "响应: $response"
        fi
        
        # 测试获取世界角色列表
        print_info "测试获取世界角色列表 GET /api/v1/game/world/$world_id/characters"
        response=$(curl -s "${API_BASE}/game/world/${world_id}/characters?page=1&limit=10")
        if echo "$response" | grep -q '"success":true'; then
            print_success "可以正确获取世界角色列表"
        else
            print_warning "获取世界角色列表失败（可能是正常的，如果世界刚创建）"
        fi
        
    else
        print_warning "没有找到测试世界ID，跳过游戏进入逻辑测试"
    fi
}

# 测试数据持久性
test_data_persistence() {
    print_header "测试数据持久性"
    
    # 再次获取世界列表，验证新创建的世界是否存在
    print_info "验证新创建的世界是否在世界列表中"
    response=$(curl -s "${API_BASE}/worlds")
    if echo "$response" | grep -q '"success":true'; then
        if [ -f /tmp/test_world_id ]; then
            test_world_id=$(cat /tmp/test_world_id)
            if echo "$response" | grep -q "\"id\":\"$test_world_id\""; then
                print_success "新创建的世界存在于世界列表中"
            else
                print_error "新创建的世界不在世界列表中"
            fi
        fi
    else
        print_error "无法获取世界列表进行验证"
    fi
}

# 测试UUID格式验证
test_uuid_format() {
    print_header "测试UUID格式验证"
    
    if [ -f /tmp/test_world_id ]; then
        world_id=$(cat /tmp/test_world_id)
        print_info "验证世界ID格式: $world_id"
        
        # 简单的UUID格式检查（8-4-4-4-12格式）
        if echo "$world_id" | grep -qE '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'; then
            print_success "世界ID符合UUID格式"
        else
            print_warning "世界ID可能不是标准UUID格式"
        fi
    fi
}

# 清理测试数据
cleanup() {
    print_header "清理测试数据"
    
    if [ -f /tmp/test_world_id ]; then
        rm /tmp/test_world_id
        print_info "清理临时文件"
    fi
}

# 主测试流程
main() {
    print_header "游戏世界管理修复验证测试（简化版）"
    print_info "测试目标："
    print_info "1. 验证模拟数据已替换为真实API调用"
    print_info "2. 验证世界创建不再返回硬编码的'world-new'"
    print_info "3. 验证游戏进入逻辑使用正确的世界ID"
    
    # 执行测试
    check_server
    test_world_list_apis
    test_world_creation
    test_uuid_format
    test_game_entry_logic
    test_data_persistence
    cleanup
    
    print_header "测试完成"
    print_success "所有测试已完成，请查看上述结果"
}

# 运行主函数
main "$@"
