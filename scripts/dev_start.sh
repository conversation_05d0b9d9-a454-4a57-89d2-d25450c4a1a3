#!/bin/bash

# AI文本游戏开发环境启动脚本
# 用于同时启动前端和后端开发服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# PID文件路径
BACKEND_PID_FILE="/tmp/ai-text-game-backend.pid"
FRONTEND_PID_FILE="/tmp/ai-text-game-frontend.pid"

# 清理函数
cleanup() {
    print_info "正在停止开发服务器..."
    
    # 停止后端服务器
    if [ -f "$BACKEND_PID_FILE" ]; then
        BACKEND_PID=$(cat "$BACKEND_PID_FILE")
        if kill -0 "$BACKEND_PID" 2>/dev/null; then
            print_info "停止后端服务器 (PID: $BACKEND_PID)"
            kill "$BACKEND_PID"
        fi
        rm -f "$BACKEND_PID_FILE"
    fi
    
    # 停止前端服务器
    if [ -f "$FRONTEND_PID_FILE" ]; then
        FRONTEND_PID=$(cat "$FRONTEND_PID_FILE")
        if kill -0 "$FRONTEND_PID" 2>/dev/null; then
            print_info "停止前端服务器 (PID: $FRONTEND_PID)"
            kill "$FRONTEND_PID"
        fi
        rm -f "$FRONTEND_PID_FILE"
    fi
    
    print_success "开发服务器已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 检查环境
check_environment() {
    print_info "检查开发环境..."
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        print_error "Go未安装，请先安装Go 1.19+"
        exit 1
    fi
    
    # 检查Node.js环境
    if ! command -v node &> /dev/null; then
        print_error "Node.js未安装，请先安装Node.js 16+"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        print_error "npm未安装，请先安装npm"
        exit 1
    fi
    
    print_success "环境检查通过"
}

# 安装依赖
install_dependencies() {
    print_info "检查并安装依赖..."
    
    # 安装Go依赖
    print_info "检查Go依赖..."
    if [ ! -f "go.sum" ] || [ "go.mod" -nt "go.sum" ]; then
        print_info "安装Go依赖..."
        go mod download
        go mod tidy
    fi
    
    # 安装前端依赖
    print_info "检查前端依赖..."
    cd web/frontend
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        print_info "安装前端依赖..."
        npm install
    fi
    cd ../..
    
    print_success "依赖检查完成"
}

# 启动后端服务器
start_backend() {
    print_info "启动后端服务器..."
    
    # 检查端口是否被占用
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口8080已被占用，尝试停止现有进程..."
        lsof -ti:8080 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # 启动后端服务器
    nohup go run cmd/server/main.go > /tmp/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > "$BACKEND_PID_FILE"
    
    # 等待后端启动
    print_info "等待后端服务器启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8080/health >/dev/null 2>&1; then
            print_success "后端服务器启动成功 (PID: $BACKEND_PID)"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "后端服务器启动超时"
            cat /tmp/backend.log
            exit 1
        fi
        sleep 1
    done
}

# 启动前端服务器
start_frontend() {
    print_info "启动前端开发服务器..."
    
    cd web/frontend
    
    # 检查端口是否被占用
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口3000已被占用，尝试停止现有进程..."
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # 启动前端服务器
    nohup npm run dev > /tmp/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$FRONTEND_PID_FILE"
    
    cd ../..
    
    # 等待前端启动
    print_info "等待前端服务器启动..."
    for i in {1..30}; do
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            print_success "前端服务器启动成功 (PID: $FRONTEND_PID)"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "前端服务器启动超时"
            cat /tmp/frontend.log
            exit 1
        fi
        sleep 1
    done
}

# 显示状态信息
show_status() {
    echo ""
    print_success "🚀 AI文本游戏开发环境已启动！"
    echo ""
    echo "服务地址:"
    echo "  前端开发服务器: http://localhost:3000"
    echo "  后端API服务器:  http://localhost:8080"
    echo "  健康检查:       http://localhost:8080/health"
    echo ""
    echo "日志文件:"
    echo "  后端日志: /tmp/backend.log"
    echo "  前端日志: /tmp/frontend.log"
    echo ""
    echo "按 Ctrl+C 停止所有服务"
    echo ""
}

# 监控服务状态
monitor_services() {
    while true; do
        # 检查后端服务器
        if [ -f "$BACKEND_PID_FILE" ]; then
            BACKEND_PID=$(cat "$BACKEND_PID_FILE")
            if ! kill -0 "$BACKEND_PID" 2>/dev/null; then
                print_error "后端服务器意外停止"
                cat /tmp/backend.log | tail -20
                cleanup
            fi
        fi
        
        # 检查前端服务器
        if [ -f "$FRONTEND_PID_FILE" ]; then
            FRONTEND_PID=$(cat "$FRONTEND_PID_FILE")
            if ! kill -0 "$FRONTEND_PID" 2>/dev/null; then
                print_error "前端服务器意外停止"
                cat /tmp/frontend.log | tail -20
                cleanup
            fi
        fi
        
        sleep 5
    done
}

# 显示帮助信息
show_help() {
    echo "AI文本游戏开发环境启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  --backend-only    只启动后端服务器"
    echo "  --frontend-only   只启动前端服务器"
    echo "  --no-install      跳过依赖安装"
    echo ""
    echo "示例:"
    echo "  $0                    # 启动完整开发环境"
    echo "  $0 --backend-only     # 只启动后端"
    echo "  $0 --frontend-only    # 只启动前端"
}

# 主函数
main() {
    local backend_only=false
    local frontend_only=false
    local skip_install=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --backend-only)
                backend_only=true
                shift
                ;;
            --frontend-only)
                frontend_only=true
                shift
                ;;
            --no-install)
                skip_install=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "启动AI文本游戏开发环境..."
    
    # 检查环境
    check_environment
    
    # 安装依赖
    if [ "$skip_install" = false ]; then
        install_dependencies
    fi
    
    # 启动服务
    if [ "$frontend_only" = false ]; then
        start_backend
    fi
    
    if [ "$backend_only" = false ]; then
        start_frontend
    fi
    
    # 显示状态
    show_status
    
    # 监控服务
    monitor_services
}

# 运行主函数
main "$@"
