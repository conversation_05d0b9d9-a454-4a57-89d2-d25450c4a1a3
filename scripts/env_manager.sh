#!/bin/bash

# 环境变量管理脚本
# 支持设置、查看和管理项目的环境变量

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                🔧 环境变量管理工具                           ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  支持多种环境变量前缀和优先级管理                            ║${NC}"
    echo -e "${CYAN}║  • AI_TEXT_GAME_* (最高优先级)                               ║${NC}"
    echo -e "${CYAN}║  • AITEXTGAME_* (中等优先级)                                 ║${NC}"
    echo -e "${CYAN}║  • 普通变量名 (最低优先级)                                   ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 获取环境变量的实际值（考虑前缀优先级）
get_effective_env_value() {
    local key="$1"
    local prefixes=("AI_TEXT_GAME_" "AITEXTGAME_" "")
    
    for prefix in "${prefixes[@]}"; do
        local full_key="${prefix}${key}"
        local value=$(printenv "$full_key" 2>/dev/null || echo "")
        if [ -n "$value" ]; then
            echo "$value"
            return 0
        fi
    done
    
    # 如果没有找到环境变量，尝试从.env文件读取
    if [ -f ".env" ]; then
        local env_value=$(grep "^${key}=" .env 2>/dev/null | cut -d'=' -f2- || echo "")
        if [ -n "$env_value" ]; then
            echo "$env_value"
            return 0
        fi
    fi
    
    echo ""
    return 1
}

# 显示当前环境变量状态
show_env_status() {
    print_header "📊 当前环境变量状态"
    echo ""
    
    # 定义要检查的关键环境变量
    local key_vars=(
        "AI_TOKEN"
        "AI_BASE_URL"
        "DB_NAME"
        "DB_HOST"
        "DB_PASSWORD"
        "SERVER_PORT"
        "ENVIRONMENT"
        "JWT_SECRET"
        "OAUTH_GOOGLE_CLIENT_ID"
        "OAUTH_GITHUB_CLIENT_ID"
    )
    
    printf "%-25s %-20s %-30s %s\n" "变量名" "来源" "值" "说明"
    printf "%-25s %-20s %-30s %s\n" "-----" "----" "---" "----"
    
    for key in "${key_vars[@]}"; do
        local value=""
        local source=""
        local display_value=""
        
        # 检查各种来源
        if [ -n "$(printenv "AI_TEXT_GAME_${key}" 2>/dev/null)" ]; then
            value=$(printenv "AI_TEXT_GAME_${key}")
            source="AI_TEXT_GAME_*"
        elif [ -n "$(printenv "AITEXTGAME_${key}" 2>/dev/null)" ]; then
            value=$(printenv "AITEXTGAME_${key}")
            source="AITEXTGAME_*"
        elif [ -n "$(printenv "${key}" 2>/dev/null)" ]; then
            value=$(printenv "${key}")
            source="环境变量"
        elif [ -f ".env" ] && grep -q "^${key}=" .env; then
            value=$(grep "^${key}=" .env | cut -d'=' -f2-)
            source=".env文件"
        else
            value=""
            source="未设置"
        fi
        
        # 处理敏感信息显示
        if [[ "$key" == *"TOKEN"* ]] || [[ "$key" == *"SECRET"* ]] || [[ "$key" == *"PASSWORD"* ]]; then
            if [ -n "$value" ]; then
                display_value="***${value: -4}"  # 只显示最后4位
            else
                display_value="未设置"
            fi
        else
            display_value="$value"
        fi
        
        # 添加说明
        local description=""
        case "$key" in
            "AI_TOKEN") description="AI服务访问令牌" ;;
            "AI_BASE_URL") description="AI服务基础URL" ;;
            "DB_NAME") description="数据库名称" ;;
            "DB_HOST") description="数据库主机" ;;
            "DB_PASSWORD") description="数据库密码" ;;
            "SERVER_PORT") description="服务器端口" ;;
            "ENVIRONMENT") description="运行环境" ;;
            "JWT_SECRET") description="JWT签名密钥" ;;
            "OAUTH_GOOGLE_CLIENT_ID") description="Google OAuth客户端ID" ;;
            "OAUTH_GITHUB_CLIENT_ID") description="GitHub OAuth客户端ID" ;;
        esac
        
        printf "%-25s %-20s %-30s %s\n" "$key" "$source" "$display_value" "$description"
    done
    
    echo ""
}

# 设置环境变量
set_env_var() {
    local key="$1"
    local value="$2"
    local prefix="${3:-}"
    
    if [ -z "$key" ] || [ -z "$value" ]; then
        print_error "使用方法: set_env_var <变量名> <值> [前缀]"
        return 1
    fi
    
    local full_key="${prefix}${key}"
    
    print_info "设置环境变量: $full_key=$value"
    export "$full_key"="$value"
    print_success "环境变量已设置"
    
    # 询问是否要持久化到.env文件
    echo -n "是否要将此配置保存到.env文件? (y/N): "
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        if [ -f ".env" ]; then
            # 如果.env文件中已存在该配置，则更新
            if grep -q "^${key}=" .env; then
                sed -i "s|^${key}=.*|${key}=${value}|" .env
                print_success "已更新.env文件中的配置"
            else
                echo "${key}=${value}" >> .env
                print_success "已添加配置到.env文件"
            fi
        else
            echo "${key}=${value}" > .env
            print_success "已创建.env文件并添加配置"
        fi
    fi
}

# 显示使用示例
show_examples() {
    print_header "💡 使用示例"
    echo ""
    
    echo "1. 查看当前环境变量状态:"
    echo "   $0 status"
    echo ""
    
    echo "2. 设置普通环境变量:"
    echo "   $0 set AI_TOKEN your_token_here"
    echo ""
    
    echo "3. 设置带AI_TEXT_GAME_前缀的环境变量:"
    echo "   $0 set AI_TOKEN your_token_here AI_TEXT_GAME_"
    echo ""
    
    echo "4. 设置带AITEXTGAME_前缀的环境变量:"
    echo "   $0 set AI_TOKEN your_token_here AITEXTGAME_"
    echo ""
    
    echo "5. 临时设置环境变量运行程序:"
    echo "   AI_TEXT_GAME_AI_TOKEN=temp_token go run cmd/server/main.go"
    echo ""
    
    echo "6. 在Docker中使用:"
    echo "   docker run -e AI_TEXT_GAME_AI_TOKEN=prod_token my-app"
    echo ""
}

# 显示帮助信息
show_help() {
    show_banner
    
    echo "使用方法: $0 <命令> [参数...]"
    echo ""
    echo "命令:"
    echo "  status                          显示当前环境变量状态"
    echo "  set <变量名> <值> [前缀]        设置环境变量"
    echo "  examples                        显示使用示例"
    echo "  help                           显示帮助信息"
    echo ""
    echo "前缀选项:"
    echo "  AI_TEXT_GAME_                  完整项目前缀（推荐生产环境）"
    echo "  AITEXTGAME_                    简短项目前缀"
    echo "  (空)                           普通环境变量"
    echo ""
    
    show_examples
}

# 主函数
main() {
    case "${1:-help}" in
        "status")
            show_banner
            show_env_status
            ;;
        "set")
            if [ $# -lt 3 ]; then
                print_error "使用方法: $0 set <变量名> <值> [前缀]"
                exit 1
            fi
            show_banner
            set_env_var "$2" "$3" "$4"
            echo ""
            show_env_status
            ;;
        "examples")
            show_examples
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
