#!/bin/bash

# 仅启动前端开发服务器的脚本
# 适用于后端服务器单独启动或使用外部后端的情况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                🎨 前端开发服务器启动                         ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  仅启动前端开发服务器，支持热重载和开发模式                   ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查前端依赖
check_frontend_deps() {
    print_info "检查前端依赖..."
    
    cd web/frontend
    
    if [ ! -d "node_modules" ]; then
        print_warning "未找到 node_modules，正在安装依赖..."
        npm install
    else
        print_success "前端依赖已安装"
    fi
    
    cd ../..
}

# 检查前端配置
check_frontend_config() {
    print_info "检查前端配置..."
    
    if [ ! -f "web/frontend/.env.development" ]; then
        print_warning "未找到前端开发配置文件，正在创建..."
        
        cat > web/frontend/.env.development << 'EOF'
# 前端开发环境配置
NODE_ENV=development
VITE_NODE_ENV=development

# API 配置
VITE_API_BASE_URL=http://localhost:8080
VITE_API_TIMEOUT=10000

# 开发模式配置
VITE_DEV_MODE=true
VITE_ENABLE_DEV_LOGIN=true

# 应用配置
VITE_APP_TITLE=AI文字游戏 - 开发模式
VITE_APP_VERSION=1.0.0-dev

# 调试配置
VITE_ENABLE_REDUX_DEVTOOLS=true
VITE_LOG_LEVEL=debug
EOF
        print_success "前端开发配置文件已创建"
    else
        print_success "前端开发配置文件已存在"
    fi
}

# 启动前端服务器
start_frontend() {
    print_info "启动前端开发服务器..."
    
    cd web/frontend
    
    # 设置环境变量
    export NODE_ENV=development
    export VITE_NODE_ENV=development
    
    print_info "前端服务器配置:"
    echo "  📍 地址: http://localhost:3000"
    echo "  🔧 模式: 开发模式"
    echo "  🔄 热重载: 启用"
    echo "  🚀 开发登录: 启用"
    echo ""
    
    print_success "前端开发服务器启动中..."
    print_info "按 Ctrl+C 停止服务器"
    echo ""
    
    # 启动开发服务器
    npm run dev
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  --check-only   仅检查环境，不启动服务器"
    echo ""
    echo "说明:"
    echo "  此脚本仅启动前端开发服务器，适用于以下场景："
    echo "  • 后端服务器已单独启动"
    echo "  • 使用外部后端服务"
    echo "  • 纯前端开发和调试"
    echo ""
    echo "前端特性:"
    echo "  • 自动检测开发模式"
    echo "  • 支持开发模式快速登录"
    echo "  • 热重载和实时更新"
    echo "  • Redux DevTools 支持"
    echo ""
    echo "访问地址:"
    echo "  前端: http://localhost:3000"
    echo ""
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --check-only)
                CHECK_ONLY=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    show_banner
    
    # 检查环境
    check_frontend_deps
    check_frontend_config
    
    if [ "$CHECK_ONLY" = true ]; then
        print_success "环境检查完成，前端开发环境已就绪"
        echo ""
        echo "启动前端服务器:"
        echo "  $0"
        echo ""
        exit 0
    fi
    
    # 启动前端服务器
    start_frontend
}

# 运行主函数
main "$@"
