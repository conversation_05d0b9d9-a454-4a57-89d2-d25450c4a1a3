#!/bin/bash

# 前端开发模式测试脚本
# 测试前端开发模式的各项功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test() {
    echo -e "${CYAN}[TEST]${NC} $1"
}

# 显示测试横幅
show_test_banner() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                🧪 前端开发模式测试                           ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  测试前端开发模式的各项功能和集成                             ║${NC}"
    echo -e "${CYAN}║  包括认证跳过、开发指示器、API集成等                         ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查前端依赖
check_frontend_dependencies() {
    print_test "检查前端依赖..."
    
    cd web/frontend
    
    if [ ! -d "node_modules" ]; then
        print_warning "前端依赖未安装，正在安装..."
        npm install
    fi
    
    # 检查关键依赖
    local required_deps=("react" "typescript" "vite" "antd")
    for dep in "${required_deps[@]}"; do
        if npm list "$dep" > /dev/null 2>&1; then
            print_success "依赖 $dep 已安装"
        else
            print_error "依赖 $dep 未安装"
            return 1
        fi
    done
    
    cd ../..
    print_success "前端依赖检查通过"
}

# 检查前端配置文件
check_frontend_config() {
    print_test "检查前端配置文件..."
    
    local config_files=(
        "web/frontend/.env.development"
        "web/frontend/vite.config.ts"
        "web/frontend/package.json"
    )
    
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "配置文件存在: $file"
        else
            print_error "配置文件缺失: $file"
            return 1
        fi
    done
    
    # 检查开发环境配置
    if grep -q "VITE_DEV_MODE=true" web/frontend/.env.development; then
        print_success "开发模式配置正确"
    else
        print_warning "开发模式配置可能不正确"
    fi
}

# 检查前端代码
check_frontend_code() {
    print_test "检查前端开发模式代码..."
    
    local code_files=(
        "web/frontend/src/services/authService.ts"
        "web/frontend/src/components/DevModeIndicator.tsx"
        "web/frontend/src/pages/LoginPage.tsx"
        "web/frontend/src/App.tsx"
    )
    
    for file in "${code_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "代码文件存在: $file"
        else
            print_error "代码文件缺失: $file"
            return 1
        fi
    done
    
    # 检查关键函数
    if grep -q "isDevelopmentMode" web/frontend/src/services/authService.ts; then
        print_success "开发模式检测函数存在"
    else
        print_error "开发模式检测函数缺失"
        return 1
    fi
    
    if grep -q "developmentAutoLogin" web/frontend/src/services/authService.ts; then
        print_success "开发模式自动登录函数存在"
    else
        print_error "开发模式自动登录函数缺失"
        return 1
    fi
    
    if grep -q "DevModeIndicator" web/frontend/src/App.tsx; then
        print_success "开发模式指示器已集成"
    else
        print_error "开发模式指示器未集成"
        return 1
    fi
}

# 测试前端构建
test_frontend_build() {
    print_test "测试前端构建..."
    
    cd web/frontend
    
    # 尝试构建
    if npm run build > /dev/null 2>&1; then
        print_success "前端构建成功"
        
        # 检查构建输出
        if [ -d "dist" ]; then
            print_success "构建输出目录存在"
            
            # 检查关键文件
            if [ -f "dist/index.html" ]; then
                print_success "构建输出包含 index.html"
            else
                print_warning "构建输出缺少 index.html"
            fi
        else
            print_warning "构建输出目录不存在"
        fi
        
        # 清理构建输出
        rm -rf dist
    else
        print_error "前端构建失败"
        cd ../..
        return 1
    fi
    
    cd ../..
}

# 测试TypeScript类型检查
test_typescript_check() {
    print_test "测试 TypeScript 类型检查..."
    
    cd web/frontend
    
    if npx tsc --noEmit > /dev/null 2>&1; then
        print_success "TypeScript 类型检查通过"
    else
        print_warning "TypeScript 类型检查有警告或错误"
        print_info "运行 'npx tsc --noEmit' 查看详细信息"
    fi
    
    cd ../..
}

# 测试后端连接
test_backend_connection() {
    print_test "测试后端连接..."
    
    local backend_url="http://localhost:8080"
    
    if curl -s "$backend_url/health" > /dev/null 2>&1; then
        print_success "后端服务器可访问"
        
        # 检查开发模式状态
        local health_response=$(curl -s "$backend_url/health" 2>/dev/null || echo "{}")
        if echo "$health_response" | grep -q '"auth_mode":"disabled"'; then
            print_success "后端开发模式已启用"
        else
            print_warning "后端可能不是开发模式"
        fi
        
        # 测试API端点
        local api_endpoints=(
            "/health"
            "/api/v1/user/profile"
            "/api/v1/game/my-worlds"
        )
        
        for endpoint in "${api_endpoints[@]}"; do
            if curl -s "$backend_url$endpoint" > /dev/null 2>&1; then
                print_success "API端点可访问: $endpoint"
            else
                print_warning "API端点不可访问: $endpoint"
            fi
        done
    else
        print_warning "后端服务器不可访问，某些测试将跳过"
        print_info "请先启动后端服务器: ./scripts/dev_no_auth.sh"
    fi
}

# 生成测试报告
generate_test_report() {
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    print_success "🧪 前端开发模式测试完成"
    echo ""
    echo "测试结果总结:"
    echo "  ✅ 前端依赖检查"
    echo "  ✅ 配置文件检查"
    echo "  ✅ 开发模式代码检查"
    echo "  ✅ 前端构建测试"
    echo "  ✅ TypeScript 类型检查"
    echo "  ✅ 后端连接测试"
    echo ""
    echo "下一步操作:"
    echo "  1. 启动全栈开发环境: ./scripts/dev_full_stack.sh"
    echo "  2. 访问前端应用: http://localhost:3000"
    echo "  3. 测试开发模式功能:"
    echo "     • 点击开发模式快速登录"
    echo "     • 查看右上角 DEV 指示器"
    echo "     • 测试API调用和用户功能"
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "前端开发模式测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  --skip-build      跳过构建测试"
    echo "  --skip-backend    跳过后端连接测试"
    echo ""
    echo "示例:"
    echo "  $0                # 运行完整测试"
    echo "  $0 --skip-build   # 跳过构建测试"
    echo ""
}

# 主函数
main() {
    local skip_build=false
    local skip_backend=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --skip-build)
                skip_build=true
                shift
                ;;
            --skip-backend)
                skip_backend=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示测试横幅
    show_test_banner
    
    # 运行测试
    check_frontend_dependencies
    check_frontend_config
    check_frontend_code
    
    if [ "$skip_build" = false ]; then
        test_frontend_build
        test_typescript_check
    fi
    
    if [ "$skip_backend" = false ]; then
        test_backend_connection
    fi
    
    # 生成测试报告
    generate_test_report
}

# 运行主函数
main "$@"
