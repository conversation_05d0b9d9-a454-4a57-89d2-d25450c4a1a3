#!/bin/bash

# API调试系统集成验证脚本
# 用于验证API调试系统是否已正确集成到开发环境中

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 服务器配置
SERVER_URL="http://localhost:8080"
TIMEOUT=10

# 检查服务器是否运行
check_server() {
    log_info "检查服务器状态..."
    
    if curl -s --max-time $TIMEOUT "$SERVER_URL/health" > /dev/null; then
        log_success "服务器正在运行"
        return 0
    else
        log_error "服务器未运行或无法访问"
        log_info "请先运行: ./scripts/dev_full_stack.sh"
        return 1
    fi
}

# 测试API端点
test_endpoint() {
    local endpoint=$1
    local description=$2
    local expected_content=$3
    
    log_info "测试 $description: $endpoint"
    
    response=$(curl -s --max-time $TIMEOUT "$SERVER_URL$endpoint")
    status_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$SERVER_URL$endpoint")
    
    if [ "$status_code" = "200" ]; then
        if [ -n "$expected_content" ] && echo "$response" | grep -q "$expected_content"; then
            log_success "$description 正常工作"
            return 0
        elif [ -z "$expected_content" ]; then
            log_success "$description 正常工作"
            return 0
        else
            log_warning "$description 返回200但内容不符合预期"
            return 1
        fi
    else
        log_error "$description 失败 (HTTP $status_code)"
        return 1
    fi
}

# 测试JSON API端点
test_json_endpoint() {
    local endpoint=$1
    local description=$2
    local expected_field=$3
    
    log_info "测试 $description: $endpoint"
    
    response=$(curl -s --max-time $TIMEOUT "$SERVER_URL$endpoint")
    status_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$SERVER_URL$endpoint")
    
    if [ "$status_code" = "200" ]; then
        if echo "$response" | jq -e ".$expected_field" > /dev/null 2>&1; then
            log_success "$description 正常工作"
            return 0
        else
            log_warning "$description 返回200但JSON结构不符合预期"
            echo "响应: $response" | head -200
            return 1
        fi
    else
        log_error "$description 失败 (HTTP $status_code)"
        return 1
    fi
}

# 测试调试功能
test_debug_functionality() {
    log_info "测试API调试功能..."
    
    # 发送调试请求
    debug_request='{
        "method": "GET",
        "url": "'$SERVER_URL'/health",
        "save_to_history": true,
        "description": "集成测试请求"
    }'
    
    response=$(curl -s --max-time $TIMEOUT -X POST \
        -H "Content-Type: application/json" \
        -d "$debug_request" \
        "$SERVER_URL/api/v1/debug/request")
    
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        log_success "调试请求发送成功"
        
        # 检查历史记录
        history_response=$(curl -s --max-time $TIMEOUT "$SERVER_URL/api/v1/debug/history")
        if echo "$history_response" | jq -e '.data.records[0]' > /dev/null 2>&1; then
            log_success "调试历史记录正常"
            return 0
        else
            log_error "调试历史记录异常"
            return 1
        fi
    else
        log_error "调试请求发送失败"
        echo "响应: $response"
        return 1
    fi
}

# 主验证函数
main() {
    echo "========================================"
    echo "    API调试系统集成验证"
    echo "========================================"
    echo ""
    
    # 检查服务器状态
    if ! check_server; then
        exit 1
    fi
    
    echo ""
    log_info "开始验证API调试系统端点..."
    echo ""
    
    # 验证计数器
    total_tests=0
    passed_tests=0
    
    # 测试HTML界面端点
    log_info "=== 测试HTML界面 ==="
    
    if test_endpoint "/api/v1/docs" "API文档主页" "AI文本游戏 - API文档"; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    if test_endpoint "/api/v1/docs/swagger" "Swagger UI界面" "Swagger UI"; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    if test_endpoint "/debug" "调试界面" "API调试工具"; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    if test_endpoint "/system" "系统状态页面" "API调试系统"; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    echo ""
    log_info "=== 测试JSON API端点 ==="
    
    if test_json_endpoint "/api/v1/docs/openapi" "OpenAPI规范" "openapi"; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    if test_json_endpoint "/api/v1/docs/stats" "文档统计" "data.total_endpoints"; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    if test_json_endpoint "/api/v1/system/status" "系统状态API" "data.service_name"; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    if test_json_endpoint "/api/v1/debug/history" "调试历史" "data.records"; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    echo ""
    log_info "=== 测试调试功能 ==="
    
    if test_debug_functionality; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    echo ""
    echo "========================================"
    log_info "验证结果: $passed_tests/$total_tests 测试通过"
    
    if [ $passed_tests -eq $total_tests ]; then
        log_success "🎉 所有测试通过！API调试系统已成功集成到开发环境中"
        echo ""
        log_info "📚 可用的API调试系统端点:"
        echo "  - API文档主页: $SERVER_URL/api/v1/docs"
        echo "  - Swagger UI: $SERVER_URL/api/v1/docs/swagger"
        echo "  - 调试界面: $SERVER_URL/debug"
        echo "  - 系统状态: $SERVER_URL/system"
        echo "  - OpenAPI规范: $SERVER_URL/api/v1/docs/openapi"
        echo ""
        log_success "开发者现在可以使用完整的API调试功能了！"
        echo "========================================"
        exit 0
    else
        log_error "❌ 部分测试失败，请检查系统配置"
        echo "========================================"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "API调试系统集成验证脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -u, --url URL       指定服务器URL (默认: http://localhost:8080)"
    echo "  -t, --timeout SEC   设置请求超时时间 (默认: 10秒)"
    echo ""
    echo "示例:"
    echo "  $0                  # 使用默认配置验证"
    echo "  $0 -u http://localhost:8081  # 指定不同的服务器URL"
    echo "  $0 -t 30            # 设置30秒超时"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            SERVER_URL="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
if ! command -v curl &> /dev/null; then
    log_error "curl 未安装，请先安装 curl"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    log_warning "jq 未安装，JSON验证功能可能受限"
fi

# 运行主函数
main
