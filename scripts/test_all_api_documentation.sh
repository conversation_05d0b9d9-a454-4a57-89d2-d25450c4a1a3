#!/bin/bash

# 全面API文档测试脚本
# 用于验证所有API接口的OpenAPI文档注解修复效果

echo "=== 全面API文档测试脚本 ==="
echo "时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SERVER_HOST=${1:-localhost}
SERVER_PORT=${2:-8080}
API_BASE_URL="http://${SERVER_HOST}:${SERVER_PORT}"

echo -e "${BLUE}测试配置:${NC}"
echo "服务器地址: ${SERVER_HOST}"
echo "服务器端口: ${SERVER_PORT}"
echo "API基础URL: ${API_BASE_URL}"
echo ""

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}测试 $TOTAL_TESTS: $test_name${NC}"
    
    # 执行测试命令
    result=$(eval "$test_command" 2>/dev/null)
    exit_code=$?
    
    if [ $exit_code -eq 0 ] && [[ "$result" == *"$expected_result"* ]]; then
        echo -e "${GREEN}✓ 通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 失败${NC}"
        echo "  期望包含: $expected_result"
        echo "  实际结果: $(echo "$result" | head -2)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    echo ""
}

# 检查JSON格式的函数
check_json_format() {
    local json_content="$1"
    if echo "$json_content" | python3 -m json.tool >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 1. 测试OpenAPI规范生成
echo -e "${BLUE}=== 测试1: OpenAPI规范生成 ===${NC}"
openapi_response=$(curl -s --max-time 10 "${API_BASE_URL}/api/v1/docs/openapi" 2>/dev/null)
if [ $? -eq 0 ]; then
    if check_json_format "$openapi_response"; then
        echo -e "${GREEN}✓ OpenAPI规范JSON格式有效${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # 保存到临时文件用于后续测试
        echo "$openapi_response" > /tmp/openapi_spec.json
    else
        echo -e "${RED}✗ OpenAPI规范JSON格式无效${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    echo -e "${RED}✗ 无法获取OpenAPI规范${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi
echo ""

# 2. 测试各模块接口路径
echo -e "${BLUE}=== 测试2: 各模块接口路径检查 ===${NC}"
if [ -f /tmp/openapi_spec.json ]; then
    # 定义要检查的接口路径
    declare -A expected_paths=(
        ["/api/v1/worlds"]="游戏世界管理"
        ["/api/v1/characters"]="角色管理"
        ["/api/v1/auth/providers"]="用户认证"
        ["/api/v1/debug/history"]="API调试"
        ["/api/v1/ai/generate"]="AI生成"
    )
    
    for path in "${!expected_paths[@]}"; do
        path_exists=$(python3 -c "
import json
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        spec = json.load(f)
    paths = spec.get('paths', {})
    if '$path' in paths:
        print('found')
    else:
        print('not_found')
except:
    print('error')
" 2>/dev/null)
        
        if [ "$path_exists" = "found" ]; then
            echo -e "${GREEN}✓ ${expected_paths[$path]}接口路径存在: $path${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ ${expected_paths[$path]}接口路径不存在: $path${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
    done
else
    echo -e "${YELLOW}⚠ 跳过测试，OpenAPI规范不可用${NC}"
fi
echo ""

# 3. 测试中文标签和描述
echo -e "${BLUE}=== 测试3: 中文标签和描述检查 ===${NC}"
if [ -f /tmp/openapi_spec.json ]; then
    # 检查是否包含中文标签
    chinese_tags=$(python3 -c "
import json
import re
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        spec = json.load(f)
    
    # 检查tags中的中文
    tags = spec.get('tags', [])
    chinese_tag_count = 0
    for tag in tags:
        if re.search(r'[\u4e00-\u9fff]', tag.get('name', '')):
            chinese_tag_count += 1
    
    print(chinese_tag_count)
except:
    print(0)
" 2>/dev/null)
    
    if [ "$chinese_tags" -gt 0 ]; then
        echo -e "${GREEN}✓ 发现 $chinese_tags 个中文标签${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 未发现中文标签${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 检查是否包含中文描述
    chinese_desc=$(python3 -c "
import json
import re
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        content = f.read()
    # 检查是否包含中文字符
    if re.search(r'[\u4e00-\u9fff]', content):
        print('found')
    else:
        print('not_found')
except:
    print('error')
" 2>/dev/null)
    
    if [ "$chinese_desc" = "found" ]; then
        echo -e "${GREEN}✓ 文档包含中文描述${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 文档缺少中文描述${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    echo -e "${YELLOW}⚠ 跳过测试，OpenAPI规范不可用${NC}"
fi
echo ""

# 4. 测试参数示例值
echo -e "${BLUE}=== 测试4: 参数示例值检查 ===${NC}"
if [ -f /tmp/openapi_spec.json ]; then
    # 检查是否包含示例值
    examples_count=$(python3 -c "
import json
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        spec = json.load(f)
    
    def count_examples(obj):
        count = 0
        if isinstance(obj, dict):
            if 'example' in obj:
                count += 1
            for value in obj.values():
                count += count_examples(value)
        elif isinstance(obj, list):
            for item in obj:
                count += count_examples(item)
        return count
    
    print(count_examples(spec))
except:
    print(0)
" 2>/dev/null)
    
    if [ "$examples_count" -gt 0 ]; then
        echo -e "${GREEN}✓ 发现 $examples_count 个示例值${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 未发现示例值${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    echo -e "${YELLOW}⚠ 跳过测试，OpenAPI规范不可用${NC}"
fi
echo ""

# 5. 测试枚举值定义
echo -e "${BLUE}=== 测试5: 枚举值定义检查 ===${NC}"
if [ -f /tmp/openapi_spec.json ]; then
    # 检查是否包含枚举值
    enums_count=$(python3 -c "
import json
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        content = f.read()
    
    # 简单计算Enums出现的次数
    import re
    enums_matches = re.findall(r'Enums\([^)]+\)', content)
    print(len(enums_matches))
except:
    print(0)
" 2>/dev/null)
    
    if [ "$enums_count" -gt 0 ]; then
        echo -e "${GREEN}✓ 发现 $enums_count 个枚举值定义${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 未发现枚举值定义${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    echo -e "${YELLOW}⚠ 跳过测试，OpenAPI规范不可用${NC}"
fi
echo ""

# 6. 测试关键接口的参数完整性
echo -e "${BLUE}=== 测试6: 关键接口参数完整性 ===${NC}"
if [ -f /tmp/openapi_spec.json ]; then
    # 检查AI生成接口的参数
    ai_params=$(python3 -c "
import json
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        spec = json.load(f)
    
    # 检查AI生成接口
    ai_path = spec.get('paths', {}).get('/api/v1/ai/generate', {})
    post_method = ai_path.get('post', {})
    
    param_count = 0
    if 'parameters' in post_method:
        param_count += len(post_method['parameters'])
    if 'requestBody' in post_method:
        param_count += 1
    
    print(param_count)
except:
    print(0)
" 2>/dev/null)
    
    if [ "$ai_params" -gt 0 ]; then
        echo -e "${GREEN}✓ AI生成接口包含 $ai_params 个参数定义${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ AI生成接口缺少参数定义${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 检查世界创建接口的参数
    world_params=$(python3 -c "
import json
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        spec = json.load(f)
    
    # 检查世界创建接口
    world_path = spec.get('paths', {}).get('/api/v1/worlds', {})
    post_method = world_path.get('post', {})
    
    param_count = 0
    if 'parameters' in post_method:
        param_count += len(post_method['parameters'])
    if 'requestBody' in post_method:
        param_count += 1
    
    print(param_count)
except:
    print(0)
" 2>/dev/null)
    
    if [ "$world_params" -gt 0 ]; then
        echo -e "${GREEN}✓ 世界创建接口包含 $world_params 个参数定义${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 世界创建接口缺少参数定义${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    echo -e "${YELLOW}⚠ 跳过测试，OpenAPI规范不可用${NC}"
fi
echo ""

# 清理临时文件
rm -f /tmp/openapi_spec.json

# 测试结果汇总
echo -e "${BLUE}=== 测试结果汇总 ===${NC}"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"

success_rate=0
if [ $TOTAL_TESTS -gt 0 ]; then
    success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
fi
echo "成功率: ${success_rate}%"
echo ""

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！API文档系统全面修复完成。${NC}"
    echo ""
    echo -e "${BLUE}修复成果:${NC}"
    echo "✅ 所有接口路径统一为 /api/v1/* 格式"
    echo "✅ 完整的中文标签和描述"
    echo "✅ 丰富的参数示例值和枚举定义"
    echo "✅ 详细的请求体参数展开"
    echo "✅ 统一的错误响应定义"
    echo ""
    echo -e "${BLUE}建议下一步操作:${NC}"
    echo "1. 在浏览器中访问 ${API_BASE_URL}/api/v1/docs/swagger 查看完整文档"
    echo "2. 测试各个模块的API接口调用"
    echo "3. 验证参数验证和错误处理"
    exit 0
elif [ $FAILED_TESTS -le 3 ]; then
    echo -e "${YELLOW}⚠ 大部分测试通过，但有少量问题需要关注。${NC}"
    echo ""
    echo -e "${BLUE}建议检查:${NC}"
    echo "1. 服务器是否正常运行"
    echo "2. 部分接口的注解是否完整"
    echo "3. OpenAPI规范生成是否正确"
    exit 1
else
    echo -e "${RED}❌ 多个测试失败，API文档系统仍存在问题。${NC}"
    echo ""
    echo -e "${BLUE}建议排查:${NC}"
    echo "1. 检查服务器日志"
    echo "2. 验证所有接口的OpenAPI注解"
    echo "3. 确认路由配置正确"
    echo "4. 重新启动服务器"
    exit 2
fi
