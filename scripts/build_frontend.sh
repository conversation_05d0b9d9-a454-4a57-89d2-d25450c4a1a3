#!/bin/bash

# AI文本游戏前端构建脚本
# 用于构建前端应用并将产物复制到正确位置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js环境
check_node_env() {
    print_info "检查Node.js环境..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js未安装，请先安装Node.js 16+"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm未安装，请先安装npm"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        print_error "Node.js版本过低，需要16+，当前版本: $(node --version)"
        exit 1
    fi
    
    print_success "Node.js环境检查通过: $(node --version)"
}

# 安装依赖
install_dependencies() {
    print_info "安装前端依赖..."
    
    cd web/frontend
    
    if [ ! -f "package.json" ]; then
        print_error "package.json文件不存在"
        exit 1
    fi
    
    # 检查是否需要安装依赖
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        print_info "正在安装npm依赖..."
        npm install
        print_success "依赖安装完成"
    else
        print_info "依赖已是最新，跳过安装"
    fi
    
    cd ../..
}

# 构建前端
build_frontend() {
    print_info "构建前端应用..."
    
    cd web/frontend
    
    # 运行构建命令
    print_info "正在运行构建命令..."
    npm run build
    
    if [ $? -eq 0 ]; then
        print_success "前端构建完成"
    else
        print_error "前端构建失败"
        exit 1
    fi
    
    cd ../..
}

# 检查构建产物
check_build_output() {
    print_info "检查构建产物..."
    
    BUILD_DIR="web/static/dist"
    
    if [ ! -d "$BUILD_DIR" ]; then
        print_error "构建产物目录不存在: $BUILD_DIR"
        exit 1
    fi
    
    if [ ! -f "$BUILD_DIR/index.html" ]; then
        print_error "index.html文件不存在"
        exit 1
    fi
    
    # 计算构建产物大小
    BUILD_SIZE=$(du -sh "$BUILD_DIR" | cut -f1)
    print_success "构建产物检查通过，大小: $BUILD_SIZE"
}

# 清理旧的构建产物
clean_build() {
    print_info "清理旧的构建产物..."
    
    BUILD_DIR="web/static/dist"
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        print_success "清理完成"
    else
        print_info "没有需要清理的文件"
    fi
}

# 显示帮助信息
show_help() {
    echo "AI文本游戏前端构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -c, --clean    清理构建产物"
    echo "  -d, --dev      开发模式构建"
    echo "  -p, --prod     生产模式构建（默认）"
    echo "  --no-install   跳过依赖安装"
    echo ""
    echo "示例:"
    echo "  $0                # 生产模式构建"
    echo "  $0 --dev          # 开发模式构建"
    echo "  $0 --clean        # 清理构建产物"
    echo "  $0 --no-install   # 跳过依赖安装"
}

# 主函数
main() {
    local clean_only=false
    local dev_mode=false
    local skip_install=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                clean_only=true
                shift
                ;;
            -d|--dev)
                dev_mode=true
                shift
                ;;
            -p|--prod)
                dev_mode=false
                shift
                ;;
            --no-install)
                skip_install=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "开始构建AI文本游戏前端..."
    
    # 如果只是清理，执行清理后退出
    if [ "$clean_only" = true ]; then
        clean_build
        print_success "清理完成！"
        exit 0
    fi
    
    # 检查环境
    check_node_env
    
    # 安装依赖（除非跳过）
    if [ "$skip_install" = false ]; then
        install_dependencies
    fi
    
    # 构建前端
    build_frontend
    
    # 检查构建产物
    check_build_output
    
    print_success "前端构建完成！"
    print_info "构建产物位置: web/static/dist/"
    print_info "现在可以启动Go服务器来测试完整应用"
}

# 运行主函数
main "$@"
