#!/bin/bash

# 全栈开发环境启动脚本 - 详细日志版本
# 同时启动前端和后端开发服务器，显示完整的日志输出

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_dev() {
    echo -e "${PURPLE}[DEV]${NC} $1"
}

print_frontend() {
    echo -e "${CYAN}[FRONTEND]${NC} $1"
}

print_backend() {
    echo -e "${PURPLE}[BACKEND]${NC} $1"
}

# 显示全栈开发模式横幅
show_fullstack_banner() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                🚀 全栈开发环境启动 (详细日志版)              ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  🎨 前端: React + TypeScript + Vite (详细日志)              ║${NC}"
    echo -e "${CYAN}║  ⚡ 后端: Go + Gin + 认证跳过 (调试模式)                     ║${NC}"
    echo -e "${CYAN}║  🔥 热重载: 前后端代码变更自动刷新                           ║${NC}"
    echo -e "${CYAN}║  🔓 认证: 完全跳过，自动登录开发用户                         ║${NC}"
    echo -e "${CYAN}║  🐛 调试: 完整的开发工具和详细日志                           ║${NC}"
    echo -e "${CYAN}║  📝 日志: 前后端所有日志实时显示                             ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  ⚠️  仅用于开发测试，请勿在生产环境使用！                    ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查端口占用
check_ports() {
    print_info "检查端口占用情况..."
    
    local ports_to_check=(3000 8080)
    local occupied_ports=()
    
    for port in "${ports_to_check[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        print_warning "以下端口被占用: ${occupied_ports[*]}"
        echo ""
        echo "占用情况:"
        for port in "${occupied_ports[@]}"; do
            echo "  端口 $port:"
            lsof -Pi :$port -sTCP:LISTEN | head -2
        done
        echo ""
        read -p "是否停止占用进程并继续？(y/N): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            for port in "${occupied_ports[@]}"; do
                print_info "停止端口 $port 的进程..."
                lsof -ti:$port | xargs kill -9 2>/dev/null || true
            done
            sleep 2
            print_success "端口清理完成"
        else
            print_error "用户取消操作"
            exit 1
        fi
    else
        print_success "端口检查通过"
    fi
}

# 创建日志目录
setup_logging() {
    print_info "设置日志目录..."
    mkdir -p logs
    
    # 清理旧日志
    rm -f logs/backend.log logs/frontend.log
    
    print_success "日志目录设置完成"
}

# 启动后端服务器
start_backend() {
    print_backend "启动后端开发服务器..."
    
    # 设置后端开发环境变量
    export ENVIRONMENT=development
    export SKIP_AUTH=true
    export DEV_ENABLE_DEBUG_LOGS=true
    
    print_backend "环境变量设置:"
    print_backend "  ENVIRONMENT=development"
    print_backend "  SKIP_AUTH=true"
    print_backend "  DEV_ENABLE_DEBUG_LOGS=true"
    
    # 启动后端服务器并记录日志
    print_backend "使用简化版服务器（完全跳过认证）..."
    print_backend "后端日志将同时显示在控制台和保存到 logs/backend.log"
    
    go run cmd/simple-server/main.go 2>&1 | tee logs/backend.log | while IFS= read -r line; do
        echo -e "${PURPLE}[BACKEND]${NC} $line"
    done &
    BACKEND_PID=$!
    
    # 等待后端启动
    print_backend "等待后端服务器启动..."
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:8080/health > /dev/null 2>&1; then
            print_success "后端服务器启动成功 (PID: $BACKEND_PID)"
            
            # 检查开发模式状态
            local health_response=$(curl -s http://localhost:8080/health 2>/dev/null || echo "{}")
            if echo "$health_response" | grep -q '"auth_mode":"disabled"'; then
                print_success "后端开发模式已启用，认证已跳过"
            fi
            return 0
        fi
        
        print_info "等待后端启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    print_error "后端服务器启动失败"
    return 1
}

# 启动前端服务器
start_frontend() {
    print_frontend "启动前端开发服务器..."
    
    cd web/frontend
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        print_frontend "安装前端依赖..."
        npm install
    fi
    
    # 设置前端开发环境变量
    export NODE_ENV=development
    export VITE_DEV_MODE=true
    export VITE_SHOW_DEV_INDICATOR=true
    export VITE_DEBUG_LOGS=true
    export VITE_VERBOSE_LOGS=true
    
    print_frontend "环境变量设置:"
    print_frontend "  NODE_ENV=development"
    print_frontend "  VITE_DEV_MODE=true"
    print_frontend "  VITE_DEBUG_LOGS=true"
    
    # 启动前端服务器并记录日志
    print_frontend "启动 Vite 开发服务器（详细模式）..."
    print_frontend "前端日志将同时显示在控制台和保存到 logs/frontend.log"
    
    npm run dev:debug 2>&1 | tee ../../logs/frontend.log | while IFS= read -r line; do
        echo -e "${CYAN}[FRONTEND]${NC} $line"
    done &
    FRONTEND_PID=$!
    
    cd ../..
    
    # 等待前端启动
    print_frontend "等待前端服务器启动..."
    local max_attempts=15
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            print_success "前端服务器启动成功 (PID: $FRONTEND_PID)"
            return 0
        fi
        
        print_info "等待前端启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    print_warning "前端服务器可能仍在启动中..."
    return 0
}

# 显示开发信息
show_dev_info() {
    echo ""
    print_success "🚀 全栈开发环境已启动（详细日志模式）"
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    echo "🌐 访问地址:"
    echo "   前端应用:      http://localhost:3000"
    echo "   后端API:       http://localhost:8080"
    echo "   健康检查:      http://localhost:8080/health"
    echo ""
    echo "🔧 开发特性:"
    echo "   ✅ 前端热重载:   代码变更自动刷新"
    echo "   ✅ 后端热重载:   使用 air 或手动重启"
    echo "   ✅ 认证跳过:     前后端完全跳过认证"
    echo "   ✅ 开发用户:     自动登录 <EMAIL>"
    echo "   ✅ 开发工具:     浏览器 F12 + 开发指示器"
    echo "   ✅ 详细日志:     前后端完整调试信息"
    echo "   ✅ 日志文件:     logs/backend.log, logs/frontend.log"
    echo ""
    echo "📝 日志管理:"
    echo "   实时查看后端日志:   tail -f logs/backend.log"
    echo "   实时查看前端日志:   tail -f logs/frontend.log"
    echo "   查看所有日志:       tail -f logs/*.log"
    echo ""
    echo "🎮 快速开始:"
    echo "   1. 打开浏览器访问 http://localhost:3000"
    echo "   2. 点击 '🚀 开发模式快速登录' 按钮"
    echo "   3. 或点击右上角 DEV 按钮查看开发状态"
    echo "   4. 开始开发和测试功能"
    echo ""
    echo "🛠️  API测试:"
    echo "   curl http://localhost:8080/health"
    echo "   curl http://localhost:8080/api/v1/user/profile"
    echo "   curl http://localhost:8080/api/v1/game/my-worlds"
    echo ""
    echo "📝 进程信息:"
    echo "   后端进程 PID:   $BACKEND_PID"
    echo "   前端进程 PID:   $FRONTEND_PID"
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    print_warning "⚠️  按 Ctrl+C 停止所有服务"
    print_warning "⚠️  此模式仅用于开发测试，请勿在生产环境使用！"
    echo ""
}

# 清理函数
cleanup() {
    echo ""
    print_info "正在停止开发服务器..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        print_backend "停止后端服务器 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        print_frontend "停止前端服务器 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # 清理可能残留的进程
    pkill -f "go run cmd/simple-server/main.go" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    
    print_success "开发环境已停止"
    print_info "日志文件保存在 logs/ 目录中"
    exit 0
}

# 主函数
main() {
    # 设置信号处理
    trap cleanup SIGINT SIGTERM
    
    # 显示全栈开发模式横幅
    show_fullstack_banner
    
    # 设置日志
    setup_logging
    
    # 检查端口占用
    check_ports
    
    # 启动后端服务器
    start_backend
    if [ $? -ne 0 ]; then
        print_error "后端启动失败"
        exit 1
    fi
    
    # 启动前端服务器
    start_frontend
    
    # 显示开发信息
    show_dev_info
    
    # 等待用户中断
    while true; do
        sleep 1
    done
}

# 运行主函数
main "$@"
