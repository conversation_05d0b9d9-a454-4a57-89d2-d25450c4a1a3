#!/bin/bash

# 停止开发服务器脚本
# 停止所有运行中的开发服务器进程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示停止横幅
show_stop_banner() {
    echo ""
    echo -e "${RED}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${RED}║                🛑 停止开发服务器                             ║${NC}"
    echo -e "${RED}║                                                              ║${NC}"
    echo -e "${RED}║  停止所有运行中的前端和后端开发服务器                         ║${NC}"
    echo -e "${RED}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 停止后端服务器
stop_backend() {
    print_info "停止后端服务器..."
    
    # 通过 PID 文件停止
    if [ -f ".backend.pid" ]; then
        BACKEND_PID=$(cat .backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            print_success "后端服务器已停止 (PID: $BACKEND_PID)"
        else
            print_warning "后端服务器进程不存在 (PID: $BACKEND_PID)"
        fi
        rm -f .backend.pid
    else
        print_info "未找到后端服务器 PID 文件"
    fi
    
    # 通过端口停止（备用方法）
    local backend_pids=$(lsof -ti:8080 2>/dev/null || true)
    if [ -n "$backend_pids" ]; then
        print_info "发现运行在端口 8080 的进程，正在停止..."
        echo $backend_pids | xargs kill -9 2>/dev/null || true
        print_success "端口 8080 上的进程已停止"
    fi
}

# 停止前端服务器
stop_frontend() {
    print_info "停止前端服务器..."
    
    # 通过 PID 文件停止
    if [ -f ".frontend.pid" ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            print_success "前端服务器已停止 (PID: $FRONTEND_PID)"
        else
            print_warning "前端服务器进程不存在 (PID: $FRONTEND_PID)"
        fi
        rm -f .frontend.pid
    else
        print_info "未找到前端服务器 PID 文件"
    fi
    
    # 通过端口停止（备用方法）
    local frontend_pids=$(lsof -ti:3000 2>/dev/null || true)
    if [ -n "$frontend_pids" ]; then
        print_info "发现运行在端口 3000 的进程，正在停止..."
        echo $frontend_pids | xargs kill -9 2>/dev/null || true
        print_success "端口 3000 上的进程已停止"
    fi
}

# 停止所有相关进程
stop_all_processes() {
    print_info "查找并停止所有相关进程..."
    
    # 停止 Go 开发服务器
    local go_pids=$(pgrep -f "go run.*main.go" 2>/dev/null || true)
    if [ -n "$go_pids" ]; then
        print_info "停止 Go 开发服务器进程..."
        echo $go_pids | xargs kill -9 2>/dev/null || true
        print_success "Go 开发服务器进程已停止"
    fi
    
    # 停止 npm/node 开发服务器
    local npm_pids=$(pgrep -f "npm run dev" 2>/dev/null || true)
    if [ -n "$npm_pids" ]; then
        print_info "停止 npm 开发服务器进程..."
        echo $npm_pids | xargs kill -9 2>/dev/null || true
        print_success "npm 开发服务器进程已停止"
    fi
    
    local vite_pids=$(pgrep -f "vite" 2>/dev/null || true)
    if [ -n "$vite_pids" ]; then
        print_info "停止 Vite 开发服务器进程..."
        echo $vite_pids | xargs kill -9 2>/dev/null || true
        print_success "Vite 开发服务器进程已停止"
    fi
}

# 清理临时文件
cleanup_temp_files() {
    print_info "清理临时文件..."
    
    # 清理 PID 文件
    rm -f .backend.pid .frontend.pid
    
    # 清理日志文件（可选）
    if [ "$1" = "--clean-logs" ]; then
        print_info "清理日志文件..."
        rm -f logs/backend.log logs/frontend.log
        print_success "日志文件已清理"
    fi
}

# 检查服务器状态
check_server_status() {
    print_info "检查服务器状态..."
    
    # 检查后端
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        print_warning "后端服务器仍在运行 (http://localhost:8080)"
    else
        print_success "后端服务器已停止"
    fi
    
    # 检查前端
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        print_warning "前端服务器仍在运行 (http://localhost:3000)"
    else
        print_success "前端服务器已停止"
    fi
}

# 显示完成信息
show_completion_info() {
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    print_success "🛑 开发服务器停止完成！"
    echo ""
    echo "所有开发服务器已停止:"
    echo "  ✅ 后端服务器 (端口 8080)"
    echo "  ✅ 前端服务器 (端口 3000)"
    echo "  ✅ 相关进程已清理"
    echo "  ✅ 临时文件已清理"
    echo ""
    echo "如需重新启动开发环境:"
    echo "  🚀 启动全栈环境: ./scripts/dev_full_stack.sh"
    echo "  🔧 仅启动后端:   ./scripts/dev_no_auth.sh"
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "停止开发服务器脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  --backend-only    仅停止后端服务器"
    echo "  --frontend-only   仅停止前端服务器"
    echo "  --clean-logs      同时清理日志文件"
    echo "  --force           强制停止所有相关进程"
    echo ""
    echo "示例:"
    echo "  $0                # 停止所有开发服务器"
    echo "  $0 --backend-only # 仅停止后端"
    echo "  $0 --clean-logs   # 停止服务器并清理日志"
    echo "  $0 --force        # 强制停止所有相关进程"
    echo ""
}

# 主函数
main() {
    local backend_only=false
    local frontend_only=false
    local clean_logs=false
    local force=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --backend-only)
                backend_only=true
                shift
                ;;
            --frontend-only)
                frontend_only=true
                shift
                ;;
            --clean-logs)
                clean_logs=true
                shift
                ;;
            --force)
                force=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示停止横幅
    show_stop_banner
    
    # 执行停止操作
    if [ "$frontend_only" = false ]; then
        stop_backend
    fi
    
    if [ "$backend_only" = false ]; then
        stop_frontend
    fi
    
    # 强制停止所有相关进程
    if [ "$force" = true ]; then
        stop_all_processes
    fi
    
    # 清理临时文件
    if [ "$clean_logs" = true ]; then
        cleanup_temp_files --clean-logs
    else
        cleanup_temp_files
    fi
    
    # 检查服务器状态
    check_server_status
    
    # 显示完成信息
    show_completion_info
}

# 运行主函数
main "$@"
