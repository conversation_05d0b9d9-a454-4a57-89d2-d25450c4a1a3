#!/bin/bash

# =============================================================================
# 测试结构验证脚本
# =============================================================================
# 功能说明：
# - 验证重新组织后的测试文件结构
# - 检查测试文件的编译状态
# - 生成测试状态报告
# - 提供修复建议
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[步骤]${NC} $1"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                🔍 测试结构验证工具                           ║${NC}"
    echo -e "${BLUE}║                                                              ║${NC}"
    echo -e "${BLUE}║  📊 统计测试文件分布                                         ║${NC}"
    echo -e "${BLUE}║  🔧 检查编译状态                                             ║${NC}"
    echo -e "${BLUE}║  📋 生成状态报告                                             ║${NC}"
    echo -e "${BLUE}║  💡 提供修复建议                                             ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 统计测试文件
count_test_files() {
    print_step "统计测试文件分布..."
    
    local unit_count=$(find tests/unit -name "*.go" -type f 2>/dev/null | wc -l)
    local integration_count=$(find tests/integration -name "*.go" -type f 2>/dev/null | wc -l)
    local e2e_count=$(find tests/e2e -name "*.go" -type f 2>/dev/null | wc -l)
    local total_count=$((unit_count + integration_count + e2e_count))
    
    echo ""
    echo "📊 测试文件统计:"
    echo "  单元测试:     $unit_count 个文件"
    echo "  集成测试:     $integration_count 个文件"
    echo "  端到端测试:   $e2e_count 个文件"
    echo "  总计:         $total_count 个文件"
    echo ""
    
    # 按模块统计
    echo "📁 按模块分布:"
    for module in ai apidoc debug game handlers migration models services validation pkg misc; do
        local module_count=$(find tests -path "*/tests/*/$module/*.go" -type f 2>/dev/null | wc -l)
        if [ $module_count -gt 0 ]; then
            printf "  %-12s %d 个文件\n" "$module:" "$module_count"
        fi
    done
    echo ""
}

# 检查Go模块和依赖
check_go_environment() {
    print_step "检查Go环境..."
    
    if ! command -v go &> /dev/null; then
        print_error "Go未安装"
        return 1
    fi
    
    local go_version=$(go version | cut -d' ' -f3)
    print_info "Go版本: $go_version"
    
    if [ ! -f "go.mod" ]; then
        print_error "go.mod文件不存在"
        return 1
    fi
    
    print_info "检查Go模块依赖..."
    go mod tidy
    print_success "Go环境检查通过"
}

# 检查测试文件编译状态
check_compilation() {
    print_step "检查测试文件编译状态..."
    
    local compilation_report="tests/compilation_report.txt"
    echo "# 测试编译状态报告" > "$compilation_report"
    echo "# 生成时间: $(date)" >> "$compilation_report"
    echo "" >> "$compilation_report"
    
    local total_files=0
    local success_files=0
    local error_files=0
    
    # 检查每个测试文件
    for test_file in $(find tests -name "*.go" -type f | sort); do
        ((total_files++))
        
        # 尝试编译单个测试文件
        local dir=$(dirname "$test_file")
        local file=$(basename "$test_file")
        
        if (cd "$dir" && go test -c "$file" -o /tmp/test_compile_check 2>/dev/null); then
            ((success_files++))
            echo "✅ $test_file" >> "$compilation_report"
            rm -f /tmp/test_compile_check
        else
            ((error_files++))
            echo "❌ $test_file" >> "$compilation_report"
            
            # 获取编译错误信息
            echo "   错误信息:" >> "$compilation_report"
            (cd "$dir" && go test -c "$file" -o /tmp/test_compile_check 2>&1 | head -5 | sed 's/^/   /' >> "$compilation_report")
            echo "" >> "$compilation_report"
        fi
    done
    
    echo "" >> "$compilation_report"
    echo "统计信息:" >> "$compilation_report"
    echo "  总文件数: $total_files" >> "$compilation_report"
    echo "  编译成功: $success_files" >> "$compilation_report"
    echo "  编译失败: $error_files" >> "$compilation_report"
    echo "  成功率: $(( success_files * 100 / total_files ))%" >> "$compilation_report"
    
    echo ""
    echo "📊 编译状态统计:"
    echo "  总文件数:     $total_files"
    echo "  编译成功:     $success_files"
    echo "  编译失败:     $error_files"
    echo "  成功率:       $(( success_files * 100 / total_files ))%"
    echo ""
    
    print_info "详细编译报告已保存到: $compilation_report"
    
    if [ $error_files -gt 0 ]; then
        print_warning "发现 $error_files 个文件编译失败"
        return 1
    else
        print_success "所有测试文件编译通过"
        return 0
    fi
}

# 尝试运行测试
try_run_tests() {
    print_step "尝试运行测试..."
    
    local test_report="tests/test_run_report.txt"
    echo "# 测试运行状态报告" > "$test_report"
    echo "# 生成时间: $(date)" >> "$test_report"
    echo "" >> "$test_report"
    
    # 尝试运行单元测试
    echo "🧪 尝试运行单元测试..." | tee -a "$test_report"
    if go test ./tests/unit/... -v > /tmp/unit_test_output 2>&1; then
        echo "✅ 单元测试运行成功" | tee -a "$test_report"
        local unit_success=true
    else
        echo "❌ 单元测试运行失败" | tee -a "$test_report"
        echo "错误信息:" >> "$test_report"
        head -20 /tmp/unit_test_output | sed 's/^/  /' >> "$test_report"
        local unit_success=false
    fi
    echo "" >> "$test_report"
    
    # 尝试运行集成测试
    echo "🔗 尝试运行集成测试..." | tee -a "$test_report"
    if go test ./tests/integration/... -v > /tmp/integration_test_output 2>&1; then
        echo "✅ 集成测试运行成功" | tee -a "$test_report"
        local integration_success=true
    else
        echo "❌ 集成测试运行失败" | tee -a "$test_report"
        echo "错误信息:" >> "$test_report"
        head -20 /tmp/integration_test_output | sed 's/^/  /' >> "$test_report"
        local integration_success=false
    fi
    echo "" >> "$test_report"
    
    print_info "测试运行报告已保存到: $test_report"
    
    if [ "$unit_success" = true ] && [ "$integration_success" = true ]; then
        print_success "所有测试运行成功"
        return 0
    else
        print_warning "部分测试运行失败，请查看报告了解详情"
        return 1
    fi
}

# 生成修复建议
generate_fix_suggestions() {
    print_step "生成修复建议..."
    
    local suggestions_file="tests/fix_suggestions.md"
    cat > "$suggestions_file" << 'EOF'
# 测试文件修复建议

## 常见问题和解决方案

### 1. Package声明问题
**问题**: 测试文件的package声明与目录结构不匹配
**解决方案**:
```go
// 原来的声明
package ai

// 修改为
package ai_test

// 并添加import
import "ai-text-game-iam-npc/internal/ai"
```

### 2. Import路径问题
**问题**: 相对路径引用失效
**解决方案**:
```go
import (
    "testing"
    "ai-text-game-iam-npc/internal/ai"
    "ai-text-game-iam-npc/internal/models"
    // 其他必要的import
)
```

### 3. 函数调用问题
**问题**: 移动后无法直接调用原包的函数
**解决方案**:
```go
// 原来的调用
schema := NewSchemaBuilder()

// 修改为
schema := ai.NewSchemaBuilder()
```

### 4. 测试辅助函数问题
**问题**: 测试辅助函数无法访问
**解决方案**:
- 将测试辅助函数移动到 `tests/helpers/` 目录
- 或者在原包中导出这些函数

## 自动修复脚本

可以使用以下命令进行批量修复：

```bash
# 修复package声明
find tests -name "*.go" -exec sed -i 's/^package \([a-z]*\)$/package \1_test/' {} \;

# 添加import语句（需要手动调整）
# 这个比较复杂，建议手动处理
```

## 手动修复步骤

1. 检查每个测试文件的package声明
2. 添加必要的import语句
3. 更新函数调用，添加包前缀
4. 验证编译和运行结果

## 验证修复结果

```bash
# 检查编译
go test -c ./tests/unit/...
go test -c ./tests/integration/...

# 运行测试
go test ./tests/unit/...
go test ./tests/integration/...
```
EOF

    print_success "修复建议已保存到: $suggestions_file"
}

# 主函数
main() {
    show_banner
    
    if [ ! -d "tests" ]; then
        print_error "tests目录不存在，请先运行测试重组脚本"
        exit 1
    fi
    
    count_test_files
    check_go_environment
    
    local compilation_ok=false
    if check_compilation; then
        compilation_ok=true
    fi
    
    if [ "$compilation_ok" = true ]; then
        try_run_tests
    else
        print_warning "由于编译失败，跳过测试运行"
    fi
    
    generate_fix_suggestions
    
    echo ""
    print_success "🎉 测试结构验证完成！"
    echo ""
    print_info "📋 生成的报告文件:"
    print_info "  tests/compilation_report.txt  - 编译状态报告"
    print_info "  tests/test_run_report.txt     - 测试运行报告"
    print_info "  tests/fix_suggestions.md      - 修复建议文档"
    echo ""
    
    if [ "$compilation_ok" = false ]; then
        print_warning "⚠️  发现编译问题，请参考修复建议进行处理"
        print_info "💡 建议先修复编译问题，然后重新运行此脚本验证"
    fi
}

# 运行主函数
main "$@"
