#!/bin/bash

# AI文本游戏测试运行脚本
# 用于运行单元测试和集成测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Go环境
check_go_env() {
    print_info "检查Go环境..."
    if ! command -v go &> /dev/null; then
        print_error "Go未安装或不在PATH中"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    print_info "Go版本: $GO_VERSION"
}

# 检查数据库连接
check_database() {
    print_info "检查数据库连接..."
    
    # 设置测试数据库环境变量（如果未设置）
    export TEST_DB_HOST=${TEST_DB_HOST:-"localhost"}
    export TEST_DB_PORT=${TEST_DB_PORT:-"5432"}
    export TEST_DB_USER=${TEST_DB_USER:-"postgres"}
    export TEST_DB_PASSWORD=${TEST_DB_PASSWORD:-""}
    export TEST_DB_NAME=${TEST_DB_NAME:-"ai_text_game_test"}
    
    # 尝试连接数据库
    if command -v psql &> /dev/null; then
        if PGPASSWORD=$TEST_DB_PASSWORD psql -h $TEST_DB_HOST -p $TEST_DB_PORT -U $TEST_DB_USER -d postgres -c "SELECT 1;" &> /dev/null; then
            print_success "数据库连接正常"
        else
            print_warning "无法连接到数据库，将跳过数据库相关测试"
            export SKIP_DB_TESTS=true
        fi
    else
        print_warning "psql未安装，无法验证数据库连接"
        export SKIP_DB_TESTS=true
    fi
}

# 安装依赖
install_deps() {
    print_info "安装Go依赖..."
    go mod download
    go mod tidy
    print_success "依赖安装完成"
}

# 运行代码格式检查
run_fmt_check() {
    print_info "运行代码格式检查..."
    
    # 检查是否有未格式化的文件
    UNFORMATTED=$(gofmt -l .)
    if [ -n "$UNFORMATTED" ]; then
        print_error "以下文件需要格式化:"
        echo "$UNFORMATTED"
        print_info "运行 'go fmt ./...' 来格式化代码"
        return 1
    fi
    
    print_success "代码格式检查通过"
}

# 运行代码静态分析
run_vet() {
    print_info "运行代码静态分析..."
    if go vet ./...; then
        print_success "静态分析通过"
    else
        print_error "静态分析发现问题"
        return 1
    fi
}

# 运行单元测试
run_unit_tests() {
    print_info "运行单元测试..."
    
    # 设置测试标志
    TEST_FLAGS="-v -race -coverprofile=coverage.out"
    
    # 如果跳过数据库测试，添加相应的标签
    if [ "$SKIP_DB_TESTS" = "true" ]; then
        TEST_FLAGS="$TEST_FLAGS -short"
    fi
    
    # 运行测试
    if go test $TEST_FLAGS ./...; then
        print_success "单元测试通过"
        
        # 生成覆盖率报告
        if [ -f coverage.out ]; then
            COVERAGE=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
            print_info "测试覆盖率: $COVERAGE"
            
            # 生成HTML覆盖率报告
            go tool cover -html=coverage.out -o coverage.html
            print_info "HTML覆盖率报告已生成: coverage.html"
        fi
    else
        print_error "单元测试失败"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    if [ "$SKIP_DB_TESTS" = "true" ]; then
        print_warning "跳过集成测试（数据库不可用）"
        return 0
    fi
    
    print_info "运行集成测试..."
    
    # 运行集成测试（标记为integration的测试）
    if go test -v -tags=integration ./...; then
        print_success "集成测试通过"
    else
        print_error "集成测试失败"
        return 1
    fi
}

# 运行基准测试
run_benchmarks() {
    print_info "运行基准测试..."
    
    if go test -bench=. -benchmem ./...; then
        print_success "基准测试完成"
    else
        print_warning "基准测试出现问题"
    fi
}

# 清理测试文件
cleanup() {
    print_info "清理测试文件..."
    
    # 删除覆盖率文件
    rm -f coverage.out coverage.html
    
    # 删除测试生成的临时文件
    find . -name "*.test" -delete
    find . -name "test_*.db" -delete
    
    print_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "AI文本游戏测试运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -u, --unit          只运行单元测试"
    echo "  -i, --integration   只运行集成测试"
    echo "  -b, --benchmark     只运行基准测试"
    echo "  -f, --fmt           只运行代码格式检查"
    echo "  -v, --vet           只运行静态分析"
    echo "  -c, --coverage      生成覆盖率报告"
    echo "  --skip-db           跳过数据库相关测试"
    echo "  --cleanup           清理测试文件"
    echo ""
    echo "环境变量:"
    echo "  TEST_DB_HOST        测试数据库主机 (默认: localhost)"
    echo "  TEST_DB_PORT        测试数据库端口 (默认: 5432)"
    echo "  TEST_DB_USER        测试数据库用户 (默认: postgres)"
    echo "  TEST_DB_PASSWORD    测试数据库密码 (默认: 空)"
    echo "  TEST_DB_NAME        测试数据库名称 (默认: ai_text_game_test)"
    echo "  SKIP_DB_TESTS       跳过数据库测试 (默认: false)"
}

# 主函数
main() {
    local run_unit=false
    local run_integration=false
    local run_benchmark=false
    local run_fmt=false
    local run_vet=false
    local run_all=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--unit)
                run_unit=true
                run_all=false
                shift
                ;;
            -i|--integration)
                run_integration=true
                run_all=false
                shift
                ;;
            -b|--benchmark)
                run_benchmark=true
                run_all=false
                shift
                ;;
            -f|--fmt)
                run_fmt=true
                run_all=false
                shift
                ;;
            -v|--vet)
                run_vet=true
                run_all=false
                shift
                ;;
            --skip-db)
                export SKIP_DB_TESTS=true
                shift
                ;;
            --cleanup)
                cleanup
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "开始运行AI文本游戏测试套件..."
    
    # 检查环境
    check_go_env
    check_database
    install_deps
    
    # 根据参数运行相应的测试
    if [ "$run_all" = true ]; then
        run_fmt_check
        run_vet
        run_unit_tests
        run_integration_tests
        run_benchmarks
    else
        if [ "$run_fmt" = true ]; then
            run_fmt_check
        fi
        
        if [ "$run_vet" = true ]; then
            run_vet
        fi
        
        if [ "$run_unit" = true ]; then
            run_unit_tests
        fi
        
        if [ "$run_integration" = true ]; then
            run_integration_tests
        fi
        
        if [ "$run_benchmark" = true ]; then
            run_benchmarks
        fi
    fi
    
    print_success "测试套件运行完成！"
}

# 运行主函数
main "$@"
