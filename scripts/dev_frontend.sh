#!/bin/bash

# 前端开发环境启动脚本
# 配合后端开发模式，提供完整的开发体验

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_dev() {
    echo -e "${PURPLE}[DEV]${NC} $1"
}

# 显示前端开发模式横幅
show_frontend_banner() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                  🎨 前端开发模式启动                         ║${NC}"
    echo -e "${PURPLE}║                                                              ║${NC}"
    echo -e "${PURPLE}║  🚀 热重载已启用                                             ║${NC}"
    echo -e "${PURPLE}║  🔧 开发工具已启用                                           ║${NC}"
    echo -e "${PURPLE}║  🔓 认证自动跳过                                             ║${NC}"
    echo -e "${PURPLE}║  📱 响应式调试                                               ║${NC}"
    echo -e "${PURPLE}║  🐛 开发指示器                                               ║${NC}"
    echo -e "${PURPLE}║                                                              ║${NC}"
    echo -e "${PURPLE}║  ⚠️  请确保后端开发服务器已启动！                            ║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查Node.js和npm
check_prerequisites() {
    print_info "检查前端开发环境..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js 16+"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 16 ]; then
        print_error "Node.js 版本过低，需要 16+，当前版本: $(node --version)"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装"
        exit 1
    fi
    
    print_success "Node.js $(node --version) 和 npm $(npm --version) 已就绪"
}

# 检查后端服务器状态
check_backend_status() {
    print_info "检查后端服务器状态..."
    
    local backend_url="http://localhost:8080"
    local max_attempts=3
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$backend_url/health" > /dev/null 2>&1; then
            print_success "后端服务器运行正常"
            
            # 检查是否为开发模式
            local health_response=$(curl -s "$backend_url/health" 2>/dev/null || echo "{}")
            if echo "$health_response" | grep -q '"auth_mode":"disabled"'; then
                print_success "后端开发模式已启用，认证已跳过"
            else
                print_warning "后端可能不是开发模式，前端自动登录可能无效"
            fi
            return 0
        fi
        
        print_warning "尝试 $attempt/$max_attempts: 后端服务器未响应"
        if [ $attempt -lt $max_attempts ]; then
            print_info "等待 3 秒后重试..."
            sleep 3
        fi
        ((attempt++))
    done
    
    print_error "无法连接到后端服务器 ($backend_url)"
    print_info "请先启动后端开发服务器："
    print_info "  ./scripts/dev_no_auth.sh"
    echo ""
    read -p "是否继续启动前端？(y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    print_info "检查前端依赖..."
    
    cd web/frontend
    
    if [ ! -d "node_modules" ] || [ ! -f "package-lock.json" ]; then
        print_info "安装前端依赖..."
        npm install
        print_success "依赖安装完成"
    else
        print_info "检查依赖更新..."
        if npm outdated --depth=0 > /dev/null 2>&1; then
            print_warning "发现依赖更新，建议运行: npm update"
        else
            print_success "依赖已是最新"
        fi
    fi
    
    cd ../..
}

# 设置开发环境变量
setup_dev_environment() {
    print_dev "设置前端开发环境变量..."
    
    cd web/frontend
    
    # 如果存在开发环境配置文件，确保它被使用
    if [ -f ".env.development" ]; then
        print_dev "使用开发环境配置: .env.development"
    else
        print_warning "未找到 .env.development 文件"
    fi
    
    # 设置额外的环境变量
    export NODE_ENV=development
    export VITE_DEV_MODE=true
    export VITE_SHOW_DEV_INDICATOR=true
    
    cd ../..
    print_success "前端开发环境变量设置完成"
}

# 启动前端开发服务器
start_frontend_dev_server() {
    print_dev "启动前端开发服务器..."
    
    cd web/frontend
    
    # 检查端口是否被占用
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口3000已被占用，尝试停止现有进程..."
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    print_dev "启动 Vite 开发服务器..."
    print_info "前端将在 http://localhost:3000 启动"
    print_info "后端API代理到 http://localhost:8080"
    echo ""
    
    # 启动开发服务器
    npm run dev
}

# 显示开发信息
show_dev_info() {
    echo ""
    print_success "🎨 前端开发环境已启动"
    echo ""
    echo "访问地址:"
    echo "  🌐 前端应用:      http://localhost:3000"
    echo "  🔧 后端API:      http://localhost:8080"
    echo "  ❤️  健康检查:     http://localhost:8080/health"
    echo ""
    echo "开发特性:"
    echo "  🔥 热重载:       已启用"
    echo "  🔧 开发工具:     已启用 (F12)"
    echo "  🔓 认证跳过:     自动检测后端模式"
    echo "  🐛 开发指示器:   右上角DEV按钮"
    echo "  📱 响应式调试:   已启用"
    echo ""
    echo "快捷操作:"
    echo "  • 点击右上角 DEV 按钮查看开发状态"
    echo "  • 登录页面有开发模式快速登录按钮"
    echo "  • 按 Ctrl+C 停止开发服务器"
    echo ""
    print_warning "⚠️  此模式仅用于开发测试，请勿在生产环境使用！"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "前端开发环境启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  --no-backend      跳过后端状态检查"
    echo "  --force-install   强制重新安装依赖"
    echo ""
    echo "示例:"
    echo "  $0                # 正常启动前端开发环境"
    echo "  $0 --no-backend   # 跳过后端检查直接启动"
    echo ""
    echo "注意:"
    echo "  • 请确保后端开发服务器已启动"
    echo "  • 建议使用 ./scripts/dev_no_auth.sh 启动后端"
    echo "  • 前端会自动代理API请求到后端"
}

# 主函数
main() {
    local skip_backend_check=false
    local force_install=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --no-backend)
                skip_backend_check=true
                shift
                ;;
            --force-install)
                force_install=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示前端开发模式横幅
    show_frontend_banner
    
    # 检查前端开发环境
    check_prerequisites
    
    # 检查后端服务器状态（除非跳过）
    if [ "$skip_backend_check" = false ]; then
        check_backend_status
    fi
    
    # 强制重新安装依赖
    if [ "$force_install" = true ]; then
        print_info "强制重新安装依赖..."
        cd web/frontend
        rm -rf node_modules package-lock.json
        cd ../..
    fi
    
    # 安装依赖
    install_dependencies
    
    # 设置开发环境
    setup_dev_environment
    
    # 显示开发信息
    show_dev_info
    
    # 启动前端开发服务器
    start_frontend_dev_server
}

# 运行主函数
main "$@"
