#!/bin/bash

# 测试环境变量覆盖机制的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test() {
    echo -e "${CYAN}[TEST]${NC} $1"
}

# 创建测试程序
create_test_program() {
    print_info "创建测试程序..."
    
    cat > test_env_override.go << 'EOF'
package main

import (
	"fmt"
	"os"

	"ai-text-game-iam-npc/internal/config"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 输出关键配置项
	fmt.Printf("AI_TOKEN=%s\n", cfg.AI.Token)
	fmt.Printf("AI_BASE_URL=%s\n", cfg.AI.BaseURL)
	fmt.Printf("DB_NAME=%s\n", cfg.Database.DBName)
	fmt.Printf("SERVER_PORT=%s\n", cfg.Server.Port)
}
EOF
    
    print_success "测试程序创建完成"
}

# 测试1: 仅使用.env文件
test_env_file_only() {
    print_test "测试1: 仅使用.env文件中的配置"
    
    # 确保没有环境变量干扰
    unset AI_TOKEN AI_BASE_URL DB_NAME SERVER_PORT
    
    echo "当前.env文件中的AI_TOKEN:"
    grep "^AI_TOKEN=" .env || echo "未找到AI_TOKEN配置"
    
    echo ""
    echo "程序读取的配置:"
    go run test_env_override.go
    echo ""
}

# 测试2: 环境变量覆盖.env文件
test_env_override() {
    print_test "测试2: 使用环境变量覆盖.env文件"
    
    # 设置环境变量
    export AI_TOKEN="env_override_token_12345"
    export AI_BASE_URL="https://env-override.example.com"
    export SERVER_PORT="9999"
    
    echo "设置的环境变量:"
    echo "  AI_TOKEN=$AI_TOKEN"
    echo "  AI_BASE_URL=$AI_BASE_URL"
    echo "  SERVER_PORT=$SERVER_PORT"
    
    echo ""
    echo "程序读取的配置:"
    go run test_env_override.go
    echo ""
    
    # 清理环境变量
    unset AI_TOKEN AI_BASE_URL SERVER_PORT
}

# 测试3: 带前缀的环境变量（需要修改配置加载逻辑）
test_prefixed_env() {
    print_test "测试3: 带前缀的环境变量（演示概念）"
    
    # 设置带前缀的环境变量
    export AI_TEXT_GAME_AI_TOKEN="prefixed_token_67890"
    export AI_TEXT_GAME_AI_BASE_URL="https://prefixed.example.com"
    export AI_TEXT_GAME_SERVER_PORT="7777"
    
    echo "设置的带前缀环境变量:"
    echo "  AI_TEXT_GAME_AI_TOKEN=$AI_TEXT_GAME_AI_TOKEN"
    echo "  AI_TEXT_GAME_AI_BASE_URL=$AI_TEXT_GAME_AI_BASE_URL"
    echo "  AI_TEXT_GAME_SERVER_PORT=$AI_TEXT_GAME_SERVER_PORT"
    
    echo ""
    echo "当前程序读取的配置（不支持前缀）:"
    go run test_env_override.go
    echo ""
    
    print_info "注意: 当前程序不支持带前缀的环境变量，需要修改配置加载逻辑"
    
    # 清理环境变量
    unset AI_TEXT_GAME_AI_TOKEN AI_TEXT_GAME_AI_BASE_URL AI_TEXT_GAME_SERVER_PORT
}

# 演示如何在shell中设置环境变量
demo_shell_usage() {
    print_test "演示: 如何在不同场景下使用环境变量"
    
    echo ""
    echo "1. 临时设置环境变量运行程序:"
    echo "   AI_TOKEN=my_custom_token go run cmd/server/main.go"
    echo ""
    
    echo "2. 在当前shell中设置环境变量:"
    echo "   export AI_TOKEN=my_custom_token"
    echo "   go run cmd/server/main.go"
    echo ""
    
    echo "3. 使用.env文件设置多个环境变量:"
    echo "   # 在.env文件中设置"
    echo "   AI_TOKEN=my_custom_token"
    echo "   AI_BASE_URL=https://my-api.com"
    echo ""
    
    echo "4. 系统级环境变量（在~/.bashrc或~/.zshrc中）:"
    echo "   export AI_TOKEN=global_token"
    echo ""
    
    echo "5. Docker容器中设置环境变量:"
    echo "   docker run -e AI_TOKEN=container_token my-app"
    echo ""
}

# 清理函数
cleanup() {
    print_info "清理测试文件..."
    rm -f test_env_override.go
    print_success "清理完成"
}

# 主函数
main() {
    echo "🧪 环境变量覆盖机制测试"
    echo "=========================="
    echo ""
    
    create_test_program
    
    test_env_file_only
    test_env_override
    test_prefixed_env
    demo_shell_usage
    
    cleanup
    
    print_success "🎉 测试完成！"
}

# 运行测试
main "$@"
