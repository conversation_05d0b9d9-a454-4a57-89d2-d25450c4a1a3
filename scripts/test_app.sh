#!/bin/bash

# AI文本游戏应用测试脚本
# 用于测试前后端集成和基本功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试后端健康检查
test_backend_health() {
    print_info "测试后端健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:8080/health >/dev/null 2>&1; then
            print_success "后端健康检查通过"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "后端健康检查失败，服务器可能未启动"
            return 1
        fi
        
        print_info "等待后端启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
}

# 测试静态文件服务
test_static_files() {
    print_info "测试静态文件服务..."
    
    # 测试前端主页
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/ | grep -q "200"; then
        print_success "前端主页访问正常"
    else
        print_error "前端主页访问失败"
        return 1
    fi
    
    # 测试静态资源
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/assets/ | grep -q "404\|200"; then
        print_success "静态资源路径配置正常"
    else
        print_warning "静态资源路径可能有问题"
    fi
}

# 测试API端点
test_api_endpoints() {
    print_info "测试API端点..."
    
    # 测试健康检查API
    local health_response=$(curl -s http://localhost:8080/health)
    if echo "$health_response" | grep -q "ok"; then
        print_success "健康检查API正常"
    else
        print_error "健康检查API异常"
        return 1
    fi
    
    # 测试不存在的API端点
    local not_found_response=$(curl -s -w "%{http_code}" http://localhost:8080/api/v1/nonexistent)
    if echo "$not_found_response" | grep -q "404"; then
        print_success "API 404处理正常"
    else
        print_warning "API 404处理可能有问题"
    fi
}

# 测试前端路由
test_frontend_routing() {
    print_info "测试前端路由..."
    
    # 测试登录页面路由
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/login | grep -q "200"; then
        print_success "前端路由 /login 正常"
    else
        print_error "前端路由 /login 失败"
        return 1
    fi
    
    # 测试游戏大厅路由
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/lobby | grep -q "200"; then
        print_success "前端路由 /lobby 正常"
    else
        print_error "前端路由 /lobby 失败"
        return 1
    fi
    
    # 测试不存在的前端路由（应该返回index.html）
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/nonexistent-page | grep -q "200"; then
        print_success "前端SPA路由处理正常"
    else
        print_error "前端SPA路由处理失败"
        return 1
    fi
}

# 检查构建产物
check_build_artifacts() {
    print_info "检查构建产物..."
    
    if [ ! -d "web/static/dist" ]; then
        print_error "构建产物目录不存在"
        return 1
    fi
    
    if [ ! -f "web/static/dist/index.html" ]; then
        print_error "index.html文件不存在"
        return 1
    fi
    
    if [ ! -d "web/static/dist/assets" ]; then
        print_error "assets目录不存在"
        return 1
    fi
    
    local file_count=$(find web/static/dist/assets -name "*.js" | wc -l)
    if [ "$file_count" -lt 3 ]; then
        print_warning "JavaScript文件数量可能不足"
    else
        print_success "构建产物检查通过"
    fi
}

# 显示测试结果摘要
show_test_summary() {
    echo ""
    print_success "🎉 AI文本游戏应用测试完成！"
    echo ""
    echo "测试结果摘要:"
    echo "✅ 后端健康检查"
    echo "✅ 静态文件服务"
    echo "✅ API端点测试"
    echo "✅ 前端路由测试"
    echo "✅ 构建产物检查"
    echo ""
    echo "应用访问地址:"
    echo "  🌐 前端应用: http://localhost:8080"
    echo "  🔧 API文档: http://localhost:8080/api/v1"
    echo "  ❤️  健康检查: http://localhost:8080/health"
    echo ""
    print_info "应用已准备就绪，可以开始使用！"
}

# 显示帮助信息
show_help() {
    echo "AI文本游戏应用测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --quick        快速测试（跳过详细检查）"
    echo "  --no-build     跳过构建产物检查"
    echo ""
    echo "示例:"
    echo "  $0             # 完整测试"
    echo "  $0 --quick     # 快速测试"
    echo "  $0 --no-build  # 跳过构建检查"
}

# 主函数
main() {
    local quick_test=false
    local skip_build_check=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --quick)
                quick_test=true
                shift
                ;;
            --no-build)
                skip_build_check=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "开始测试AI文本游戏应用..."
    
    # 检查构建产物
    if [ "$skip_build_check" = false ]; then
        check_build_artifacts || exit 1
    fi
    
    # 测试后端健康检查
    test_backend_health || exit 1
    
    # 测试静态文件服务
    test_static_files || exit 1
    
    # 测试API端点
    test_api_endpoints || exit 1
    
    # 测试前端路由
    if [ "$quick_test" = false ]; then
        test_frontend_routing || exit 1
    fi
    
    # 显示测试结果
    show_test_summary
}

# 运行主函数
main "$@"
