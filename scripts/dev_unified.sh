#!/bin/bash

# =============================================================================
# AI文本游戏统一开发环境启动脚本
# =============================================================================
# 作者: AI助手
# 版本: 1.0.0
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
# 
# 功能说明：
# - 支持全栈、前端、后端独立启动模式
# - 集成调试日志开关功能，支持详细日志输出控制
# - 自动检测和处理端口占用问题
# - 支持多种数据库模式（SQLite/PostgreSQL）
# - 提供完整的开发环境配置和状态监控
# - 支持认证跳过模式，便于开发调试
# - 统一的错误处理和进程管理
# =============================================================================

set -e

# =============================================================================
# 配置变量
# =============================================================================

# 调试日志控制变量
DEBUG_ENABLED=${DEBUG_ENABLED:-false}
VERBOSE_LOGS=${VERBOSE_LOGS:-false}

# 服务端口配置
FRONTEND_PORT=${FRONTEND_PORT:-3000}
BACKEND_PORT=${BACKEND_PORT:-8080}

# 数据库配置
DATABASE_MODE=${DATABASE_MODE:-sqlite}  # sqlite 或 postgres
SKIP_AUTH=${SKIP_AUTH:-true}

# PID文件路径
BACKEND_PID_FILE="/tmp/ai-text-game-backend.pid"
FRONTEND_PID_FILE="/tmp/ai-text-game-frontend.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# 日志输出函数
# =============================================================================

# 调试日志输出函数
print_debug() {
    if [ "$DEBUG_ENABLED" = true ]; then
        echo -e "${PURPLE}[调试]${NC} $(date '+%H:%M:%S') $1"
    fi
}

# 详细日志输出函数
print_verbose() {
    if [ "$VERBOSE_LOGS" = true ]; then
        echo -e "${CYAN}[详细]${NC} $(date '+%H:%M:%S') $1"
    fi
}

# 基础日志输出函数
print_info() {
    echo -e "${BLUE}[信息]${NC} $(date '+%H:%M:%S') $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $(date '+%H:%M:%S') $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $(date '+%H:%M:%S') $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $(date '+%H:%M:%S') $1"
}

print_frontend() {
    echo -e "${CYAN}[前端]${NC} $(date '+%H:%M:%S') $1"
}

print_backend() {
    echo -e "${PURPLE}[后端]${NC} $(date '+%H:%M:%S') $1"
}

# =============================================================================
# 横幅显示函数
# =============================================================================

# 显示统一开发环境横幅
show_banner() {
    local mode=$1
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    case $mode in
        "fullstack")
            echo -e "${CYAN}║                🚀 AI文本游戏全栈开发环境                     ║${NC}"
            ;;
        "frontend")
            echo -e "${CYAN}║                🎨 AI文本游戏前端开发环境                     ║${NC}"
            ;;
        "backend")
            echo -e "${CYAN}║                ⚡ AI文本游戏后端开发环境                     ║${NC}"
            ;;
    esac
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  🎨 前端: React + TypeScript + Vite + Ant Design            ║${NC}"
    echo -e "${CYAN}║  ⚡ 后端: Go + Gin + GORM + ${DATABASE_MODE^^}                ║${NC}"
    echo -e "${CYAN}║  🔥 热重载: 代码变更自动刷新                                 ║${NC}"
    echo -e "${CYAN}║  🔓 认证: $([ "$SKIP_AUTH" = true ] && echo "跳过模式已启用" || echo "正常认证模式")                                   ║${NC}"
    echo -e "${CYAN}║  🐛 调试: $([ "$DEBUG_ENABLED" = true ] && echo "调试日志已启用" || echo "标准日志模式")                                 ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  ⚠️  仅用于开发测试，请勿在生产环境使用！                    ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    # 显示当前配置
    print_info "当前配置："
    print_info "  - 前端端口: $FRONTEND_PORT"
    print_info "  - 后端端口: $BACKEND_PORT"
    print_info "  - 数据库模式: $DATABASE_MODE"
    print_info "  - 认证跳过: $SKIP_AUTH"
    print_info "  - 调试日志: $DEBUG_ENABLED"
    print_info "  - 详细日志: $VERBOSE_LOGS"
    echo ""
}

# =============================================================================
# 环境检查函数
# =============================================================================

# 检查开发环境依赖
check_prerequisites() {
    print_info "检查开发环境依赖..."
    
    # 检查 Go 环境
    if ! command -v go &> /dev/null; then
        print_error "Go 未安装，请先安装 Go 1.19+"
        exit 1
    fi
    local go_version=$(go version | cut -d' ' -f3)
    print_debug "Go 版本: $go_version"
    
    # 检查 Node.js 环境
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js 16+"
        exit 1
    fi
    local node_version=$(node --version)
    print_debug "Node.js 版本: $node_version"
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    local npm_version=$(npm --version)
    print_debug "npm 版本: $npm_version"
    
    print_success "开发环境依赖检查通过"
}

# 检查端口占用
check_ports() {
    print_info "检查端口占用情况..."
    
    local ports_to_check=($FRONTEND_PORT $BACKEND_PORT)
    local occupied_ports=()
    
    for port in "${ports_to_check[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            occupied_ports+=($port)
            print_debug "端口 $port 被占用"
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        print_warning "以下端口被占用: ${occupied_ports[*]}"
        echo ""
        echo "占用情况:"
        for port in "${occupied_ports[@]}"; do
            echo "  端口 $port:"
            lsof -Pi :$port -sTCP:LISTEN | head -2
        done
        echo ""
        read -p "是否停止占用进程并继续？(y/N): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            for port in "${occupied_ports[@]}"; do
                print_info "停止端口 $port 的进程..."
                lsof -ti:$port | xargs kill -9 2>/dev/null || true
            done
            sleep 2
            print_success "端口清理完成"
        else
            print_error "用户取消操作"
            exit 1
        fi
    else
        print_success "端口检查通过"
    fi
}

# 安装依赖
install_dependencies() {
    print_info "检查并安装依赖..."
    
    # 安装Go依赖
    print_verbose "检查Go依赖..."
    if [ ! -f "go.sum" ] || [ "go.mod" -nt "go.sum" ]; then
        print_info "安装Go依赖..."
        go mod download
        go mod tidy
        print_success "Go依赖安装完成"
    else
        print_debug "Go依赖已是最新"
    fi
    
    # 安装前端依赖
    print_verbose "检查前端依赖..."
    cd web/frontend
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        print_info "安装前端依赖..."
        npm install
        print_success "前端依赖安装完成"
    else
        print_debug "前端依赖已是最新"
    fi
    cd ../..
    
    print_success "依赖检查完成"
}

# =============================================================================
# 服务启动函数
# =============================================================================

# 启动后端服务器
start_backend() {
    print_backend "启动后端开发服务器..."
    
    # 设置后端环境变量
    export ENVIRONMENT=development
    export SKIP_AUTH=$SKIP_AUTH
    export DEV_ENABLE_DEBUG_LOGS=$DEBUG_ENABLED
    export DATABASE_MODE=$DATABASE_MODE
    
    print_debug "后端环境变量已设置"
    print_verbose "ENVIRONMENT=development"
    print_verbose "SKIP_AUTH=$SKIP_AUTH"
    print_verbose "DEV_ENABLE_DEBUG_LOGS=$DEBUG_ENABLED"
    print_verbose "DATABASE_MODE=$DATABASE_MODE"
    
    # 选择合适的服务器启动命令
    local server_cmd
    if [ "$SKIP_AUTH" = true ]; then
        server_cmd="cmd/simple-server/main.go"
        print_backend "使用简化版服务器（认证跳过模式）"
    else
        server_cmd="cmd/server/main.go"
        print_backend "使用完整版服务器（正常认证模式）"
    fi
    
    # 启动后端服务器
    print_backend "启动后端服务器，端口: $BACKEND_PORT"
    if [ "$DEBUG_ENABLED" = true ]; then
        # 调试模式下显示实时日志
        go run $server_cmd 2>&1 | while IFS= read -r line; do
            echo -e "${PURPLE}[后端]${NC} $line"
        done &
    else
        # 标准模式下后台运行
        nohup go run $server_cmd > /tmp/backend.log 2>&1 &
    fi
    
    BACKEND_PID=$!
    echo $BACKEND_PID > "$BACKEND_PID_FILE"
    
    # 等待后端启动
    print_backend "等待后端服务器启动..."
    local max_attempts=15
    local attempt=1
    
    sleep 3  # 给后端一些启动时间
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:$BACKEND_PORT/health > /dev/null 2>&1; then
            print_success "后端服务器启动成功 (PID: $BACKEND_PID)"
            
            # 检查开发模式状态
            local health_response=$(curl -s http://localhost:$BACKEND_PORT/health 2>/dev/null || echo "{}")
            if echo "$health_response" | grep -q '"auth_mode":"disabled"'; then
                print_success "后端开发模式已启用，认证已跳过"
            fi
            
            print_debug "后端健康检查响应: $health_response"
            return 0
        fi
        
        print_verbose "等待后端启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    print_error "后端服务器启动失败"
    if [ -f "/tmp/backend.log" ]; then
        print_error "后端日志："
        tail -20 /tmp/backend.log
    fi
    return 1
}

# 启动前端服务器
start_frontend() {
    print_frontend "启动前端开发服务器..."

    cd web/frontend

    # 设置前端环境变量
    export NODE_ENV=development
    export VITE_DEV_MODE=true
    export VITE_SHOW_DEV_INDICATOR=true
    export VITE_API_BASE_URL="http://localhost:$BACKEND_PORT"

    print_debug "前端环境变量已设置"
    print_verbose "NODE_ENV=development"
    print_verbose "VITE_DEV_MODE=true"
    print_verbose "VITE_API_BASE_URL=http://localhost:$BACKEND_PORT"

    # 启动前端服务器
    print_frontend "启动 Vite 开发服务器，端口: $FRONTEND_PORT"
    if [ "$DEBUG_ENABLED" = true ]; then
        # 调试模式下显示实时日志
        npm run dev 2>&1 | while IFS= read -r line; do
            echo -e "${CYAN}[前端]${NC} $line"
        done &
    else
        # 标准模式下后台运行
        nohup npm run dev > /tmp/frontend.log 2>&1 &
    fi

    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$FRONTEND_PID_FILE"

    cd ../..

    # 等待前端启动
    print_frontend "等待前端服务器启动..."
    local max_attempts=20
    local attempt=1

    sleep 3  # 给前端一些启动时间

    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:$FRONTEND_PORT > /dev/null 2>&1; then
            print_success "前端服务器启动成功 (PID: $FRONTEND_PID)"
            return 0
        fi

        print_verbose "等待前端启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done

    print_warning "前端服务器可能仍在启动中..."
    if [ -f "/tmp/frontend.log" ]; then
        print_warning "前端日志："
        tail -10 /tmp/frontend.log
    fi
    return 0
}

# =============================================================================
# 状态显示和监控函数
# =============================================================================

# 显示开发环境信息
show_dev_info() {
    local mode=$1
    echo ""
    print_success "🚀 AI文本游戏开发环境已启动"
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    echo "🌐 访问地址:"

    case $mode in
        "fullstack"|"frontend")
            echo "   前端应用:      http://localhost:$FRONTEND_PORT"
            ;;
    esac

    case $mode in
        "fullstack"|"backend")
            echo "   后端API:       http://localhost:$BACKEND_PORT"
            echo "   健康检查:      http://localhost:$BACKEND_PORT/health"
            echo "   API文档:       http://localhost:$BACKEND_PORT/api/docs"
            ;;
    esac

    echo ""
    echo "🔧 开发特性:"
    echo "   ✅ 热重载:       代码变更自动刷新"
    echo "   ✅ 开发工具:     浏览器 F12 + 开发指示器"
    echo "   ✅ 认证模式:     $([ "$SKIP_AUTH" = true ] && echo "跳过认证" || echo "正常认证")"
    echo "   ✅ 数据库:       $DATABASE_MODE 模式"
    echo "   ✅ 调试日志:     $([ "$DEBUG_ENABLED" = true ] && echo "已启用" || echo "标准模式")"
    echo ""
    echo "🎮 快速开始:"
    if [ "$mode" = "fullstack" ] || [ "$mode" = "frontend" ]; then
        echo "   1. 打开浏览器访问 http://localhost:$FRONTEND_PORT"
        if [ "$SKIP_AUTH" = true ]; then
            echo "   2. 点击 '🚀 开发模式快速登录' 按钮"
        fi
        echo "   3. 点击右上角 DEV 按钮查看开发状态"
        echo "   4. 开始开发和测试功能"
    fi
    echo ""
    echo "🛠️  API测试:"
    if [ "$mode" = "fullstack" ] || [ "$mode" = "backend" ]; then
        echo "   curl http://localhost:$BACKEND_PORT/health"
        echo "   curl http://localhost:$BACKEND_PORT/api/v1/user/profile"
        echo "   curl http://localhost:$BACKEND_PORT/api/v1/game/my-worlds"
    fi
    echo ""
    echo "📝 进程信息:"
    if [ -f "$BACKEND_PID_FILE" ]; then
        echo "   后端进程 PID:   $(cat $BACKEND_PID_FILE)"
    fi
    if [ -f "$FRONTEND_PID_FILE" ]; then
        echo "   前端进程 PID:   $(cat $FRONTEND_PID_FILE)"
    fi
    echo ""
    echo "📋 日志文件:"
    if [ "$DEBUG_ENABLED" = false ]; then
        echo "   后端日志:       /tmp/backend.log"
        echo "   前端日志:       /tmp/frontend.log"
        echo "   查看实时日志:   tail -f /tmp/backend.log"
    else
        echo "   实时日志已在控制台显示"
    fi
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    print_warning "⚠️  按 Ctrl+C 停止所有服务"
    print_warning "⚠️  此模式仅用于开发测试，请勿在生产环境使用！"
    echo ""
}

# 监控服务状态
monitor_services() {
    local mode=$1
    print_info "开始监控服务状态..."

    while true; do
        local services_running=true

        # 检查后端服务器
        if [ "$mode" = "fullstack" ] || [ "$mode" = "backend" ]; then
            if [ -f "$BACKEND_PID_FILE" ]; then
                local backend_pid=$(cat "$BACKEND_PID_FILE")
                if ! kill -0 "$backend_pid" 2>/dev/null; then
                    print_error "后端服务器意外停止 (PID: $backend_pid)"
                    if [ -f "/tmp/backend.log" ]; then
                        print_error "后端日志（最后20行）："
                        tail -20 /tmp/backend.log
                    fi
                    services_running=false
                fi
            fi
        fi

        # 检查前端服务器
        if [ "$mode" = "fullstack" ] || [ "$mode" = "frontend" ]; then
            if [ -f "$FRONTEND_PID_FILE" ]; then
                local frontend_pid=$(cat "$FRONTEND_PID_FILE")
                if ! kill -0 "$frontend_pid" 2>/dev/null; then
                    print_error "前端服务器意外停止 (PID: $frontend_pid)"
                    if [ -f "/tmp/frontend.log" ]; then
                        print_error "前端日志（最后20行）："
                        tail -20 /tmp/frontend.log
                    fi
                    services_running=false
                fi
            fi
        fi

        # 如果有服务停止，执行清理并退出
        if [ "$services_running" = false ]; then
            print_error "检测到服务异常停止，正在清理..."
            cleanup
            exit 1
        fi

        # 每5秒检查一次
        sleep 5
    done
}

# =============================================================================
# 清理和信号处理函数
# =============================================================================

# 清理函数
cleanup() {
    echo ""
    print_info "正在停止开发服务器..."

    # 停止后端服务器
    if [ -f "$BACKEND_PID_FILE" ]; then
        local backend_pid=$(cat "$BACKEND_PID_FILE")
        if kill -0 "$backend_pid" 2>/dev/null; then
            print_backend "停止后端服务器 (PID: $backend_pid)..."
            kill "$backend_pid" 2>/dev/null || true
            # 等待进程结束
            local count=0
            while kill -0 "$backend_pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                ((count++))
            done
            # 如果进程仍在运行，强制终止
            if kill -0 "$backend_pid" 2>/dev/null; then
                print_warning "强制终止后端进程..."
                kill -9 "$backend_pid" 2>/dev/null || true
            fi
        fi
        rm -f "$BACKEND_PID_FILE"
    fi

    # 停止前端服务器
    if [ -f "$FRONTEND_PID_FILE" ]; then
        local frontend_pid=$(cat "$FRONTEND_PID_FILE")
        if kill -0 "$frontend_pid" 2>/dev/null; then
            print_frontend "停止前端服务器 (PID: $frontend_pid)..."
            kill "$frontend_pid" 2>/dev/null || true
            # 等待进程结束
            local count=0
            while kill -0 "$frontend_pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                ((count++))
            done
            # 如果进程仍在运行，强制终止
            if kill -0 "$frontend_pid" 2>/dev/null; then
                print_warning "强制终止前端进程..."
                kill -9 "$frontend_pid" 2>/dev/null || true
            fi
        fi
        rm -f "$FRONTEND_PID_FILE"
    fi

    # 清理可能残留的进程
    print_debug "清理残留进程..."
    pkill -f "go run cmd/simple-server/main.go" 2>/dev/null || true
    pkill -f "go run cmd/server/main.go" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true

    # 清理临时文件
    rm -f /tmp/backend.log /tmp/frontend.log 2>/dev/null || true

    print_success "开发环境已停止"
    exit 0
}

# =============================================================================
# 帮助和主函数
# =============================================================================

# 显示帮助信息
show_help() {
    echo "AI文本游戏统一开发环境启动脚本"
    echo ""
    echo "用法: $0 [模式] [选项]"
    echo ""
    echo "模式:"
    echo "  fullstack         启动完整的全栈开发环境（默认）"
    echo "  frontend          仅启动前端开发服务器"
    echo "  backend           仅启动后端开发服务器"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  --debug           启用调试日志输出"
    echo "  --verbose         启用详细日志输出"
    echo "  --no-auth         跳过认证（默认已启用）"
    echo "  --auth            启用正常认证模式"
    echo "  --sqlite          使用SQLite数据库（默认）"
    echo "  --postgres        使用PostgreSQL数据库"
    echo "  --frontend-port   设置前端端口（默认3000）"
    echo "  --backend-port    设置后端端口（默认8080）"
    echo "  --no-install      跳过依赖安装检查"
    echo ""
    echo "环境变量:"
    echo "  DEBUG_ENABLED     启用调试日志（true/false）"
    echo "  VERBOSE_LOGS      启用详细日志（true/false）"
    echo "  SKIP_AUTH         跳过认证（true/false）"
    echo "  DATABASE_MODE     数据库模式（sqlite/postgres）"
    echo "  FRONTEND_PORT     前端端口号"
    echo "  BACKEND_PORT      后端端口号"
    echo ""
    echo "示例:"
    echo "  $0                        # 启动完整全栈环境"
    echo "  $0 frontend --debug       # 启动前端并启用调试日志"
    echo "  $0 backend --postgres     # 启动后端使用PostgreSQL"
    echo "  $0 fullstack --verbose    # 启动全栈并启用详细日志"
    echo ""
    echo "  DEBUG_ENABLED=true $0     # 通过环境变量启用调试"
    echo "  FRONTEND_PORT=3001 $0     # 使用自定义前端端口"
    echo ""
    echo "特性:"
    echo "  • 统一的启动脚本，支持多种模式"
    echo "  • 智能的端口检查和冲突处理"
    echo "  • 完整的日志控制和调试功能"
    echo "  • 自动依赖检查和安装"
    echo "  • 进程监控和异常处理"
    echo "  • 优雅的停止和清理机制"
}

# 主函数
main() {
    local mode="fullstack"
    local skip_install=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            fullstack|frontend|backend)
                mode=$1
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            --debug)
                DEBUG_ENABLED=true
                shift
                ;;
            --verbose)
                VERBOSE_LOGS=true
                shift
                ;;
            --no-auth)
                SKIP_AUTH=true
                shift
                ;;
            --auth)
                SKIP_AUTH=false
                shift
                ;;
            --sqlite)
                DATABASE_MODE=sqlite
                shift
                ;;
            --postgres)
                DATABASE_MODE=postgres
                shift
                ;;
            --frontend-port)
                FRONTEND_PORT=$2
                shift 2
                ;;
            --backend-port)
                BACKEND_PORT=$2
                shift 2
                ;;
            --no-install)
                skip_install=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                echo ""
                show_help
                exit 1
                ;;
        esac
    done

    # 设置信号处理
    trap cleanup SIGINT SIGTERM

    # 显示横幅
    show_banner $mode

    # 检查开发环境
    check_prerequisites

    # 检查端口占用
    check_ports

    # 安装依赖
    if [ "$skip_install" = false ]; then
        install_dependencies
    fi

    # 根据模式启动服务
    case $mode in
        "fullstack")
            print_info "启动全栈开发环境..."
            start_backend
            if [ $? -ne 0 ]; then
                print_error "后端启动失败，停止启动流程"
                cleanup
                exit 1
            fi
            start_frontend
            ;;
        "backend")
            print_info "启动后端开发环境..."
            start_backend
            if [ $? -ne 0 ]; then
                print_error "后端启动失败"
                cleanup
                exit 1
            fi
            ;;
        "frontend")
            print_info "启动前端开发环境..."
            start_frontend
            ;;
    esac

    # 显示开发信息
    show_dev_info $mode

    # 开始监控服务
    monitor_services $mode
}

# =============================================================================
# 脚本入口点
# =============================================================================

# 检查是否在项目根目录
if [ ! -f "go.mod" ] || [ ! -d "web/frontend" ]; then
    print_error "请在项目根目录下运行此脚本"
    print_info "当前目录: $(pwd)"
    print_info "期望文件: go.mod, web/frontend/"
    exit 1
fi

# 运行主函数
main "$@"
