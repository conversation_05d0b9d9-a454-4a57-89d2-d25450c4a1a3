#!/bin/bash

# 测试带前缀环境变量功能的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test() {
    echo -e "${CYAN}[TEST]${NC} $1"
}

# 创建测试程序
create_test_program() {
    print_info "创建测试程序..."
    
    cat > test_prefixed_env.go << 'EOF'
package main

import (
	"fmt"
	"os"

	"ai-text-game-iam-npc/internal/config"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 输出关键配置项
	fmt.Printf("AI_TOKEN=%s\n", cfg.AI.Token)
	fmt.Printf("AI_BASE_URL=%s\n", cfg.AI.BaseURL)
	fmt.Printf("DB_NAME=%s\n", cfg.Database.DBName)
	fmt.Printf("SERVER_PORT=%s\n", cfg.Server.Port)
	fmt.Printf("AI_MOCK_ENABLED=%t\n", cfg.AI.MockEnabled)
}
EOF
    
    print_success "测试程序创建完成"
}

# 清理所有相关环境变量
cleanup_env() {
    unset AI_TOKEN AI_BASE_URL DB_NAME SERVER_PORT AI_MOCK_ENABLED
    unset AI_TEXT_GAME_AI_TOKEN AI_TEXT_GAME_AI_BASE_URL AI_TEXT_GAME_DB_NAME AI_TEXT_GAME_SERVER_PORT AI_TEXT_GAME_AI_MOCK_ENABLED
    unset AITEXTGAME_AI_TOKEN AITEXTGAME_AI_BASE_URL AITEXTGAME_DB_NAME AITEXTGAME_SERVER_PORT AITEXTGAME_AI_MOCK_ENABLED
}

# 测试1: 基础配置（.env文件）
test_baseline() {
    print_test "测试1: 基础配置（仅.env文件）"
    
    cleanup_env
    
    echo "程序读取的配置:"
    go run test_prefixed_env.go
    echo ""
}

# 测试2: 普通环境变量覆盖
test_normal_env() {
    print_test "测试2: 普通环境变量覆盖"
    
    cleanup_env
    
    export AI_TOKEN="normal_env_token"
    export AI_BASE_URL="https://normal-env.example.com"
    export SERVER_PORT="8888"
    
    echo "设置的普通环境变量:"
    echo "  AI_TOKEN=$AI_TOKEN"
    echo "  AI_BASE_URL=$AI_BASE_URL"
    echo "  SERVER_PORT=$SERVER_PORT"
    
    echo ""
    echo "程序读取的配置:"
    go run test_prefixed_env.go
    echo ""
}

# 测试3: AI_TEXT_GAME_ 前缀环境变量
test_prefixed_env_long() {
    print_test "测试3: AI_TEXT_GAME_ 前缀环境变量"
    
    cleanup_env
    
    export AI_TEXT_GAME_AI_TOKEN="prefixed_long_token"
    export AI_TEXT_GAME_AI_BASE_URL="https://prefixed-long.example.com"
    export AI_TEXT_GAME_SERVER_PORT="7777"
    export AI_TEXT_GAME_AI_MOCK_ENABLED="false"
    
    echo "设置的AI_TEXT_GAME_前缀环境变量:"
    echo "  AI_TEXT_GAME_AI_TOKEN=$AI_TEXT_GAME_AI_TOKEN"
    echo "  AI_TEXT_GAME_AI_BASE_URL=$AI_TEXT_GAME_AI_BASE_URL"
    echo "  AI_TEXT_GAME_SERVER_PORT=$AI_TEXT_GAME_SERVER_PORT"
    echo "  AI_TEXT_GAME_AI_MOCK_ENABLED=$AI_TEXT_GAME_AI_MOCK_ENABLED"
    
    echo ""
    echo "程序读取的配置:"
    go run test_prefixed_env.go
    echo ""
}

# 测试4: AITEXTGAME_ 前缀环境变量
test_prefixed_env_short() {
    print_test "测试4: AITEXTGAME_ 前缀环境变量"
    
    cleanup_env
    
    export AITEXTGAME_AI_TOKEN="prefixed_short_token"
    export AITEXTGAME_AI_BASE_URL="https://prefixed-short.example.com"
    export AITEXTGAME_SERVER_PORT="6666"
    
    echo "设置的AITEXTGAME_前缀环境变量:"
    echo "  AITEXTGAME_AI_TOKEN=$AITEXTGAME_AI_TOKEN"
    echo "  AITEXTGAME_AI_BASE_URL=$AITEXTGAME_AI_BASE_URL"
    echo "  AITEXTGAME_SERVER_PORT=$AITEXTGAME_SERVER_PORT"
    
    echo ""
    echo "程序读取的配置:"
    go run test_prefixed_env.go
    echo ""
}

# 测试5: 优先级测试（同时设置多种环境变量）
test_priority() {
    print_test "测试5: 优先级测试（AI_TEXT_GAME_ > AITEXTGAME_ > 普通变量）"
    
    cleanup_env
    
    # 设置所有类型的环境变量
    export AI_TOKEN="normal_token"
    export AITEXTGAME_AI_TOKEN="short_prefix_token"
    export AI_TEXT_GAME_AI_TOKEN="long_prefix_token"
    
    export AI_BASE_URL="https://normal.example.com"
    export AITEXTGAME_AI_BASE_URL="https://short-prefix.example.com"
    export AI_TEXT_GAME_AI_BASE_URL="https://long-prefix.example.com"
    
    echo "设置的环境变量（按优先级从低到高）:"
    echo "  AI_TOKEN=$AI_TOKEN (优先级: 3)"
    echo "  AITEXTGAME_AI_TOKEN=$AITEXTGAME_AI_TOKEN (优先级: 2)"
    echo "  AI_TEXT_GAME_AI_TOKEN=$AI_TEXT_GAME_AI_TOKEN (优先级: 1 - 最高)"
    echo ""
    echo "  AI_BASE_URL=$AI_BASE_URL (优先级: 3)"
    echo "  AITEXTGAME_AI_BASE_URL=$AITEXTGAME_AI_BASE_URL (优先级: 2)"
    echo "  AI_TEXT_GAME_AI_BASE_URL=$AI_TEXT_GAME_AI_BASE_URL (优先级: 1 - 最高)"
    
    echo ""
    echo "程序读取的配置（应该使用最高优先级的值）:"
    go run test_prefixed_env.go
    echo ""
}

# 演示实际使用场景
demo_usage_scenarios() {
    print_test "演示: 实际使用场景"
    
    echo ""
    echo "🌟 使用场景示例:"
    echo ""
    
    echo "1. 开发环境 - 使用.env文件:"
    echo "   # .env文件中"
    echo "   AI_TOKEN=dev_token"
    echo ""
    
    echo "2. 测试环境 - 使用普通环境变量:"
    echo "   export AI_TOKEN=test_token"
    echo "   go run cmd/server/main.go"
    echo ""
    
    echo "3. 生产环境 - 使用项目前缀避免冲突:"
    echo "   export AI_TEXT_GAME_AI_TOKEN=prod_token_secure"
    echo "   export AI_TEXT_GAME_DB_NAME=ai_text_game_prod"
    echo "   ./server"
    echo ""
    
    echo "4. Docker容器 - 使用短前缀:"
    echo "   docker run -e AITEXTGAME_AI_TOKEN=container_token my-app"
    echo ""
    
    echo "5. 多项目环境 - 避免环境变量冲突:"
    echo "   # 项目A"
    echo "   export AI_TEXT_GAME_AI_TOKEN=project_a_token"
    echo "   # 项目B"
    echo "   export OTHER_PROJECT_AI_TOKEN=project_b_token"
    echo ""
    
    echo "6. CI/CD管道 - 使用项目特定的环境变量:"
    echo "   # GitHub Actions secrets"
    echo "   AI_TEXT_GAME_AI_TOKEN: \${{ secrets.AI_TOKEN }}"
    echo "   AI_TEXT_GAME_DB_PASSWORD: \${{ secrets.DB_PASSWORD }}"
    echo ""
}

# 清理函数
cleanup() {
    print_info "清理测试文件和环境变量..."
    rm -f test_prefixed_env.go
    cleanup_env
    print_success "清理完成"
}

# 主函数
main() {
    echo "🧪 带前缀环境变量功能测试"
    echo "============================"
    echo ""
    
    create_test_program
    
    test_baseline
    test_normal_env
    test_prefixed_env_long
    test_prefixed_env_short
    test_priority
    demo_usage_scenarios
    
    cleanup
    
    print_success "🎉 测试完成！"
    echo ""
    print_info "现在你可以使用以下前缀来设置项目特定的环境变量："
    echo "  • AI_TEXT_GAME_* (推荐用于生产环境)"
    echo "  • AITEXTGAME_* (简短版本)"
    echo "  • 普通变量名 (向后兼容)"
}

# 运行测试
main "$@"
