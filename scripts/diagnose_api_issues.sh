#!/bin/bash

# API问题诊断脚本
# 用于排查API请求挂起和端口转发问题

echo "=== AI文本游戏API问题诊断脚本 ==="
echo "时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SERVER_HOST=${1:-localhost}
SERVER_PORT=${2:-8080}
API_BASE_URL="http://${SERVER_HOST}:${SERVER_PORT}"

echo -e "${BLUE}配置信息:${NC}"
echo "服务器地址: ${SERVER_HOST}"
echo "服务器端口: ${SERVER_PORT}"
echo "API基础URL: ${API_BASE_URL}"
echo ""

# 1. 检查端口是否开放
echo -e "${BLUE}1. 检查端口连通性${NC}"
if command -v nc >/dev/null 2>&1; then
    if nc -z -w5 ${SERVER_HOST} ${SERVER_PORT}; then
        echo -e "${GREEN}✓ 端口 ${SERVER_PORT} 可达${NC}"
    else
        echo -e "${RED}✗ 端口 ${SERVER_PORT} 不可达${NC}"
        echo "可能的原因:"
        echo "  - 服务器未启动"
        echo "  - 端口转发配置错误"
        echo "  - 防火墙阻止连接"
    fi
else
    echo -e "${YELLOW}⚠ nc命令不可用，跳过端口检查${NC}"
fi
echo ""

# 2. 检查健康检查端点
echo -e "${BLUE}2. 检查健康检查端点${NC}"
health_response=$(curl -s -w "HTTPSTATUS:%{http_code};TIME:%{time_total}" --max-time 10 "${API_BASE_URL}/health" 2>/dev/null)
if [ $? -eq 0 ]; then
    http_code=$(echo $health_response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    time_total=$(echo $health_response | grep -o "TIME:[0-9.]*" | cut -d: -f2)
    response_body=$(echo $health_response | sed -E 's/HTTPSTATUS:[0-9]*;TIME:[0-9.]*$//')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ 健康检查成功 (HTTP $http_code, ${time_total}s)${NC}"
        echo "响应内容: $response_body"
    else
        echo -e "${YELLOW}⚠ 健康检查返回HTTP $http_code (${time_total}s)${NC}"
        echo "响应内容: $response_body"
    fi
else
    echo -e "${RED}✗ 健康检查失败 - 请求超时或连接失败${NC}"
fi
echo ""

# 3. 检查API文档端点
echo -e "${BLUE}3. 检查API文档端点${NC}"
docs_response=$(curl -s -w "HTTPSTATUS:%{http_code};TIME:%{time_total}" --max-time 10 "${API_BASE_URL}/api/v1/docs" 2>/dev/null)
if [ $? -eq 0 ]; then
    http_code=$(echo $docs_response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    time_total=$(echo $docs_response | grep -o "TIME:[0-9.]*" | cut -d: -f2)
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ API文档端点可访问 (HTTP $http_code, ${time_total}s)${NC}"
    else
        echo -e "${YELLOW}⚠ API文档端点返回HTTP $http_code (${time_total}s)${NC}"
    fi
else
    echo -e "${RED}✗ API文档端点不可访问${NC}"
fi
echo ""

# 4. 检查Swagger UI端点
echo -e "${BLUE}4. 检查Swagger UI端点${NC}"
swagger_response=$(curl -s -w "HTTPSTATUS:%{http_code};TIME:%{time_total}" --max-time 10 "${API_BASE_URL}/api/v1/docs/swagger" 2>/dev/null)
if [ $? -eq 0 ]; then
    http_code=$(echo $swagger_response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    time_total=$(echo $swagger_response | grep -o "TIME:[0-9.]*" | cut -d: -f2)
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ Swagger UI端点可访问 (HTTP $http_code, ${time_total}s)${NC}"
        # 检查响应是否包含Swagger UI内容
        if echo "$swagger_response" | grep -q "swagger-ui"; then
            echo -e "${GREEN}✓ Swagger UI内容正常${NC}"
        else
            echo -e "${YELLOW}⚠ Swagger UI内容可能有问题${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ Swagger UI端点返回HTTP $http_code (${time_total}s)${NC}"
    fi
else
    echo -e "${RED}✗ Swagger UI端点不可访问${NC}"
fi
echo ""

# 5. 检查OpenAPI规范端点
echo -e "${BLUE}5. 检查OpenAPI规范端点${NC}"
openapi_response=$(curl -s -w "HTTPSTATUS:%{http_code};TIME:%{time_total}" --max-time 10 "${API_BASE_URL}/api/v1/docs/openapi" 2>/dev/null)
if [ $? -eq 0 ]; then
    http_code=$(echo $openapi_response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    time_total=$(echo $openapi_response | grep -o "TIME:[0-9.]*" | cut -d: -f2)
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ OpenAPI规范端点可访问 (HTTP $http_code, ${time_total}s)${NC}"
        # 检查是否是有效的JSON
        response_body=$(echo $openapi_response | sed -E 's/HTTPSTATUS:[0-9]*;TIME:[0-9.]*$//')
        if echo "$response_body" | python3 -m json.tool >/dev/null 2>&1; then
            echo -e "${GREEN}✓ OpenAPI规范JSON格式有效${NC}"
        else
            echo -e "${YELLOW}⚠ OpenAPI规范JSON格式可能有问题${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ OpenAPI规范端点返回HTTP $http_code (${time_total}s)${NC}"
    fi
else
    echo -e "${RED}✗ OpenAPI规范端点不可访问${NC}"
fi
echo ""

# 6. 网络延迟测试
echo -e "${BLUE}6. 网络延迟测试${NC}"
echo "执行多次请求测试网络稳定性..."
total_time=0
success_count=0
for i in {1..5}; do
    start_time=$(date +%s.%N)
    response=$(curl -s --max-time 5 "${API_BASE_URL}/health" 2>/dev/null)
    end_time=$(date +%s.%N)
    
    if [ $? -eq 0 ]; then
        duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "N/A")
        echo "  请求 $i: 成功 (${duration}s)"
        success_count=$((success_count + 1))
        if command -v bc >/dev/null 2>&1; then
            total_time=$(echo "$total_time + $duration" | bc -l)
        fi
    else
        echo "  请求 $i: 失败"
    fi
done

if [ $success_count -gt 0 ] && command -v bc >/dev/null 2>&1; then
    avg_time=$(echo "scale=3; $total_time / $success_count" | bc -l)
    echo "成功率: $success_count/5 ($(echo "scale=1; $success_count * 20" | bc -l)%)"
    echo "平均响应时间: ${avg_time}s"
else
    echo "成功率: $success_count/5"
fi
echo ""

# 7. 系统资源检查
echo -e "${BLUE}7. 系统资源检查${NC}"
if command -v ps >/dev/null 2>&1; then
    server_process=$(ps aux | grep -E "(server|main)" | grep -v grep | head -1)
    if [ -n "$server_process" ]; then
        echo -e "${GREEN}✓ 发现服务器进程${NC}"
        echo "$server_process"
    else
        echo -e "${YELLOW}⚠ 未发现服务器进程${NC}"
    fi
else
    echo -e "${YELLOW}⚠ ps命令不可用，跳过进程检查${NC}"
fi
echo ""

# 8. 端口转发检查（针对VSCode SSH）
echo -e "${BLUE}8. VSCode SSH端口转发检查${NC}"
if [ "$SERVER_HOST" = "localhost" ] || [ "$SERVER_HOST" = "127.0.0.1" ]; then
    echo "检查本地端口转发..."
    if command -v netstat >/dev/null 2>&1; then
        listening_ports=$(netstat -tlnp 2>/dev/null | grep ":${SERVER_PORT}")
        if [ -n "$listening_ports" ]; then
            echo -e "${GREEN}✓ 端口 ${SERVER_PORT} 正在监听${NC}"
            echo "$listening_ports"
        else
            echo -e "${RED}✗ 端口 ${SERVER_PORT} 未在监听${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ netstat命令不可用${NC}"
    fi
    
    echo ""
    echo "VSCode SSH端口转发建议:"
    echo "1. 在VSCode中按 Ctrl+Shift+P"
    echo "2. 输入 'Forward a Port'"
    echo "3. 输入端口号: ${SERVER_PORT}"
    echo "4. 确保端口转发状态为 'Running'"
else
    echo "远程服务器连接，跳过本地端口转发检查"
fi
echo ""

# 9. 诊断建议
echo -e "${BLUE}9. 诊断建议${NC}"
echo "如果遇到API请求挂起问题，请检查:"
echo "1. 服务器日志中是否有错误信息"
echo "2. VSCode的端口转发是否正常工作"
echo "3. 防火墙设置是否阻止了连接"
echo "4. 服务器是否有足够的资源处理请求"
echo "5. 网络连接是否稳定"
echo ""
echo "如果Swagger UI无法加载，请检查:"
echo "1. CSP策略是否允许外部资源"
echo "2. 网络是否能访问unpkg.com"
echo "3. 浏览器控制台是否有错误信息"
echo ""

echo -e "${GREEN}诊断完成！${NC}"
echo "如需更详细的日志，请查看服务器日志文件。"
