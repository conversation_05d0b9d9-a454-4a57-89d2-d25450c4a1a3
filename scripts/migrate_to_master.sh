#!/bin/bash

# =============================================================================
# 启动脚本迁移工具
# =============================================================================
# 功能说明：
# - 帮助用户从旧的启动脚本迁移到新的主控脚本
# - 创建向后兼容的符号链接或包装脚本
# - 提供迁移建议和测试功能
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 日志函数
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_step() {
    echo -e "${BOLD}${PURPLE}[步骤]${NC} $1"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${BOLD}${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${CYAN}║                🔄 启动脚本迁移工具                           ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  📋 分析现有脚本使用情况                                     ║${NC}"
    echo -e "${CYAN}║  🔗 创建向后兼容链接                                         ║${NC}"
    echo -e "${CYAN}║  🧪 测试新脚本功能                                           ║${NC}"
    echo -e "${CYAN}║  📚 生成迁移报告                                             ║${NC}"
    echo -e "${BOLD}${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 脚本映射表
declare -A SCRIPT_MAPPING=(
    ["dev_full_stack.sh"]="fullstack"
    ["dev_full_stack_verbose.sh"]="fullstack --verbose"
    ["dev_frontend.sh"]="frontend"
    ["dev_frontend_only.sh"]="frontend"
    ["dev_no_auth.sh"]="fullstack --no-auth"
    ["dev_start.sh"]="fullstack"
    ["dev_with_sqlite.sh"]="fullstack --sqlite"
    ["build_frontend.sh"]="build"
    ["stop_dev_servers.sh"]="stop"
)

# 分析现有脚本
analyze_existing_scripts() {
    print_step "分析现有启动脚本..."
    
    local existing_scripts=()
    local missing_scripts=()
    
    for script in "${!SCRIPT_MAPPING[@]}"; do
        if [ -f "scripts/$script" ]; then
            existing_scripts+=("$script")
        else
            missing_scripts+=("$script")
        fi
    done
    
    echo ""
    print_info "现有脚本分析结果:"
    echo ""
    
    if [ ${#existing_scripts[@]} -gt 0 ]; then
        echo "✅ 找到的脚本 (${#existing_scripts[@]}个):"
        for script in "${existing_scripts[@]}"; do
            local new_cmd="${SCRIPT_MAPPING[$script]}"
            printf "   %-25s -> ./scripts/dev_master.sh %s\n" "$script" "$new_cmd"
        done
    fi
    
    if [ ${#missing_scripts[@]} -gt 0 ]; then
        echo ""
        echo "❌ 未找到的脚本 (${#missing_scripts[@]}个):"
        for script in "${missing_scripts[@]}"; do
            echo "   $script"
        done
    fi
    
    echo ""
    return ${#existing_scripts[@]}
}

# 创建向后兼容包装脚本
create_compatibility_wrappers() {
    print_step "创建向后兼容包装脚本..."
    
    local created_count=0
    local backup_dir="scripts/backup_$(date +%Y%m%d_%H%M%S)"
    
    # 创建备份目录
    mkdir -p "$backup_dir"
    print_info "备份目录: $backup_dir"
    
    for script in "${!SCRIPT_MAPPING[@]}"; do
        if [ -f "scripts/$script" ]; then
            # 备份原脚本
            cp "scripts/$script" "$backup_dir/"
            print_info "已备份: $script"
            
            # 创建包装脚本
            local new_cmd="${SCRIPT_MAPPING[$script]}"
            cat > "scripts/$script" << EOF
#!/bin/bash

# =============================================================================
# 向后兼容包装脚本 - $script
# =============================================================================
# 此脚本是为了向后兼容而自动生成的包装脚本
# 实际功能由 dev_master.sh 提供
# 
# 原脚本已备份到: $backup_dir/$script
# 新的等效命令: ./scripts/dev_master.sh $new_cmd
# =============================================================================

# 显示迁移提示
echo "🔄 兼容模式: $script"
echo "   实际执行: ./scripts/dev_master.sh $new_cmd"
echo "   建议直接使用新命令以获得更好的功能和性能"
echo ""

# 执行新的主控脚本
exec "\$(dirname "\$0")/dev_master.sh" $new_cmd "\$@"
EOF
            
            # 添加执行权限
            chmod +x "scripts/$script"
            ((created_count++))
            
            print_success "已创建包装脚本: $script"
        fi
    done
    
    echo ""
    print_success "创建了 $created_count 个向后兼容包装脚本"
    print_info "原脚本已备份到: $backup_dir"
    
    return $created_count
}

# 测试新脚本功能
test_new_script() {
    print_step "测试新主控脚本功能..."
    
    # 检查脚本是否存在
    if [ ! -f "scripts/dev_master.sh" ]; then
        print_error "dev_master.sh 不存在"
        return 1
    fi
    
    # 检查脚本权限
    if [ ! -x "scripts/dev_master.sh" ]; then
        print_error "dev_master.sh 没有执行权限"
        return 1
    fi
    
    # 测试帮助功能
    print_info "测试帮助功能..."
    if ./scripts/dev_master.sh --help > /dev/null 2>&1; then
        print_success "帮助功能正常"
    else
        print_error "帮助功能异常"
        return 1
    fi
    
    # 测试版本信息
    print_info "测试版本信息..."
    if ./scripts/dev_master.sh --version > /dev/null 2>&1; then
        print_success "版本信息正常"
    else
        print_error "版本信息异常"
        return 1
    fi
    
    # 测试干运行模式
    print_info "测试干运行模式..."
    if ./scripts/dev_master.sh fullstack --dry-run > /dev/null 2>&1; then
        print_success "干运行模式正常"
    else
        print_error "干运行模式异常"
        return 1
    fi
    
    print_success "新脚本功能测试通过"
    return 0
}

# 生成迁移报告
generate_migration_report() {
    print_step "生成迁移报告..."
    
    local report_file="scripts/migration_report.md"
    
    cat > "$report_file" << 'EOF'
# 启动脚本迁移报告

## 迁移概述

本报告记录了从多个独立启动脚本到统一主控脚本的迁移过程。

## 迁移映射表

| 原脚本 | 新命令 | 说明 |
|--------|--------|------|
EOF

    # 添加映射表
    for script in "${!SCRIPT_MAPPING[@]}"; do
        local new_cmd="${SCRIPT_MAPPING[$script]}"
        local status="❌ 未找到"
        if [ -f "scripts/$script" ]; then
            status="✅ 已迁移"
        fi
        echo "| $script | \`./scripts/dev_master.sh $new_cmd\` | $status |" >> "$report_file"
    done
    
    cat >> "$report_file" << 'EOF'

## 新功能特性

### 🚀 统一的命令接口
- 所有启动功能集成到一个脚本中
- 一致的参数和选项命名
- 更好的错误处理和用户体验

### 🐛 增强的调试功能
- 多级日志控制（--debug, --verbose, --quiet）
- 实时日志显示和文件记录
- 详细的错误诊断信息

### ⚙️ 灵活的配置选项
- 支持环境变量和命令行参数
- 数据库模式选择（SQLite/PostgreSQL）
- 认证模式控制（跳过/启用）
- 端口和超时配置

### 🔧 智能的服务管理
- 自动端口冲突检测和处理
- 进程监控和异常恢复
- 优雅的服务停止机制

## 使用建议

### 立即可用
所有原脚本都已创建向后兼容包装，可以继续使用原命令。

### 推荐迁移
建议逐步迁移到新的统一命令：

```bash
# 原命令
./scripts/dev_full_stack.sh

# 新命令（推荐）
./scripts/dev_master.sh fullstack
```

### 新功能体验
尝试新的调试和配置功能：

```bash
# 启用详细日志
./scripts/dev_master.sh fullstack --verbose

# 使用PostgreSQL
./scripts/dev_master.sh fullstack --postgres

# 自定义端口
./scripts/dev_master.sh fullstack --frontend-port 3001
```

## 故障排除

### 如果遇到问题
1. 检查原脚本备份：`scripts/backup_*/`
2. 使用干运行模式：`--dry-run`
3. 查看详细日志：`--verbose`
4. 恢复原脚本：从备份目录复制

### 获取帮助
```bash
./scripts/dev_master.sh --help
./scripts/dev_master.sh --compatibility
```

## 迁移完成检查清单

- [ ] 测试常用的启动场景
- [ ] 验证新功能正常工作
- [ ] 更新开发文档和README
- [ ] 通知团队成员新的使用方式
- [ ] 可选：删除不再需要的原脚本

EOF

    print_success "迁移报告已生成: $report_file"
}

# 显示迁移建议
show_migration_suggestions() {
    echo ""
    print_info "🎯 迁移建议："
    echo ""
    echo "1. 📋 立即可用："
    echo "   所有原脚本命令仍然可用（通过兼容包装）"
    echo ""
    echo "2. 🧪 测试新功能："
    echo "   ./scripts/dev_master.sh --help"
    echo "   ./scripts/dev_master.sh fullstack --dry-run"
    echo ""
    echo "3. 📚 学习新命令："
    echo "   查看迁移报告: scripts/migration_report.md"
    echo "   查看详细帮助: ./scripts/dev_master.sh --help"
    echo ""
    echo "4. 🔄 逐步迁移："
    echo "   在日常开发中尝试使用新命令"
    echo "   体验新的调试和配置功能"
    echo ""
    echo "5. 📝 更新文档："
    echo "   更新项目README和开发指南"
    echo "   通知团队成员新的使用方式"
    echo ""
}

# 主函数
main() {
    show_banner
    
    # 检查项目根目录
    if [ ! -f "go.mod" ]; then
        print_error "请在项目根目录下运行此脚本"
        exit 1
    fi
    
    # 分析现有脚本
    analyze_existing_scripts
    local script_count=$?
    
    if [ $script_count -eq 0 ]; then
        print_warning "没有找到需要迁移的脚本"
        exit 0
    fi
    
    # 询问是否继续
    echo ""
    read -p "是否创建向后兼容包装脚本？(y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        create_compatibility_wrappers
        test_new_script
        generate_migration_report
        show_migration_suggestions
        
        echo ""
        print_success "🎉 迁移完成！"
        print_info "所有原脚本命令仍然可用，同时可以开始使用新的统一脚本"
    else
        print_info "迁移已取消"
    fi
}

# 运行主函数
main "$@"
