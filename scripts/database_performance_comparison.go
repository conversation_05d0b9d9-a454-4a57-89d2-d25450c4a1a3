package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"os"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/database"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PerformanceTestResult 性能测试结果
type PerformanceTestResult struct {
	DatabaseType    string        `json:"database_type"`
	TestName        string        `json:"test_name"`
	RecordCount     int           `json:"record_count"`
	InsertTime      time.Duration `json:"insert_time"`
	QueryTime       time.Duration `json:"query_time"`
	ComplexQueryTime time.Duration `json:"complex_query_time"`
	MemoryUsage     int64         `json:"memory_usage_mb"`
	FileSize        int64         `json:"file_size_mb"`
}

// DatabasePerformanceTester 数据库性能测试器
type DatabasePerformanceTester struct {
	results []PerformanceTestResult
}

func NewDatabasePerformanceTester() *DatabasePerformanceTester {
	return &DatabasePerformanceTester{
		results: make([]PerformanceTestResult, 0),
	}
}

// TestSQLitePerformance 测试SQLite性能
func (tester *DatabasePerformanceTester) TestSQLitePerformance(recordCount int) error {
	log.Printf("开始SQLite性能测试 - %d条记录", recordCount)
	
	// 创建临时SQLite数据库
	dbPath := fmt.Sprintf("test_sqlite_%d.db", recordCount)
	defer os.Remove(dbPath)
	
	cfg := &config.DatabaseConfig{
		DBName:       dbPath,
		MaxOpenConns: 10,
		MaxIdleConns: 2,
		MaxLifetime:  time.Minute,
	}
	
	db, err := database.New(cfg)
	if err != nil {
		return fmt.Errorf("创建SQLite连接失败: %w", err)
	}
	defer database.Close(db)
	
	// 执行性能测试
	result, err := tester.runPerformanceTest(db, "SQLite", recordCount)
	if err != nil {
		return err
	}
	
	// 获取文件大小
	if stat, err := os.Stat(dbPath); err == nil {
		result.FileSize = stat.Size() / 1024 / 1024 // MB
	}
	
	tester.results = append(tester.results, result)
	return nil
}

// TestPostgreSQLPerformance 测试PostgreSQL性能（如果可用）
func (tester *DatabasePerformanceTester) TestPostgreSQLPerformance(recordCount int) error {
	// 检查是否有PostgreSQL环境
	pgDSN := os.Getenv("POSTGRES_TEST_DSN")
	if pgDSN == "" {
		log.Println("跳过PostgreSQL测试 - 未设置POSTGRES_TEST_DSN环境变量")
		return nil
	}
	
	log.Printf("开始PostgreSQL性能测试 - %d条记录", recordCount)
	
	cfg := &config.DatabaseConfig{
		Host:         "localhost",
		Port:         5432,
		User:         "postgres",
		Password:     "postgres",
		DBName:       "test_performance",
		SSLMode:      "disable",
		MaxOpenConns: 25,
		MaxIdleConns: 5,
		MaxLifetime:  time.Minute,
	}
	
	db, err := database.New(cfg)
	if err != nil {
		log.Printf("PostgreSQL连接失败，跳过测试: %v", err)
		return nil
	}
	defer database.Close(db)
	
	// 执行性能测试
	result, err := tester.runPerformanceTest(db, "PostgreSQL", recordCount)
	if err != nil {
		return err
	}
	
	tester.results = append(tester.results, result)
	return nil
}

// runPerformanceTest 执行性能测试
func (tester *DatabasePerformanceTester) runPerformanceTest(db *gorm.DB, dbType string, recordCount int) (PerformanceTestResult, error) {
	ctx := context.Background()
	result := PerformanceTestResult{
		DatabaseType: dbType,
		TestName:     "AI文本游戏数据库性能测试",
		RecordCount:  recordCount,
	}
	
	// 自动迁移
	if err := db.AutoMigrate(&models.User{}, &models.World{}, &models.Character{}, &models.AIInteraction{}); err != nil {
		return result, fmt.Errorf("数据库迁移失败: %w", err)
	}
	
	// 清理现有数据
	db.Exec("DELETE FROM ai_interactions")
	db.Exec("DELETE FROM characters")
	db.Exec("DELETE FROM worlds")
	db.Exec("DELETE FROM users")
	
	// 1. 测试插入性能
	log.Printf("测试%s插入性能...", dbType)
	users := generateTestUsers(recordCount)
	worlds := generateTestWorlds(recordCount / 10) // 10%的世界数量
	
	start := time.Now()
	
	// 批量插入用户
	if err := db.CreateInBatches(users, 1000).Error; err != nil {
		return result, fmt.Errorf("插入用户失败: %w", err)
	}
	
	// 批量插入世界
	if err := db.CreateInBatches(worlds, 100).Error; err != nil {
		return result, fmt.Errorf("插入世界失败: %w", err)
	}
	
	result.InsertTime = time.Since(start)
	log.Printf("%s插入完成: %v", dbType, result.InsertTime)
	
	// 2. 测试简单JSON查询性能
	log.Printf("测试%s简单查询性能...", dbType)
	start = time.Now()
	
	var queryResults []models.User
	queries := []string{
		buildJSONQuery(dbType, "preferences", "ui.theme", "dark"),
		buildJSONQuery(dbType, "preferences", "game.ai_speed", "fast"),
		buildJSONQuery(dbType, "preferences", "ui.language", "zh-CN"),
	}
	
	for _, query := range queries {
		if err := db.Where(query).Find(&queryResults).Error; err != nil {
			return result, fmt.Errorf("查询失败: %w", err)
		}
	}
	
	result.QueryTime = time.Since(start)
	log.Printf("%s简单查询完成: %v", dbType, result.QueryTime)
	
	// 3. 测试复杂查询性能
	log.Printf("测试%s复杂查询性能...", dbType)
	start = time.Now()
	
	// 复杂JOIN查询
	var complexResults []struct {
		UserID      string `json:"user_id"`
		DisplayName string `json:"display_name"`
		WorldCount  int64  `json:"world_count"`
		Theme       string `json:"theme"`
	}
	
	complexQuery := buildComplexQuery(dbType)
	if err := db.Raw(complexQuery).Scan(&complexResults).Error; err != nil {
		log.Printf("复杂查询失败: %v", err)
		// 不返回错误，继续测试
	}
	
	result.ComplexQueryTime = time.Since(start)
	log.Printf("%s复杂查询完成: %v", dbType, result.ComplexQueryTime)
	
	// 4. 获取内存使用情况（简化版）
	var memStats struct {
		Used int64
	}
	
	if dbType == "SQLite" {
		// SQLite内存使用查询
		db.Raw("PRAGMA cache_size").Scan(&memStats.Used)
	} else {
		// PostgreSQL内存使用查询
		db.Raw("SELECT pg_size_pretty(pg_database_size(current_database()))").Scan(&memStats.Used)
	}
	
	result.MemoryUsage = memStats.Used / 1024 / 1024 // 转换为MB
	
	return result, nil
}

// buildJSONQuery 构建JSON查询（适配不同数据库）
func buildJSONQuery(dbType, column, path, value string) string {
	switch dbType {
	case "PostgreSQL":
		return fmt.Sprintf("%s->>'%s' = '%s'", column, path, value)
	case "SQLite":
		return fmt.Sprintf("JSON_EXTRACT(%s, '$.%s') = '%s'", column, path, value)
	default:
		return fmt.Sprintf("JSON_EXTRACT(%s, '$.%s') = '%s'", column, path, value)
	}
}

// buildComplexQuery 构建复杂查询
func buildComplexQuery(dbType string) string {
	switch dbType {
	case "PostgreSQL":
		return `
		SELECT 
			u.id as user_id,
			u.display_name,
			COUNT(w.id) as world_count,
			u.preferences->>'ui.theme' as theme
		FROM users u
		LEFT JOIN worlds w ON u.id = w.creator_id
		WHERE u.preferences->>'game.ai_speed' = 'fast'
		GROUP BY u.id, u.display_name, u.preferences->>'ui.theme'
		ORDER BY world_count DESC
		LIMIT 100`
	case "SQLite":
		return `
		SELECT 
			u.id as user_id,
			u.display_name,
			COUNT(w.id) as world_count,
			JSON_EXTRACT(u.preferences, '$.ui.theme') as theme
		FROM users u
		LEFT JOIN worlds w ON u.id = w.creator_id
		WHERE JSON_EXTRACT(u.preferences, '$.game.ai_speed') = 'fast'
		GROUP BY u.id, u.display_name, JSON_EXTRACT(u.preferences, '$.ui.theme')
		ORDER BY world_count DESC
		LIMIT 100`
	default:
		return ""
	}
}

// generateTestUsers 生成测试用户数据
func generateTestUsers(count int) []models.User {
	users := make([]models.User, count)
	themes := []string{"light", "dark", "auto"}
	speeds := []string{"slow", "balanced", "fast"}
	languages := []string{"zh-CN", "en-US", "ja-JP", "ko-KR", "fr-FR"}
	
	for i := 0; i < count; i++ {
		preferences := map[string]interface{}{
			"ui": map[string]interface{}{
				"theme":      themes[rand.Intn(len(themes))],
				"language":   languages[rand.Intn(len(languages))],
				"font_size":  []string{"small", "medium", "large"}[rand.Intn(3)],
				"animations": rand.Intn(2) == 1,
			},
			"game": map[string]interface{}{
				"ai_speed":     speeds[rand.Intn(len(speeds))],
				"auto_save":    rand.Intn(2) == 1,
				"sound_effects": rand.Intn(2) == 1,
			},
			"privacy": map[string]interface{}{
				"profile_visible": rand.Intn(2) == 1,
				"allow_invites":   rand.Intn(2) == 1,
			},
		}
		
		displayName := fmt.Sprintf("TestUser%d", i)
		email := fmt.Sprintf("<EMAIL>", i)
		
		users[i] = models.User{
			ID:               uuid.New().String(),
			ExternalID:       fmt.Sprintf("ext-%d", i),
			ExternalProvider: "test",
			Email:            email,
			DisplayName:      &displayName,
			GameRoles:        models.StringArray{"user"},
			Status:           "active",
			Preferences:      models.JSON(preferences),
		}
	}
	
	return users
}

// generateTestWorlds 生成测试世界数据
func generateTestWorlds(count int) []models.World {
	worlds := make([]models.World, count)
	genres := []string{"fantasy", "sci-fi", "historical", "modern", "cyberpunk"}
	moods := []string{"light", "dark", "neutral", "mysterious"}
	
	for i := 0; i < count; i++ {
		worldConfig := map[string]interface{}{
			"theme": map[string]interface{}{
				"genre":      genres[rand.Intn(len(genres))],
				"mood":       moods[rand.Intn(len(moods))],
				"complexity": []string{"simple", "medium", "complex"}[rand.Intn(3)],
			},
			"rules": map[string]interface{}{
				"ai_creativity":  []string{"conservative", "balanced", "creative"}[rand.Intn(3)],
				"content_rating": []string{"family", "teen", "mature"}[rand.Intn(3)],
			},
		}
		
		description := fmt.Sprintf("Test World %d Description", i)
		
		worlds[i] = models.World{
			ID:          uuid.New().String(),
			Name:        fmt.Sprintf("TestWorld%d", i),
			Description: &description,
			CreatorID:   uuid.New().String(), // 随机创建者ID
			WorldConfig: models.JSON(worldConfig),
			Status:      "active",
			Tags:        models.StringArray{"test", "performance"},
		}
	}
	
	return worlds
}

// GenerateReport 生成性能测试报告
func (tester *DatabasePerformanceTester) GenerateReport() error {
	report := map[string]interface{}{
		"test_time": time.Now().Format(time.RFC3339),
		"results":   tester.results,
		"summary":   tester.generateSummary(),
	}
	
	// 输出到控制台
	fmt.Println("\n=== 数据库性能测试报告 ===")
	for _, result := range tester.results {
		fmt.Printf("\n数据库类型: %s\n", result.DatabaseType)
		fmt.Printf("记录数量: %d\n", result.RecordCount)
		fmt.Printf("插入时间: %v\n", result.InsertTime)
		fmt.Printf("查询时间: %v\n", result.QueryTime)
		fmt.Printf("复杂查询时间: %v\n", result.ComplexQueryTime)
		fmt.Printf("文件大小: %d MB\n", result.FileSize)
		fmt.Println(strings.Repeat("-", 40))
	}
	
	// 保存到JSON文件
	reportJSON, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return err
	}
	
	filename := fmt.Sprintf("database_performance_report_%s.json", time.Now().Format("20060102_150405"))
	return os.WriteFile(filename, reportJSON, 0644)
}

func (tester *DatabasePerformanceTester) generateSummary() map[string]interface{} {
	if len(tester.results) == 0 {
		return map[string]interface{}{"message": "没有测试结果"}
	}
	
	summary := map[string]interface{}{
		"total_tests": len(tester.results),
		"databases":   make([]string, 0),
	}
	
	for _, result := range tester.results {
		summary["databases"] = append(summary["databases"].([]string), result.DatabaseType)
	}
	
	return summary
}

func main() {
	log.Println("开始数据库性能对比测试")
	
	tester := NewDatabasePerformanceTester()
	
	// 测试不同数据量
	testSizes := []int{1000, 10000}
	
	for _, size := range testSizes {
		log.Printf("测试数据量: %d", size)
		
		// 测试SQLite
		if err := tester.TestSQLitePerformance(size); err != nil {
			log.Printf("SQLite测试失败: %v", err)
		}
		
		// 测试PostgreSQL（如果可用）
		if err := tester.TestPostgreSQLPerformance(size); err != nil {
			log.Printf("PostgreSQL测试失败: %v", err)
		}
	}
	
	// 生成报告
	if err := tester.GenerateReport(); err != nil {
		log.Printf("生成报告失败: %v", err)
	} else {
		log.Println("性能测试完成，报告已生成")
	}
}
