#!/bin/bash

# =============================================================================
# 测试文件Import路径修复脚本
# =============================================================================
# 功能说明：
# - 修复移动后的测试文件中的package声明
# - 更新import路径以确保测试能正常运行
# - 添加必要的import语句
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 修复单个测试文件
fix_test_file() {
    local file=$1
    local original_package=$2
    local new_package_suffix=$3
    
    print_info "修复文件: $file"
    
    # 备份原文件
    cp "$file" "$file.backup"
    
    # 更新package声明
    sed -i "s/^package $original_package$/package ${original_package}_test/" "$file"
    
    # 添加必要的import
    local module_path="ai-text-game-iam-npc"
    local import_path=""
    
    # 根据原始位置确定import路径
    case $original_package in
        "ai")
            import_path="$module_path/internal/ai"
            ;;
        "apidoc")
            import_path="$module_path/internal/apidoc"
            ;;
        "debug")
            import_path="$module_path/internal/debug"
            ;;
        "game")
            import_path="$module_path/internal/game"
            ;;
        "handlers")
            import_path="$module_path/internal/handlers"
            ;;
        "migration")
            import_path="$module_path/internal/migration"
            ;;
        "models")
            import_path="$module_path/internal/models"
            ;;
        "services")
            import_path="$module_path/internal/services"
            ;;
        "validation")
            import_path="$module_path/internal/validation"
            ;;
        "database")
            import_path="$module_path/pkg/database"
            ;;
    esac
    
    # 检查是否已经有import语句
    if ! grep -q "import (" "$file"; then
        # 如果没有import语句，在package声明后添加
        sed -i "/^package ${original_package}_test$/a\\
\\
import (\\
\\t\"testing\"\\
\\t\"$import_path\"\\
)" "$file"
    else
        # 如果已有import语句，检查是否需要添加模块import
        if ! grep -q "\"$import_path\"" "$file"; then
            # 在import语句中添加模块路径
            sed -i "/import (/a\\
\\t\"$import_path\"" "$file"
        fi
    fi
    
    print_success "已修复: $file"
}

# 修复所有测试文件
fix_all_test_files() {
    print_info "开始修复测试文件的import路径..."
    
    # 修复AI模块测试
    for file in tests/unit/ai/*.go tests/integration/ai/*.go; do
        if [ -f "$file" ]; then
            fix_test_file "$file" "ai" "test"
        fi
    done
    
    # 修复其他模块测试
    local modules=("apidoc" "debug" "game" "handlers" "migration" "models" "services" "validation")
    
    for module in "${modules[@]}"; do
        for test_type in unit integration; do
            for file in tests/$test_type/$module/*.go; do
                if [ -f "$file" ]; then
                    # 从文件名中提取原始包名
                    local basename=$(basename "$file")
                    if [[ $basename =~ ^${module}_(.+)_test\.go$ ]]; then
                        fix_test_file "$file" "$module" "test"
                    fi
                fi
            done
        done
    done
    
    # 修复pkg模块测试
    for file in tests/integration/pkg/*.go; do
        if [ -f "$file" ]; then
            fix_test_file "$file" "database" "test"
        fi
    done
    
    print_success "测试文件import路径修复完成"
}

# 验证修复结果
verify_fixes() {
    print_info "验证修复结果..."
    
    local error_count=0
    
    # 检查所有测试文件的语法
    for file in $(find tests -name "*.go" -type f); do
        if ! go fmt "$file" > /dev/null 2>&1; then
            print_error "语法错误: $file"
            ((error_count++))
        fi
    done
    
    if [ $error_count -eq 0 ]; then
        print_success "所有测试文件语法检查通过"
    else
        print_error "发现 $error_count 个语法错误"
        return 1
    fi
}

# 运行简单的编译测试
test_compilation() {
    print_info "测试编译..."
    
    # 尝试编译单元测试
    if go test -c ./tests/unit/... > /dev/null 2>&1; then
        print_success "单元测试编译通过"
    else
        print_warning "单元测试编译可能有问题，请手动检查"
    fi
    
    # 尝试编译集成测试
    if go test -c ./tests/integration/... > /dev/null 2>&1; then
        print_success "集成测试编译通过"
    else
        print_warning "集成测试编译可能有问题，请手动检查"
    fi
}

# 清理备份文件
cleanup_backups() {
    print_info "清理备份文件..."
    find tests -name "*.backup" -delete
    print_success "备份文件清理完成"
}

# 主函数
main() {
    echo ""
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                🔧 测试文件Import路径修复工具                  ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    if [ ! -d "tests" ]; then
        print_error "tests目录不存在，请先运行测试重组脚本"
        exit 1
    fi
    
    fix_all_test_files
    verify_fixes
    test_compilation
    
    echo ""
    read -p "是否清理备份文件？(y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup_backups
    else
        print_info "备份文件保留在原位置，文件名后缀为 .backup"
    fi
    
    echo ""
    print_success "🎉 测试文件import路径修复完成！"
    echo ""
    print_info "建议执行以下命令验证修复结果："
    print_info "  go test ./tests/unit/..."
    print_info "  go test ./tests/integration/..."
    echo ""
}

# 运行主函数
main "$@"
