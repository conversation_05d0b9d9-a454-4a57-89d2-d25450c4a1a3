#!/bin/bash

# 开发模式测试脚本
# 用于验证开发环境的认证跳过功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印函数
print_info() {
    echo -e "${BLUE}[测试]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_error() {
    echo -e "${RED}[失败]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

# 服务器地址
SERVER_URL="http://localhost:8080"

# 测试健康检查
test_health_check() {
    print_info "测试健康检查接口..."
    
    response=$(curl -s "$SERVER_URL/health" || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        print_error "无法连接到服务器，请确保服务器已启动"
        return 1
    fi
    
    # 检查是否包含开发模式标识
    if echo "$response" | grep -q '"auth_mode":"disabled"'; then
        print_success "健康检查通过，开发模式已启用"
        echo "响应: $response" | jq '.' 2>/dev/null || echo "响应: $response"
        return 0
    else
        print_warning "服务器运行中，但可能不是开发模式"
        echo "响应: $response" | jq '.' 2>/dev/null || echo "响应: $response"
        return 1
    fi
}

# 测试用户信息接口（无需认证）
test_user_profile() {
    print_info "测试用户信息接口（无需认证）..."
    
    response=$(curl -s "$SERVER_URL/api/v1/user/profile" || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        print_error "用户信息接口请求失败"
        return 1
    fi
    
    # 检查是否返回了开发用户信息
    if echo "$response" | grep -q '"email":"<EMAIL>"'; then
        print_success "用户信息接口测试通过，返回开发用户信息"
        echo "响应: $response" | jq '.' 2>/dev/null || echo "响应: $response"
        return 0
    else
        print_error "用户信息接口返回异常"
        echo "响应: $response"
        return 1
    fi
}

# 测试世界列表接口
test_worlds_api() {
    print_info "测试世界列表接口..."
    
    response=$(curl -s "$SERVER_URL/api/v1/game/my-worlds" || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        print_error "世界列表接口请求失败"
        return 1
    fi
    
    # 检查响应状态
    if echo "$response" | grep -q '"success":true\|"status":"ok"\|"data":\[\]'; then
        print_success "世界列表接口测试通过"
        echo "响应: $response" | jq '.' 2>/dev/null || echo "响应: $response"
        return 0
    else
        print_warning "世界列表接口返回异常，但可能是正常的空数据"
        echo "响应: $response"
        return 0
    fi
}

# 测试AI接口（Mock模式）
test_ai_api() {
    print_info "测试AI接口（Mock模式）..."
    
    response=$(curl -s -X POST "$SERVER_URL/api/v1/ai/generate" \
        -H "Content-Type: application/json" \
        -d '{"prompt":"测试提示","type":"scene"}' || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        print_error "AI接口请求失败"
        return 1
    fi
    
    # 检查响应
    if echo "$response" | grep -q '"success":true\|"data"\|"content"'; then
        print_success "AI接口测试通过"
        echo "响应: $response" | jq '.' 2>/dev/null || echo "响应: $response"
        return 0
    else
        print_warning "AI接口返回异常"
        echo "响应: $response"
        return 1
    fi
}

# 测试响应头
test_response_headers() {
    print_info "测试开发模式响应头..."
    
    headers=$(curl -s -I "$SERVER_URL/health" || echo "ERROR")
    
    if [[ "$headers" == "ERROR" ]]; then
        print_error "无法获取响应头"
        return 1
    fi
    
    if echo "$headers" | grep -q "X-Dev-Mode: true"; then
        print_success "开发模式响应头测试通过"
        echo "相关头信息:"
        echo "$headers" | grep -E "(X-Dev-Mode|X-Auth-Bypass|Access-Control)"
        return 0
    else
        print_warning "未检测到开发模式响应头"
        echo "所有响应头:"
        echo "$headers"
        return 1
    fi
}

# 运行所有测试
run_all_tests() {
    echo ""
    echo "🧪 开始开发模式测试..."
    echo ""
    
    local failed_tests=0
    
    # 测试健康检查
    if ! test_health_check; then
        ((failed_tests++))
    fi
    echo ""
    
    # 测试用户信息
    if ! test_user_profile; then
        ((failed_tests++))
    fi
    echo ""
    
    # 测试世界列表
    if ! test_worlds_api; then
        ((failed_tests++))
    fi
    echo ""
    
    # 测试AI接口
    if ! test_ai_api; then
        ((failed_tests++))
    fi
    echo ""
    
    # 测试响应头
    if ! test_response_headers; then
        ((failed_tests++))
    fi
    echo ""
    
    # 显示测试结果
    if [ $failed_tests -eq 0 ]; then
        print_success "🎉 所有测试通过！开发模式配置正确"
        echo ""
        echo "✅ 身份认证已成功跳过"
        echo "✅ 开发用户自动设置"
        echo "✅ API接口正常访问"
        echo "✅ 开发模式标识正确"
        echo ""
        echo "您现在可以直接调用API接口进行开发测试，无需处理认证逻辑。"
    else
        print_error "❌ $failed_tests 个测试失败"
        echo ""
        echo "请检查："
        echo "1. 服务器是否已启动"
        echo "2. 是否使用了正确的开发模式启动"
        echo "3. 环境变量是否正确设置"
        echo ""
        echo "启动命令："
        echo "  ./scripts/dev_no_auth.sh"
        return 1
    fi
}

# 显示使用说明
show_usage() {
    echo "开发模式测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  --health          只测试健康检查"
    echo "  --user            只测试用户接口"
    echo "  --worlds          只测试世界接口"
    echo "  --ai              只测试AI接口"
    echo "  --headers         只测试响应头"
    echo ""
    echo "示例:"
    echo "  $0                # 运行所有测试"
    echo "  $0 --health       # 只测试健康检查"
    echo ""
    echo "注意: 请确保开发服务器已启动"
    echo "启动命令: ./scripts/dev_no_auth.sh"
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_usage
            exit 0
            ;;
        --health)
            test_health_check
            ;;
        --user)
            test_user_profile
            ;;
        --worlds)
            test_worlds_api
            ;;
        --ai)
            test_ai_api
            ;;
        --headers)
            test_response_headers
            ;;
        "")
            run_all_tests
            ;;
        *)
            print_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
