#!/bin/bash

# API文档生成测试脚本
# 用于验证API文档系统是否能正确识别和显示所有参数

echo "=== API文档生成测试脚本 ==="
echo "时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SERVER_HOST=${1:-localhost}
SERVER_PORT=${2:-8080}
API_BASE_URL="http://${SERVER_HOST}:${SERVER_PORT}"

echo -e "${BLUE}测试配置:${NC}"
echo "服务器地址: ${SERVER_HOST}"
echo "服务器端口: ${SERVER_PORT}"
echo "API基础URL: ${API_BASE_URL}"
echo ""

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}测试 $TOTAL_TESTS: $test_name${NC}"
    
    # 执行测试命令
    result=$(eval "$test_command" 2>/dev/null)
    exit_code=$?
    
    if [ $exit_code -eq 0 ] && [[ "$result" == *"$expected_result"* ]]; then
        echo -e "${GREEN}✓ 通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 失败${NC}"
        echo "  期望包含: $expected_result"
        echo "  实际结果: $(echo "$result" | head -3)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    echo ""
}

# 检查JSON格式的函数
check_json_format() {
    local json_content="$1"
    if echo "$json_content" | python3 -m json.tool >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 1. 测试OpenAPI规范生成
echo -e "${BLUE}=== 测试1: OpenAPI规范生成 ===${NC}"
openapi_response=$(curl -s --max-time 10 "${API_BASE_URL}/api/v1/docs/openapi" 2>/dev/null)
if [ $? -eq 0 ]; then
    if check_json_format "$openapi_response"; then
        echo -e "${GREEN}✓ OpenAPI规范JSON格式有效${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # 保存到临时文件用于后续测试
        echo "$openapi_response" > /tmp/openapi_spec.json
    else
        echo -e "${RED}✗ OpenAPI规范JSON格式无效${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    echo -e "${RED}✗ 无法获取OpenAPI规范${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi
echo ""

# 2. 测试AI生成接口参数识别
echo -e "${BLUE}=== 测试2: AI生成接口参数识别 ===${NC}"
if [ -f /tmp/openapi_spec.json ]; then
    # 检查是否包含AI生成接口
    ai_generate_path=$(python3 -c "
import json
import sys
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        spec = json.load(f)
    paths = spec.get('paths', {})
    if '/api/v1/ai/generate' in paths:
        print('found')
    else:
        print('not_found')
except:
    print('error')
" 2>/dev/null)
    
    if [ "$ai_generate_path" = "found" ]; then
        echo -e "${GREEN}✓ AI生成接口路径存在${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # 检查参数定义
        ai_params=$(python3 -c "
import json
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        spec = json.load(f)
    path_info = spec['paths']['/api/v1/ai/generate']['post']
    if 'parameters' in path_info or 'requestBody' in path_info:
        print('has_params')
    else:
        print('no_params')
except:
    print('error')
" 2>/dev/null)
        
        if [ "$ai_params" = "has_params" ]; then
            echo -e "${GREEN}✓ AI生成接口包含参数定义${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ AI生成接口缺少参数定义${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        echo -e "${RED}✗ AI生成接口路径不存在${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 2))
else
    echo -e "${YELLOW}⚠ 跳过测试，OpenAPI规范不可用${NC}"
fi
echo ""

# 3. 测试GenerateRequest结构体参数
echo -e "${BLUE}=== 测试3: GenerateRequest结构体参数 ===${NC}"
if [ -f /tmp/openapi_spec.json ]; then
    # 检查关键参数字段
    required_fields=("type" "prompt")
    optional_fields=("context" "schema" "max_tokens" "temperature" "world_id")
    
    for field in "${required_fields[@]}"; do
        field_exists=$(python3 -c "
import json
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        spec = json.load(f)
    # 查找包含该字段的schema
    def find_field_in_schemas(obj, field_name):
        if isinstance(obj, dict):
            if 'properties' in obj and field_name in obj['properties']:
                return True
            for value in obj.values():
                if find_field_in_schemas(value, field_name):
                    return True
        elif isinstance(obj, list):
            for item in obj:
                if find_field_in_schemas(item, field_name):
                    return True
        return False
    
    if find_field_in_schemas(spec, '$field'):
        print('found')
    else:
        print('not_found')
except:
    print('error')
" 2>/dev/null)
        
        if [ "$field_exists" = "found" ]; then
            echo -e "${GREEN}✓ 字段 '$field' 存在于文档中${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ 字段 '$field' 不存在于文档中${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
    done
else
    echo -e "${YELLOW}⚠ 跳过测试，OpenAPI规范不可用${NC}"
fi
echo ""

# 4. 测试中文描述和示例
echo -e "${BLUE}=== 测试4: 中文描述和示例 ===${NC}"
if [ -f /tmp/openapi_spec.json ]; then
    # 检查是否包含中文描述
    chinese_desc=$(python3 -c "
import json
import re
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        content = f.read()
    # 检查是否包含中文字符
    if re.search(r'[\u4e00-\u9fff]', content):
        print('found')
    else:
        print('not_found')
except:
    print('error')
" 2>/dev/null)
    
    if [ "$chinese_desc" = "found" ]; then
        echo -e "${GREEN}✓ 文档包含中文描述${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 文档缺少中文描述${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 检查是否包含示例值
    examples_exist=$(python3 -c "
import json
try:
    with open('/tmp/openapi_spec.json', 'r') as f:
        spec = json.load(f)
    
    def find_examples(obj):
        if isinstance(obj, dict):
            if 'example' in obj:
                return True
            for value in obj.values():
                if find_examples(value):
                    return True
        elif isinstance(obj, list):
            for item in obj:
                if find_examples(item):
                    return True
        return False
    
    if find_examples(spec):
        print('found')
    else:
        print('not_found')
except:
    print('error')
" 2>/dev/null)
    
    if [ "$examples_exist" = "found" ]; then
        echo -e "${GREEN}✓ 文档包含示例值${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 文档缺少示例值${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    echo -e "${YELLOW}⚠ 跳过测试，OpenAPI规范不可用${NC}"
fi
echo ""

# 5. 测试Swagger UI加载
echo -e "${BLUE}=== 测试5: Swagger UI加载 ===${NC}"
swagger_response=$(curl -s --max-time 10 "${API_BASE_URL}/api/v1/docs/swagger" 2>/dev/null)
if [ $? -eq 0 ]; then
    if echo "$swagger_response" | grep -q "swagger-ui"; then
        echo -e "${GREEN}✓ Swagger UI页面加载成功${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # 检查是否包含OpenAPI规范URL
        if echo "$swagger_response" | grep -q "/api/v1/docs/openapi"; then
            echo -e "${GREEN}✓ Swagger UI配置了正确的OpenAPI规范URL${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ Swagger UI未配置OpenAPI规范URL${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        echo -e "${RED}✗ Swagger UI页面加载失败${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 2))
else
    echo -e "${RED}✗ 无法访问Swagger UI页面${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi
echo ""

# 6. 测试API端点列表
echo -e "${BLUE}=== 测试6: API端点列表 ===${NC}"
endpoints_response=$(curl -s --max-time 10 "${API_BASE_URL}/api/v1/docs/endpoints" 2>/dev/null)
if [ $? -eq 0 ]; then
    if check_json_format "$endpoints_response"; then
        echo -e "${GREEN}✓ API端点列表JSON格式有效${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # 检查是否包含AI相关端点
        ai_endpoints=$(echo "$endpoints_response" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    ai_count = 0
    for endpoint in data:
        if '/ai/' in endpoint.get('path', ''):
            ai_count += 1
    print(ai_count)
except:
    print(0)
" 2>/dev/null)
        
        if [ "$ai_endpoints" -gt 0 ]; then
            echo -e "${GREEN}✓ 发现 $ai_endpoints 个AI相关端点${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ 未发现AI相关端点${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        echo -e "${RED}✗ API端点列表JSON格式无效${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 2))
else
    echo -e "${RED}✗ 无法获取API端点列表${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi
echo ""

# 清理临时文件
rm -f /tmp/openapi_spec.json

# 测试结果汇总
echo -e "${BLUE}=== 测试结果汇总 ===${NC}"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"

success_rate=0
if [ $TOTAL_TESTS -gt 0 ]; then
    success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
fi
echo "成功率: ${success_rate}%"
echo ""

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！API文档生成系统工作正常。${NC}"
    echo ""
    echo -e "${BLUE}建议下一步操作:${NC}"
    echo "1. 在浏览器中访问 ${API_BASE_URL}/api/v1/docs/swagger 查看Swagger UI"
    echo "2. 测试AI生成接口的参数输入和调用"
    echo "3. 验证所有参数的中文描述和示例是否正确显示"
    exit 0
elif [ $FAILED_TESTS -le 2 ]; then
    echo -e "${YELLOW}⚠ 大部分测试通过，但有少量问题需要关注。${NC}"
    echo ""
    echo -e "${BLUE}建议检查:${NC}"
    echo "1. 服务器是否正常运行"
    echo "2. API文档生成器配置是否正确"
    echo "3. 结构体注解是否完整"
    exit 1
else
    echo -e "${RED}❌ 多个测试失败，API文档生成系统存在问题。${NC}"
    echo ""
    echo -e "${BLUE}建议排查:${NC}"
    echo "1. 检查服务器日志"
    echo "2. 验证API文档扫描器配置"
    echo "3. 确认OpenAPI注解格式正确"
    echo "4. 重新启动服务器"
    exit 2
fi
