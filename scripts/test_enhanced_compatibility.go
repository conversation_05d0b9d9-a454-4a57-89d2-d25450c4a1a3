package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/repository"
	"ai-text-game-iam-npc/internal/service"
	"ai-text-game-iam-npc/pkg/database"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TestLogger 测试日志器
type TestLogger struct{}

func (l TestLogger) Info(msg string, fields ...interface{}) {
	log.Printf("[INFO] %s %v", msg, fields)
}

func (l TestLogger) Error(msg string, fields ...interface{}) {
	log.Printf("[ERROR] %s %v", msg, fields)
}

func (l TestLogger) Warn(msg string, fields ...interface{}) {
	log.Printf("[WARN] %s %v", msg, fields)
}

func (l TestLogger) Debug(msg string, fields ...interface{}) {
	log.Printf("[DEBUG] %s %v", msg, fields)
}

func main() {
	logger := TestLogger{}
	logger.Info("🧪 开始增强兼容性测试")

	// 测试SQLite
	if err := testDatabase("sqlite", "test_sqlite.db", logger); err != nil {
		log.Fatalf("❌ SQLite测试失败: %v", err)
	}

	// 清理SQLite测试文件
	os.Remove("test_sqlite.db")

	// 如果有PostgreSQL环境，也进行测试
	if pgDSN := os.Getenv("POSTGRES_TEST_DSN"); pgDSN != "" {
		if err := testDatabase("postgresql", pgDSN, logger); err != nil {
			log.Printf("⚠️ PostgreSQL测试失败: %v", err)
		}
	} else {
		logger.Info("跳过PostgreSQL测试（未设置POSTGRES_TEST_DSN环境变量）")
	}

	logger.Info("✅ 增强兼容性测试完成")
}

func testDatabase(dbType, dsn string, logger TestLogger) error {
	logger.Info("测试数据库", "type", dbType, "dsn", dsn)

	// 1. 创建数据库连接
	cfg := &config.DatabaseConfig{
		DBName:       dsn,
		MaxOpenConns: 10,
		MaxIdleConns: 2,
		MaxLifetime:  time.Minute,
	}

	db, err := database.New(cfg)
	if err != nil {
		return fmt.Errorf("创建数据库连接失败: %w", err)
	}
	defer database.Close(db)

	// 2. 执行迁移
	if err := db.AutoMigrate(&models.User{}, &models.World{}, &models.Scene{}); err != nil {
		return fmt.Errorf("数据库迁移失败: %w", err)
	}

	// 3. 创建服务管理器
	serviceManager := service.NewServiceManager(db, logger)
	ctx := context.Background()

	if err := serviceManager.Initialize(ctx); err != nil {
		return fmt.Errorf("初始化服务管理器失败: %w", err)
	}

	// 4. 测试用户操作
	if err := testUserOperations(ctx, serviceManager, logger); err != nil {
		return fmt.Errorf("用户操作测试失败: %w", err)
	}

	// 5. 测试世界操作
	if err := testWorldOperations(ctx, serviceManager, logger); err != nil {
		return fmt.Errorf("世界操作测试失败: %w", err)
	}

	// 6. 测试JSON查询
	if err := testJSONQueries(ctx, db, logger); err != nil {
		return fmt.Errorf("JSON查询测试失败: %w", err)
	}

	// 7. 测试性能监控
	if err := testPerformanceMonitoring(ctx, db, logger); err != nil {
		return fmt.Errorf("性能监控测试失败: %w", err)
	}

	logger.Info("数据库测试完成", "type", dbType)
	return nil
}

func testUserOperations(ctx context.Context, sm *service.ServiceManager, logger TestLogger) error {
	logger.Info("测试用户操作")

	// 创建用户
	createReq := &service.CreateUserRequest{
		ExternalID:       "test-user-" + uuid.New().String()[:8],
		ExternalProvider: "test",
		Email:            "<EMAIL>",
		DisplayName:      "测试用户",
		Preferences: map[string]interface{}{
			"ui": map[string]interface{}{
				"theme":    "dark",
				"language": "zh-CN",
			},
			"game": map[string]interface{}{
				"ai_speed": "balanced",
				"language": "zh-CN",
			},
		},
	}

	user, err := sm.User.CreateUser(ctx, createReq)
	if err != nil {
		return fmt.Errorf("创建用户失败: %w", err)
	}

	logger.Info("用户创建成功", "user_id", user.ID)

	// 更新用户偏好
	updates := map[string]interface{}{
		"ui.theme":       "light",
		"game.ai_speed":  "fast",
		"new_preference": "test_value",
	}

	if err := sm.User.UpdateUserPreferences(ctx, user.ID, updates); err != nil {
		return fmt.Errorf("更新用户偏好失败: %w", err)
	}

	logger.Info("用户偏好更新成功")

	// 搜索用户
	searchCriteria := map[string]interface{}{
		"ui.theme": "light",
	}

	users, err := sm.User.SearchUsersByPreferences(ctx, searchCriteria)
	if err != nil {
		return fmt.Errorf("搜索用户失败: %w", err)
	}

	logger.Info("用户搜索完成", "found_count", len(users))

	// 验证搜索结果
	found := false
	for _, foundUser := range users {
		if foundUser.ID == user.ID {
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("搜索结果中未找到创建的用户")
	}

	return nil
}

func testWorldOperations(ctx context.Context, sm *service.ServiceManager, logger TestLogger) error {
	logger.Info("测试世界操作")

	// 首先创建一个用户作为世界创建者
	createUserReq := &service.CreateUserRequest{
		ExternalID:       "world-creator-" + uuid.New().String()[:8],
		ExternalProvider: "test",
		Email:            "<EMAIL>",
		DisplayName:      "世界创建者",
	}

	creator, err := sm.User.CreateUser(ctx, createUserReq)
	if err != nil {
		return fmt.Errorf("创建世界创建者失败: %w", err)
	}

	// 创建世界
	createWorldReq := &service.CreateWorldRequest{
		Name:        "测试世界",
		Description: "这是一个测试世界",
		CreatorID:   creator.ID,
		WorldConfig: map[string]interface{}{
			"theme": map[string]interface{}{
				"genre":      "fantasy",
				"mood":       "neutral",
				"complexity": "medium",
			},
			"rules": map[string]interface{}{
				"ai_creativity":  "balanced",
				"content_rating": "teen",
			},
		},
		Tags: []string{"测试", "幻想"},
	}

	world, err := sm.World.CreateWorld(ctx, createWorldReq)
	if err != nil {
		return fmt.Errorf("创建世界失败: %w", err)
	}

	logger.Info("世界创建成功", "world_id", world.ID)

	// 更新世界配置
	configUpdates := map[string]interface{}{
		"theme.mood":         "dark",
		"rules.ai_creativity": "creative",
		"new_setting":        "test_value",
	}

	if err := sm.World.UpdateWorldConfig(ctx, world.ID, configUpdates); err != nil {
		return fmt.Errorf("更新世界配置失败: %w", err)
	}

	logger.Info("世界配置更新成功")

	// 根据类型搜索世界
	worlds, err := sm.World.FindWorldsByGenre(ctx, "fantasy")
	if err != nil {
		return fmt.Errorf("根据类型搜索世界失败: %w", err)
	}

	logger.Info("世界搜索完成", "found_count", len(worlds))

	// 验证搜索结果
	found := false
	for _, foundWorld := range worlds {
		if foundWorld.ID == world.ID {
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("搜索结果中未找到创建的世界")
	}

	return nil
}

func testJSONQueries(ctx context.Context, db *gorm.DB, logger TestLogger) error {
	logger.Info("测试JSON查询")

	// 创建增强兼容性管理器
	compatibility := database.NewEnhancedCompatibility(db)

	// 测试JSON查询构建器
	queryBuilder := compatibility.NewJSONQueryBuilder("users", "preferences")

	// 测试字段等于查询
	condition, args := queryBuilder.FieldEquals("ui.theme", "dark")
	logger.Info("字段等于查询", "condition", condition, "args", args)

	// 测试字段存在查询
	condition, args = queryBuilder.FieldExists("ui.theme")
	logger.Info("字段存在查询", "condition", condition, "args", args)

	// 测试嵌套字段查询
	condition, args = queryBuilder.NestedFieldEquals("theme.genre", "fantasy")
	logger.Info("嵌套字段查询", "condition", condition, "args", args)

	// 测试包含对象查询
	searchObj := map[string]interface{}{
		"ui.theme":      "dark",
		"game.language": "zh-CN",
	}
	condition, args = queryBuilder.ContainsObject(searchObj)
	logger.Info("包含对象查询", "condition", condition, "args_count", len(args))

	return nil
}

func testPerformanceMonitoring(ctx context.Context, db *gorm.DB, logger TestLogger) error {
	logger.Info("测试性能监控")

	// 创建性能监控器
	compatibility := database.NewEnhancedCompatibility(db)
	monitor := compatibility.NewPerformanceMonitor()

	// 测量查询性能
	stats, err := monitor.MeasureQuery(ctx, "test_query", func() *gorm.DB {
		var users []models.User
		return db.Where("status = ?", "active").Find(&users)
	})

	if err != nil {
		return fmt.Errorf("性能监控失败: %w", err)
	}

	statsJSON, _ := json.MarshalIndent(stats, "", "  ")
	logger.Info("查询性能统计", "stats", string(statsJSON))

	return nil
}
