#!/bin/bash

# API调试系统测试运行脚本
# 用于运行所有相关的单元测试和集成测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查Go环境
check_go_env() {
    log_info "检查Go环境..."
    
    if ! command -v go &> /dev/null; then
        log_error "Go未安装或不在PATH中"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}')
    log_success "Go版本: $GO_VERSION"
}

# 检查项目依赖
check_dependencies() {
    log_info "检查项目依赖..."
    
    if [ ! -f "go.mod" ]; then
        log_error "未找到go.mod文件，请在项目根目录运行此脚本"
        exit 1
    fi
    
    log_info "下载依赖包..."
    go mod download
    go mod tidy
    
    log_success "依赖检查完成"
}

# 运行单元测试
run_unit_tests() {
    log_info "运行单元测试..."
    
    # API文档模块测试
    log_info "测试API文档模块..."
    go test -v ./internal/apidoc/... -count=1
    
    if [ $? -eq 0 ]; then
        log_success "API文档模块测试通过"
    else
        log_error "API文档模块测试失败"
        return 1
    fi
    
    # 调试模块测试
    log_info "测试调试模块..."
    go test -v ./internal/debug/... -count=1
    
    if [ $? -eq 0 ]; then
        log_success "调试模块测试通过"
    else
        log_error "调试模块测试失败"
        return 1
    fi
    
    # 集成模块测试
    log_info "测试集成模块..."
    go test -v ./internal/apidebug/... -count=1
    
    if [ $? -eq 0 ]; then
        log_success "集成模块测试通过"
    else
        log_error "集成模块测试失败"
        return 1
    fi
    
    log_success "所有单元测试通过"
}

# 运行集成测试
run_integration_tests() {
    log_info "运行集成测试..."
    
    go test -v ./test/... -count=1 -timeout=60s
    
    if [ $? -eq 0 ]; then
        log_success "集成测试通过"
    else
        log_error "集成测试失败"
        return 1
    fi
}

# 运行性能测试
run_benchmark_tests() {
    log_info "运行性能测试..."
    
    # API文档模块性能测试
    log_info "API文档模块性能测试..."
    go test -bench=. -benchmem ./internal/apidoc/... -count=1
    
    # 调试模块性能测试
    log_info "调试模块性能测试..."
    go test -bench=. -benchmem ./internal/debug/... -count=1
    
    log_success "性能测试完成"
}

# 生成测试覆盖率报告
generate_coverage() {
    log_info "生成测试覆盖率报告..."
    
    # 创建覆盖率目录
    mkdir -p coverage
    
    # 运行测试并生成覆盖率数据
    go test -coverprofile=coverage/coverage.out ./internal/apidoc/... ./internal/debug/... ./internal/apidebug/...
    
    if [ $? -eq 0 ]; then
        # 生成HTML报告
        go tool cover -html=coverage/coverage.out -o coverage/coverage.html
        
        # 显示覆盖率统计
        go tool cover -func=coverage/coverage.out
        
        log_success "覆盖率报告已生成: coverage/coverage.html"
    else
        log_error "生成覆盖率报告失败"
        return 1
    fi
}

# 运行代码质量检查
run_quality_checks() {
    log_info "运行代码质量检查..."
    
    # 检查代码格式
    log_info "检查代码格式..."
    UNFORMATTED=$(gofmt -l internal/)
    if [ -n "$UNFORMATTED" ]; then
        log_warning "以下文件需要格式化:"
        echo "$UNFORMATTED"
        log_info "运行 'go fmt ./...' 来格式化代码"
    else
        log_success "代码格式检查通过"
    fi
    
    # 运行go vet
    log_info "运行go vet检查..."
    go vet ./internal/apidoc/... ./internal/debug/... ./internal/apidebug/...
    
    if [ $? -eq 0 ]; then
        log_success "go vet检查通过"
    else
        log_error "go vet检查发现问题"
        return 1
    fi
    
    # 检查是否安装了golint
    if command -v golint &> /dev/null; then
        log_info "运行golint检查..."
        golint ./internal/apidoc/... ./internal/debug/... ./internal/apidebug/...
        log_success "golint检查完成"
    else
        log_warning "golint未安装，跳过lint检查"
        log_info "安装命令: go install golang.org/x/lint/golint@latest"
    fi
}

# 清理测试文件
cleanup() {
    log_info "清理测试文件..."
    
    # 清理临时文件
    find . -name "*.test" -delete
    find . -name "test_*" -type d -exec rm -rf {} + 2>/dev/null || true
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "API调试系统测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -u, --unit          只运行单元测试"
    echo "  -i, --integration   只运行集成测试"
    echo "  -b, --benchmark     只运行性能测试"
    echo "  -c, --coverage      生成覆盖率报告"
    echo "  -q, --quality       运行代码质量检查"
    echo "  -a, --all           运行所有测试（默认）"
    echo "  --cleanup           清理测试文件"
    echo ""
    echo "示例:"
    echo "  $0                  # 运行所有测试"
    echo "  $0 -u               # 只运行单元测试"
    echo "  $0 -c               # 生成覆盖率报告"
    echo "  $0 -q               # 运行代码质量检查"
}

# 主函数
main() {
    local run_unit=false
    local run_integration=false
    local run_benchmark=false
    local run_coverage=false
    local run_quality=false
    local run_cleanup=false
    local run_all=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--unit)
                run_unit=true
                run_all=false
                shift
                ;;
            -i|--integration)
                run_integration=true
                run_all=false
                shift
                ;;
            -b|--benchmark)
                run_benchmark=true
                run_all=false
                shift
                ;;
            -c|--coverage)
                run_coverage=true
                run_all=false
                shift
                ;;
            -q|--quality)
                run_quality=true
                run_all=false
                shift
                ;;
            -a|--all)
                run_all=true
                shift
                ;;
            --cleanup)
                run_cleanup=true
                run_all=false
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示开始信息
    echo "========================================"
    echo "    API调试系统测试脚本"
    echo "========================================"
    echo ""
    
    # 检查环境
    check_go_env
    check_dependencies
    
    echo ""
    
    # 执行相应的测试
    if [ "$run_cleanup" = true ]; then
        cleanup
        exit 0
    fi
    
    if [ "$run_all" = true ]; then
        run_unit_tests || exit 1
        echo ""
        run_integration_tests || exit 1
        echo ""
        run_benchmark_tests
        echo ""
        generate_coverage || exit 1
        echo ""
        run_quality_checks || exit 1
    else
        if [ "$run_unit" = true ]; then
            run_unit_tests || exit 1
        fi
        
        if [ "$run_integration" = true ]; then
            run_integration_tests || exit 1
        fi
        
        if [ "$run_benchmark" = true ]; then
            run_benchmark_tests
        fi
        
        if [ "$run_coverage" = true ]; then
            generate_coverage || exit 1
        fi
        
        if [ "$run_quality" = true ]; then
            run_quality_checks || exit 1
        fi
    fi
    
    echo ""
    echo "========================================"
    log_success "所有测试完成！"
    echo "========================================"
}

# 运行主函数
main "$@"
