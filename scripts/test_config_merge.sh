#!/bin/bash

# 测试配置合并功能的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建测试环境
setup_test() {
    print_info "设置测试环境..."
    
    # 备份原始文件
    [ -f ".env" ] && cp .env .env.test_backup
    [ -f ".env.development.sqlite" ] && cp .env.development.sqlite .env.development.sqlite.test_backup
    
    # 创建测试用的.env文件（模拟用户配置）
    cat > .env.test << 'EOF'
# 用户自定义配置
AI_TOKEN=user_custom_token_12345
AI_BASE_URL=https://custom.api.com
OAUTH_GOOGLE_CLIENT_ID=user_google_client_id
JWT_SECRET=user_custom_jwt_secret

# 其他配置
SERVER_PORT=8080
DB_NAME=old_db_name
EOF

    # 创建测试用的SQLite配置文件
    cat > .env.development.sqlite.test << 'EOF'
# SQLite开发配置
AI_TOKEN=dev_token
AI_BASE_URL=https://wm.atjog.com
AI_MOCK_ENABLED=true

# 数据库配置
DB_NAME=dev.db
DB_TYPE=sqlite
SKIP_AUTH=true

# 新增配置项
SQLITE_JOURNAL_MODE=WAL
NEW_CONFIG_ITEM=sqlite_value
EOF

    print_success "测试环境设置完成"
}

# 测试配置合并
test_merge() {
    print_info "测试配置合并功能..."
    
    # 使用脚本中的合并函数（需要先source脚本）
    source scripts/dev_with_sqlite.sh
    
    # 调用合并函数
    merge_env_configs ".env.test" ".env.development.sqlite.test" ".env.merged"
    
    print_info "检查合并结果..."
    
    # 检查用户配置是否被保留
    local user_token=$(grep "^AI_TOKEN=" .env.merged | cut -d'=' -f2)
    local user_base_url=$(grep "^AI_BASE_URL=" .env.merged | cut -d'=' -f2)
    local user_google_id=$(grep "^OAUTH_GOOGLE_CLIENT_ID=" .env.merged | cut -d'=' -f2)
    local user_jwt_secret=$(grep "^JWT_SECRET=" .env.merged | cut -d'=' -f2)
    
    # 检查SQLite特有配置是否存在
    local sqlite_journal=$(grep "^SQLITE_JOURNAL_MODE=" .env.merged | cut -d'=' -f2)
    local new_config=$(grep "^NEW_CONFIG_ITEM=" .env.merged | cut -d'=' -f2)
    local db_type=$(grep "^DB_TYPE=" .env.merged | cut -d'=' -f2)
    
    echo ""
    echo "合并结果检查："
    echo "  • AI_TOKEN: $user_token (应该是: user_custom_token_12345)"
    echo "  • AI_BASE_URL: $user_base_url (应该是: https://custom.api.com)"
    echo "  • OAUTH_GOOGLE_CLIENT_ID: $user_google_id (应该是: user_google_client_id)"
    echo "  • JWT_SECRET: $user_jwt_secret (应该是: user_custom_jwt_secret)"
    echo "  • SQLITE_JOURNAL_MODE: $sqlite_journal (应该是: WAL)"
    echo "  • NEW_CONFIG_ITEM: $new_config (应该是: sqlite_value)"
    echo "  • DB_TYPE: $db_type (应该是: sqlite)"
    echo ""
    
    # 验证结果
    local errors=0
    
    [ "$user_token" != "user_custom_token_12345" ] && { print_error "AI_TOKEN 未正确保留"; errors=$((errors+1)); }
    [ "$user_base_url" != "https://custom.api.com" ] && { print_error "AI_BASE_URL 未正确保留"; errors=$((errors+1)); }
    [ "$user_google_id" != "user_google_client_id" ] && { print_error "OAUTH_GOOGLE_CLIENT_ID 未正确保留"; errors=$((errors+1)); }
    [ "$user_jwt_secret" != "user_custom_jwt_secret" ] && { print_error "JWT_SECRET 未正确保留"; errors=$((errors+1)); }
    [ "$sqlite_journal" != "WAL" ] && { print_error "SQLITE_JOURNAL_MODE 未正确添加"; errors=$((errors+1)); }
    [ "$new_config" != "sqlite_value" ] && { print_error "NEW_CONFIG_ITEM 未正确添加"; errors=$((errors+1)); }
    [ "$db_type" != "sqlite" ] && { print_error "DB_TYPE 未正确设置"; errors=$((errors+1)); }
    
    if [ $errors -eq 0 ]; then
        print_success "✅ 配置合并测试通过！"
    else
        print_error "❌ 配置合并测试失败，发现 $errors 个错误"
        return 1
    fi
}

# 清理测试环境
cleanup_test() {
    print_info "清理测试环境..."
    
    # 删除测试文件
    rm -f .env.test .env.development.sqlite.test .env.merged
    
    # 恢复原始文件
    [ -f ".env.test_backup" ] && mv .env.test_backup .env
    [ -f ".env.development.sqlite.test_backup" ] && mv .env.development.sqlite.test_backup .env.development.sqlite
    
    print_success "测试环境清理完成"
}

# 主函数
main() {
    echo "🧪 配置合并功能测试"
    echo "===================="
    echo ""
    
    setup_test
    
    if test_merge; then
        print_success "🎉 所有测试通过！"
        exit_code=0
    else
        print_error "💥 测试失败！"
        exit_code=1
    fi
    
    cleanup_test
    exit $exit_code
}

# 运行测试
main "$@"
