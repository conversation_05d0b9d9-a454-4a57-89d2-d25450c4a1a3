#!/bin/bash

# AI文本游戏开发环境启动脚本 - 跳过身份认证版本
# 用于开发测试，完全跳过身份认证和API网关

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_dev() {
    echo -e "${PURPLE}[DEV]${NC} $1"
}

# 显示开发模式横幅
show_dev_banner() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                    🚀 开发模式启动                           ║${NC}"
    echo -e "${PURPLE}║                                                              ║${NC}"
    echo -e "${PURPLE}║  ⚠️  身份认证已跳过 - 仅用于开发测试                         ║${NC}"
    echo -e "${PURPLE}║  🔓 API网关限制已禁用                                        ║${NC}"
    echo -e "${PURPLE}║  👤 自动使用开发测试用户                                     ║${NC}"
    echo -e "${PURPLE}║  🛡️  安全策略已放宽                                          ║${NC}"
    echo -e "${PURPLE}║                                                              ║${NC}"
    echo -e "${PURPLE}║  ⚠️  请勿在生产环境使用此模式！                              ║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 设置开发环境变量
setup_dev_environment() {
    print_dev "设置开发环境变量..."
    
    # 导出开发环境配置
    export ENVIRONMENT=development
    export SKIP_AUTH=true
    export DEV_ENABLE_DEBUG_LOGS=true
    export DEV_ENABLE_CORS_ALL=true
    export DEV_DISABLE_RATE_LIMIT=true
    export AI_MOCK_ENABLED=true
    
    # 如果存在开发环境配置文件，则加载它
    if [ -f ".env.development" ]; then
        print_dev "加载开发环境配置文件: .env.development"
        set -a
        source .env.development
        set +a
    fi
    
    print_success "开发环境变量设置完成"
}

# 启动简化版服务器（推荐）
start_simple_server() {
    print_dev "启动简化版服务器（无认证）..."
    
    # 检查端口是否被占用
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口8080已被占用，尝试停止现有进程..."
        lsof -ti:8080 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    print_dev "使用简化版服务器，完全跳过认证系统..."
    go run cmd/simple-server/main.go
}

# 启动完整服务器（开发模式）
start_full_server() {
    print_dev "启动完整服务器（开发模式，认证跳过）..."
    
    # 检查端口是否被占用
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口8080已被占用，尝试停止现有进程..."
        lsof -ti:8080 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    print_dev "使用完整服务器，但认证中间件将跳过验证..."
    go run cmd/server/main.go
}

# 显示开发信息
show_dev_info() {
    echo ""
    print_success "🚀 开发环境已启动（认证跳过模式）"
    echo ""
    echo "服务地址:"
    echo "  🌐 应用服务器:    http://localhost:8080"
    echo "  ❤️  健康检查:     http://localhost:8080/health"
    echo "  📚 API文档:      http://localhost:8080/api/v1"
    echo ""
    echo "开发特性:"
    echo "  🔓 身份认证:     已跳过"
    echo "  👤 默认用户:     开发测试用户 (<EMAIL>)"
    echo "  🛡️  用户权限:     admin, premium, user (全权限)"
    echo "  🤖 AI服务:      Mock模式已启用"
    echo "  🔍 调试日志:     已启用"
    echo "  🌍 CORS策略:    允许所有来源"
    echo ""
    echo "测试API示例:"
    echo "  curl http://localhost:8080/health"
    echo "  curl http://localhost:8080/api/v1/user/profile"
    echo "  curl http://localhost:8080/api/v1/worlds"
    echo ""
    print_warning "⚠️  此模式仅用于开发测试，请勿在生产环境使用！"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "AI文本游戏开发环境启动脚本（跳过认证版本）"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  --simple          使用简化版服务器（推荐，完全无认证）"
    echo "  --full            使用完整服务器（开发模式，认证跳过）"
    echo ""
    echo "示例:"
    echo "  $0                # 默认使用简化版服务器"
    echo "  $0 --simple       # 使用简化版服务器"
    echo "  $0 --full         # 使用完整服务器（开发模式）"
    echo ""
    echo "特性:"
    echo "  • 完全跳过身份认证"
    echo "  • 自动设置开发测试用户"
    echo "  • 禁用API网关限制"
    echo "  • 启用AI Mock模式"
    echo "  • 放宽安全策略"
}

# 主函数
main() {
    local use_simple=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --simple)
                use_simple=true
                shift
                ;;
            --full)
                use_simple=false
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示开发模式横幅
    show_dev_banner
    
    # 设置开发环境
    setup_dev_environment
    
    # 显示开发信息
    show_dev_info
    
    # 启动服务器
    if [ "$use_simple" = true ]; then
        start_simple_server
    else
        start_full_server
    fi
}

# 运行主函数
main "$@"
