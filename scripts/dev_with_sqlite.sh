#!/bin/bash

# 使用SQLite的开发环境启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                🚀 SQLite开发环境启动                         ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  使用SQLite数据库的轻量级开发环境                            ║${NC}"
    echo -e "${CYAN}║  • 无需PostgreSQL服务器                                     ║${NC}"
    echo -e "${CYAN}║  • 快速启动和测试                                           ║${NC}"
    echo -e "${CYAN}║  • 完全兼容生产环境的PostgreSQL                              ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查Go
    if ! command -v go &> /dev/null; then
        print_error "Go未安装，请先安装Go"
        exit 1
    fi
    
    print_success "Go已安装: $(go version)"
    
    # 检查SQLite支持
    if ! go list -m github.com/mattn/go-sqlite3 &> /dev/null; then
        print_warning "SQLite驱动未安装，正在安装..."
        go get github.com/mattn/go-sqlite3
    fi
    
    print_success "SQLite驱动已安装"
}

# 合并环境配置文件
merge_env_configs() {
    local base_file="$1"
    local sqlite_file="$2"
    local output_file="$3"

    print_info "合并配置文件: $base_file + $sqlite_file -> $output_file"

    # 创建临时文件来存储合并结果
    local temp_file=$(mktemp)

    # 首先复制SQLite配置作为基础
    cp "$sqlite_file" "$temp_file"

    # 如果存在原始.env文件，提取用户自定义的配置
    if [ -f "$base_file" ]; then
        print_info "检测到现有配置文件，保留用户自定义配置..."

        # 需要保留的用户配置项（这些通常是用户手动配置的）
        local preserve_keys=(
            "AI_TOKEN"
            "AI_BASE_URL"
            "OAUTH_GOOGLE_CLIENT_ID"
            "OAUTH_GOOGLE_CLIENT_SECRET"
            "OAUTH_GITHUB_CLIENT_ID"
            "OAUTH_GITHUB_CLIENT_SECRET"
            "JWT_SECRET"
        )

        # 从原始文件中提取需要保留的配置
        for key in "${preserve_keys[@]}"; do
            # 查找原始文件中的配置值（排除注释行和空值）
            local value=$(grep "^${key}=" "$base_file" 2>/dev/null | head -1 | cut -d'=' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            # 如果找到了非空值，则更新到临时文件中
            if [ -n "$value" ] && [ "$value" != "dev_token" ] && [ "$value" != "" ]; then
                print_info "保留用户配置: $key=$value"

                # 在临时文件中更新这个配置项
                if grep -q "^${key}=" "$temp_file"; then
                    # 如果配置项存在，则替换
                    sed -i "s|^${key}=.*|${key}=${value}|" "$temp_file"
                else
                    # 如果配置项不存在，则添加
                    echo "${key}=${value}" >> "$temp_file"
                fi
            fi
        done
    fi

    # 将合并结果复制到输出文件
    cp "$temp_file" "$output_file"
    rm -f "$temp_file"

    print_success "配置文件合并完成"
}

# 设置环境
setup_environment() {
    print_info "设置SQLite开发环境..."

    # 检查SQLite配置文件是否存在
    if [ ! -f ".env.development.sqlite" ]; then
        print_error "SQLite配置文件不存在: .env.development.sqlite"
        exit 1
    fi

    # 备份原始.env文件（如果存在）
    if [ -f ".env" ]; then
        print_info "备份原始配置文件..."
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        print_success "原始配置已备份"
    fi

    # 智能合并配置文件
    merge_env_configs ".env" ".env.development.sqlite" ".env"
    print_success "已使用SQLite配置文件（保留用户自定义配置）"

    # 创建数据目录
    mkdir -p data

    # 设置环境变量
    export ENVIRONMENT=development
    export DB_NAME=data/dev.db
    export SKIP_AUTH=true

    print_success "环境变量已设置"
}

# 初始化数据库
init_database() {
    print_info "初始化SQLite数据库..."
    
    # 删除旧的数据库文件（如果存在）
    if [ -f "data/dev.db" ]; then
        print_warning "删除旧的数据库文件..."
        rm -f data/dev.db
    fi
    
    # 创建数据库初始化程序
    cat > init_sqlite_db.go << 'EOF'
package main

import (
	"fmt"
	"log"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/migration"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/database"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	fmt.Printf("正在初始化SQLite数据库: %s\n", cfg.Database.DBName)

	// 连接数据库
	db, err := database.New(&cfg.Database)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 创建智能迁移器
	smartMigrator := migration.NewSmartMigrator(db, &cfg.Database, "migrations")

	// 创建开发环境模式
	modelsList := []interface{}{
		&models.User{},
		&models.UserStats{},
		// 添加其他模型...
	}

	if err := smartMigrator.CreateDevelopmentSchema(modelsList...); err != nil {
		log.Fatalf("创建数据库模式失败: %v", err)
	}

	fmt.Println("✅ SQLite数据库初始化完成")

	// 创建测试数据
	testUser := &models.User{
		ExternalID:       "dev_user_001",
		ExternalProvider: "development",
		Email:           "<EMAIL>",
		Status:          "active",
	}

	if err := db.Create(testUser).Error; err != nil {
		log.Printf("创建测试用户失败: %v", err)
	} else {
		fmt.Printf("✅ 创建测试用户成功，ID: %s\n", testUser.ID)
	}

	// 创建用户统计
	testStats := &models.UserStats{
		UserID:        testUser.ID,
		TotalPlayTime: 0,
		Level:         1,
		Experience:    0,
	}

	if err := db.Create(testStats).Error; err != nil {
		log.Printf("创建用户统计失败: %v", err)
	} else {
		fmt.Println("✅ 创建用户统计成功")
	}

	fmt.Println("🎉 数据库初始化和测试数据创建完成！")
}
EOF

    # 运行初始化
    if go run init_sqlite_db.go; then
        print_success "SQLite数据库初始化成功"
    else
        print_error "SQLite数据库初始化失败"
        exit 1
    fi
    
    # 清理临时文件
    rm -f init_sqlite_db.go
}

# 启动服务
start_server() {
    print_info "启动开发服务器..."
    
    print_info "服务器配置："
    echo "  • 数据库: SQLite (data/dev.db)"
    echo "  • 端口: 8080"
    echo "  • 认证: 跳过"
    echo "  • 环境: development"
    echo ""
    
    print_success "🚀 正在启动服务器..."
    echo ""
    echo "访问地址："
    echo "  • API: http://localhost:8080"
    echo "  • 健康检查: http://localhost:8080/api/health"
    echo "  • 前端: http://localhost:3000 (需要单独启动)"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    echo ""
    
    # 启动Go服务器
    go run cmd/server/main.go
}

# 清理函数
cleanup() {
    print_info "清理环境..."

    # 查找最新的备份文件并恢复
    local latest_backup=$(ls -t .env.backup.* 2>/dev/null | head -1)

    if [ -n "$latest_backup" ] && [ -f "$latest_backup" ]; then
        print_info "恢复原始配置文件: $latest_backup"
        cp "$latest_backup" .env
        print_success "已恢复原始配置文件"

        # 询问是否删除备份文件
        echo -n "是否删除备份文件? (y/N): "
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            rm -f .env.backup.*
            print_info "备份文件已删除"
        fi
    elif [ -f ".env.development" ]; then
        # 如果没有备份文件，尝试使用默认的开发配置
        cp .env.development .env
        print_info "已恢复默认开发配置文件"
    else
        print_warning "未找到可恢复的配置文件"
    fi
}

# 显示配置状态
show_config_status() {
    print_info "当前配置状态："
    echo ""

    if [ -f ".env" ]; then
        echo "📄 当前 .env 文件存在"

        # 显示关键配置项
        local ai_token=$(grep "^AI_TOKEN=" .env 2>/dev/null | cut -d'=' -f2)
        local ai_base_url=$(grep "^AI_BASE_URL=" .env 2>/dev/null | cut -d'=' -f2)
        local db_name=$(grep "^DB_NAME=" .env 2>/dev/null | cut -d'=' -f2)

        echo "  • AI_TOKEN: ${ai_token:-"未设置"}"
        echo "  • AI_BASE_URL: ${ai_base_url:-"未设置"}"
        echo "  • DB_NAME: ${db_name:-"未设置"}"
    else
        echo "❌ .env 文件不存在"
    fi

    echo ""

    # 显示备份文件
    local backups=$(ls -t .env.backup.* 2>/dev/null)
    if [ -n "$backups" ]; then
        echo "💾 找到配置备份文件："
        for backup in $backups; do
            echo "  • $backup ($(date -r "$backup" '+%Y-%m-%d %H:%M:%S'))"
        done
    else
        echo "📁 未找到配置备份文件"
    fi

    echo ""
}

# 显示帮助
show_help() {
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help       显示帮助信息"
    echo "  --init-only      仅初始化数据库，不启动服务器"
    echo "  --clean          清理数据库文件"
    echo "  --status         显示当前配置状态"
    echo "  --restore        恢复最新的配置备份"
    echo ""
    echo "功能说明:"
    echo "  • 脚本会自动备份现有的 .env 文件"
    echo "  • 保留用户自定义的 AI_TOKEN 等重要配置"
    echo "  • 退出时可选择恢复原始配置"
    echo ""
    echo "示例:"
    echo "  $0                # 初始化并启动服务器"
    echo "  $0 --init-only    # 仅初始化数据库"
    echo "  $0 --clean        # 清理数据库文件"
    echo "  $0 --status       # 查看配置状态"
    echo "  $0 --restore      # 恢复配置备份"
}

# 恢复配置备份
restore_config() {
    print_info "恢复配置备份..."

    # 查找所有备份文件
    local backups=($(ls -t .env.backup.* 2>/dev/null))

    if [ ${#backups[@]} -eq 0 ]; then
        print_error "未找到配置备份文件"
        exit 1
    fi

    echo "找到以下备份文件："
    for i in "${!backups[@]}"; do
        local backup="${backups[$i]}"
        local date_str=$(date -r "$backup" '+%Y-%m-%d %H:%M:%S')
        echo "  $((i+1)). $backup ($date_str)"
    done

    echo -n "请选择要恢复的备份 (1-${#backups[@]}，回车选择最新): "
    read -r choice

    local selected_backup
    if [ -z "$choice" ]; then
        selected_backup="${backups[0]}"
    elif [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${#backups[@]} ]; then
        selected_backup="${backups[$((choice-1))]}"
    else
        print_error "无效选择"
        exit 1
    fi

    print_info "恢复备份: $selected_backup"
    cp "$selected_backup" .env
    print_success "配置已恢复"

    # 显示恢复后的配置状态
    show_config_status
}

# 清理数据库
clean_database() {
    print_info "清理SQLite数据库..."

    rm -f data/dev.db
    rm -f data/dev.db-shm
    rm -f data/dev.db-wal

    print_success "数据库文件已清理"
}

# 主函数
main() {
    # 解析命令行参数
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        --status)
            show_config_status
            exit 0
            ;;
        --restore)
            restore_config
            exit 0
            ;;
        --init-only)
            # 设置清理陷阱
            trap cleanup EXIT
            show_banner
            check_dependencies
            setup_environment
            init_database
            print_success "🎉 数据库初始化完成！"
            exit 0
            ;;
        --clean)
            clean_database
            exit 0
            ;;
        "")
            # 设置清理陷阱
            trap cleanup EXIT
            # 默认行为：完整启动
            show_banner
            check_dependencies
            setup_environment
            init_database
            start_server
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
