# AI文本游戏部署运维设计文档

## 1. 部署架构概述

### 1.1. 整体部署架构
```
                    ┌─────────────────────────────────────┐
                    │              CDN层                  │
                    │  ┌─────────────┐ ┌─────────────┐    │
                    │  │ Cloudflare  │ │   静态资源  │    │
                    │  │     CDN     │ │    缓存     │    │
                    │  └─────────────┘ └─────────────┘    │
                    └─────────────┬───────────────────────┘
                                  │
                    ┌─────────────┴───────────────────────┐
                    │            负载均衡层               │
                    │  ┌─────────────┐ ┌─────────────┐    │
                    │  │   Nginx     │ │  HAProxy    │    │
                    │  │  Ingress    │ │  健康检查   │    │
                    │  └─────────────┘ └─────────────┘    │
                    └─────────────┬───────────────────────┘
                                  │
                    ┌─────────────┴───────────────────────┐
                    │          Kubernetes集群             │
                    │                                     │
                    │  ┌─────────────────────────────┐    │
                    │  │        应用服务层           │    │
                    │  │ ┌─────┐ ┌─────┐ ┌─────┐    │    │
                    │  │ │API  │ │Game │ │Auth │    │    │
                    │  │ │Pod  │ │Pod  │ │Pod  │    │    │
                    │  │ └─────┘ └─────┘ └─────┘    │    │
                    │  └─────────────────────────────┘    │
                    │                                     │
                    │  ┌─────────────────────────────┐    │
                    │  │        中间件层             │    │
                    │  │ ┌─────┐ ┌─────┐ ┌─────┐    │    │
                    │  │ │Redis│ │MQ   │ │Cache│    │    │
                    │  │ │Pod  │ │Pod  │ │Pod  │    │    │
                    │  │ └─────┘ └─────┘ └─────┘    │    │
                    │  └─────────────────────────────┘    │
                    │                                     │
                    │  ┌─────────────────────────────┐    │
                    │  │        数据存储层           │    │
                    │  │ ┌─────┐ ┌─────┐ ┌─────┐    │    │
                    │  │ │PG   │ │PG   │ │备份 │    │    │
                    │  │ │主库 │ │从库 │ │存储 │    │    │
                    │  │ └─────┘ └─────┘ └─────┘    │    │
                    │  └─────────────────────────────┘    │
                    └─────────────────────────────────────┘
```

### 1.2. 环境规划
- **开发环境**: 本地Docker Compose + 开发数据库
- **测试环境**: Kubernetes集群 + 测试数据
- **预发环境**: 生产级Kubernetes + 脱敏生产数据
- **生产环境**: 高可用Kubernetes + 生产数据

### 1.3. 服务拆分策略
```yaml
# 微服务划分
services:
  # 核心业务服务
  api-gateway:          # API网关服务
    port: 8080
    replicas: 3
    
  auth-service:         # 认证授权服务
    port: 8081
    replicas: 2
    
  world-service:        # 世界管理服务
    port: 8082
    replicas: 3
    
  character-service:    # 角色管理服务
    port: 8083
    replicas: 3
    
  game-engine:          # 游戏引擎服务
    port: 8084
    replicas: 5
    
  ai-service:           # AI接口服务
    port: 8085
    replicas: 3
    
  notification-service: # 通知服务
    port: 8086
    replicas: 2
    
  # 支撑服务
  redis-cluster:        # Redis集群
    replicas: 6
    
  postgresql:           # PostgreSQL主从
    master: 1
    slaves: 2
    
  message-queue:        # 消息队列
    replicas: 3
```

## 2. Docker容器化设计

### 2.1. 多阶段构建Dockerfile

#### Go服务Dockerfile
```dockerfile
# 多阶段构建 - 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要工具
RUN apk add --no-cache git ca-certificates tzdata

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/api

# 运行阶段
FROM alpine:latest

# 安装ca证书和时区数据
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN addgroup -g 1001 appgroup && \
    adduser -D -s /bin/sh -u 1001 -G appgroup appuser

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .
COPY --from=builder /app/configs ./configs

# 更改文件所有者
RUN chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# 启动应用
CMD ["./main"]
```

#### 前端Dockerfile
```dockerfile
# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 运行阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
```

### 2.2. Docker Compose开发环境

#### docker-compose.yml
```yaml
version: '3.8'

services:
  # API网关
  api-gateway:
    build:
      context: .
      dockerfile: Dockerfile.gateway
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=******************************************/gamedb?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=dev-secret-key
    depends_on:
      - postgres
      - redis
    networks:
      - game-network

  # 认证服务
  auth-service:
    build:
      context: .
      dockerfile: Dockerfile.auth
    ports:
      - "8081:8081"
    environment:
      - DATABASE_URL=******************************************/gamedb?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - IDP_CLIENT_ID=${IDP_CLIENT_ID}
      - IDP_CLIENT_SECRET=${IDP_CLIENT_SECRET}
    depends_on:
      - postgres
      - redis
    networks:
      - game-network

  # 游戏引擎服务
  game-engine:
    build:
      context: .
      dockerfile: Dockerfile.game
    ports:
      - "8084:8084"
    environment:
      - DATABASE_URL=******************************************/gamedb?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - AI_API_URL=${AI_API_URL}
      - AI_API_KEY=${AI_API_KEY}
    depends_on:
      - postgres
      - redis
    networks:
      - game-network

  # PostgreSQL数据库
  postgres:
    image: postgres:14-alpine
    environment:
      - POSTGRES_DB=gamedb
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - game-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - game-network

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - api-gateway
    networks:
      - game-network

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - game-network

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - game-network

volumes:
  postgres_data:
  redis_data:
  grafana_data:

networks:
  game-network:
    driver: bridge
```

## 3. Kubernetes部署配置

### 3.1. 命名空间和资源配额

#### namespace.yaml
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ai-text-game
  labels:
    name: ai-text-game
    environment: production

---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: game-resource-quota
  namespace: ai-text-game
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"

---
apiVersion: v1
kind: LimitRange
metadata:
  name: game-limit-range
  namespace: ai-text-game
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
```

### 3.2. 配置管理

#### configmap.yaml
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: game-config
  namespace: ai-text-game
data:
  # 应用配置
  app.yaml: |
    server:
      port: 8080
      read_timeout: 30s
      write_timeout: 30s
      idle_timeout: 60s
    
    database:
      max_open_conns: 100
      max_idle_conns: 10
      conn_max_lifetime: 1h
    
    redis:
      pool_size: 100
      min_idle_conns: 10
      dial_timeout: 5s
      read_timeout: 3s
      write_timeout: 3s
    
    ai:
      timeout: 30s
      max_retries: 3
      batch_size: 10
    
    game:
      max_players_per_world: 100
      auto_save_interval: 5m
      session_timeout: 30m

  # Nginx配置
  nginx.conf: |
    upstream backend {
        server api-gateway:8080 max_fails=3 fail_timeout=30s;
    }
    
    server {
        listen 80;
        server_name _;
        
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /ws {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }
        
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
    }

---
apiVersion: v1
kind: Secret
metadata:
  name: game-secrets
  namespace: ai-text-game
type: Opaque
data:
  # Base64编码的敏感信息
  jwt-secret: <base64-encoded-jwt-secret>
  database-password: <base64-encoded-db-password>
  redis-password: <base64-encoded-redis-password>
  ai-api-key: <base64-encoded-ai-key>
  idp-client-secret: <base64-encoded-idp-secret>
```

### 3.3. 应用部署配置

#### api-gateway-deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: ai-text-game
  labels:
    app: api-gateway
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        version: v1.0.0
    spec:
      containers:
      - name: api-gateway
        image: ai-text-game/api-gateway:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: game-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: game-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: game-secrets
              key: jwt-secret
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/configs
          readOnly: true
      volumes:
      - name: config-volume
        configMap:
          name: game-config
      imagePullSecrets:
      - name: registry-secret

---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: ai-text-game
  labels:
    app: api-gateway
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: api-gateway

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-gateway-ingress
  namespace: ai-text-game
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.ai-text-game.com
    secretName: api-tls-secret
  rules:
  - host: api.ai-text-game.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 8080
```

### 3.4. 数据库部署

#### postgresql-deployment.yaml
```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql-master
  namespace: ai-text-game
spec:
  serviceName: postgresql-master
  replicas: 1
  selector:
    matchLabels:
      app: postgresql
      role: master
  template:
    metadata:
      labels:
        app: postgresql
        role: master
    spec:
      containers:
      - name: postgresql
        image: postgres:14-alpine
        env:
        - name: POSTGRES_DB
          value: gamedb
        - name: POSTGRES_USER
          value: postgres
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: game-secrets
              key: database-password
        - name: POSTGRES_REPLICATION_USER
          value: replicator
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: game-secrets
              key: replication-password
        ports:
        - containerPort: 5432
          name: postgresql
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        - name: postgresql-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-config
        configMap:
          name: postgresql-config
  volumeClaimTemplates:
  - metadata:
      name: postgresql-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 100Gi

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-master
  namespace: ai-text-game
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
  selector:
    app: postgresql
    role: master
```

## 4. CI/CD流水线设计

### 4.1. GitLab CI配置

#### .gitlab-ci.yml
```yaml
stages:
  - test
  - build
  - security
  - deploy-dev
  - deploy-staging
  - deploy-prod

variables:
  DOCKER_REGISTRY: registry.gitlab.com/ai-text-game
  KUBERNETES_NAMESPACE_DEV: ai-text-game-dev
  KUBERNETES_NAMESPACE_STAGING: ai-text-game-staging
  KUBERNETES_NAMESPACE_PROD: ai-text-game

# 测试阶段
unit-test:
  stage: test
  image: golang:1.21
  services:
    - postgres:14
    - redis:7
  variables:
    POSTGRES_DB: testdb
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: testpass
    DATABASE_URL: ******************************************/testdb?sslmode=disable
    REDIS_URL: redis://redis:6379
  script:
    - go mod download
    - go test -v -race -coverprofile=coverage.out ./...
    - go tool cover -func=coverage.out
  coverage: '/total:\s+\(statements\)\s+(\d+\.\d+\%)/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml

integration-test:
  stage: test
  image: golang:1.21
  services:
    - postgres:14
    - redis:7
  script:
    - go test -v -tags=integration ./tests/integration/...
  only:
    - merge_requests
    - main

# 构建阶段
build-backend:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $DOCKER_REGISTRY/api-gateway:$CI_COMMIT_SHA -f Dockerfile.gateway .
    - docker build -t $DOCKER_REGISTRY/auth-service:$CI_COMMIT_SHA -f Dockerfile.auth .
    - docker build -t $DOCKER_REGISTRY/game-engine:$CI_COMMIT_SHA -f Dockerfile.game .
    - docker push $DOCKER_REGISTRY/api-gateway:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/auth-service:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/game-engine:$CI_COMMIT_SHA
  only:
    - main
    - develop

build-frontend:
  stage: build
  image: node:18
  script:
    - cd frontend
    - npm ci
    - npm run build
    - docker build -t $DOCKER_REGISTRY/frontend:$CI_COMMIT_SHA .
    - docker push $DOCKER_REGISTRY/frontend:$CI_COMMIT_SHA
  only:
    - main
    - develop

# 安全扫描
security-scan:
  stage: security
  image: securecodewarrior/docker-scanner
  script:
    - docker-scanner scan $DOCKER_REGISTRY/api-gateway:$CI_COMMIT_SHA
    - docker-scanner scan $DOCKER_REGISTRY/frontend:$CI_COMMIT_SHA
  allow_failure: true
  only:
    - main

# 开发环境部署
deploy-dev:
  stage: deploy-dev
  image: bitnami/kubectl:latest
  script:
    - kubectl config use-context $KUBE_CONTEXT_DEV
    - kubectl set image deployment/api-gateway api-gateway=$DOCKER_REGISTRY/api-gateway:$CI_COMMIT_SHA -n $KUBERNETES_NAMESPACE_DEV
    - kubectl set image deployment/auth-service auth-service=$DOCKER_REGISTRY/auth-service:$CI_COMMIT_SHA -n $KUBERNETES_NAMESPACE_DEV
    - kubectl set image deployment/game-engine game-engine=$DOCKER_REGISTRY/game-engine:$CI_COMMIT_SHA -n $KUBERNETES_NAMESPACE_DEV
    - kubectl rollout status deployment/api-gateway -n $KUBERNETES_NAMESPACE_DEV
  environment:
    name: development
    url: https://dev.ai-text-game.com
  only:
    - develop

# 预发环境部署
deploy-staging:
  stage: deploy-staging
  image: bitnami/kubectl:latest
  script:
    - kubectl config use-context $KUBE_CONTEXT_STAGING
    - envsubst < k8s/staging/kustomization.yaml | kubectl apply -f -
    - kubectl rollout status deployment/api-gateway -n $KUBERNETES_NAMESPACE_STAGING
  environment:
    name: staging
    url: https://staging.ai-text-game.com
  when: manual
  only:
    - main

# 生产环境部署
deploy-prod:
  stage: deploy-prod
  image: bitnami/kubectl:latest
  script:
    - kubectl config use-context $KUBE_CONTEXT_PROD
    - envsubst < k8s/production/kustomization.yaml | kubectl apply -f -
    - kubectl rollout status deployment/api-gateway -n $KUBERNETES_NAMESPACE_PROD
  environment:
    name: production
    url: https://ai-text-game.com
  when: manual
  only:
    - main
  before_script:
    - echo "部署到生产环境需要人工确认"
```

### 4.2. Helm Chart配置

#### Chart.yaml
```yaml
apiVersion: v2
name: ai-text-game
description: AI驱动的文本冒险游戏
type: application
version: 1.0.0
appVersion: "1.0.0"
keywords:
  - game
  - ai
  - text-adventure
home: https://ai-text-game.com
sources:
  - https://github.com/ai-text-game/backend
maintainers:
  - name: AI Text Game Team
    email: <EMAIL>
```

#### values.yaml
```yaml
# 全局配置
global:
  imageRegistry: registry.gitlab.com/ai-text-game
  imageTag: "1.0.0"
  imagePullPolicy: IfNotPresent
  storageClass: fast-ssd

# API网关配置
apiGateway:
  enabled: true
  replicaCount: 3
  image:
    repository: api-gateway
    tag: ""
  service:
    type: ClusterIP
    port: 8080
  resources:
    requests:
      cpu: 200m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 512Mi
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70

# 认证服务配置
authService:
  enabled: true
  replicaCount: 2
  image:
    repository: auth-service
    tag: ""
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 300m
      memory: 256Mi

# 游戏引擎配置
gameEngine:
  enabled: true
  replicaCount: 5
  image:
    repository: game-engine
    tag: ""
  resources:
    requests:
      cpu: 300m
      memory: 512Mi
    limits:
      cpu: 1000m
      memory: 1Gi
  autoscaling:
    enabled: true
    minReplicas: 5
    maxReplicas: 20
    targetCPUUtilizationPercentage: 80

# PostgreSQL配置
postgresql:
  enabled: true
  auth:
    postgresPassword: ""
    database: gamedb
  primary:
    persistence:
      enabled: true
      size: 100Gi
      storageClass: fast-ssd
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2000m
        memory: 4Gi
  readReplicas:
    replicaCount: 2
    persistence:
      enabled: true
      size: 100Gi

# Redis配置
redis:
  enabled: true
  architecture: replication
  auth:
    enabled: true
    password: ""
  master:
    persistence:
      enabled: true
      size: 20Gi
  replica:
    replicaCount: 2
    persistence:
      enabled: true
      size: 20Gi

# Ingress配置
ingress:
  enabled: true
  className: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
  hosts:
    - host: api.ai-text-game.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: api-tls-secret
      hosts:
        - api.ai-text-game.com

# 监控配置
monitoring:
  enabled: true
  prometheus:
    enabled: true
  grafana:
    enabled: true
    adminPassword: ""
```

## 5. 监控告警系统

### 5.1. Prometheus监控配置

#### prometheus.yml
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Kubernetes API Server
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
    - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
      action: keep
      regex: default;kubernetes;https

  # 应用服务监控
  - job_name: 'ai-text-game-services'
    kubernetes_sd_configs:
    - role: pod
      namespaces:
        names:
        - ai-text-game
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
    - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
      action: replace
      regex: ([^:]+)(?::\d+)?;(\d+)
      replacement: $1:$2
      target_label: __address__

  # PostgreSQL监控
  - job_name: 'postgresql'
    static_configs:
    - targets: ['postgres-exporter:9187']

  # Redis监控
  - job_name: 'redis'
    static_configs:
    - targets: ['redis-exporter:9121']

  # Node监控
  - job_name: 'node-exporter'
    kubernetes_sd_configs:
    - role: node
    relabel_configs:
    - action: labelmap
      regex: __meta_kubernetes_node_label_(.+)
```

### 5.2. 告警规则配置

#### alert_rules.yml
```yaml
groups:
- name: ai-text-game-alerts
  rules:
  # 服务可用性告警
  - alert: ServiceDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "服务 {{ $labels.job }} 不可用"
      description: "服务 {{ $labels.job }} 在实例 {{ $labels.instance }} 上已经停止运行超过1分钟"

  # 高错误率告警
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "高错误率检测到"
      description: "服务 {{ $labels.job }} 的错误率超过10%，当前值: {{ $value }}"

  # 响应时间告警
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "响应时间过长"
      description: "服务 {{ $labels.job }} 的95%响应时间超过500ms，当前值: {{ $value }}s"

  # CPU使用率告警
  - alert: HighCPUUsage
    expr: rate(container_cpu_usage_seconds_total[5m]) * 100 > 80
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "CPU使用率过高"
      description: "容器 {{ $labels.container }} CPU使用率超过80%，当前值: {{ $value }}%"

  # 内存使用率告警
  - alert: HighMemoryUsage
    expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) * 100 > 90
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "内存使用率过高"
      description: "容器 {{ $labels.container }} 内存使用率超过90%，当前值: {{ $value }}%"

  # 数据库连接告警
  - alert: DatabaseConnectionHigh
    expr: pg_stat_activity_count > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "数据库连接数过高"
      description: "PostgreSQL连接数超过80，当前值: {{ $value }}"

  # Redis内存告警
  - alert: RedisMemoryHigh
    expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Redis内存使用率过高"
      description: "Redis内存使用率超过90%，当前值: {{ $value }}"

  # 磁盘空间告警
  - alert: DiskSpaceLow
    expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "磁盘空间不足"
      description: "节点 {{ $labels.instance }} 磁盘空间不足10%，当前剩余: {{ $value }}%"
```

### 5.3. Grafana仪表板

#### 应用监控仪表板配置
```json
{
  "dashboard": {
    "title": "AI文本游戏监控仪表板",
    "tags": ["ai-text-game", "monitoring"],
    "timezone": "Asia/Shanghai",
    "panels": [
      {
        "title": "服务状态概览",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=~\".*ai-text-game.*\"}",
            "legendFormat": "{{ job }}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        }
      },
      {
        "title": "请求速率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{ method }} {{ handler }}"
          }
        ]
      },
      {
        "title": "响应时间分布",
        "type": "heatmap",
        "targets": [
          {
            "expr": "rate(http_request_duration_seconds_bucket[5m])",
            "legendFormat": "{{ le }}"
          }
        ]
      },
      {
        "title": "错误率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "错误率"
          }
        ]
      },
      {
        "title": "数据库性能",
        "type": "graph",
        "targets": [
          {
            "expr": "pg_stat_activity_count",
            "legendFormat": "活跃连接数"
          },
          {
            "expr": "rate(pg_stat_database_tup_inserted[5m])",
            "legendFormat": "插入速率"
          }
        ]
      }
    ]
  }
}
```

这份部署运维设计文档为AI文本游戏项目提供了完整的容器化部署、Kubernetes编排、CI/CD流水线和监控告警解决方案，确保系统能够稳定、高效地运行在生产环境中。
