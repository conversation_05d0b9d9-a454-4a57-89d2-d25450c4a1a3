# 数据库迁移实施策略

**版本**: v1.0
**制定日期**: 2025-01-08
**基于**: 数据库结构对比分析报告v1.0

## 策略概述

本文档制定了从现有数据库结构迁移到目标设计文档v4.0结构的详细实施策略，确保数据完整性、系统稳定性和向后兼容性。

## 第一部分：迁移总体策略

### 1. 迁移原则

- **数据完整性优先**: 确保所有现有数据在迁移过程中不丢失
- **向后兼容性**: 保持现有API和业务逻辑的兼容性
- **分阶段实施**: 将复杂迁移拆分为多个独立阶段
- **可回滚设计**: 每个阶段都可以独立回滚
- **最小停机时间**: 尽量减少系统停机时间

### 2. 迁移策略选择

采用**渐进式迁移策略**：
- 保留现有表结构，逐步添加新字段和新表
- 使用数据同步机制确保新旧结构数据一致
- 在验证完成后逐步移除旧字段和旧表
- 支持PostgreSQL和SQLite双数据库兼容

### 3. 迁移阶段划分

| 阶段 | 名称 | 主要任务 | 风险等级 | 预估时间 |
|------|------|----------|----------|----------|
| 1 | 基础准备 | 备份、环境准备、工具开发 | 低 | 1天 |
| 2 | 新表创建 | 创建新的独立表 | 低 | 1天 |
| 3 | 字段扩展 | 添加新的JSON字段 | 中 | 1-2天 |
| 4 | 数据迁移 | 迁移和转换现有数据 | 高 | 2-3天 |
| 5 | 索引优化 | 创建新索引，优化查询 | 中 | 1天 |
| 6 | 验证清理 | 数据验证和旧字段清理 | 中 | 1-2天 |

## 第二部分：阶段1 - 基础准备

### 1. 数据备份策略

#### PostgreSQL备份
```bash
# 完整数据库备份
pg_dump -h localhost -U postgres -d ai_text_game > backup_$(date +%Y%m%d_%H%M%S).sql

# 仅数据备份（用于快速恢复）
pg_dump -h localhost -U postgres -d ai_text_game --data-only > data_backup_$(date +%Y%m%d_%H%M%S).sql

# 仅结构备份
pg_dump -h localhost -U postgres -d ai_text_game --schema-only > schema_backup_$(date +%Y%m%d_%H%M%S).sql
```

#### SQLite备份
```bash
# SQLite数据库备份
cp game.db backup_game_$(date +%Y%m%d_%H%M%S).db

# 导出为SQL格式
sqlite3 game.db .dump > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 迁移工具开发

#### 迁移管理器
```go
// pkg/migration/manager.go
package migration

import (
    "context"
    "fmt"
    "gorm.io/gorm"
)

// MigrationManager 迁移管理器
type MigrationManager struct {
    db       *gorm.DB
    dbType   string
    logger   Logger
    backupDir string
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *gorm.DB, dbType string) *MigrationManager {
    return &MigrationManager{
        db:       db,
        dbType:   dbType,
        logger:   NewLogger(),
        backupDir: "./backups",
    }
}

// ExecuteMigration 执行迁移
func (m *MigrationManager) ExecuteMigration(ctx context.Context, migration Migration) error {
    // 1. 创建备份
    if err := m.createBackup(ctx); err != nil {
        return fmt.Errorf("备份失败: %w", err)
    }

    // 2. 开始事务
    tx := m.db.Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()

    // 3. 执行迁移
    if err := migration.Up(ctx, tx); err != nil {
        tx.Rollback()
        return fmt.Errorf("迁移执行失败: %w", err)
    }

    // 4. 验证迁移结果
    if err := migration.Validate(ctx, tx); err != nil {
        tx.Rollback()
        return fmt.Errorf("迁移验证失败: %w", err)
    }

    // 5. 提交事务
    if err := tx.Commit().Error; err != nil {
        return fmt.Errorf("事务提交失败: %w", err)
    }

    m.logger.Info("迁移执行成功", "migration", migration.Name())
    return nil
}

// Migration 迁移接口
type Migration interface {
    Name() string
    Up(ctx context.Context, tx *gorm.DB) error
    Down(ctx context.Context, tx *gorm.DB) error
    Validate(ctx context.Context, tx *gorm.DB) error
}
```

### 3. 环境准备检查清单

- [ ] 数据库连接测试
- [ ] 备份存储空间检查
- [ ] 迁移工具编译和测试
- [ ] 权限验证（DDL和DML权限）
- [ ] 监控和日志配置
- [ ] 回滚脚本准备

## 第三部分：阶段2 - 新表创建

### 1. 新表创建顺序

按依赖关系确定创建顺序：
1. `user_sessions` (依赖users和worlds)
2. `character_memories` (依赖characters)
3. `character_experiences` (依赖characters)
4. `specialized_entities` (依赖entities)
5. `game_events` (独立表，替代events)

### 2. 具体迁移脚本

#### user_sessions表创建
```sql
-- PostgreSQL版本
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    active_character_id UUID, -- 软引用，避免循环依赖
    session_data JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT unique_user_world_session UNIQUE(user_id, world_id)
);

-- SQLite版本
CREATE TABLE user_sessions (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(6)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    world_id TEXT NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    active_character_id TEXT,
    session_data TEXT NOT NULL DEFAULT '{}',
    created_at DATETIME DEFAULT (datetime('now')),
    updated_at DATETIME DEFAULT (datetime('now')),
    last_activity_at DATETIME DEFAULT (datetime('now')),
    CONSTRAINT unique_user_world_session UNIQUE(user_id, world_id)
);
```

#### character_memories表创建
```sql
-- PostgreSQL版本
CREATE TABLE character_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    memory_type VARCHAR(50) NOT NULL,
    importance_score FLOAT NOT NULL DEFAULT 0.5 CHECK (importance_score >= 0 AND importance_score <= 1),
    emotional_impact FLOAT NOT NULL DEFAULT 0.0 CHECK (emotional_impact >= -1 AND emotional_impact <= 1),
    current_strength FLOAT NOT NULL DEFAULT 1.0 CHECK (current_strength >= 0 AND current_strength <= 1),
    decay_rate FLOAT NOT NULL DEFAULT 0.05 CHECK (decay_rate >= 0 AND decay_rate <= 1),
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    associated_entities JSONB DEFAULT '[]',
    tags JSONB DEFAULT '[]',
    context JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SQLite版本
CREATE TABLE character_memories (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(6)))),
    character_id TEXT NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    memory_type VARCHAR(50) NOT NULL,
    importance_score REAL NOT NULL DEFAULT 0.5 CHECK (importance_score >= 0 AND importance_score <= 1),
    emotional_impact REAL NOT NULL DEFAULT 0.0 CHECK (emotional_impact >= -1 AND emotional_impact <= 1),
    current_strength REAL NOT NULL DEFAULT 1.0 CHECK (current_strength >= 0 AND current_strength <= 1),
    decay_rate REAL NOT NULL DEFAULT 0.05 CHECK (decay_rate >= 0 AND decay_rate <= 1),
    last_accessed DATETIME DEFAULT (datetime('now')),
    associated_entities TEXT DEFAULT '[]',
    tags TEXT DEFAULT '[]',
    context TEXT DEFAULT '{}',
    created_at DATETIME DEFAULT (datetime('now')),
    updated_at DATETIME DEFAULT (datetime('now'))
);
```

### 3. 索引创建策略

#### PostgreSQL索引
```sql
-- user_sessions表索引
CREATE INDEX idx_user_sessions_active ON user_sessions(user_id, world_id, active_character_id);
CREATE INDEX idx_user_sessions_activity ON user_sessions(last_activity_at DESC);

-- character_memories表索引
CREATE INDEX idx_character_memories_character ON character_memories(character_id, importance_score DESC, created_at DESC);
CREATE INDEX idx_character_memories_type ON character_memories(character_id, memory_type, current_strength DESC);
CREATE INDEX idx_character_memories_tags_gin ON character_memories USING GIN (tags);
```

#### SQLite索引
```sql
-- user_sessions表索引
CREATE INDEX idx_user_sessions_active ON user_sessions(user_id, world_id, active_character_id);
CREATE INDEX idx_user_sessions_activity ON user_sessions(last_activity_at DESC);

-- character_memories表索引
CREATE INDEX idx_character_memories_character ON character_memories(character_id, importance_score DESC, created_at DESC);
CREATE INDEX idx_character_memories_type ON character_memories(character_id, memory_type, current_strength DESC);
```

## 第四部分：阶段3 - 字段扩展

### 1. users表字段扩展

#### 添加profile字段
```sql
-- PostgreSQL版本
ALTER TABLE users ADD COLUMN profile JSONB DEFAULT '{}';

-- SQLite版本
ALTER TABLE users ADD COLUMN profile TEXT DEFAULT '{}';
```

#### 数据迁移脚本
```sql
-- 将现有字段合并到profile中
UPDATE users SET profile =
CASE
    WHEN profile IS NULL OR profile = '{}' THEN
        json_build_object(
            'display_name', COALESCE(display_name, ''),
            'avatar', json_build_object(
                'url', COALESCE(avatar_url, ''),
                'source', 'external'
            ),
            'locale', 'zh-CN',
            'timezone', 'Asia/Shanghai'
        )
    ELSE profile
END
WHERE display_name IS NOT NULL OR avatar_url IS NOT NULL;
```

### 2. worlds表字段扩展

#### 添加新字段
```sql
-- PostgreSQL版本
ALTER TABLE worlds ADD COLUMN access_settings JSONB DEFAULT '{}';
ALTER TABLE worlds ADD COLUMN tags JSONB DEFAULT '[]';
ALTER TABLE worlds ADD COLUMN time_config JSONB DEFAULT '{}';

-- SQLite版本
ALTER TABLE worlds ADD COLUMN access_settings TEXT DEFAULT '{}';
ALTER TABLE worlds ADD COLUMN tags TEXT DEFAULT '[]';
ALTER TABLE worlds ADD COLUMN time_config TEXT DEFAULT '{}';
```

#### 数据迁移脚本
```sql
-- 迁移访问设置
UPDATE worlds SET access_settings = json_build_object(
    'is_public', is_public,
    'max_players', max_players,
    'join_policy', CASE WHEN is_public THEN 'open' ELSE 'invite' END,
    'player_permissions', json_build_object(
        'can_create_characters', true,
        'can_modify_world', false,
        'can_invite_others', false
    )
);

-- 设置默认时间配置
UPDATE worlds SET time_config = json_build_object(
    'time_multiplier', 1.0,
    'pause_when_empty', true,
    'day_night_cycle', true,
    'season_length_days', 30
);
```

### 3. scenes表字段扩展

#### 添加新字段
```sql
-- PostgreSQL版本
ALTER TABLE scenes ADD COLUMN tags JSONB DEFAULT '[]';
ALTER TABLE scenes ADD COLUMN access_rules JSONB DEFAULT '{}';
ALTER TABLE scenes ADD COLUMN connections JSONB DEFAULT '[]';

-- SQLite版本
ALTER TABLE scenes ADD COLUMN tags TEXT DEFAULT '[]';
ALTER TABLE scenes ADD COLUMN access_rules TEXT DEFAULT '{}';
ALTER TABLE scenes ADD COLUMN connections TEXT DEFAULT '[]';
```

#### 数据迁移脚本
```sql
-- 将scene_type转换为tags
UPDATE scenes SET tags = json_build_array(scene_type)
WHERE scene_type IS NOT NULL AND scene_type != '';

-- 设置默认访问规则
UPDATE scenes SET access_rules = json_build_object(
    'visibility', 'public',
    'entry_requirements', json_build_array(),
    'capacity', json_build_object(
        'description', '这里可以容纳很多人',
        'soft_limit', 20
    )
);

-- 转换连接格式（从简单的key-value到复杂结构）
UPDATE scenes SET connections = (
    SELECT json_agg(
        json_build_object(
            'id', gen_random_uuid()::text,
            'target_scene_id', value,
            'connection_type', 'bidirectional',
            'direction', json_build_object(
                'from_description', '向' || key || '走',
                'compass_direction', key
            ),
            'travel', json_build_object(
                'description', '一条通往' || key || '的路径',
                'difficulty', '轻松的步行',
                'time_description', '几分钟的路程'
            )
        )
    )
    FROM json_each_text(connected_scenes)
)
WHERE connected_scenes IS NOT NULL AND connected_scenes != '{}';
```

这个迁移策略确保了数据的完整性和系统的稳定性，同时为后续的GORM模型实现奠定了基础。