package main

import (
	"fmt"
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"github.com/google/uuid"
)

// 简化的模型定义，兼容SQLite
type User struct {
	ID               string `gorm:"primaryKey;type:text"`
	ExternalID       string `gorm:"uniqueIndex;not null"`
	ExternalProvider string `gorm:"not null"`
	Email            string `gorm:"uniqueIndex"`
	Status           string `gorm:"default:'active'"`
	CreatedAt        string
	UpdatedAt        string
}

type UserStats struct {
	ID        string `gorm:"primaryKey;type:text"`
	UserID    string `gorm:"not null;index"`
	CreatedAt string
	UpdatedAt string
}

type World struct {
	ID             string `gorm:"primaryKey;type:text"`
	Name           string `gorm:"not null;size:200"`
	Description    string
	CreatorID      string `gorm:"not null;index"`
	WorldConfig    string `gorm:"type:text;not null"` // JSON as text
	WorldState     string `gorm:"type:text;not null"` // JSON as text
	Status         string `gorm:"default:'active';index"`
	IsPublic       bool   `gorm:"default:false;index"`
	MaxPlayers     int    `gorm:"default:10"`
	CurrentPlayers int    `gorm:"default:0"`
	GameTime       int64  `gorm:"default:0"`
	CreatedAt      string
	UpdatedAt      string
}

type Scene struct {
	ID               string `gorm:"primaryKey;type:text"`
	WorldID          string `gorm:"not null;index"`
	Name             string `gorm:"not null;size:200"`
	Description      string
	SceneType        string `gorm:"default:'normal'"`
	Properties       string `gorm:"type:text;default:'{}'"`
	EntitiesPresent  string `gorm:"type:text;default:'[]'"`
	ConnectedScenes  string `gorm:"type:text;default:'{}'"`
	Environment      string `gorm:"type:text;default:'{}'"`
	Status           string `gorm:"default:'active'"`
	CreatedAt        string
	UpdatedAt        string
}

type Character struct {
	ID              string `gorm:"primaryKey;type:text"`
	WorldID         string `gorm:"not null;index"`
	UserID          string `gorm:"index"`
	Name            string `gorm:"not null;size:100"`
	Description     string
	CharacterType   string `gorm:"default:'player'"`
	CurrentSceneID  string `gorm:"index"`
	Traits          string `gorm:"type:text;default:'[]'"`
	Memories        string `gorm:"type:text;default:'[]'"`
	Experiences     string `gorm:"type:text;default:'{}'"`
	Relationships   string `gorm:"type:text;default:'{}'"`
	Status          string `gorm:"default:'active'"`
	LastActionAt    string
	CreatedAt       string
	UpdatedAt       string
}

type Entity struct {
	ID              string `gorm:"primaryKey;type:text"`
	WorldID         string `gorm:"not null;index"`
	Name            string `gorm:"not null;size:200"`
	Description     string
	EntityType      string `gorm:"not null;size:50"`
	Properties      string `gorm:"type:text;default:'{}'"`
	Traits          string `gorm:"type:text;default:'[]'"`
	CurrentSceneID  string `gorm:"index"`
	OwnerID         string `gorm:"index"`
	Status          string `gorm:"default:'active'"`
	CreatedAt       string
	UpdatedAt       string
}

type Event struct {
	ID           string `gorm:"primaryKey;type:text"`
	WorldID      string `gorm:"not null;index"`
	SceneID      string `gorm:"index"`
	CreatorID    string `gorm:"index"`
	EventType    string `gorm:"not null;size:50"`
	Name         string `gorm:"size:200"`
	Description  string
	EventData    string `gorm:"type:text;default:'{}'"`
	Participants string `gorm:"type:text;default:'[]'"`
	Status       string `gorm:"default:'pending'"`
	Priority     int    `gorm:"default:0"`
	ScheduledAt  string
	ProcessedAt  string
	Result       string `gorm:"type:text"`
	CreatedAt    string
	UpdatedAt    string
}

type AIInteraction struct {
	ID               string `gorm:"primaryKey;type:text"`
	WorldID          string `gorm:"index"`
	UserID           string `gorm:"index"`
	InteractionType  string `gorm:"not null;size:50"`
	Prompt           string `gorm:"type:text;not null"`
	Response         string `gorm:"type:text"`
	ResponseSchema   string `gorm:"type:text"`
	TokenUsage       int
	ResponseTime     int
	Status           string `gorm:"default:'pending'"`
	ErrorMessage     string `gorm:"type:text"`
	CreatedAt        string
}

type ValidationLog struct {
	ID        string `gorm:"primaryKey;type:text"`
	UserID    string `gorm:"not null;index"`
	Content   string `gorm:"type:text;not null"`
	Type      string `gorm:"not null;size:50"`
	Result    string `gorm:"type:text"`
	CreatedAt string
}

func main() {
	fmt.Println("🔧 初始化SQLite数据库...")

	// 删除旧数据库
	fmt.Println("🗑️ 删除旧数据库文件...")
	// 不需要删除，让GORM处理

	// 连接数据库
	db, err := gorm.Open(sqlite.Open("data/dev.db"), &gorm.Config{})
	if err != nil {
		log.Fatalf("❌ 连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 自动迁移所有表
	fmt.Println("🚀 开始创建表...")
	
	models := []interface{}{
		&User{},
		&UserStats{},
		&World{},
		&Scene{},
		&Character{},
		&Entity{},
		&Event{},
		&AIInteraction{},
		&ValidationLog{},
	}

	for _, model := range models {
		if err := db.AutoMigrate(model); err != nil {
			log.Fatalf("❌ 创建表失败 %T: %v", model, err)
		}
		fmt.Printf("  ✅ %T 表创建成功\n", model)
	}

	// 创建测试用户
	fmt.Println("👤 创建测试用户...")
	testUser := &User{
		ID:               uuid.New().String(),
		ExternalID:       "dev_user_001",
		ExternalProvider: "development",
		Email:           "<EMAIL>",
		Status:          "active",
		CreatedAt:       "2025-08-04T18:50:00Z",
		UpdatedAt:       "2025-08-04T18:50:00Z",
	}

	result := db.FirstOrCreate(testUser, User{ExternalID: "dev_user_001"})
	if result.Error != nil {
		log.Printf("⚠️ 创建测试用户失败: %v", result.Error)
	} else {
		fmt.Printf("✅ 测试用户ID: %s\n", testUser.ID)
	}

	// 验证表创建
	fmt.Println("🔍 验证表创建情况...")
	tableNames := []string{"users", "user_stats", "worlds", "scenes", "characters", "entities", "events", "ai_interactions", "validation_logs"}
	for _, tableName := range tableNames {
		if db.Migrator().HasTable(tableName) {
			fmt.Printf("  ✅ %s 表存在\n", tableName)
		} else {
			fmt.Printf("  ❌ %s 表不存在\n", tableName)
		}
	}

	fmt.Println("🎉 SQLite数据库初始化完成！")
}
