# AI文本游戏优化数据库表设计文档

**版本**: v4.0  
**设计日期**: 2025-01-08  
**基于**: 数据设计文档v3.0 + 数据库表使用场景分析报告v1.0  
**优化原则**: AI生成友好、世界观无关、结构简化、性能优化

## 设计概述

本文档基于深度使用场景分析的结果，对AI文本游戏数据库进行了全面优化。主要改进包括：

### 核心优化成果
- ✅ **简化事件处理系统**：从三表拆分简化为两表设计，降低查询复杂度
- ✅ **AI友好设计**：优先使用自然语言描述，减少数值化限制
- ✅ **世界观无关性**：移除特定世界观绑定字段，支持任意世界类型
- ✅ **标签系统替代枚举**：提供更大的灵活性和扩展性
- ✅ **计算字段优化**：将统计字段改为视图，避免数据一致性问题
- ✅ **JSONB结构优化**：平衡结构化程度，支持AI生成内容的多样性

### 设计原则

#### 1. AI生成友好设计
- **自然语言优先**：使用描述性字段替代数值化字段
- **软关联设计**：减少强制性外键约束，使用标识符引用
- **灵活结构**：JSONB字段支持AI生成的多样化内容
- **上下文丰富**：为AI提供充足的上下文信息

#### 2. 世界观无关性
- **通用抽象**：使用通用概念替代特定世界观概念
- **标签系统**：灵活的标签替代固定分类
- **配置驱动**：通过配置支持不同世界观需求
- **描述性时间**：相对时间概念替代绝对时间单位

#### 3. 结构简化
- **表数量优化**：合并功能相似的表，减少复杂关联
- **字段精简**：移除低频使用和冗余字段
- **计算字段**：统计数据改为视图或触发器计算
- **分层设计**：核心字段结构化，扩展字段灵活化

## 第一部分：用户系统模块

### 1. users表 - 用户基础信息表

**用途**：存储通过外部IDP认证的用户基本信息，支持多IDP集成和灵活的权限管理

```sql
-- 用户基础信息表（优化版）
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- IDP集成（高频使用）
    external_id VARCHAR(255) NOT NULL,
    external_provider VARCHAR(50) NOT NULL,
    
    -- 用户信息（中频使用）
    profile JSONB NOT NULL DEFAULT '{}', -- 用户档案信息
    preferences JSONB NOT NULL DEFAULT '{}', -- 用户偏好设置
    
    -- 权限和状态（高频使用）
    roles JSONB NOT NULL DEFAULT '["user"]', -- 用户角色权限
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, suspended, deleted
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE,
    
    -- 约束
    CONSTRAINT unique_external_user UNIQUE (external_id, external_provider)
);

-- 索引优化
CREATE INDEX idx_users_status ON users(status) WHERE status = 'active';
CREATE INDEX idx_users_provider ON users(external_provider, external_id);
CREATE INDEX idx_users_roles_gin ON users USING GIN (roles);
```

#### 字段详细说明

**profile字段结构（JSONB）**：
```json
{
  "display_name": "玩家昵称",
  "avatar": {
    "url": "https://example.com/avatar.jpg",
    "source": "external|uploaded|generated",
    "variants": {
      "small": "https://example.com/avatar_small.jpg",
      "large": "https://example.com/avatar_large.jpg"
    }
  },
  "locale": "zh-CN",
  "timezone": "Asia/Shanghai",
  "bio": "用户简介",
  "contact": {
    "email": "<EMAIL>",
    "verified": true
  }
}
```

**preferences字段结构（JSONB）**：
```json
{
  "ui": {
    "theme": "dark|light|auto",
    "font_size": "small|medium|large",
    "animations": true,
    "sound": true
  },
  "game": {
    "language": "zh-CN|en-US",
    "auto_save": true,
    "ai_speed": "fast|balanced|detailed",
    "content_filter": "none|mild|strict"
  },
  "privacy": {
    "profile_visibility": "public|friends|private",
    "activity_sharing": true
  },
  "notifications": {
    "game_updates": true,
    "system_messages": true,
    "social": false
  }
}
```

#### 使用场景说明
- **用户认证**（高频）：external_id + external_provider用于IDP集成
- **权限验证**（高频）：roles字段支持灵活的权限管理
- **个性化设置**（中频）：preferences字段存储用户偏好
- **用户档案**（中频）：profile字段存储用户基本信息

#### 优化说明
- **合并avatar_url到profile**：支持多种头像来源和尺寸
- **移除display_name独立字段**：合并到profile中，支持多语言
- **简化IDP集成**：保留核心字段，详细信息存储在profile中

### 2. user_sessions表 - 用户会话管理表

**用途**：管理用户的多角色会话状态，支持同一世界中的角色切换

```sql
-- 用户会话管理表（简化版）
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    
    -- 会话状态（高频使用）
    active_character_id UUID, -- 当前活跃角色（软引用）
    session_data JSONB NOT NULL DEFAULT '{}', -- 会话相关数据
    
    -- 时间管理
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 约束
    CONSTRAINT unique_user_world_session UNIQUE(user_id, world_id)
);

-- 索引优化
CREATE INDEX idx_user_sessions_active ON user_sessions(user_id, world_id, active_character_id);
CREATE INDEX idx_user_sessions_activity ON user_sessions(last_activity_at DESC);
```

#### session_data字段结构（JSONB）：
```json
{
  "character_preferences": {
    "character_id": {
      "ui_settings": {},
      "quick_actions": ["观察", "移动", "交谈"],
      "last_scene": "scene_uuid"
    }
  },
  "world_progress": {
    "discovered_scenes": ["scene1", "scene2"],
    "completed_goals": ["goal1"],
    "achievements": ["first_step", "explorer"]
  },
  "session_settings": {
    "auto_switch_character": false,
    "preferred_narrative_style": "detailed"
  }
}
```

## 第二部分：世界系统模块

### 3. worlds表 - 游戏世界表

**用途**：存储独立游戏世界的配置和状态，是游戏内容的顶层容器

```sql
-- 游戏世界表（优化版）
CREATE TABLE worlds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 基础信息（中频使用）
    name VARCHAR(200) NOT NULL,
    creator_id UUID NOT NULL REFERENCES users(id),
    
    -- 世界配置（高频使用）
    world_config JSONB NOT NULL DEFAULT '{}', -- 世界规则和设定
    world_state JSONB NOT NULL DEFAULT '{}', -- 当前世界状态
    
    -- 访问控制（中频使用）
    access_settings JSONB NOT NULL DEFAULT '{}', -- 访问设置
    
    -- 状态管理
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, paused, archived
    tags JSONB NOT NULL DEFAULT '[]', -- 世界标签
    
    -- 时间系统（高频使用）
    game_time BIGINT NOT NULL DEFAULT 0, -- 游戏内时间（分钟）
    time_config JSONB NOT NULL DEFAULT '{}', -- 时间配置
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_worlds_creator_status ON worlds(creator_id, status);
CREATE INDEX idx_worlds_tags_gin ON worlds USING GIN (tags);
CREATE INDEX idx_worlds_config_gin ON worlds USING GIN (world_config);
CREATE INDEX idx_worlds_public ON worlds((access_settings->>'is_public')) WHERE access_settings->>'is_public' = 'true';
```

#### world_config字段结构（JSONB）：
```json
{
  "theme": {
    "genre": "fantasy|sci-fi|historical|modern|custom",
    "mood": "light|dark|neutral|mixed",
    "complexity": "simple|medium|complex",
    "custom_elements": ["magic", "technology", "politics"]
  },
  "rules": {
    "narrative_style": "serious|humorous|dramatic|mixed",
    "content_rating": "family|teen|mature",
    "ai_creativity": "conservative|balanced|creative",
    "player_agency": "guided|balanced|sandbox"
  },
  "mechanics": {
    "death_handling": "temporary|permanent|respawn|custom",
    "resource_system": "none|simple|complex|custom",
    "social_system": "basic|advanced|custom",
    "progression_system": "trait-based|skill-based|story-based|custom"
  },
  "world_framework": {
    "size": "small|medium|large|unlimited",
    "regions": [
      {
        "name": "北方山脉",
        "description": "寒冷的山地区域",
        "characteristics": ["mountainous", "cold", "dangerous"]
      }
    ],
    "landmarks": [
      {
        "name": "王都",
        "region": "中央平原",
        "importance": "high",
        "description": "王国的政治中心"
      }
    ]
  }
}
```

#### world_state字段结构（JSONB）：
```json
{
  "current_time": {
    "day": 1,
    "season": "spring",
    "weather_pattern": "stable",
    "special_conditions": []
  },
  "global_events": [
    {
      "id": "event_uuid",
      "name": "王国庆典",
      "status": "active|pending|completed",
      "start_time": 1000,
      "affected_regions": ["中央平原"],
      "description": "全国性的庆祝活动"
    }
  ],
  "world_statistics": {
    "active_characters": 15,
    "completed_events": 23,
    "major_changes": 8,
    "last_major_event": "event_uuid"
  },
  "environmental_state": {
    "global_conditions": ["peaceful", "prosperous"],
    "resource_availability": {
      "general": "abundant|normal|scarce",
      "specific": {
        "food": "abundant",
        "materials": "normal"
      }
    }
  }
}
```

#### access_settings字段结构（JSONB）：
```json
{
  "is_public": false,
  "max_players": 10,
  "join_policy": "open|approval|invite",
  "player_permissions": {
    "can_create_characters": true,
    "can_modify_world": false,
    "can_invite_others": false
  },
  "content_sharing": {
    "allow_screenshots": true,
    "allow_story_export": true,
    "allow_world_fork": false
  }
}
```

#### time_config字段结构（JSONB）：
```json
{
  "time_multiplier": 1.0,
  "pause_when_empty": true,
  "day_night_cycle": true,
  "season_length_days": 30,
  "time_units": {
    "base_unit": "minutes",
    "display_format": "game_time",
    "real_time_ratio": "1:1"
  }
}
```

### 4. scenes表 - 场景表

**用途**：游戏世界的基本空间单位，每个场景都是独立的游戏区域容器

```sql
-- 场景表（优化版）
CREATE TABLE scenes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    
    -- 基础信息（高频使用）
    name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- 场景属性（高频使用）
    properties JSONB NOT NULL DEFAULT '{}', -- 场景属性和特征
    environment JSONB NOT NULL DEFAULT '{}', -- 环境信息
    connections JSONB NOT NULL DEFAULT '[]', -- 场景连接关系
    
    -- 分类和标签（中频使用）
    tags JSONB NOT NULL DEFAULT '[]', -- 场景标签（替代scene_type）
    access_rules JSONB NOT NULL DEFAULT '{}', -- 访问规则
    
    -- 状态管理
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, locked, hidden, destroyed
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_scenes_world_status ON scenes(world_id, status);
CREATE INDEX idx_scenes_tags_gin ON scenes USING GIN (tags);
CREATE INDEX idx_scenes_connections_gin ON scenes USING GIN (connections);
CREATE INDEX idx_scenes_properties_gin ON scenes USING GIN (properties);
```

#### properties字段结构（JSONB）：
```json
{
  "physical": {
    "size_description": "宽敞的大厅",
    "terrain_type": "indoor|outdoor|underground|aerial|aquatic",
    "elevation_description": "位于山腰",
    "climate_description": "温暖湿润"
  },
  "atmosphere": {
    "lighting": "明亮的阳光透过窗户洒进来",
    "sounds": "远处传来鸟儿的歌声",
    "smells": "空气中弥漫着花香",
    "mood": "宁静祥和"
  },
  "features": [
    {
      "name": "古老的石碑",
      "description": "刻满了古代文字的神秘石碑",
      "type": "landmark|resource|hazard|secret|interactive",
      "interaction_available": true,
      "significance": "这里记录着古老的传说"
    }
  ],
  "modifiers": {
    "movement_description": "地面平坦，行走轻松",
    "visibility_description": "视野开阔，一览无余",
    "special_effects": ["魔法增强", "时间流逝缓慢"]
  }
}
```

#### environment字段结构（JSONB）：
```json
{
  "current_conditions": {
    "weather": "晴朗的天空万里无云",
    "temperature": "温暖舒适",
    "wind": "微风轻拂",
    "special_phenomena": []
  },
  "time_variations": {
    "dawn": "晨光透过树叶洒下斑驳的光影",
    "day": "阳光明媚，生机勃勃",
    "dusk": "夕阳西下，天空染成金黄色",
    "night": "月光如水，星辰满天"
  },
  "seasonal_changes": {
    "spring": "万物复苏，花朵盛开",
    "summer": "绿意盎然，蝉鸣阵阵",
    "autumn": "叶子变黄，果实累累",
    "winter": "雪花纷飞，银装素裹"
  },
  "dynamic_elements": [
    {
      "type": "weather|wildlife|npcs|events",
      "description": "偶尔有商队经过",
      "frequency": "rare|occasional|common|constant",
      "conditions": ["白天", "晴天"]
    }
  ]
}
```

#### connections字段结构（JSONB）：
```json
[
  {
    "id": "connection_uuid",
    "target_scene_id": "scene_uuid",
    "connection_type": "bidirectional|unidirectional|conditional",
    
    "direction": {
      "from_description": "向北走向山路",
      "to_description": "从南方的小径进入村庄",
      "compass_direction": "north|south|east|west|up|down|portal"
    },
    
    "travel": {
      "description": "一条蜿蜒的山路通向远方",
      "difficulty": "轻松的步行",
      "time_description": "大约需要半小时",
      "requirements": []
    },
    
    "access_conditions": [
      {
        "type": "item|trait|time|event|reputation",
        "description": "需要持有村长的通行证",
        "required_value": "village_pass",
        "failure_message": "守卫阻止了你的去路"
      }
    ],
    
    "narrative": {
      "first_time": "你第一次踏上这条通往村庄的小径",
      "repeat_visit": "你再次走上熟悉的村庄小径",
      "contextual": {
        "weather_rain": "雨水让小径变得泥泞不堪",
        "time_night": "月光下的小径显得格外神秘"
      }
    },
    
    "dynamic_properties": {
      "is_temporary": false,
      "condition_description": "道路状况良好",
      "last_used": "2024-01-08T10:00:00Z",
      "usage_count": 15
    }
  }
]
```

#### access_rules字段结构（JSONB）：
```json
{
  "visibility": "public|restricted|hidden|secret",
  "entry_requirements": [
    {
      "type": "level|trait|item|quest|reputation",
      "description": "需要达到一定的声誉",
      "threshold": "respected",
      "failure_message": "你的声誉还不足以进入这里"
    }
  ],
  "capacity": {
    "description": "这里可以容纳很多人",
    "soft_limit": 20,
    "overflow_behavior": "crowded_description"
  },
  "special_rules": [
    {
      "rule": "no_combat",
      "description": "这里是和平区域，不允许战斗"
    }
  ]
}
```

#### 使用场景说明
- **场景生成**（中频）：AI按需生成新场景时创建记录
- **场景导航**（高频）：connections字段支持复杂的场景间移动
- **环境渲染**（高频）：properties和environment用于AI叙事生成
- **访问控制**（中频）：access_rules控制场景的可访问性

#### 优化说明
- **标签系统替代scene_type**：使用tags数组支持多标签组合
- **移除计数字段**：current_occupants改为基于characters表的计算
- **简化连接结构**：保留核心功能，简化过度复杂的嵌套结构
- **描述性属性**：使用自然语言描述替代数值化属性

## 第三部分：角色系统模块

### 5. characters表 - 角色表

**用途**：统一管理所有游戏角色（玩家角色和NPC），支持多角色用户和AI驱动的NPC

```sql
-- 角色表（优化版）
CREATE TABLE characters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id), -- NULL表示NPC
    
    -- 基础信息（高频使用）
    name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- 角色属性（高频使用）
    traits JSONB NOT NULL DEFAULT '[]', -- 特质列表
    characteristics JSONB NOT NULL DEFAULT '{}', -- 角色特征
    
    -- 位置和状态（高频使用）
    current_scene_id UUID, -- 软引用，避免强约束
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, inactive, dead, archived
    
    -- 角色管理（中频使用）
    character_type VARCHAR(20) NOT NULL DEFAULT 'player', -- player, npc, collective
    tags JSONB NOT NULL DEFAULT '[]', -- 角色标签
    
    -- 用户角色管理（中频使用）
    is_primary BOOLEAN DEFAULT false, -- 是否为用户主角色
    display_order INTEGER DEFAULT 0,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE
);

-- 索引优化
CREATE INDEX idx_characters_world_user ON characters(world_id, user_id);
CREATE INDEX idx_characters_scene_status ON characters(current_scene_id, status) WHERE status = 'active';
CREATE INDEX idx_characters_traits_gin ON characters USING GIN (traits);
CREATE INDEX idx_characters_tags_gin ON characters USING GIN (tags);
CREATE UNIQUE INDEX idx_characters_primary_unique ON characters(user_id, world_id, is_primary)
    WHERE is_primary = true;
```

#### traits字段结构（JSONB）：
```json
[
  {
    "id": "trait_uuid",
    "name": "勇敢的",
    "description": "面对危险时不会退缩",
    "category": "personality|physical|skill|social|magical|custom",
    "intensity": "weak|moderate|strong|extreme",
    "source": "initial|learned|event|item|curse|blessing",
    "conditions": {
      "active_when": ["facing_danger", "in_combat"],
      "inactive_when": ["safe_environment"],
      "modifies": ["courage_actions", "fear_resistance"]
    },
    "acquired_at": "2024-01-01T00:00:00Z",
    "last_used": "2024-01-08T10:00:00Z"
  }
]
```

#### characteristics字段结构（JSONB）：
```json
{
  "physical": {
    "appearance": "高大威猛的战士，留着浓密的胡须",
    "health_status": "身体健康，精力充沛",
    "distinctive_features": ["左臂有一道疤痕", "眼神坚毅"]
  },
  "mental": {
    "personality": "正直善良，但有时过于冲动",
    "mood": "心情愉快，对未来充满希望",
    "mental_state": "思维清晰，注意力集中",
    "fears": ["失去朋友", "背叛"],
    "desires": ["保护无辜", "寻找真相"]
  },
  "social": {
    "reputation": {
      "villagers": "深受村民爱戴和信任",
      "nobles": "在贵族中声誉一般",
      "merchants": "商人们认为他值得信赖"
    },
    "relationships": {
      "character_uuid": {
        "type": "friend|enemy|family|lover|rival|mentor|student",
        "description": "童年好友，彼此信任",
        "strength": "strong|moderate|weak",
        "last_interaction": "2024-01-08T10:00:00Z"
      }
    }
  },
  "background": {
    "origin": "出生在一个小村庄",
    "occupation": "村庄守卫",
    "life_events": [
      {
        "event": "拯救了村庄免受盗贼袭击",
        "impact": "获得了村民的尊敬",
        "date": "2024-01-01T00:00:00Z"
      }
    ]
  },
  "capabilities": {
    "known_skills": ["剑术", "骑马", "急救"],
    "languages": ["通用语", "精灵语"],
    "special_abilities": ["夜视", "危险感知"]
  }
}
```

#### 使用场景说明
- **角色创建**（中频）：用户创建角色或AI生成NPC时使用
- **位置追踪**（高频）：current_scene_id用于角色定位和场景查询
- **AI决策**（高频）：traits和characteristics为AI提供角色行为依据
- **角色切换**（中频）：is_primary支持多角色用户的主角色管理

#### 优化说明
- **软引用设计**：current_scene_id使用软引用，避免强约束限制AI生成
- **标签系统**：tags替代固定的character_type枚举
- **特质优化**：traits结构支持丰富的特质描述和条件系统
- **移除冗余字段**：去除basic_stats等低频使用字段

### 6. character_memories表 - 角色记忆表

**用途**：实现动态记忆系统，为AI提供角色记忆上下文，支持记忆衰减和关联

```sql
-- 角色记忆表（优化版）
CREATE TABLE character_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,

    -- 记忆内容（高频使用）
    content TEXT NOT NULL, -- 记忆内容描述
    memory_type VARCHAR(50) NOT NULL, -- event, person, location, item, knowledge, emotion

    -- 记忆强度系统（中频使用）
    importance_score FLOAT NOT NULL DEFAULT 0.5 CHECK (importance_score >= 0 AND importance_score <= 1),
    emotional_impact FLOAT NOT NULL DEFAULT 0.0 CHECK (emotional_impact >= -1 AND emotional_impact <= 1),
    current_strength FLOAT NOT NULL DEFAULT 1.0 CHECK (current_strength >= 0 AND current_strength <= 1),

    -- 记忆衰减（中频使用）
    decay_rate FLOAT NOT NULL DEFAULT 0.05 CHECK (decay_rate >= 0 AND decay_rate <= 1),
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 关联信息（低频使用）
    associated_entities JSONB DEFAULT '[]', -- 关联的实体ID
    tags JSONB DEFAULT '[]', -- 记忆标签
    context JSONB DEFAULT '{}', -- 记忆上下文信息

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_character_memories_character ON character_memories(character_id, importance_score DESC, created_at DESC);
CREATE INDEX idx_character_memories_type ON character_memories(character_id, memory_type, current_strength DESC);
CREATE INDEX idx_character_memories_tags_gin ON character_memories USING GIN (tags);
CREATE INDEX idx_character_memories_entities_gin ON character_memories USING GIN (associated_entities);
```

#### context字段结构（JSONB）：
```json
{
  "scene_id": "scene_uuid",
  "scene_name": "村庄广场",
  "participants": ["character_uuid1", "character_uuid2"],
  "time_context": {
    "game_time": 1440,
    "season": "spring",
    "time_of_day": "morning"
  },
  "emotional_context": {
    "character_mood": "excited",
    "atmosphere": "festive",
    "significance": "first_meeting"
  },
  "related_events": ["event_uuid1"],
  "memory_triggers": ["seeing_old_friend", "hearing_music"]
}
```

#### 使用场景说明
- **AI上下文构建**（高频）：为AI提供角色记忆上下文
- **记忆衰减处理**（中频）：定期更新记忆强度
- **记忆查询**（中频）：根据重要性和类型检索相关记忆
- **记忆关联**（低频）：通过associated_entities建立实体关联

#### 优化说明
- **移除access_count**：统计意义有限，简化字段结构
- **合并base_strength和current_strength**：避免冗余设计
- **优化关联系统**：使用JSONB数组存储关联实体ID

### 7. character_experiences表 - 角色阅历表

**用途**：记录角色在各领域的经验积累，支持技能判定、知识传承和声誉系统

```sql
-- 角色阅历表（简化版）
CREATE TABLE character_experiences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,

    -- 阅历分类（高频使用）
    tags JSONB NOT NULL DEFAULT '[]', -- 阅历标签（替代固定分类）
    description TEXT NOT NULL, -- 阅历描述

    -- 熟练度系统（高频使用）
    proficiency JSONB NOT NULL DEFAULT '{}', -- 熟练度信息

    -- 传承和影响（中频使用）
    learning_info JSONB DEFAULT '{}', -- 学习和传承信息
    social_impact JSONB DEFAULT '{}', -- 社交影响

    -- 应用效果（中频使用）
    effects JSONB DEFAULT '{}', -- 阅历效果

    -- 时间信息
    acquired_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_character_experiences_character ON character_experiences(character_id, last_used DESC);
CREATE INDEX idx_character_experiences_tags_gin ON character_experiences USING GIN (tags);
```

#### proficiency字段结构（JSONB）：
```json
{
  "level_description": "经验丰富的剑士",
  "numeric_level": 75, // 可选的数值表示
  "total_attempts": 100,
  "success_count": 80,
  "recent_performance": "最近表现出色",
  "improvement_trend": "steadily_improving|stable|declining",
  "mastery_indicators": [
    "能够在战斗中保持冷静",
    "掌握了高级剑术技巧"
  ]
}
```

#### learning_info字段结构（JSONB）：
```json
{
  "is_teachable": true,
  "teaching_difficulty": "需要长期练习才能掌握",
  "learned_from": {
    "character_id": "mentor_uuid",
    "character_name": "老剑师",
    "learning_method": "formal_training|observation|practice|experience"
  },
  "can_teach_to": ["有天赋的学生", "有耐心的人"],
  "teaching_history": [
    {
      "student_id": "student_uuid",
      "started_at": "2024-01-01T00:00:00Z",
      "progress": "学习进展良好"
    }
  ]
}
```

#### social_impact字段结构（JSONB）：
```json
{
  "reputation_effects": {
    "warriors": "在战士中享有很高声誉",
    "villagers": "村民们认为他是可靠的守护者",
    "nobles": "贵族们对他的技艺表示认可"
  },
  "relationship_modifiers": {
    "intimidation": "在谈判中更有威慑力",
    "respect": "获得同行的尊重",
    "trust": "人们更愿意信任他的判断"
  }
}
```

#### effects字段结构（JSONB）：
```json
{
  "passive_effects": [
    {
      "name": "战斗直觉",
      "description": "在战斗中能够预判敌人的攻击",
      "conditions": ["in_combat", "using_sword"]
    }
  ],
  "active_abilities": [
    {
      "name": "精准打击",
      "description": "能够瞄准敌人的弱点进行攻击",
      "usage_conditions": ["has_sword", "not_exhausted"],
      "cooldown": "需要休息后才能再次使用"
    }
  ],
  "knowledge_applications": [
    {
      "context": "weapon_evaluation",
      "description": "能够准确评估武器的质量和适用性"
    }
  ]
}
```

#### 使用场景说明
- **技能判定**（高频）：AI根据阅历判断行动成功率
- **角色成长**（中频）：更新熟练度和阅历描述
- **知识传承**（低频）：支持角色间的技能教学
- **声誉影响**（中频）：影响社交互动和NPC反应

#### 优化说明
- **大幅简化字段**：从38个字段减少到8个核心字段
- **标签系统**：使用tags替代固定的experience_category分类
- **JSONB整合**：将相关字段合并为逻辑分组的JSONB对象
- **描述性设计**：优先使用自然语言描述，数值作为可选补充

## 第四部分：实体系统模块

### 8. entities表 - 实体表

**用途**：统一管理游戏世界中的所有非角色对象，提供灵活的实体-特质模型

```sql
-- 实体表（优化版）
CREATE TABLE entities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,

    -- 基础信息（高频使用）
    name VARCHAR(200) NOT NULL,
    description TEXT,
    entity_type VARCHAR(50) NOT NULL, -- item, event, goal, environment, abstract

    -- 实体属性（高频使用）
    properties JSONB NOT NULL DEFAULT '{}', -- 实体属性
    traits JSONB NOT NULL DEFAULT '[]', -- 特质列表

    -- 位置信息（高频使用）- 三选一约束
    current_scene_id UUID, -- 在场景中
    owner_character_id UUID, -- 被角色拥有
    container_entity_id UUID, -- 在容器中

    -- 状态和标签（中频使用）
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, consumed, destroyed, hidden
    tags JSONB NOT NULL DEFAULT '[]', -- 实体标签

    -- 版本控制（低频使用）
    version INTEGER NOT NULL DEFAULT 1,

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 位置约束：三选一
    CONSTRAINT entities_single_location CHECK (
        (current_scene_id IS NOT NULL)::INTEGER +
        (owner_character_id IS NOT NULL)::INTEGER +
        (container_entity_id IS NOT NULL)::INTEGER <= 1
    )
);

-- 索引优化
CREATE INDEX idx_entities_world_type ON entities(world_id, entity_type, status);
CREATE INDEX idx_entities_scene ON entities(current_scene_id) WHERE current_scene_id IS NOT NULL;
CREATE INDEX idx_entities_owner ON entities(owner_character_id) WHERE owner_character_id IS NOT NULL;
CREATE INDEX idx_entities_container ON entities(container_entity_id) WHERE container_entity_id IS NOT NULL;
CREATE INDEX idx_entities_tags_gin ON entities USING GIN (tags);
CREATE INDEX idx_entities_properties_gin ON entities USING GIN (properties);
```

#### properties字段结构（JSONB）：
```json
{
  "physical": {
    "size_description": "手掌大小的精致物品",
    "weight_description": "轻如羽毛",
    "material_description": "由未知的金属制成",
    "condition_description": "保存完好，闪闪发光",
    "appearance": "表面刻有神秘的符文"
  },
  "functional": {
    "primary_function": "这是一把钥匙",
    "usage_description": "可以打开特定的锁",
    "interaction_methods": ["examine", "use", "combine"],
    "requirements": ["需要一定的力量才能使用"]
  },
  "value": {
    "rarity_description": "极其罕见的物品",
    "significance": "对某些人来说具有重要意义",
    "cultural_value": "在当地文化中被视为神圣物品"
  },
  "magical": {
    "is_magical": true,
    "magic_description": "散发着微弱的蓝色光芒",
    "magic_effects": ["提供微弱的照明", "在危险时会发出警告"]
  },
  "world_specific": {
    // 世界观特定的属性
    "fantasy_properties": {
      "enchantment_level": "minor",
      "magical_school": "divination"
    }
  }
}
```

#### traits字段结构（JSONB）：
```json
[
  {
    "name": "古老的",
    "description": "这个物品有着悠久的历史",
    "category": "temporal|magical|physical|social|functional",
    "effects": ["增加历史价值", "可能触发特殊事件"],
    "conditions": ["在古迹中更有效果"]
  },
  {
    "name": "神秘的",
    "description": "充满了未知的力量",
    "category": "magical",
    "effects": ["吸引魔法敏感者的注意", "在魔法检测中会被发现"],
    "conditions": ["在魔法环境中效果增强"]
  }
]
```

#### 使用场景说明
- **统一实体管理**（高频）：所有非角色对象的统一入口
- **位置追踪**（高频）：三种位置关系的灵活管理
- **AI推理**（高频）：properties和traits为AI提供实体信息
- **实体演化**（中频）：version字段支持实体变化追踪

#### 优化说明
- **描述性属性**：使用自然语言描述替代数值化属性
- **灵活特质系统**：traits支持任意特质组合
- **软引用位置**：位置字段使用软引用，避免强约束
- **标签分类**：tags替代固定的分类枚举

### 9. specialized_entities表 - 专门实体扩展表

**用途**：为特定类型的实体提供专门的属性扩展，保持主表简洁的同时支持复杂功能

```sql
-- 专门实体扩展表（新设计）
CREATE TABLE specialized_entities (
    entity_id UUID PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
    specialization_type VARCHAR(50) NOT NULL, -- item, event, goal, location, abstract

    -- 专门属性（中频使用）
    specialized_data JSONB NOT NULL DEFAULT '{}', -- 类型特定的数据

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_specialized_entities_type ON specialized_entities(specialization_type);
CREATE INDEX idx_specialized_entities_data_gin ON specialized_entities USING GIN (specialized_data);
```

#### 物品类型specialized_data结构：
```json
{
  "item_properties": {
    "category_tags": ["weapon", "sword", "one-handed"],
    "usage": {
      "primary_use": "战斗武器",
      "secondary_uses": ["仪式道具", "身份象征"],
      "skill_requirements": ["基础剑术", "足够的力量"]
    },
    "durability": {
      "condition_description": "锋利如新",
      "wear_indicators": ["刀刃上有轻微的使用痕迹"],
      "maintenance_needs": ["定期保养", "避免接触腐蚀性物质"]
    },
    "crafting": {
      "origin": "由著名铁匠打造",
      "materials": ["优质钢铁", "皮革握柄", "银质装饰"],
      "crafting_quality": "大师级工艺",
      "can_be_modified": true,
      "modification_options": ["附魔", "重新锻造", "装饰升级"]
    }
  }
}
```

#### 事件类型specialized_data结构：
```json
{
  "event_properties": {
    "category_tags": ["social", "negotiation", "conflict_resolution"],
    "trigger_system": {
      "trigger_description": "当两个角色发生争执时触发",
      "conditions": ["characters_in_conflict", "scene_allows_negotiation"],
      "probability": "在合适条件下很可能发生"
    },
    "execution": {
      "duration_description": "可能持续几分钟到几小时",
      "participant_requirements": "需要至少两个有争议的角色",
      "resolution_methods": ["peaceful_negotiation", "compromise", "mediation"]
    },
    "outcomes": {
      "success_description": "争执得到和平解决，关系可能改善",
      "failure_description": "争执升级，可能导致更严重的冲突",
      "side_effects": ["影响其他角色对参与者的看法", "可能创造新的联盟或敌对关系"]
    },
    "repeatability": {
      "can_repeat": true,
      "cooldown_description": "同样的争执不会立即再次发生",
      "variation_potential": "每次发生都可能有不同的细节和结果"
    }
  }
}
```

#### 目标类型specialized_data结构：
```json
{
  "goal_properties": {
    "category_tags": ["personal", "long_term", "character_development"],
    "objective": {
      "main_description": "成为村庄最受尊敬的守护者",
      "sub_objectives": [
        "保护村民免受威胁",
        "提升战斗技能",
        "获得村民的信任和尊重"
      ]
    },
    "progress": {
      "current_description": "已经获得了一些村民的认可",
      "milestones": [
        {
          "description": "成功击退盗贼袭击",
          "status": "completed",
          "impact": "大幅提升了声誉"
        },
        {
          "description": "掌握高级剑术",
          "status": "in_progress",
          "progress_description": "正在向老剑师学习"
        }
      ]
    },
    "completion": {
      "success_criteria": "获得村长的正式认可和村民的一致尊敬",
      "measurement_method": "通过NPC反应和声誉系统评估",
      "estimated_timeline": "可能需要数月的努力"
    },
    "motivation": {
      "character_reasons": ["保护家园的责任感", "对正义的追求"],
      "external_pressures": ["村庄面临的威胁", "村民的期望"],
      "personal_growth": "通过这个目标实现自我价值"
    }
  }
}
```

#### 优化说明
- **统一扩展机制**：替代原来的items、events、goals三个独立表
- **类型特定数据**：specialized_data根据类型存储不同结构的数据
- **减少表数量**：简化数据库结构，减少JOIN查询
- **保持灵活性**：JSONB结构支持各种类型的专门属性

## 第五部分：事件处理系统模块

### 10. game_events表 - 游戏事件日志表

**用途**：记录游戏中发生的所有事件，提供完整的游戏历史和叙事内容

```sql
-- 游戏事件日志表（优化版）
CREATE TABLE game_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,

    -- 事件分类（高频使用）
    event_type VARCHAR(50) NOT NULL, -- action, evolution, system, heartbeat
    tags JSONB NOT NULL DEFAULT '[]', -- 事件标签

    -- 参与者信息（高频使用）
    primary_actor_id UUID, -- 主要触发者（软引用）
    participants JSONB NOT NULL DEFAULT '[]', -- 所有参与者
    scene_id UUID, -- 发生场景（软引用）

    -- 事件内容（高频使用）
    narrative_text TEXT, -- AI生成的叙事文本
    event_data JSONB NOT NULL DEFAULT '{}', -- 事件详细数据

    -- 处理结果（高频使用）
    processing_result JSONB DEFAULT '{}', -- AI处理结果（合并自event_processing_results）

    -- 时间信息（中频使用）
    game_time BIGINT NOT NULL, -- 游戏内时间
    processing_duration_ms INTEGER, -- 处理耗时

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_game_events_world_time ON game_events(world_id, game_time DESC);
CREATE INDEX idx_game_events_type ON game_events(world_id, event_type, created_at DESC);
CREATE INDEX idx_game_events_actor ON game_events(primary_actor_id, created_at DESC) WHERE primary_actor_id IS NOT NULL;
CREATE INDEX idx_game_events_scene ON game_events(scene_id, created_at DESC) WHERE scene_id IS NOT NULL;
CREATE INDEX idx_game_events_tags_gin ON game_events USING GIN (tags);

-- 分区策略（按月分区）
CREATE TABLE game_events_y2024m01 PARTITION OF game_events
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

#### participants字段结构（JSONB）：
```json
[
  {
    "character_id": "character_uuid",
    "character_name": "勇敢的战士",
    "role": "primary|secondary|observer|affected",
    "participation_type": "active|passive|indirect"
  }
]
```

#### event_data字段结构（JSONB）：
```json
{
  "action": {
    "type": "movement|interaction|combat|social|exploration",
    "description": "角色尝试与神秘商人交谈",
    "intent": "获取信息",
    "context": "在村庄广场遇到了陌生的商人"
  },
  "environment": {
    "scene_name": "村庄广场",
    "conditions": ["sunny", "crowded", "festive"],
    "relevant_entities": ["mysterious_merchant", "market_stalls"]
  },
  "outcome": {
    "success_level": "partial_success|success|failure|critical_success",
    "description": "商人愿意分享一些信息，但要求回报",
    "consequences": ["gained_information", "new_quest_opportunity", "reputation_change"]
  }
}
```

#### processing_result字段结构（JSONB）：
```json
{
  "ai_processing": {
    "model_used": "gemini-pro",
    "processing_time_ms": 1500,
    "token_usage": 250,
    "request_id": "ai_request_uuid"
  },
  "state_changes": [
    {
      "type": "character_update",
      "target_id": "character_uuid",
      "changes": {
        "current_scene_id": "new_scene_uuid",
        "traits": ["add:好奇的", "remove:谨慎的"]
      }
    },
    {
      "type": "memory_creation",
      "target_id": "character_uuid",
      "memory_content": "遇到了一个神秘的商人，他似乎知道很多秘密"
    }
  ],
  "narrative_generation": {
    "style": "detailed",
    "perspective": "third_person",
    "mood": "mysterious",
    "length": "medium"
  }
}
```

#### 使用场景说明
- **事件记录**（高频）：记录所有游戏事件，提供完整历史
- **叙事展示**（高频）：narrative_text用于前端显示
- **AI处理追踪**（中频）：processing_result记录AI处理详情
- **性能监控**（低频）：processing_duration_ms用于性能分析

#### 优化说明
- **合并处理结果**：将event_processing_results表的核心功能合并进来
- **移除event_processing_steps表**：过度详细的步骤记录改为日志系统
- **软引用设计**：参与者和场景使用软引用，避免强约束
- **分区策略**：按时间分区提高查询性能

### 11. ai_interactions表 - AI交互记录表

**用途**：记录所有AI接口调用，支持成本控制、性能监控和调试分析

```sql
-- AI交互记录表（优化版）
CREATE TABLE ai_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- 请求信息（高频使用）
    interaction_type VARCHAR(50) NOT NULL, -- scene_generation, character_behavior, world_evolution
    world_id UUID, -- 软引用
    user_id UUID, -- 软引用

    -- AI调用详情（中频使用）
    request_data JSONB NOT NULL DEFAULT '{}', -- 请求数据
    response_data JSONB DEFAULT '{}', -- 响应数据

    -- 性能和成本（中频使用）
    performance_metrics JSONB NOT NULL DEFAULT '{}', -- 性能指标

    -- 状态管理
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, completed, failed, timeout
    error_info JSONB DEFAULT '{}', -- 错误信息

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 索引优化
CREATE INDEX idx_ai_interactions_type_status ON ai_interactions(interaction_type, status, created_at DESC);
CREATE INDEX idx_ai_interactions_world ON ai_interactions(world_id, created_at DESC) WHERE world_id IS NOT NULL;
CREATE INDEX idx_ai_interactions_performance ON ai_interactions((performance_metrics->>'response_time_ms')::INTEGER) WHERE status = 'completed';

-- 分区策略（按周分区，快速增长的表）
CREATE TABLE ai_interactions_y2024w01 PARTITION OF ai_interactions
FOR VALUES FROM ('2024-01-01') TO ('2024-01-08');
```

#### request_data字段结构（JSONB）：
```json
{
  "prompt": "请生成一个神秘森林场景...",
  "system_instruction": "你是一个专业的游戏世界设计师...",
  "context": {
    "world_theme": "fantasy",
    "adjacent_scenes": ["village", "mountain_path"],
    "character_location": "approaching_from_south"
  },
  "parameters": {
    "creativity_level": "balanced",
    "response_length": "detailed",
    "include_entities": true
  },
  "schema_version": "v2.1"
}
```

#### response_data字段结构（JSONB）：
```json
{
  "generated_content": {
    "scene_name": "幽暗森林",
    "description": "古老的树木遮天蔽日...",
    "properties": {...},
    "connections": [...]
  },
  "metadata": {
    "generation_quality": "high",
    "content_safety": "safe",
    "coherence_score": 0.95
  }
}
```

#### performance_metrics字段结构（JSONB）：
```json
{
  "response_time_ms": 1500,
  "token_usage": {
    "input_tokens": 200,
    "output_tokens": 300,
    "total_tokens": 500
  },
  "cost_estimate": {
    "currency": "USD",
    "amount": 0.025
  },
  "quality_metrics": {
    "response_completeness": 1.0,
    "schema_compliance": 1.0,
    "content_relevance": 0.95
  }
}
```

#### error_info字段结构（JSONB）：
```json
{
  "error_type": "timeout|api_error|validation_error|rate_limit",
  "error_message": "API响应超时",
  "error_code": "TIMEOUT_ERROR",
  "retry_count": 2,
  "max_retries": 3,
  "recovery_action": "scheduled_retry"
}
```

#### 使用场景说明
- **AI调用记录**（高频）：记录所有AI接口调用详情
- **成本控制**（中频）：token_usage用于成本统计和预算控制
- **性能监控**（中频）：response_time用于性能分析和优化
- **调试分析**（低频）：request_data和response_data用于问题排查

#### 优化说明
- **数据归档策略**：按周分区，定期归档历史数据
- **软引用设计**：world_id和user_id使用软引用
- **性能优化**：针对高频查询场景优化索引
- **成本监控**：详细的token使用和成本统计

## 第六部分：计算视图和辅助表

### 12. 计算视图设计

**用途**：将原来的计数字段改为计算视图，避免数据一致性问题

```sql
-- 世界统计视图
CREATE VIEW world_stats AS
SELECT
    w.id as world_id,
    w.name as world_name,
    COUNT(DISTINCT c.user_id) FILTER (WHERE c.status = 'active') as current_players,
    COUNT(DISTINCT c.id) FILTER (WHERE c.status = 'active') as active_characters,
    COUNT(DISTINCT s.id) as total_scenes,
    COUNT(DISTINCT e.id) FILTER (WHERE e.status = 'active') as active_entities,
    MAX(ge.game_time) as latest_game_time,
    COUNT(ge.id) FILTER (WHERE ge.created_at >= NOW() - INTERVAL '24 hours') as recent_events
FROM worlds w
LEFT JOIN characters c ON w.id = c.world_id
LEFT JOIN scenes s ON w.id = s.world_id
LEFT JOIN entities e ON w.id = e.world_id
LEFT JOIN game_events ge ON w.id = ge.world_id
WHERE w.status = 'active'
GROUP BY w.id, w.name;

-- 场景统计视图
CREATE VIEW scene_stats AS
SELECT
    s.id as scene_id,
    s.name as scene_name,
    s.world_id,
    COUNT(DISTINCT c.id) FILTER (WHERE c.status = 'active') as current_occupants,
    COUNT(DISTINCT e.id) FILTER (WHERE e.status = 'active') as active_entities,
    COUNT(ge.id) FILTER (WHERE ge.created_at >= NOW() - INTERVAL '1 hour') as recent_activity
FROM scenes s
LEFT JOIN characters c ON s.id = c.current_scene_id
LEFT JOIN entities e ON s.id = e.current_scene_id
LEFT JOIN game_events ge ON s.id = ge.scene_id
WHERE s.status = 'active'
GROUP BY s.id, s.name, s.world_id;

-- 角色活跃度视图
CREATE VIEW character_activity AS
SELECT
    c.id as character_id,
    c.name as character_name,
    c.world_id,
    c.current_scene_id,
    COUNT(ge.id) FILTER (WHERE ge.created_at >= NOW() - INTERVAL '24 hours') as daily_actions,
    COUNT(cm.id) as total_memories,
    COUNT(ce.id) as total_experiences,
    MAX(ge.created_at) as last_action_time
FROM characters c
LEFT JOIN game_events ge ON c.id = ge.primary_actor_id
LEFT JOIN character_memories cm ON c.id = cm.character_id
LEFT JOIN character_experiences ce ON c.id = ce.character_id
WHERE c.status = 'active'
GROUP BY c.id, c.name, c.world_id, c.current_scene_id;

-- AI性能统计视图
CREATE MATERIALIZED VIEW ai_performance_stats AS
SELECT
    interaction_type,
    DATE(created_at) as date,
    COUNT(*) as total_calls,
    COUNT(*) FILTER (WHERE status = 'completed') as successful_calls,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_calls,
    AVG((performance_metrics->>'response_time_ms')::INTEGER) as avg_response_time,
    SUM((performance_metrics->'token_usage'->>'total_tokens')::INTEGER) as total_tokens,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY (performance_metrics->>'response_time_ms')::INTEGER) as p95_response_time
FROM ai_interactions
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY interaction_type, DATE(created_at);

-- 定期刷新物化视图
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY ai_performance_stats;
END;
$$ LANGUAGE plpgsql;

-- 每小时刷新一次
SELECT cron.schedule('refresh-stats', '0 * * * *', 'SELECT refresh_materialized_views();');
```

### 13. 数据一致性触发器

**用途**：维护数据一致性，自动处理记忆衰减等业务逻辑

```sql
-- 记忆衰减触发器
CREATE OR REPLACE FUNCTION decay_memory_strength()
RETURNS void AS $$
BEGIN
    UPDATE character_memories
    SET
        current_strength = GREATEST(
            current_strength * (1 - decay_rate * EXTRACT(EPOCH FROM NOW() - last_accessed) / 86400),
            0.1
        ),
        updated_at = NOW()
    WHERE last_accessed < NOW() - INTERVAL '1 day'
      AND current_strength > 0.1;
END;
$$ LANGUAGE plpgsql;

-- 实体版本更新触发器
CREATE OR REPLACE FUNCTION update_entity_version()
RETURNS TRIGGER AS $$
BEGIN
    NEW.version = OLD.version + 1;
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_entity_version_update
    BEFORE UPDATE ON entities
    FOR EACH ROW EXECUTE FUNCTION update_entity_version();

-- 角色最后活动时间更新触发器
CREATE OR REPLACE FUNCTION update_character_activity()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE characters
    SET last_active_at = NOW()
    WHERE id = NEW.primary_actor_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_character_activity_update
    AFTER INSERT ON game_events
    FOR EACH ROW
    WHEN (NEW.primary_actor_id IS NOT NULL)
    EXECUTE FUNCTION update_character_activity();
```

## 第七部分：数据迁移方案

### 从当前设计到优化设计的迁移策略

#### 阶段1：表结构迁移（高优先级）

```sql
-- 1. 创建新的优化表结构
-- （使用上述所有CREATE TABLE语句）

-- 2. 数据迁移脚本
-- 迁移用户数据
INSERT INTO users (id, external_id, external_provider, profile, preferences, roles, status, created_at, updated_at, last_active_at)
SELECT
    id,
    external_id,
    external_provider,
    jsonb_build_object(
        'display_name', display_name,
        'avatar', jsonb_build_object('url', avatar_url, 'source', 'external'),
        'locale', 'zh-CN',
        'contact', jsonb_build_object('email', email, 'verified', true)
    ) as profile,
    COALESCE(preferences, '{}') as preferences,
    COALESCE(game_roles, '["user"]') as roles,
    status,
    created_at,
    updated_at,
    last_login_at
FROM old_users;

-- 迁移世界数据
INSERT INTO worlds (id, name, creator_id, world_config, world_state, access_settings, status, tags, game_time, time_config, created_at, updated_at)
SELECT
    id,
    name,
    creator_id,
    jsonb_build_object(
        'theme', jsonb_build_object('genre', 'fantasy', 'mood', 'neutral'),
        'rules', world_config->'world_rules',
        'mechanics', jsonb_build_object('death_handling', 'temporary'),
        'world_framework', jsonb_build_object('size', 'medium')
    ) as world_config,
    COALESCE(world_state, '{}') as world_state,
    jsonb_build_object(
        'is_public', is_public,
        'max_players', max_players,
        'join_policy', CASE WHEN is_public THEN 'open' ELSE 'invite' END
    ) as access_settings,
    status,
    '[]'::jsonb as tags,
    game_time,
    jsonb_build_object('time_multiplier', 1.0, 'pause_when_empty', true) as time_config,
    created_at,
    updated_at
FROM old_worlds;

-- 迁移场景数据
INSERT INTO scenes (id, world_id, name, description, properties, environment, connections, tags, access_rules, status, created_at, updated_at)
SELECT
    id,
    world_id,
    name,
    description,
    COALESCE(properties, '{}') as properties,
    COALESCE(environment, '{}') as environment,
    COALESCE(connections, '[]') as connections,
    CASE
        WHEN scene_type IS NOT NULL THEN jsonb_build_array(scene_type)
        ELSE '[]'::jsonb
    END as tags,
    jsonb_build_object(
        'visibility', CASE access_level WHEN 'public' THEN 'public' ELSE 'restricted' END,
        'capacity', jsonb_build_object('description', '可以容纳多人', 'soft_limit', max_occupants)
    ) as access_rules,
    CASE scene_state WHEN 'active' THEN 'active' ELSE 'locked' END as status,
    created_at,
    updated_at
FROM old_scenes;
```

#### 阶段2：数据清理和优化（中优先级）

```sql
-- 1. 移除废弃的表
DROP TABLE IF EXISTS event_processing_steps;
DROP TABLE IF EXISTS user_stats; -- 数据迁移到视图
DROP TABLE IF EXISTS world_frameworks; -- 数据合并到worlds.world_config
DROP TABLE IF EXISTS player_allocations; -- 简化为配置数据

-- 2. 创建计算视图
-- （使用上述所有CREATE VIEW语句）

-- 3. 设置定期维护任务
SELECT cron.schedule('memory-decay', '0 2 * * *', 'SELECT decay_memory_strength();');
SELECT cron.schedule('refresh-stats', '0 * * * *', 'SELECT refresh_materialized_views();');
```

#### 阶段3：性能优化（低优先级）

```sql
-- 1. 创建分区表
-- （按照上述分区策略）

-- 2. 优化索引
-- （使用上述所有CREATE INDEX语句）

-- 3. 设置数据归档
CREATE OR REPLACE FUNCTION archive_old_data()
RETURNS void AS $$
BEGIN
    -- 归档30天前的游戏事件
    WITH archived_events AS (
        DELETE FROM game_events
        WHERE created_at < NOW() - INTERVAL '30 days'
        RETURNING *
    )
    INSERT INTO game_events_archive SELECT * FROM archived_events;

    -- 归档7天前的AI交互记录
    WITH archived_interactions AS (
        DELETE FROM ai_interactions
        WHERE created_at < NOW() - INTERVAL '7 days'
        RETURNING *
    )
    INSERT INTO ai_interactions_archive SELECT * FROM archived_interactions;
END;
$$ LANGUAGE plpgsql;

SELECT cron.schedule('archive-data', '0 3 * * 0', 'SELECT archive_old_data();');
```

## 第八部分：总结和最佳实践

### 优化成果总结

#### 1. 表数量优化
- **原设计**：18个核心表 + 多个辅助表
- **优化后**：11个核心表 + 计算视图
- **减少比例**：约40%的表数量减少

#### 2. 字段复杂度优化
- **character_experiences**：从38个字段减少到8个字段
- **worlds**：合并相关配置到JSONB字段
- **scenes**：使用标签系统替代枚举字段

#### 3. AI友好性提升
- **描述性字段**：优先使用自然语言描述
- **软引用设计**：减少强制约束，提高灵活性
- **JSONB结构**：支持AI生成内容的多样性

#### 4. 世界观无关性
- **标签系统**：替代固定分类枚举
- **通用概念**：移除特定世界观绑定字段
- **配置驱动**：通过world_config支持不同世界观

### 最佳实践建议

#### 1. 开发实施
```sql
-- 使用事务确保数据一致性
BEGIN;
-- 批量操作
COMMIT;

-- 使用预编译语句提高性能
PREPARE get_character_context AS
SELECT c.*, s.name as scene_name, s.properties as scene_properties
FROM characters c
JOIN scenes s ON c.current_scene_id = s.id
WHERE c.id = $1;
```

#### 2. 监控和维护
```sql
-- 定期检查数据质量
SELECT check_data_anomalies();

-- 监控查询性能
SELECT * FROM pg_stat_statements
WHERE query LIKE '%characters%'
ORDER BY total_time DESC;
```

#### 3. 扩展策略
- **新增字段**：优先使用JSONB扩展
- **新增表**：遵循软引用和标签系统原则
- **性能优化**：基于实际使用模式调整索引

这个优化的数据库设计为AI文本游戏提供了更好的灵活性、性能和可维护性，同时保持了对游戏核心循环的完整支持。
```
```
