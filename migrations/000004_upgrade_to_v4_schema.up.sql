-- 升级到数据库设计文档v4.0架构
-- 这个迁移脚本将现有数据库升级到新的优化架构

-- =====================================================
-- 阶段1：创建新的独立表
-- =====================================================

-- 1. 创建用户会话管理表
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,

    -- 会话状态（高频使用）
    active_character_id UUID, -- 当前活跃角色（软引用）
    session_data JSONB NOT NULL DEFAULT '{}', -- 会话相关数据

    -- 时间管理
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 约束
    CONSTRAINT unique_user_world_session UNIQUE(user_id, world_id)
);

-- 添加注释
COMMENT ON TABLE user_sessions IS '用户会话管理表 - 跟踪用户在不同世界中的会话状态';
COMMENT ON COLUMN user_sessions.active_character_id IS '当前活跃的角色ID（软引用）';
COMMENT ON COLUMN user_sessions.session_data IS '会话相关数据，包括UI状态、临时设置等';

-- 2. 创建角色记忆表
CREATE TABLE character_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,

    -- 记忆内容（高频使用）
    content TEXT NOT NULL, -- 记忆内容描述
    memory_type VARCHAR(50) NOT NULL, -- event, person, location, item, knowledge, emotion

    -- 记忆强度系统（中频使用）
    importance_score FLOAT NOT NULL DEFAULT 0.5 CHECK (importance_score >= 0 AND importance_score <= 1),
    emotional_impact FLOAT NOT NULL DEFAULT 0.0 CHECK (emotional_impact >= -1 AND emotional_impact <= 1),
    current_strength FLOAT NOT NULL DEFAULT 1.0 CHECK (current_strength >= 0 AND current_strength <= 1),

    -- 记忆衰减（中频使用）
    decay_rate FLOAT NOT NULL DEFAULT 0.05 CHECK (decay_rate >= 0 AND decay_rate <= 1),
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 关联信息（低频使用）
    associated_entities JSONB DEFAULT '[]', -- 关联的实体ID
    tags JSONB DEFAULT '[]', -- 记忆标签
    context JSONB DEFAULT '{}', -- 记忆上下文信息

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 添加注释
COMMENT ON TABLE character_memories IS '角色记忆表 - 存储角色的记忆信息，支持记忆强度和衰减机制';
COMMENT ON COLUMN character_memories.memory_type IS '记忆类型：event(事件), person(人物), location(地点), item(物品), knowledge(知识), emotion(情感)';
COMMENT ON COLUMN character_memories.importance_score IS '重要性评分 (0-1)，影响记忆的保留和检索优先级';
COMMENT ON COLUMN character_memories.emotional_impact IS '情感影响 (-1到1)，负值表示负面情感，正值表示正面情感';
COMMENT ON COLUMN character_memories.current_strength IS '当前记忆强度 (0-1)，会随时间衰减';
COMMENT ON COLUMN character_memories.decay_rate IS '衰减率 (0-1)，控制记忆强度的衰减速度';

-- 3. 创建角色阅历表
CREATE TABLE character_experiences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,

    -- 阅历分类（高频使用）
    tags JSONB NOT NULL DEFAULT '[]', -- 阅历标签（替代固定分类）
    description TEXT NOT NULL, -- 阅历描述

    -- 熟练度系统（高频使用）
    proficiency JSONB NOT NULL DEFAULT '{}', -- 熟练度信息

    -- 传承和影响（中频使用）
    learning_info JSONB DEFAULT '{}', -- 学习和传承信息
    social_impact JSONB DEFAULT '{}', -- 社交影响

    -- 应用效果（中频使用）
    effects JSONB DEFAULT '{}', -- 阅历效果

    -- 时间信息
    acquired_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 添加注释
COMMENT ON TABLE character_experiences IS '角色阅历表 - 存储角色的技能、知识和经验';
COMMENT ON COLUMN character_experiences.tags IS '阅历标签，用于分类和检索（如：combat, magic, social, crafting等）';
COMMENT ON COLUMN character_experiences.proficiency IS '熟练度信息，包括等级、经验值、专精方向等';
COMMENT ON COLUMN character_experiences.learning_info IS '学习信息，包括师傅、学习方式、传承关系等';
COMMENT ON COLUMN character_experiences.social_impact IS '社交影响，包括声誉、关系网络等';

-- 4. 创建专门实体扩展表
CREATE TABLE specialized_entities (
    entity_id UUID PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
    specialization_type VARCHAR(50) NOT NULL, -- item, event, goal, location, abstract

    -- 专门属性（中频使用）
    specialized_data JSONB NOT NULL DEFAULT '{}', -- 类型特定的数据

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 添加注释
COMMENT ON TABLE specialized_entities IS '专门实体扩展表 - 为不同类型的实体提供特定的属性扩展';
COMMENT ON COLUMN specialized_entities.specialization_type IS '专门化类型：item(物品), event(事件), goal(目标), location(地点), abstract(抽象概念)';
COMMENT ON COLUMN specialized_entities.specialized_data IS '类型特定的数据，根据specialization_type包含不同的结构';

-- 5. 创建游戏事件日志表（替代原events表）
CREATE TABLE game_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,

    -- 事件分类（高频使用）
    event_type VARCHAR(50) NOT NULL, -- action, evolution, system, heartbeat
    tags JSONB NOT NULL DEFAULT '[]', -- 事件标签

    -- 参与者信息（高频使用）
    primary_actor_id UUID, -- 主要触发者（软引用）
    participants JSONB NOT NULL DEFAULT '[]', -- 所有参与者
    scene_id UUID, -- 发生场景（软引用）

    -- 事件内容（高频使用）
    narrative_text TEXT, -- AI生成的叙事文本
    event_data JSONB NOT NULL DEFAULT '{}', -- 事件详细数据

    -- 处理结果（高频使用）
    processing_result JSONB DEFAULT '{}', -- AI处理结果

    -- 时间信息（中频使用）
    game_time BIGINT NOT NULL, -- 游戏内时间
    processing_duration_ms INTEGER, -- 处理耗时

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 添加注释
COMMENT ON TABLE game_events IS '游戏事件日志表 - 记录游戏中发生的所有事件';
COMMENT ON COLUMN game_events.event_type IS '事件类型：action(行动), evolution(演化), system(系统), heartbeat(心跳)';
COMMENT ON COLUMN game_events.primary_actor_id IS '主要触发者ID（可以是角色或实体）';
COMMENT ON COLUMN game_events.participants IS '所有参与者的ID列表';
COMMENT ON COLUMN game_events.narrative_text IS 'AI生成的叙事文本，用于向玩家展示';
COMMENT ON COLUMN game_events.game_time IS '游戏内时间戳（毫秒）';

-- =====================================================
-- 阶段2：为现有表添加新字段
-- =====================================================

-- 1. 扩展users表
ALTER TABLE users ADD COLUMN profile JSONB DEFAULT '{}';
ALTER TABLE users ADD COLUMN last_active_at TIMESTAMP WITH TIME ZONE;

-- 添加注释
COMMENT ON COLUMN users.profile IS '用户档案信息，包括显示名称、头像、语言偏好等';
COMMENT ON COLUMN users.last_active_at IS '用户最后活跃时间';

-- 2. 扩展worlds表
ALTER TABLE worlds ADD COLUMN access_settings JSONB DEFAULT '{}';
ALTER TABLE worlds ADD COLUMN tags JSONB DEFAULT '[]';
ALTER TABLE worlds ADD COLUMN time_config JSONB DEFAULT '{}';

-- 添加注释
COMMENT ON COLUMN worlds.access_settings IS '访问设置，包括公开性、最大玩家数、加入策略等';
COMMENT ON COLUMN worlds.tags IS '世界标签，用于分类和搜索';
COMMENT ON COLUMN worlds.time_config IS '时间配置，包括时间倍率、暂停设置等';

-- 3. 扩展scenes表
ALTER TABLE scenes ADD COLUMN tags JSONB DEFAULT '[]';
ALTER TABLE scenes ADD COLUMN access_rules JSONB DEFAULT '{}';
ALTER TABLE scenes ADD COLUMN connections JSONB DEFAULT '[]';

-- 添加注释
COMMENT ON COLUMN scenes.tags IS '场景标签，替代原来的scene_type字段';
COMMENT ON COLUMN scenes.access_rules IS '访问规则，包括可见性、进入要求等';
COMMENT ON COLUMN scenes.connections IS '场景连接信息，包含详细的连接描述和规则';

-- 4. 扩展characters表
ALTER TABLE characters ADD COLUMN characteristics JSONB DEFAULT '{}';
ALTER TABLE characters ADD COLUMN is_primary BOOLEAN DEFAULT false;
ALTER TABLE characters ADD COLUMN display_order INTEGER DEFAULT 0;
ALTER TABLE characters ADD COLUMN last_active_at TIMESTAMP WITH TIME ZONE;

-- 添加注释
COMMENT ON COLUMN characters.characteristics IS '角色特征信息，包括外观、性格等详细描述';
COMMENT ON COLUMN characters.is_primary IS '是否为用户的主要角色';
COMMENT ON COLUMN characters.display_order IS '显示顺序，用于角色列表排序';
COMMENT ON COLUMN characters.last_active_at IS '角色最后活跃时间';

-- 5. 扩展entities表
ALTER TABLE entities ADD COLUMN container_entity_id UUID REFERENCES entities(id);
ALTER TABLE entities ADD COLUMN version INTEGER NOT NULL DEFAULT 1;
ALTER TABLE entities ADD COLUMN tags JSONB DEFAULT '[]';

-- 添加注释
COMMENT ON COLUMN entities.container_entity_id IS '容器实体ID，支持实体的包含关系';
COMMENT ON COLUMN entities.version IS '实体版本号，用于版本控制和冲突解决';
COMMENT ON COLUMN entities.tags IS '实体标签，用于分类和搜索';

-- =====================================================
-- 阶段3：创建索引优化
-- =====================================================

-- user_sessions表索引
CREATE INDEX idx_user_sessions_active ON user_sessions(user_id, world_id, active_character_id);
CREATE INDEX idx_user_sessions_activity ON user_sessions(last_activity_at DESC);

-- character_memories表索引
CREATE INDEX idx_character_memories_character ON character_memories(character_id, importance_score DESC, created_at DESC);
CREATE INDEX idx_character_memories_type ON character_memories(character_id, memory_type, current_strength DESC);
CREATE INDEX idx_character_memories_tags_gin ON character_memories USING GIN (tags);
CREATE INDEX idx_character_memories_entities_gin ON character_memories USING GIN (associated_entities);

-- character_experiences表索引
CREATE INDEX idx_character_experiences_character ON character_experiences(character_id, last_used DESC);
CREATE INDEX idx_character_experiences_tags_gin ON character_experiences USING GIN (tags);

-- specialized_entities表索引
CREATE INDEX idx_specialized_entities_type ON specialized_entities(specialization_type);
CREATE INDEX idx_specialized_entities_data_gin ON specialized_entities USING GIN (specialized_data);

-- game_events表索引
CREATE INDEX idx_game_events_world_time ON game_events(world_id, game_time DESC);
CREATE INDEX idx_game_events_type ON game_events(world_id, event_type, created_at DESC);
CREATE INDEX idx_game_events_actor ON game_events(primary_actor_id, created_at DESC) WHERE primary_actor_id IS NOT NULL;
CREATE INDEX idx_game_events_scene ON game_events(scene_id, created_at DESC) WHERE scene_id IS NOT NULL;
CREATE INDEX idx_game_events_tags_gin ON game_events USING GIN (tags);

-- 现有表的新字段索引
CREATE INDEX idx_users_profile_gin ON users USING GIN (profile);
CREATE INDEX idx_worlds_config_gin ON worlds USING GIN (world_config);
CREATE INDEX idx_worlds_tags_gin ON worlds USING GIN (tags);
CREATE INDEX idx_scenes_tags_gin ON scenes USING GIN (tags);
CREATE INDEX idx_scenes_connections_gin ON scenes USING GIN (connections);
CREATE INDEX idx_characters_characteristics_gin ON characters USING GIN (characteristics);
CREATE INDEX idx_entities_tags_gin ON entities USING GIN (tags);

-- 唯一约束
CREATE UNIQUE INDEX idx_characters_primary_unique ON characters(user_id, world_id, is_primary)
    WHERE is_primary = true;

-- =====================================================
-- 阶段4：数据迁移和转换
-- =====================================================

-- 1. 迁移users表数据到profile字段
UPDATE users SET profile =
    CASE
        WHEN profile IS NULL OR profile = '{}' THEN
            jsonb_build_object(
                'display_name', COALESCE(display_name, ''),
                'avatar', jsonb_build_object(
                    'url', COALESCE(avatar_url, ''),
                    'source', 'external'
                ),
                'locale', 'zh-CN',
                'timezone', 'Asia/Shanghai'
            )
        ELSE profile
    END
WHERE display_name IS NOT NULL OR avatar_url IS NOT NULL;

-- 2. 迁移worlds表数据
UPDATE worlds SET
    access_settings = jsonb_build_object(
        'is_public', COALESCE(is_public, false),
        'max_players', COALESCE(max_players, 10),
        'join_policy', CASE WHEN COALESCE(is_public, false) THEN 'open' ELSE 'invite' END,
        'player_permissions', jsonb_build_object(
            'can_create_characters', true,
            'can_modify_world', false,
            'can_invite_others', false
        )
    ),
    tags = '[]'::jsonb,
    time_config = jsonb_build_object(
        'time_multiplier', 1.0,
        'pause_when_empty', true,
        'day_night_cycle', true,
        'season_length_days', 30
    );

-- 3. 迁移scenes表数据
UPDATE scenes SET
    tags = CASE
        WHEN scene_type IS NOT NULL AND scene_type != '' THEN
            jsonb_build_array(scene_type)
        ELSE '[]'::jsonb
    END,
    access_rules = jsonb_build_object(
        'visibility', 'public',
        'entry_requirements', '[]'::jsonb,
        'capacity', jsonb_build_object(
            'description', '这里可以容纳很多人',
            'soft_limit', 20
        )
    );

-- 4. 转换scenes表的连接格式
UPDATE scenes SET connections = (
    SELECT COALESCE(
        jsonb_agg(
            jsonb_build_object(
                'id', gen_random_uuid()::text,
                'target_scene_id', value::text,
                'connection_type', 'bidirectional',
                'direction', jsonb_build_object(
                    'from_description', '向' || key || '走',
                    'compass_direction', key
                ),
                'travel', jsonb_build_object(
                    'description', '一条通往' || key || '的路径',
                    'difficulty', '轻松的步行',
                    'time_description', '几分钟的路程'
                )
            )
        ),
        '[]'::jsonb
    )
    FROM jsonb_each_text(connected_scenes)
)
WHERE connected_scenes IS NOT NULL AND connected_scenes != '{}' AND connected_scenes != '[]';

-- 5. 迁移characters表的traits字段
UPDATE characters SET characteristics =
    jsonb_build_object(
        'traits', CASE
            WHEN traits IS NOT NULL AND array_length(traits, 1) > 0 THEN
                (SELECT jsonb_agg(
                    jsonb_build_object(
                        'name', trait,
                        'category', 'personality',
                        'intensity', 0.7,
                        'description', trait || '的特质'
                    )
                ) FROM unnest(traits) AS trait)
            ELSE '[]'::jsonb
        END,
        'appearance', jsonb_build_object(
            'description', '一个有趣的角色',
            'distinctive_features', '[]'::jsonb
        ),
        'personality', jsonb_build_object(
            'core_values', '[]'::jsonb,
            'motivations', '[]'::jsonb,
            'fears', '[]'::jsonb
        )
    )
WHERE traits IS NOT NULL;