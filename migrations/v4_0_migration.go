package migrations

import (
	"fmt"
	"log"

	"gorm.io/gorm"
	"ai-text-game-iam-npc/internal/models"
)

// V4Migration v4.0架构迁移
type V4Migration struct {
	db *gorm.DB
}

// NewV4Migration 创建v4.0迁移实例
func NewV4Migration(db *gorm.DB) *V4Migration {
	return &V4Migration{db: db}
}

// Up 执行向上迁移（升级到v4.0）
func (m *V4Migration) Up() error {
	log.Println("开始执行v4.0架构迁移...")

	// 1. 创建新表
	if err := m.createNewTables(); err != nil {
		return fmt.Errorf("创建新表失败: %w", err)
	}

	// 2. 更新现有表结构
	if err := m.updateExistingTables(); err != nil {
		return fmt.Errorf("更新现有表失败: %w", err)
	}

	// 3. 迁移数据
	if err := m.migrateData(); err != nil {
		return fmt.Errorf("数据迁移失败: %w", err)
	}

	// 4. 创建索引
	if err := m.createIndexes(); err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}

	log.Println("v4.0架构迁移完成")
	return nil
}

// Down 执行向下迁移（回滚到v3.x）
func (m *V4Migration) Down() error {
	log.Println("开始回滚v4.0架构迁移...")

	// 1. 删除新表
	if err := m.dropNewTables(); err != nil {
		return fmt.Errorf("删除新表失败: %w", err)
	}

	// 2. 回滚表结构变更
	if err := m.rollbackTableChanges(); err != nil {
		return fmt.Errorf("回滚表结构失败: %w", err)
	}

	log.Println("v4.0架构迁移回滚完成")
	return nil
}

// createNewTables 创建新表
func (m *V4Migration) createNewTables() error {
	log.Println("创建新表...")

	// 创建用户会话表
	if err := m.db.AutoMigrate(&models.UserSession{}); err != nil {
		return fmt.Errorf("创建用户会话表失败: %w", err)
	}

	// 创建角色记忆表
	if err := m.db.AutoMigrate(&models.CharacterMemory{}); err != nil {
		return fmt.Errorf("创建角色记忆表失败: %w", err)
	}

	// 创建角色阅历表
	if err := m.db.AutoMigrate(&models.CharacterExperience{}); err != nil {
		return fmt.Errorf("创建角色阅历表失败: %w", err)
	}

	// 创建专门实体表
	if err := m.db.AutoMigrate(&models.SpecializedEntity{}); err != nil {
		return fmt.Errorf("创建专门实体表失败: %w", err)
	}

	// 创建游戏事件表
	if err := m.db.AutoMigrate(&models.GameEvent{}); err != nil {
		return fmt.Errorf("创建游戏事件表失败: %w", err)
	}

	log.Println("新表创建完成")
	return nil
}

// updateExistingTables 更新现有表结构
func (m *V4Migration) updateExistingTables() error {
	log.Println("更新现有表结构...")

	// 更新用户表
	if err := m.updateUsersTable(); err != nil {
		return fmt.Errorf("更新用户表失败: %w", err)
	}

	// 更新世界表
	if err := m.updateWorldsTable(); err != nil {
		return fmt.Errorf("更新世界表失败: %w", err)
	}

	// 更新角色表
	if err := m.updateCharactersTable(); err != nil {
		return fmt.Errorf("更新角色表失败: %w", err)
	}

	// 更新实体表
	if err := m.updateEntitiesTable(); err != nil {
		return fmt.Errorf("更新实体表失败: %w", err)
	}

	// 更新场景表
	if err := m.updateScenesTable(); err != nil {
		return fmt.Errorf("更新场景表失败: %w", err)
	}

	log.Println("现有表结构更新完成")
	return nil
}

// updateUsersTable 更新用户表
func (m *V4Migration) updateUsersTable() error {
	// 添加新字段
	if err := m.db.Exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS profile TEXT DEFAULT '{}'").Error; err != nil {
		return err
	}
	if err := m.db.Exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS last_active_at DATETIME").Error; err != nil {
		return err
	}
	return nil
}

// updateWorldsTable 更新世界表
func (m *V4Migration) updateWorldsTable() error {
	// 添加新字段
	if err := m.db.Exec("ALTER TABLE worlds ADD COLUMN IF NOT EXISTS access_settings TEXT DEFAULT '{}'").Error; err != nil {
		return err
	}
	if err := m.db.Exec("ALTER TABLE worlds ADD COLUMN IF NOT EXISTS tags TEXT DEFAULT '[]'").Error; err != nil {
		return err
	}
	if err := m.db.Exec("ALTER TABLE worlds ADD COLUMN IF NOT EXISTS time_config TEXT DEFAULT '{}'").Error; err != nil {
		return err
	}
	return nil
}

// updateCharactersTable 更新角色表
func (m *V4Migration) updateCharactersTable() error {
	// 添加新字段
	if err := m.db.Exec("ALTER TABLE characters ADD COLUMN IF NOT EXISTS characteristics TEXT DEFAULT '{}'").Error; err != nil {
		return err
	}
	if err := m.db.Exec("ALTER TABLE characters ADD COLUMN IF NOT EXISTS is_primary BOOLEAN DEFAULT FALSE").Error; err != nil {
		return err
	}
	if err := m.db.Exec("ALTER TABLE characters ADD COLUMN IF NOT EXISTS display_order INTEGER DEFAULT 0").Error; err != nil {
		return err
	}
	if err := m.db.Exec("ALTER TABLE characters ADD COLUMN IF NOT EXISTS last_active_at DATETIME").Error; err != nil {
		return err
	}
	return nil
}

// updateEntitiesTable 更新实体表
func (m *V4Migration) updateEntitiesTable() error {
	// 添加新字段
	if err := m.db.Exec("ALTER TABLE entities ADD COLUMN IF NOT EXISTS container_entity_id TEXT").Error; err != nil {
		return err
	}
	if err := m.db.Exec("ALTER TABLE entities ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1").Error; err != nil {
		return err
	}
	if err := m.db.Exec("ALTER TABLE entities ADD COLUMN IF NOT EXISTS tags TEXT DEFAULT '[]'").Error; err != nil {
		return err
	}
	return nil
}

// updateScenesTable 更新场景表
func (m *V4Migration) updateScenesTable() error {
	// 添加新字段
	if err := m.db.Exec("ALTER TABLE scenes ADD COLUMN IF NOT EXISTS tags TEXT DEFAULT '[]'").Error; err != nil {
		return err
	}
	if err := m.db.Exec("ALTER TABLE scenes ADD COLUMN IF NOT EXISTS access_rules TEXT DEFAULT '{}'").Error; err != nil {
		return err
	}
	if err := m.db.Exec("ALTER TABLE scenes ADD COLUMN IF NOT EXISTS connections TEXT DEFAULT '[]'").Error; err != nil {
		return err
	}
	return nil
}

// migrateData 迁移数据
func (m *V4Migration) migrateData() error {
	log.Println("开始数据迁移...")

	// 迁移用户档案数据
	if err := m.migrateUserProfiles(); err != nil {
		return fmt.Errorf("迁移用户档案失败: %w", err)
	}

	// 迁移角色特征数据
	if err := m.migrateCharacteristics(); err != nil {
		return fmt.Errorf("迁移角色特征失败: %w", err)
	}

	// 迁移世界访问设置
	if err := m.migrateWorldAccessSettings(); err != nil {
		return fmt.Errorf("迁移世界访问设置失败: %w", err)
	}

	log.Println("数据迁移完成")
	return nil
}

// migrateUserProfiles 迁移用户档案数据
func (m *V4Migration) migrateUserProfiles() error {
	// 将display_name和avatar_url迁移到profile字段
	query := `
		UPDATE users 
		SET profile = json_object(
			'display_name', COALESCE(display_name, ''),
			'avatar', json_object(
				'url', COALESCE(avatar_url, ''),
				'source', 'legacy'
			),
			'locale', 'zh-CN',
			'timezone', 'Asia/Shanghai'
		)
		WHERE profile = '{}' OR profile IS NULL
	`
	return m.db.Exec(query).Error
}

// migrateCharacteristics 迁移角色特征数据
func (m *V4Migration) migrateCharacteristics() error {
	// 将traits数组迁移到characteristics对象
	var characters []models.Character
	if err := m.db.Find(&characters).Error; err != nil {
		return err
	}

	for _, char := range characters {
		if len(char.Characteristics) == 0 {
			// 从traits构建characteristics
			traits := make(map[string]interface{})
			for _, trait := range char.Traits {
				traits[trait] = map[string]interface{}{
					"name":         trait,
					"category":     "personality",
					"intensity":    0.7,
					"description":  trait + "的特质",
				}
			}

			characteristics := map[string]interface{}{
				"traits": traits,
				"appearance": map[string]interface{}{
					"description": func() string {
						if char.Description != nil {
							return *char.Description
						}
						return "一个有趣的角色"
					}(),
				},
			}

			char.Characteristics = models.JSON(characteristics)
			if err := m.db.Save(&char).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// migrateWorldAccessSettings 迁移世界访问设置
func (m *V4Migration) migrateWorldAccessSettings() error {
	// 将is_public和max_players迁移到access_settings
	query := `
		UPDATE worlds 
		SET access_settings = json_object(
			'is_public', is_public,
			'max_players', max_players,
			'join_policy', CASE WHEN is_public THEN 'open' ELSE 'invite' END
		)
		WHERE access_settings = '{}' OR access_settings IS NULL
	`
	return m.db.Exec(query).Error
}

// createIndexes 创建索引
func (m *V4Migration) createIndexes() error {
	log.Println("创建索引...")

	// 用户会话索引
	m.db.Exec("CREATE INDEX IF NOT EXISTS idx_user_sessions_user_world ON user_sessions(user_id, world_id)")
	m.db.Exec("CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity_at)")

	// 角色记忆索引
	m.db.Exec("CREATE INDEX IF NOT EXISTS idx_character_memories_character ON character_memories(character_id)")
	m.db.Exec("CREATE INDEX IF NOT EXISTS idx_character_memories_type ON character_memories(memory_type)")
	m.db.Exec("CREATE INDEX IF NOT EXISTS idx_character_memories_importance ON character_memories(importance_score)")

	// 角色阅历索引
	m.db.Exec("CREATE INDEX IF NOT EXISTS idx_character_experiences_character ON character_experiences(character_id)")
	m.db.Exec("CREATE INDEX IF NOT EXISTS idx_character_experiences_last_used ON character_experiences(last_used)")

	// 游戏事件索引
	m.db.Exec("CREATE INDEX IF NOT EXISTS idx_game_events_world ON game_events(world_id)")
	m.db.Exec("CREATE INDEX IF NOT EXISTS idx_game_events_type ON game_events(event_type)")
	m.db.Exec("CREATE INDEX IF NOT EXISTS idx_game_events_game_time ON game_events(game_time)")

	log.Println("索引创建完成")
	return nil
}

// dropNewTables 删除新表
func (m *V4Migration) dropNewTables() error {
	tables := []string{
		"user_sessions",
		"character_memories", 
		"character_experiences",
		"specialized_entities",
		"game_events",
	}

	for _, table := range tables {
		if err := m.db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s", table)).Error; err != nil {
			return err
		}
	}

	return nil
}

// rollbackTableChanges 回滚表结构变更
func (m *V4Migration) rollbackTableChanges() error {
	// 删除新增的列（SQLite不支持DROP COLUMN，需要重建表）
	log.Println("警告：SQLite不支持删除列，回滚操作可能不完整")
	return nil
}
