-- 创建内容校验日志表
CREATE TABLE validation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content_type VARCHAR(50) NOT NULL,           -- 内容类型
    text_length INTEGER NOT NULL,                -- 文本长度
    is_valid BOOLEAN NOT NULL DEFAULT FALSE,     -- 是否通过校验
    error_count INTEGER NOT NULL DEFAULT 0,      -- 错误数量
    warning_count INTEGER NOT NULL DEFAULT 0,    -- 警告数量
    metadata JSONB DEFAULT '{}',                  -- 校验元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_validation_logs_user_id ON validation_logs(user_id);
CREATE INDEX idx_validation_logs_content_type ON validation_logs(content_type);
CREATE INDEX idx_validation_logs_is_valid ON validation_logs(is_valid);
CREATE INDEX idx_validation_logs_created_at ON validation_logs(created_at);
CREATE INDEX idx_validation_logs_user_created ON validation_logs(user_id, created_at);

-- 创建敏感词表（可选，用于动态管理敏感词）
CREATE TABLE profanity_words (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    word VARCHAR(100) NOT NULL UNIQUE,
    category VARCHAR(50) DEFAULT 'general',      -- 分类：general, violence, sexual, etc.
    severity INTEGER DEFAULT 1,                  -- 严重程度：1-5
    language VARCHAR(10) DEFAULT 'zh',           -- 语言代码
    is_active BOOLEAN DEFAULT TRUE,              -- 是否启用
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_profanity_words_word ON profanity_words(word);
CREATE INDEX idx_profanity_words_category ON profanity_words(category);
CREATE INDEX idx_profanity_words_language ON profanity_words(language);
CREATE INDEX idx_profanity_words_is_active ON profanity_words(is_active);

-- 创建内容审核规则表
CREATE TABLE content_moderation_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rule_name VARCHAR(100) NOT NULL UNIQUE,
    rule_type VARCHAR(50) NOT NULL,              -- regex, keyword, ai_model, etc.
    pattern TEXT,                                -- 正则表达式或关键词
    action VARCHAR(50) DEFAULT 'warn',           -- block, warn, filter, log
    severity INTEGER DEFAULT 1,                  -- 严重程度：1-5
    content_types JSONB DEFAULT '[]',            -- 适用的内容类型
    is_active BOOLEAN DEFAULT TRUE,              -- 是否启用
    description TEXT,                            -- 规则描述
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_moderation_rules_rule_type ON content_moderation_rules(rule_type);
CREATE INDEX idx_moderation_rules_is_active ON content_moderation_rules(is_active);

-- 创建内容审核日志表
CREATE TABLE content_moderation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rule_id UUID REFERENCES content_moderation_rules(id) ON DELETE SET NULL,
    content_type VARCHAR(50) NOT NULL,
    original_text TEXT NOT NULL,
    filtered_text TEXT,
    action_taken VARCHAR(50) NOT NULL,           -- blocked, warned, filtered, logged
    match_details JSONB DEFAULT '{}',            -- 匹配详情
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_moderation_logs_user_id ON content_moderation_logs(user_id);
CREATE INDEX idx_moderation_logs_rule_id ON content_moderation_logs(rule_id);
CREATE INDEX idx_moderation_logs_action_taken ON content_moderation_logs(action_taken);
CREATE INDEX idx_moderation_logs_created_at ON content_moderation_logs(created_at);

-- 创建用户频率限制表
CREATE TABLE user_rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    endpoint VARCHAR(100) NOT NULL,              -- API端点
    request_count INTEGER DEFAULT 0,             -- 请求次数
    window_start TIMESTAMP WITH TIME ZONE NOT NULL, -- 时间窗口开始
    window_duration INTEGER NOT NULL,            -- 时间窗口长度（秒）
    limit_type VARCHAR(20) DEFAULT 'minute',     -- minute, hour, day
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 唯一约束
    UNIQUE(user_id, endpoint, window_start, limit_type)
);

-- 创建索引
CREATE INDEX idx_rate_limits_user_id ON user_rate_limits(user_id);
CREATE INDEX idx_rate_limits_endpoint ON user_rate_limits(endpoint);
CREATE INDEX idx_rate_limits_window_start ON user_rate_limits(window_start);
CREATE INDEX idx_rate_limits_limit_type ON user_rate_limits(limit_type);

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_profanity_words_updated_at 
    BEFORE UPDATE ON profanity_words 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_moderation_rules_updated_at 
    BEFORE UPDATE ON content_moderation_rules 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_rate_limits_updated_at 
    BEFORE UPDATE ON user_rate_limits 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入一些基础的敏感词数据
INSERT INTO profanity_words (word, category, severity, language) VALUES
-- 英文敏感词
('fuck', 'profanity', 3, 'en'),
('shit', 'profanity', 2, 'en'),
('damn', 'profanity', 1, 'en'),
('bitch', 'profanity', 3, 'en'),
('asshole', 'profanity', 3, 'en'),

-- 中文敏感词
('傻逼', 'profanity', 3, 'zh'),
('操你妈', 'profanity', 5, 'zh'),
('去死', 'violence', 4, 'zh'),
('滚蛋', 'profanity', 2, 'zh'),
('白痴', 'profanity', 2, 'zh');

-- 插入一些基础的内容审核规则
INSERT INTO content_moderation_rules (rule_name, rule_type, pattern, action, severity, content_types, description) VALUES
-- SQL注入检测
('sql_injection_detection', 'regex', '(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute)', 'block', 5, '["character_name", "world_name", "scene_name", "description"]', '检测SQL注入攻击'),

-- XSS攻击检测
('xss_detection', 'regex', '(?i)(<script|</script|<iframe|</iframe|javascript:|on\w+\s*=)', 'block', 5, '["description", "dialogue", "content"]', '检测XSS攻击'),

-- 过长文本检测
('text_length_check', 'length', '10000', 'warn', 2, '["description", "dialogue", "content"]', '检测过长文本'),

-- HTML标签检测
('html_tag_detection', 'regex', '<[^>]*>', 'filter', 2, '["character_name", "world_name", "scene_name"]', '过滤HTML标签'),

-- 重复字符检测
('repeated_chars', 'regex', '(.)\1{10,}', 'warn', 1, '["description", "dialogue", "content"]', '检测重复字符');

-- 创建清理过期数据的函数
CREATE OR REPLACE FUNCTION cleanup_old_validation_logs()
RETURNS void AS $$
BEGIN
    -- 删除30天前的校验日志
    DELETE FROM validation_logs 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- 删除30天前的内容审核日志
    DELETE FROM content_moderation_logs 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- 删除1天前的频率限制记录
    DELETE FROM user_rate_limits 
    WHERE window_start < NOW() - INTERVAL '1 day';
END;
$$ LANGUAGE plpgsql;

-- 创建统计函数
CREATE OR REPLACE FUNCTION get_validation_stats(p_user_id UUID, p_days INTEGER DEFAULT 7)
RETURNS TABLE(
    total_requests BIGINT,
    valid_requests BIGINT,
    invalid_requests BIGINT,
    success_rate NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_requests,
        COUNT(*) FILTER (WHERE is_valid = true) as valid_requests,
        COUNT(*) FILTER (WHERE is_valid = false) as invalid_requests,
        ROUND(
            (COUNT(*) FILTER (WHERE is_valid = true)::NUMERIC / 
             NULLIF(COUNT(*), 0) * 100), 2
        ) as success_rate
    FROM validation_logs 
    WHERE user_id = p_user_id 
    AND created_at > NOW() - (p_days || ' days')::INTERVAL;
END;
$$ LANGUAGE plpgsql;
