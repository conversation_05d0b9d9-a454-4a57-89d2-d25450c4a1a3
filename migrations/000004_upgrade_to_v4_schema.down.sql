-- 回滚v4.0架构升级
-- 这个脚本用于回滚到升级前的数据库状态

-- =====================================================
-- 阶段1：删除新创建的索引
-- =====================================================

-- 删除新表的索引
DROP INDEX IF EXISTS idx_user_sessions_active;
DROP INDEX IF EXISTS idx_user_sessions_activity;
DROP INDEX IF EXISTS idx_character_memories_character;
DROP INDEX IF EXISTS idx_character_memories_type;
DROP INDEX IF EXISTS idx_character_memories_tags_gin;
DROP INDEX IF EXISTS idx_character_memories_entities_gin;
DROP INDEX IF EXISTS idx_character_experiences_character;
DROP INDEX IF EXISTS idx_character_experiences_tags_gin;
DROP INDEX IF EXISTS idx_specialized_entities_type;
DROP INDEX IF EXISTS idx_specialized_entities_data_gin;
DROP INDEX IF EXISTS idx_game_events_world_time;
DROP INDEX IF EXISTS idx_game_events_type;
DROP INDEX IF EXISTS idx_game_events_actor;
DROP INDEX IF EXISTS idx_game_events_scene;
DROP INDEX IF EXISTS idx_game_events_tags_gin;

-- 删除现有表的新字段索引
DROP INDEX IF EXISTS idx_users_profile_gin;
DROP INDEX IF EXISTS idx_worlds_config_gin;
DROP INDEX IF EXISTS idx_worlds_tags_gin;
DROP INDEX IF EXISTS idx_scenes_tags_gin;
DROP INDEX IF EXISTS idx_scenes_connections_gin;
DROP INDEX IF EXISTS idx_characters_characteristics_gin;
DROP INDEX IF EXISTS idx_entities_tags_gin;
DROP INDEX IF EXISTS idx_characters_primary_unique;

-- =====================================================
-- 阶段2：删除新创建的表
-- =====================================================

-- 按依赖关系逆序删除表
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS character_memories;
DROP TABLE IF EXISTS character_experiences;
DROP TABLE IF EXISTS specialized_entities;
DROP TABLE IF EXISTS game_events;

-- =====================================================
-- 阶段3：删除新添加的字段
-- =====================================================

-- 删除users表的新字段
ALTER TABLE users DROP COLUMN IF EXISTS profile;
ALTER TABLE users DROP COLUMN IF EXISTS last_active_at;

-- 删除worlds表的新字段
ALTER TABLE worlds DROP COLUMN IF EXISTS access_settings;
ALTER TABLE worlds DROP COLUMN IF EXISTS tags;
ALTER TABLE worlds DROP COLUMN IF EXISTS time_config;

-- 删除scenes表的新字段
ALTER TABLE scenes DROP COLUMN IF EXISTS tags;
ALTER TABLE scenes DROP COLUMN IF EXISTS access_rules;
ALTER TABLE scenes DROP COLUMN IF EXISTS connections;

-- 删除characters表的新字段
ALTER TABLE characters DROP COLUMN IF EXISTS characteristics;
ALTER TABLE characters DROP COLUMN IF EXISTS is_primary;
ALTER TABLE characters DROP COLUMN IF EXISTS display_order;
ALTER TABLE characters DROP COLUMN IF EXISTS last_active_at;

-- 删除entities表的新字段
ALTER TABLE entities DROP COLUMN IF EXISTS container_entity_id;
ALTER TABLE entities DROP COLUMN IF EXISTS version;
ALTER TABLE entities DROP COLUMN IF EXISTS tags;

-- =====================================================
-- 阶段4：恢复原始约束和索引
-- =====================================================

-- 恢复原始的索引（如果需要的话）
-- 这里可以添加原始索引的重建语句

-- =====================================================
-- 完成回滚
-- =====================================================

-- 更新统计信息
ANALYZE users;
ANALYZE worlds;
ANALYZE scenes;
ANALYZE characters;
ANALYZE entities;