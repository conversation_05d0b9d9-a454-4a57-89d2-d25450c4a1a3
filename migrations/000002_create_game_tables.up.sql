-- 创建游戏世界表
CREATE TABLE worlds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    creator_id UUID NOT NULL REFERENCES users(id),
    world_config JSONB NOT NULL, -- 世界配置(时间倍率、规则等)
    world_state JSONB NOT NULL, -- 当前世界状态
    status VARCHAR(20) DEFAULT 'active', -- active, paused, archived
    is_public BOOLEAN DEFAULT false, -- 是否公开
    max_players INTEGER DEFAULT 10, -- 最大玩家数
    current_players INTEGER DEFAULT 0, -- 当前玩家数
    game_time BIGINT DEFAULT 0, -- 游戏内时间(分钟)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建场景表
CREATE TABLE scenes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    scene_type VARCHAR(50) DEFAULT 'normal', -- normal, special, hidden
    properties JSONB DEFAULT '{}', -- 场景属性和特质
    entities_present JSONB DEFAULT '[]', -- 当前场景中的实体ID列表
    connected_scenes JSONB DEFAULT '{}', -- 连接的场景 {"north": "scene_id", "south": "scene_id"}
    environment JSONB DEFAULT '{}', -- 环境信息(天气、光照等)
    status VARCHAR(20) DEFAULT 'active', -- active, locked, hidden
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建角色表
CREATE TABLE characters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id), -- NULL表示NPC
    name VARCHAR(100) NOT NULL,
    description TEXT,
    character_type VARCHAR(20) DEFAULT 'player', -- player, npc, collective
    current_scene_id UUID REFERENCES scenes(id),
    traits JSONB DEFAULT '[]', -- 特质列表
    memories JSONB DEFAULT '[]', -- 记忆系统
    experiences JSONB DEFAULT '{}', -- 阅历系统
    relationships JSONB DEFAULT '{}', -- 关系网络
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, dead
    last_action_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建实体表
CREATE TABLE entities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    entity_type VARCHAR(50) NOT NULL, -- item, event, goal, weather, etc.
    properties JSONB DEFAULT '{}', -- 实体属性
    traits JSONB DEFAULT '[]', -- 特质列表
    current_scene_id UUID REFERENCES scenes(id), -- 当前所在场景
    owner_id UUID REFERENCES characters(id), -- 拥有者(如果适用)
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, consumed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建事件表
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    scene_id UUID REFERENCES scenes(id),
    creator_id UUID REFERENCES characters(id), -- 创建事件的角色
    event_type VARCHAR(50) NOT NULL, -- action, environment, system, etc.
    name VARCHAR(200),
    description TEXT,
    event_data JSONB DEFAULT '{}', -- 事件数据
    participants JSONB DEFAULT '[]', -- 参与者ID列表
    status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
    priority INTEGER DEFAULT 0, -- 事件优先级
    scheduled_at TIMESTAMP WITH TIME ZONE, -- 计划执行时间
    processed_at TIMESTAMP WITH TIME ZONE, -- 实际处理时间
    result JSONB, -- 事件结果
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建AI交互日志表
CREATE TABLE ai_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID REFERENCES worlds(id),
    user_id UUID REFERENCES users(id),
    interaction_type VARCHAR(50) NOT NULL, -- world_generation, scene_generation, event_processing, etc.
    prompt TEXT NOT NULL, -- 发送给AI的提示
    response TEXT, -- AI的响应
    response_schema JSONB, -- 响应的JSON schema
    token_usage INTEGER, -- Token使用量
    response_time INTEGER, -- 响应时间(毫秒)
    status VARCHAR(20) DEFAULT 'pending', -- pending, completed, failed
    error_message TEXT, -- 错误信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
-- 世界表索引
CREATE INDEX idx_worlds_creator_id ON worlds(creator_id);
CREATE INDEX idx_worlds_status ON worlds(status);
CREATE INDEX idx_worlds_is_public ON worlds(is_public);
CREATE INDEX idx_worlds_created_at ON worlds(created_at);

-- 场景表索引
CREATE INDEX idx_scenes_world_id ON scenes(world_id);
CREATE INDEX idx_scenes_scene_type ON scenes(scene_type);
CREATE INDEX idx_scenes_status ON scenes(status);

-- 角色表索引
CREATE INDEX idx_characters_world_id ON characters(world_id);
CREATE INDEX idx_characters_user_id ON characters(user_id);
CREATE INDEX idx_characters_current_scene_id ON characters(current_scene_id);
CREATE INDEX idx_characters_character_type ON characters(character_type);
CREATE INDEX idx_characters_status ON characters(status);

-- 实体表索引
CREATE INDEX idx_entities_world_id ON entities(world_id);
CREATE INDEX idx_entities_entity_type ON entities(entity_type);
CREATE INDEX idx_entities_current_scene_id ON entities(current_scene_id);
CREATE INDEX idx_entities_owner_id ON entities(owner_id);
CREATE INDEX idx_entities_status ON entities(status);

-- 事件表索引
CREATE INDEX idx_events_world_id ON events(world_id);
CREATE INDEX idx_events_scene_id ON events(scene_id);
CREATE INDEX idx_events_creator_id ON events(creator_id);
CREATE INDEX idx_events_event_type ON events(event_type);
CREATE INDEX idx_events_status ON events(status);
CREATE INDEX idx_events_priority ON events(priority);
CREATE INDEX idx_events_scheduled_at ON events(scheduled_at);

-- AI交互日志表索引
CREATE INDEX idx_ai_interactions_world_id ON ai_interactions(world_id);
CREATE INDEX idx_ai_interactions_user_id ON ai_interactions(user_id);
CREATE INDEX idx_ai_interactions_interaction_type ON ai_interactions(interaction_type);
CREATE INDEX idx_ai_interactions_status ON ai_interactions(status);
CREATE INDEX idx_ai_interactions_created_at ON ai_interactions(created_at);

-- 为新表添加更新时间触发器
CREATE TRIGGER update_worlds_updated_at 
    BEFORE UPDATE ON worlds 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scenes_updated_at 
    BEFORE UPDATE ON scenes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_characters_updated_at 
    BEFORE UPDATE ON characters 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_entities_updated_at 
    BEFORE UPDATE ON entities 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_events_updated_at 
    BEFORE UPDATE ON events 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
