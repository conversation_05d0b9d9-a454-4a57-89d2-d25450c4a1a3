-- 数据迁移到v4.0架构
-- 这个脚本负责将现有数据迁移到新的表结构中

-- =====================================================
-- 阶段1：迁移users表数据
-- =====================================================

-- 1. 迁移用户档案数据到profile字段
UPDATE users SET profile =
    jsonb_build_object(
        'display_name', COALESCE(display_name, ''),
        'avatar', jsonb_build_object(
            'url', COALESCE(avatar_url, ''),
            'source', 'external'
        ),
        'locale', 'zh-CN',
        'timezone', 'Asia/Shanghai',
        'ui_preferences', jsonb_build_object(
            'theme', 'auto',
            'language', 'zh-CN',
            'notifications', true
        )
    )
WHERE display_name IS NOT NULL OR avatar_url IS NOT NULL;

-- 2. 设置用户最后活跃时间
UPDATE users SET last_active_at = updated_at WHERE updated_at IS NOT NULL;

-- =====================================================
-- 阶段2：迁移worlds表数据
-- =====================================================

-- 1. 迁移世界访问设置
UPDATE worlds SET
    access_settings = jsonb_build_object(
        'is_public', COALESCE(is_public, false),
        'max_players', COALESCE(max_players, 10),
        'join_policy', CASE WHEN COALESCE(is_public, false) THEN 'open' ELSE 'invite' END,
        'player_permissions', jsonb_build_object(
            'can_create_characters', true,
            'can_modify_world', false,
            'can_invite_others', false
        ),
        'content_rating', 'general',
        'moderation', jsonb_build_object(
            'auto_moderation', true,
            'content_filters', jsonb_build_array('violence', 'adult_content')
        )
    );

-- 2. 设置世界标签（基于现有数据推断）
UPDATE worlds SET tags =
    CASE
        WHEN world_config->>'theme' IS NOT NULL THEN
            jsonb_build_array(world_config->>'theme')
        ELSE '[]'::jsonb
    END;

-- 3. 设置时间配置
UPDATE worlds SET time_config =
    jsonb_build_object(
        'time_multiplier', 1.0,
        'pause_when_empty', true,
        'day_night_cycle', true,
        'season_length_days', 30,
        'weather_system', true,
        'calendar', jsonb_build_object(
            'type', 'gregorian',
            'start_date', '2024-01-01'
        )
    );

-- =====================================================
-- 阶段3：迁移scenes表数据
-- =====================================================

-- 1. 将scene_type转换为tags
UPDATE scenes SET tags =
    CASE
        WHEN scene_type IS NOT NULL AND scene_type != '' THEN
            jsonb_build_array(scene_type)
        ELSE '[]'::jsonb
    END;

-- 2. 设置场景访问规则
UPDATE scenes SET access_rules =
    jsonb_build_object(
        'visibility', 'public',
        'entry_requirements', '[]'::jsonb,
        'capacity', jsonb_build_object(
            'description', COALESCE(description, '这里可以容纳很多人'),
            'soft_limit', 20,
            'hard_limit', 50
        ),
        'restrictions', jsonb_build_object(
            'time_based', '[]'::jsonb,
            'character_based', '[]'::jsonb
        )
    );

-- 3. 转换场景连接格式（复杂转换）
UPDATE scenes SET connections = (
    SELECT COALESCE(
        jsonb_agg(
            jsonb_build_object(
                'id', gen_random_uuid()::text,
                'target_scene_id', value::text,
                'connection_type', 'bidirectional',
                'direction', jsonb_build_object(
                    'from_description', '向' || key || '走',
                    'to_description', '从' || key || '来',
                    'compass_direction', key
                ),
                'travel', jsonb_build_object(
                    'description', '一条通往' || key || '的路径',
                    'difficulty', 'easy',
                    'time_description', '几分钟的路程',
                    'requirements', '[]'::jsonb
                ),
                'visibility', jsonb_build_object(
                    'is_visible', true,
                    'discovery_requirements', '[]'::jsonb
                )
            )
        ),
        '[]'::jsonb
    )
    FROM jsonb_each_text(connected_scenes)
)
WHERE connected_scenes IS NOT NULL
  AND connected_scenes != '{}'
  AND connected_scenes != '[]'
  AND jsonb_typeof(connected_scenes) = 'object';

-- =====================================================
-- 阶段4：迁移characters表数据
-- =====================================================

-- 1. 迁移角色特征数据
UPDATE characters SET characteristics =
    jsonb_build_object(
        'traits', CASE
            WHEN traits IS NOT NULL AND array_length(traits, 1) > 0 THEN
                (SELECT jsonb_agg(
                    jsonb_build_object(
                        'name', trait,
                        'category', 'personality',
                        'intensity', 0.7,
                        'description', trait || '的特质',
                        'manifestation', '在行为中体现出' || trait || '的特点'
                    )
                ) FROM unnest(traits) AS trait)
            ELSE '[]'::jsonb
        END,
        'appearance', jsonb_build_object(
            'description', COALESCE(description, '一个有趣的角色'),
            'distinctive_features', '[]'::jsonb,
            'clothing', jsonb_build_object(
                'style', 'casual',
                'description', '普通的服装'
            )
        ),
        'personality', jsonb_build_object(
            'core_values', '[]'::jsonb,
            'motivations', '[]'::jsonb,
            'fears', '[]'::jsonb,
            'quirks', '[]'::jsonb
        ),
        'background', jsonb_build_object(
            'origin', '未知的过去',
            'family', '[]'::jsonb,
            'education', '[]'::jsonb
        )
    );

-- 2. 设置主要角色标记（每个用户在每个世界的第一个角色为主要角色）
WITH first_characters AS (
    SELECT DISTINCT ON (user_id, world_id)
        id, user_id, world_id
    FROM characters
    ORDER BY user_id, world_id, created_at ASC
)
UPDATE characters SET is_primary = true
WHERE id IN (SELECT id FROM first_characters);

-- 3. 设置显示顺序
UPDATE characters SET display_order = (
    SELECT row_number() OVER (
        PARTITION BY user_id, world_id
        ORDER BY is_primary DESC, created_at ASC
    ) - 1
    FROM characters c2
    WHERE c2.id = characters.id
);

-- 4. 设置角色最后活跃时间
UPDATE characters SET last_active_at = updated_at WHERE updated_at IS NOT NULL;

-- =====================================================
-- 阶段5：迁移character记忆和阅历数据
-- =====================================================

-- 1. 从characters表的memories字段迁移到character_memories表
INSERT INTO character_memories (character_id, content, memory_type, importance_score, context)
SELECT
    c.id as character_id,
    memory_item->>'content' as content,
    COALESCE(memory_item->>'type', 'knowledge') as memory_type,
    COALESCE((memory_item->>'importance')::float, 0.5) as importance_score,
    jsonb_build_object(
        'source', 'migration_from_old_memories',
        'original_data', memory_item
    ) as context
FROM characters c,
     jsonb_array_elements(c.memories) as memory_item
WHERE c.memories IS NOT NULL
  AND jsonb_typeof(c.memories) = 'array'
  AND jsonb_array_length(c.memories) > 0;

-- 2. 从characters表的experiences字段迁移到character_experiences表
INSERT INTO character_experiences (character_id, tags, description, proficiency, learning_info)
SELECT
    c.id as character_id,
    CASE
        WHEN exp_item->>'category' IS NOT NULL THEN
            jsonb_build_array(exp_item->>'category')
        ELSE '["general"]'::jsonb
    END as tags,
    COALESCE(exp_item->>'description', '未知的阅历') as description,
    jsonb_build_object(
        'level', COALESCE((exp_item->>'level')::int, 1),
        'experience_points', COALESCE((exp_item->>'experience')::int, 0),
        'specializations', COALESCE(exp_item->'specializations', '[]'::jsonb)
    ) as proficiency,
    jsonb_build_object(
        'source', 'migration_from_old_experiences',
        'original_data', exp_item
    ) as learning_info
FROM characters c,
     jsonb_array_elements(c.experiences) as exp_item
WHERE c.experiences IS NOT NULL
  AND jsonb_typeof(c.experiences) = 'array'
  AND jsonb_array_length(c.experiences) > 0;

-- =====================================================
-- 阶段6：迁移entities表数据
-- =====================================================

-- 1. 设置实体标签（基于entity_type）
UPDATE entities SET tags =
    jsonb_build_array(entity_type)
WHERE entity_type IS NOT NULL AND entity_type != '';

-- 2. 为特殊实体创建specialized_entities记录
INSERT INTO specialized_entities (entity_id, specialization_type, specialized_data)
SELECT
    e.id as entity_id,
    CASE
        WHEN e.entity_type IN ('weapon', 'armor', 'tool', 'consumable', 'treasure') THEN 'item'
        WHEN e.entity_type IN ('quest', 'mission', 'objective') THEN 'goal'
        WHEN e.entity_type IN ('building', 'landmark', 'area') THEN 'location'
        WHEN e.entity_type IN ('spell', 'ability', 'skill') THEN 'abstract'
        ELSE 'item'
    END as specialization_type,
    jsonb_build_object(
        'original_type', e.entity_type,
        'migrated_properties', e.properties,
        'migration_source', 'entities_table'
    ) as specialized_data
FROM entities e
WHERE e.entity_type IS NOT NULL;

-- =====================================================
-- 阶段7：迁移events表数据到game_events表
-- =====================================================

-- 1. 迁移事件数据
INSERT INTO game_events (
    world_id, event_type, tags, primary_actor_id, participants,
    narrative_text, event_data, processing_result, game_time, created_at
)
SELECT
    e.world_id,
    e.event_type,
    CASE
        WHEN e.event_type IS NOT NULL THEN
            jsonb_build_array(e.event_type)
        ELSE '[]'::jsonb
    END as tags,
    -- 从participants中提取主要参与者
    CASE
        WHEN e.participants IS NOT NULL AND jsonb_array_length(e.participants) > 0 THEN
            e.participants->0->>'id'
        ELSE NULL
    END as primary_actor_id,
    COALESCE(e.participants, '[]'::jsonb) as participants,
    -- 生成叙事文本（如果没有的话）
    CASE
        WHEN e.event_data->>'description' IS NOT NULL THEN
            e.event_data->>'description'
        ELSE '发生了一个' || COALESCE(e.event_type, '未知') || '事件'
    END as narrative_text,
    COALESCE(e.event_data, '{}'::jsonb) as event_data,
    COALESCE(e.result, '{}'::jsonb) as processing_result,
    -- 使用created_at作为游戏时间（转换为毫秒时间戳）
    EXTRACT(EPOCH FROM e.created_at)::bigint * 1000 as game_time,
    e.created_at
FROM events e
WHERE e.id IS NOT NULL;

-- =====================================================
-- 阶段8：创建初始会话数据
-- =====================================================

-- 为现有的用户-世界组合创建会话记录
INSERT INTO user_sessions (user_id, world_id, active_character_id, session_data, last_activity_at)
SELECT DISTINCT
    c.user_id,
    c.world_id,
    -- 选择主要角色作为活跃角色
    (SELECT id FROM characters c2
     WHERE c2.user_id = c.user_id
       AND c2.world_id = c.world_id
       AND c2.is_primary = true
     LIMIT 1) as active_character_id,
    jsonb_build_object(
        'ui_state', jsonb_build_object(
            'current_view', 'world',
            'sidebar_collapsed', false
        ),
        'game_state', jsonb_build_object(
            'tutorial_completed', true,
            'last_scene_id', NULL
        )
    ) as session_data,
    GREATEST(c.updated_at, c.last_active_at) as last_activity_at
FROM characters c
WHERE c.user_id IS NOT NULL AND c.world_id IS NOT NULL
ON CONFLICT (user_id, world_id) DO NOTHING;

-- =====================================================
-- 阶段9：数据验证和清理
-- =====================================================

-- 1. 验证数据完整性
DO $$
BEGIN
    -- 检查用户档案数据
    IF EXISTS (SELECT 1 FROM users WHERE profile IS NULL OR profile = '{}') THEN
        RAISE NOTICE '警告：存在未迁移的用户档案数据';
    END IF;

    -- 检查世界设置数据
    IF EXISTS (SELECT 1 FROM worlds WHERE access_settings IS NULL OR access_settings = '{}') THEN
        RAISE NOTICE '警告：存在未迁移的世界设置数据';
    END IF;

    -- 检查角色特征数据
    IF EXISTS (SELECT 1 FROM characters WHERE characteristics IS NULL OR characteristics = '{}') THEN
        RAISE NOTICE '警告：存在未迁移的角色特征数据';
    END IF;

    RAISE NOTICE '数据迁移验证完成';
END $$;

-- 2. 更新统计信息
ANALYZE user_sessions;
ANALYZE character_memories;
ANALYZE character_experiences;
ANALYZE specialized_entities;
ANALYZE game_events;