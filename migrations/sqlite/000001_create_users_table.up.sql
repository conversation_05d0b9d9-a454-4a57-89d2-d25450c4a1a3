-- SQLite版本：创建用户基础信息表
CREATE TABLE users (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(6)))),
    external_id VARCHAR(255) NOT NULL,  -- 外部IDP用户ID
    external_provider VARCHAR(50) NOT NULL,    -- IDP提供商 (auth0, keycloak, etc.)
    email VARCHAR(255) NOT NULL,               -- 从IDP获取的邮箱
    display_name VARCHAR(100),                 -- 显示名称
    avatar_url TEXT,                           -- 头像URL
    game_roles TEXT DEFAULT '["user"]',        -- 游戏内角色 (JSON格式)
    status VARCHAR(20) DEFAULT 'active',       -- active, suspended, deleted
    preferences TEXT DEFAULT '{}',             -- 用户偏好设置 (JSON格式)
    idp_claims TEXT DEFAULT '{}',              -- 从IDP获取的额外声明 (JSON格式)
    created_at DATETIME DEFAULT (datetime('now')),
    updated_at DATETIME DEFAULT (datetime('now')),
    last_login_at DATETIME,
    deleted_at DATETIME,

    -- 约束
    CONSTRAINT unique_external_user UNIQUE (external_id, external_provider)
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_external_provider ON users(external_provider);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);

-- 创建用户统计信息表
CREATE TABLE user_stats (
    user_id TEXT PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    total_play_time INTEGER DEFAULT 0, -- 总游戏时间(分钟)
    worlds_created INTEGER DEFAULT 0, -- 创建的世界数量
    worlds_joined INTEGER DEFAULT 0, -- 加入的世界数量
    achievements TEXT DEFAULT '[]', -- 成就列表 (JSON格式)
    level INTEGER DEFAULT 1, -- 用户等级
    experience INTEGER DEFAULT 0, -- 经验值
    updated_at DATETIME DEFAULT (datetime('now'))
);

-- 创建索引
CREATE INDEX idx_user_stats_level ON user_stats(level);
CREATE INDEX idx_user_stats_experience ON user_stats(experience);

-- 创建更新时间触发器 (SQLite版本)
CREATE TRIGGER update_users_updated_at 
    AFTER UPDATE ON users 
    FOR EACH ROW 
    WHEN NEW.updated_at = OLD.updated_at
    BEGIN 
        UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;
    END;

-- 为用户统计表创建更新时间触发器
CREATE TRIGGER update_user_stats_updated_at 
    AFTER UPDATE ON user_stats 
    FOR EACH ROW 
    WHEN NEW.updated_at = OLD.updated_at
    BEGIN 
        UPDATE user_stats SET updated_at = datetime('now') WHERE user_id = NEW.user_id;
    END;
