-- 升级到数据库设计文档v4.0架构 (SQLite版本)
-- 这个迁移脚本将现有SQLite数据库升级到新的优化架构

-- =====================================================
-- 阶段1：创建新的独立表
-- =====================================================

-- 1. 创建用户会话管理表
CREATE TABLE user_sessions (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(6)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    world_id TEXT NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,

    -- 会话状态（高频使用）
    active_character_id TEXT, -- 当前活跃角色（软引用）
    session_data TEXT NOT NULL DEFAULT '{}', -- 会话相关数据

    -- 时间管理
    created_at DATETIME DEFAULT (datetime('now')),
    updated_at DATETIME DEFAULT (datetime('now')),
    last_activity_at DATETIME DEFAULT (datetime('now')),

    -- 约束
    CONSTRAINT unique_user_world_session UNIQUE(user_id, world_id)
);

-- 2. 创建角色记忆表
CREATE TABLE character_memories (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(6)))),
    character_id TEXT NOT NULL REFERENCES characters(id) ON DELETE CASCADE,

    -- 记忆内容（高频使用）
    content TEXT NOT NULL, -- 记忆内容描述
    memory_type VARCHAR(50) NOT NULL, -- event, person, location, item, knowledge, emotion

    -- 记忆强度系统（中频使用）
    importance_score REAL NOT NULL DEFAULT 0.5 CHECK (importance_score >= 0 AND importance_score <= 1),
    emotional_impact REAL NOT NULL DEFAULT 0.0 CHECK (emotional_impact >= -1 AND emotional_impact <= 1),
    current_strength REAL NOT NULL DEFAULT 1.0 CHECK (current_strength >= 0 AND current_strength <= 1),

    -- 记忆衰减（中频使用）
    decay_rate REAL NOT NULL DEFAULT 0.05 CHECK (decay_rate >= 0 AND decay_rate <= 1),
    last_accessed DATETIME DEFAULT (datetime('now')),

    -- 关联信息（低频使用）
    associated_entities TEXT DEFAULT '[]', -- 关联的实体ID
    tags TEXT DEFAULT '[]', -- 记忆标签
    context TEXT DEFAULT '{}', -- 记忆上下文信息

    -- 时间戳
    created_at DATETIME DEFAULT (datetime('now')),
    updated_at DATETIME DEFAULT (datetime('now'))
);

-- 3. 创建角色阅历表
CREATE TABLE character_experiences (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(6)))),
    character_id TEXT NOT NULL REFERENCES characters(id) ON DELETE CASCADE,

    -- 阅历分类（高频使用）
    tags TEXT NOT NULL DEFAULT '[]', -- 阅历标签（替代固定分类）
    description TEXT NOT NULL, -- 阅历描述

    -- 熟练度系统（高频使用）
    proficiency TEXT NOT NULL DEFAULT '{}', -- 熟练度信息

    -- 传承和影响（中频使用）
    learning_info TEXT DEFAULT '{}', -- 学习和传承信息
    social_impact TEXT DEFAULT '{}', -- 社交影响

    -- 应用效果（中频使用）
    effects TEXT DEFAULT '{}', -- 阅历效果

    -- 时间信息
    acquired_at DATETIME DEFAULT (datetime('now')),
    last_used DATETIME DEFAULT (datetime('now')),
    updated_at DATETIME DEFAULT (datetime('now'))
);

-- 4. 创建专门实体扩展表
CREATE TABLE specialized_entities (
    entity_id TEXT PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
    specialization_type VARCHAR(50) NOT NULL, -- item, event, goal, location, abstract

    -- 专门属性（中频使用）
    specialized_data TEXT NOT NULL DEFAULT '{}', -- 类型特定的数据

    -- 时间戳
    created_at DATETIME DEFAULT (datetime('now')),
    updated_at DATETIME DEFAULT (datetime('now'))
);

-- 5. 创建游戏事件日志表（替代原events表）
CREATE TABLE game_events (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(6)))),
    world_id TEXT NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,

    -- 事件分类（高频使用）
    event_type VARCHAR(50) NOT NULL, -- action, evolution, system, heartbeat
    tags TEXT NOT NULL DEFAULT '[]', -- 事件标签

    -- 参与者信息（高频使用）
    primary_actor_id TEXT, -- 主要触发者（软引用）
    participants TEXT NOT NULL DEFAULT '[]', -- 所有参与者
    scene_id TEXT, -- 发生场景（软引用）

    -- 事件内容（高频使用）
    narrative_text TEXT, -- AI生成的叙事文本
    event_data TEXT NOT NULL DEFAULT '{}', -- 事件详细数据

    -- 处理结果（高频使用）
    processing_result TEXT DEFAULT '{}', -- AI处理结果

    -- 时间信息（中频使用）
    game_time INTEGER NOT NULL, -- 游戏内时间
    processing_duration_ms INTEGER, -- 处理耗时

    -- 时间戳
    created_at DATETIME DEFAULT (datetime('now'))
);

-- =====================================================
-- 阶段2：为现有表添加新字段
-- =====================================================

-- 1. 扩展users表
ALTER TABLE users ADD COLUMN profile TEXT DEFAULT '{}';
ALTER TABLE users ADD COLUMN last_active_at DATETIME;

-- 2. 扩展worlds表
ALTER TABLE worlds ADD COLUMN access_settings TEXT DEFAULT '{}';
ALTER TABLE worlds ADD COLUMN tags TEXT DEFAULT '[]';
ALTER TABLE worlds ADD COLUMN time_config TEXT DEFAULT '{}';

-- 3. 扩展scenes表
ALTER TABLE scenes ADD COLUMN tags TEXT DEFAULT '[]';
ALTER TABLE scenes ADD COLUMN access_rules TEXT DEFAULT '{}';
ALTER TABLE scenes ADD COLUMN connections TEXT DEFAULT '[]';

-- 4. 扩展characters表
ALTER TABLE characters ADD COLUMN characteristics TEXT DEFAULT '{}';
ALTER TABLE characters ADD COLUMN is_primary BOOLEAN DEFAULT false;
ALTER TABLE characters ADD COLUMN display_order INTEGER DEFAULT 0;
ALTER TABLE characters ADD COLUMN last_active_at DATETIME;

-- 5. 扩展entities表
ALTER TABLE entities ADD COLUMN container_entity_id TEXT REFERENCES entities(id);
ALTER TABLE entities ADD COLUMN version INTEGER NOT NULL DEFAULT 1;
ALTER TABLE entities ADD COLUMN tags TEXT DEFAULT '[]';

-- =====================================================
-- 阶段3：创建SQLite索引
-- =====================================================

-- user_sessions表索引
CREATE INDEX idx_user_sessions_active ON user_sessions(user_id, world_id, active_character_id);
CREATE INDEX idx_user_sessions_activity ON user_sessions(last_activity_at DESC);

-- character_memories表索引
CREATE INDEX idx_character_memories_character ON character_memories(character_id, importance_score DESC, created_at DESC);
CREATE INDEX idx_character_memories_type ON character_memories(character_id, memory_type, current_strength DESC);

-- character_experiences表索引
CREATE INDEX idx_character_experiences_character ON character_experiences(character_id, last_used DESC);

-- specialized_entities表索引
CREATE INDEX idx_specialized_entities_type ON specialized_entities(specialization_type);

-- game_events表索引
CREATE INDEX idx_game_events_world_time ON game_events(world_id, game_time DESC);
CREATE INDEX idx_game_events_type ON game_events(world_id, event_type, created_at DESC);
CREATE INDEX idx_game_events_actor ON game_events(primary_actor_id, created_at DESC);
CREATE INDEX idx_game_events_scene ON game_events(scene_id, created_at DESC);

-- 现有表的新字段索引（SQLite JSON索引）
CREATE INDEX idx_users_profile_display_name ON users (json_extract(profile, '$.display_name'));
CREATE INDEX idx_worlds_config_genre ON worlds (json_extract(world_config, '$.theme.genre'));
CREATE INDEX idx_scenes_properties_type ON scenes (json_extract(properties, '$.physical.terrain_type'));

-- 唯一约束（SQLite方式）
CREATE UNIQUE INDEX idx_characters_primary_unique ON characters(user_id, world_id)
    WHERE is_primary = 1;