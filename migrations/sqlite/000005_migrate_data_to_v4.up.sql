-- 数据迁移到v4.0架构 (SQLite版本)
-- 这个脚本负责将现有SQLite数据迁移到新的表结构中

-- =====================================================
-- 阶段1：迁移users表数据
-- =====================================================

-- 1. 迁移用户档案数据到profile字段
UPDATE users SET profile =
    json_object(
        'display_name', COALESCE(display_name, ''),
        'avatar', json_object(
            'url', COALESCE(avatar_url, ''),
            'source', 'external'
        ),
        'locale', 'zh-CN',
        'timezone', 'Asia/Shanghai',
        'ui_preferences', json_object(
            'theme', 'auto',
            'language', 'zh-CN',
            'notifications', 1
        )
    )
WHERE display_name IS NOT NULL OR avatar_url IS NOT NULL;

-- 2. 设置用户最后活跃时间
UPDATE users SET last_active_at = updated_at WHERE updated_at IS NOT NULL;

-- =====================================================
-- 阶段2：迁移worlds表数据
-- =====================================================

-- 1. 迁移世界访问设置
UPDATE worlds SET
    access_settings = json_object(
        'is_public', COALESCE(is_public, 0),
        'max_players', COALESCE(max_players, 10),
        'join_policy', CASE WHEN COALESCE(is_public, 0) = 1 THEN 'open' ELSE 'invite' END,
        'player_permissions', json_object(
            'can_create_characters', 1,
            'can_modify_world', 0,
            'can_invite_others', 0
        ),
        'content_rating', 'general',
        'moderation', json_object(
            'auto_moderation', 1,
            'content_filters', json_array('violence', 'adult_content')
        )
    );

-- 2. 设置世界标签（基于现有数据推断）
UPDATE worlds SET tags =
    CASE
        WHEN json_extract(world_config, '$.theme') IS NOT NULL THEN
            json_array(json_extract(world_config, '$.theme'))
        ELSE '[]'
    END;

-- 3. 设置时间配置
UPDATE worlds SET time_config =
    json_object(
        'time_multiplier', 1.0,
        'pause_when_empty', 1,
        'day_night_cycle', 1,
        'season_length_days', 30,
        'weather_system', 1,
        'calendar', json_object(
            'type', 'gregorian',
            'start_date', '2024-01-01'
        )
    );

-- =====================================================
-- 阶段3：迁移scenes表数据
-- =====================================================

-- 1. 将scene_type转换为tags
UPDATE scenes SET tags =
    CASE
        WHEN scene_type IS NOT NULL AND scene_type != '' THEN
            json_array(scene_type)
        ELSE '[]'
    END;

-- 2. 设置场景访问规则
UPDATE scenes SET access_rules =
    json_object(
        'visibility', 'public',
        'entry_requirements', json_array(),
        'capacity', json_object(
            'description', COALESCE(description, '这里可以容纳很多人'),
            'soft_limit', 20,
            'hard_limit', 50
        ),
        'restrictions', json_object(
            'time_based', json_array(),
            'character_based', json_array()
        )
    );

-- 3. 转换场景连接格式（SQLite简化版本）
-- 注意：SQLite不支持复杂的JSON操作，这里使用简化的迁移策略
UPDATE scenes SET connections = '[]'
WHERE connected_scenes IS NOT NULL AND connected_scenes != '{}';

-- 手动处理连接数据（需要应用层协助）
-- 这部分将在GORM模型中通过代码逻辑处理

-- =====================================================
-- 阶段4：迁移characters表数据
-- =====================================================

-- 1. 迁移角色特征数据（SQLite简化版本）
UPDATE characters SET characteristics =
    json_object(
        'traits', '[]',
        'appearance', json_object(
            'description', COALESCE(description, '一个有趣的角色'),
            'distinctive_features', json_array(),
            'clothing', json_object(
                'style', 'casual',
                'description', '普通的服装'
            )
        ),
        'personality', json_object(
            'core_values', json_array(),
            'motivations', json_array(),
            'fears', json_array(),
            'quirks', json_array()
        ),
        'background', json_object(
            'origin', '未知的过去',
            'family', json_array(),
            'education', json_array()
        )
    );

-- 2. 设置主要角色标记
UPDATE characters SET is_primary = 1
WHERE rowid IN (
    SELECT MIN(rowid)
    FROM characters
    GROUP BY user_id, world_id
);

-- 3. 设置显示顺序
UPDATE characters SET display_order = (
    SELECT COUNT(*)
    FROM characters c2
    WHERE c2.user_id = characters.user_id
      AND c2.world_id = characters.world_id
      AND (c2.is_primary > characters.is_primary
           OR (c2.is_primary = characters.is_primary AND c2.rowid < characters.rowid))
);

-- 4. 设置角色最后活跃时间
UPDATE characters SET last_active_at = updated_at WHERE updated_at IS NOT NULL;

-- =====================================================
-- 阶段5：迁移entities表数据
-- =====================================================

-- 1. 设置实体标签
UPDATE entities SET tags =
    CASE
        WHEN entity_type IS NOT NULL AND entity_type != '' THEN
            json_array(entity_type)
        ELSE '[]'
    END;

-- 2. 为特殊实体创建specialized_entities记录
INSERT INTO specialized_entities (entity_id, specialization_type, specialized_data)
SELECT
    e.id as entity_id,
    CASE
        WHEN e.entity_type IN ('weapon', 'armor', 'tool', 'consumable', 'treasure') THEN 'item'
        WHEN e.entity_type IN ('quest', 'mission', 'objective') THEN 'goal'
        WHEN e.entity_type IN ('building', 'landmark', 'area') THEN 'location'
        WHEN e.entity_type IN ('spell', 'ability', 'skill') THEN 'abstract'
        ELSE 'item'
    END as specialization_type,
    json_object(
        'original_type', e.entity_type,
        'migrated_properties', e.properties,
        'migration_source', 'entities_table'
    ) as specialized_data
FROM entities e
WHERE e.entity_type IS NOT NULL;

-- =====================================================
-- 阶段6：创建初始会话数据
-- =====================================================

-- 为现有的用户-世界组合创建会话记录
INSERT INTO user_sessions (user_id, world_id, active_character_id, session_data, last_activity_at)
SELECT DISTINCT
    c.user_id,
    c.world_id,
    -- 选择主要角色作为活跃角色
    (SELECT id FROM characters c2
     WHERE c2.user_id = c.user_id
       AND c2.world_id = c.world_id
       AND c2.is_primary = 1
     LIMIT 1) as active_character_id,
    json_object(
        'ui_state', json_object(
            'current_view', 'world',
            'sidebar_collapsed', 0
        ),
        'game_state', json_object(
            'tutorial_completed', 1,
            'last_scene_id', NULL
        )
    ) as session_data,
    MAX(COALESCE(c.updated_at, c.last_active_at, c.created_at)) as last_activity_at
FROM characters c
WHERE c.user_id IS NOT NULL AND c.world_id IS NOT NULL
GROUP BY c.user_id, c.world_id;