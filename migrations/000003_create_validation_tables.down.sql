-- 删除函数
DROP FUNCTION IF EXISTS get_validation_stats(UUID, INTEGER);
DROP FUNCTION IF EXISTS cleanup_old_validation_logs();

-- 删除触发器
DROP TRIGGER IF EXISTS update_user_rate_limits_updated_at ON user_rate_limits;
DROP TRIGGER IF EXISTS update_content_moderation_rules_updated_at ON content_moderation_rules;
DROP TRIGGER IF EXISTS update_profanity_words_updated_at ON profanity_words;

-- 删除表（注意删除顺序，先删除有外键依赖的表）
DROP TABLE IF EXISTS content_moderation_logs;
DROP TABLE IF EXISTS user_rate_limits;
DROP TABLE IF EXISTS content_moderation_rules;
DROP TABLE IF EXISTS profanity_words;
DROP TABLE IF EXISTS validation_logs;
