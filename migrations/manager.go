package migrations

import (
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
)

// MigrationRecord 迁移记录
type MigrationRecord struct {
	ID        uint      `gorm:"primaryKey"`
	Version   string    `gorm:"uniqueIndex;not null"`
	AppliedAt time.Time `gorm:"not null"`
	Rollback  bool      `gorm:"default:false"`
}

// TableName 指定表名
func (MigrationRecord) TableName() string {
	return "migration_records"
}

// Migration 迁移接口
type Migration interface {
	Up() error   // 向上迁移
	Down() error // 向下迁移
}

// Manager 迁移管理器
type Manager struct {
	db         *gorm.DB
	migrations map[string]Migration
}

// NewManager 创建迁移管理器
func NewManager(db *gorm.DB) *Manager {
	manager := &Manager{
		db:         db,
		migrations: make(map[string]Migration),
	}

	// 注册所有迁移
	manager.registerMigrations()

	return manager
}

// registerMigrations 注册所有迁移
func (m *Manager) registerMigrations() {
	// 注册v4.0迁移
	m.migrations["v4.0"] = NewV4Migration(m.db)
}

// Init 初始化迁移系统
func (m *Manager) Init() error {
	// 创建迁移记录表
	if err := m.db.AutoMigrate(&MigrationRecord{}); err != nil {
		return fmt.Errorf("创建迁移记录表失败: %w", err)
	}

	log.Println("迁移系统初始化完成")
	return nil
}

// Migrate 执行迁移到指定版本
func (m *Manager) Migrate(targetVersion string) error {
	log.Printf("开始迁移到版本: %s", targetVersion)

	// 检查目标版本是否存在
	migration, exists := m.migrations[targetVersion]
	if !exists {
		return fmt.Errorf("未找到版本 %s 的迁移", targetVersion)
	}

	// 检查是否已经应用过该迁移
	var record MigrationRecord
	err := m.db.Where("version = ? AND rollback = ?", targetVersion, false).First(&record).Error
	if err == nil {
		log.Printf("版本 %s 已经应用过，跳过迁移", targetVersion)
		return nil
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("检查迁移记录失败: %w", err)
	}

	// 执行迁移
	if err := migration.Up(); err != nil {
		return fmt.Errorf("执行迁移失败: %w", err)
	}

	// 记录迁移
	record = MigrationRecord{
		Version:   targetVersion,
		AppliedAt: time.Now(),
		Rollback:  false,
	}
	if err := m.db.Create(&record).Error; err != nil {
		return fmt.Errorf("记录迁移失败: %w", err)
	}

	log.Printf("迁移到版本 %s 完成", targetVersion)
	return nil
}

// Rollback 回滚指定版本的迁移
func (m *Manager) Rollback(version string) error {
	log.Printf("开始回滚版本: %s", version)

	// 检查版本是否存在
	migration, exists := m.migrations[version]
	if !exists {
		return fmt.Errorf("未找到版本 %s 的迁移", version)
	}

	// 检查是否已经应用过该迁移
	var record MigrationRecord
	err := m.db.Where("version = ? AND rollback = ?", version, false).First(&record).Error
	if err == gorm.ErrRecordNotFound {
		log.Printf("版本 %s 未应用过，无需回滚", version)
		return nil
	}
	if err != nil {
		return fmt.Errorf("检查迁移记录失败: %w", err)
	}

	// 执行回滚
	if err := migration.Down(); err != nil {
		return fmt.Errorf("执行回滚失败: %w", err)
	}

	// 记录回滚
	rollbackRecord := MigrationRecord{
		Version:   version,
		AppliedAt: time.Now(),
		Rollback:  true,
	}
	if err := m.db.Create(&rollbackRecord).Error; err != nil {
		return fmt.Errorf("记录回滚失败: %w", err)
	}

	log.Printf("回滚版本 %s 完成", version)
	return nil
}

// Status 显示迁移状态
func (m *Manager) Status() error {
	log.Println("迁移状态:")

	var records []MigrationRecord
	if err := m.db.Order("applied_at DESC").Find(&records).Error; err != nil {
		return fmt.Errorf("查询迁移记录失败: %w", err)
	}

	if len(records) == 0 {
		log.Println("  无迁移记录")
		return nil
	}

	for _, record := range records {
		status := "应用"
		if record.Rollback {
			status = "回滚"
		}
		log.Printf("  版本: %s, 状态: %s, 时间: %s", 
			record.Version, status, record.AppliedAt.Format("2006-01-02 15:04:05"))
	}

	return nil
}

// GetCurrentVersion 获取当前版本
func (m *Manager) GetCurrentVersion() (string, error) {
	var record MigrationRecord
	err := m.db.Where("rollback = ?", false).Order("applied_at DESC").First(&record).Error
	if err == gorm.ErrRecordNotFound {
		return "", nil // 无迁移记录
	}
	if err != nil {
		return "", fmt.Errorf("查询当前版本失败: %w", err)
	}

	return record.Version, nil
}

// ListAvailableVersions 列出可用版本
func (m *Manager) ListAvailableVersions() []string {
	versions := make([]string, 0, len(m.migrations))
	for version := range m.migrations {
		versions = append(versions, version)
	}
	return versions
}

// AutoMigrate 自动迁移到最新版本
func (m *Manager) AutoMigrate() error {
	log.Println("开始自动迁移到最新版本...")

	// 目前只有v4.0版本
	latestVersion := "v4.0"
	
	currentVersion, err := m.GetCurrentVersion()
	if err != nil {
		return fmt.Errorf("获取当前版本失败: %w", err)
	}

	if currentVersion == latestVersion {
		log.Printf("已经是最新版本 %s", latestVersion)
		return nil
	}

	// 执行迁移
	if err := m.Migrate(latestVersion); err != nil {
		return fmt.Errorf("自动迁移失败: %w", err)
	}

	log.Printf("自动迁移到版本 %s 完成", latestVersion)
	return nil
}

// ValidateSchema 验证数据库架构
func (m *Manager) ValidateSchema() error {
	log.Println("验证数据库架构...")

	// 检查必要的表是否存在
	requiredTables := []string{
		"users", "user_stats", "worlds", "characters", "entities", "scenes",
		"user_sessions", "character_memories", "character_experiences", 
		"specialized_entities", "game_events",
	}

	for _, table := range requiredTables {
		if !m.db.Migrator().HasTable(table) {
			return fmt.Errorf("缺少必要的表: %s", table)
		}
	}

	log.Println("数据库架构验证通过")
	return nil
}

// RepairSchema 修复数据库架构
func (m *Manager) RepairSchema() error {
	log.Println("修复数据库架构...")

	// 重新运行所有模型的自动迁移
	models := []interface{}{
		&MigrationRecord{},
	}

	for _, model := range models {
		if err := m.db.AutoMigrate(model); err != nil {
			return fmt.Errorf("修复表结构失败: %w", err)
		}
	}

	log.Println("数据库架构修复完成")
	return nil
}
