-- 创建用户基础信息表 (外部IDP集成)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    external_id VARCHAR(255) NOT NULL,  -- 外部IDP用户ID
    external_provider VARCHAR(50) NOT NULL,    -- IDP提供商 (auth0, keycloak, etc.)
    email VARCHAR(255) NOT NULL,               -- 从IDP获取的邮箱
    display_name VARCHAR(100),                 -- 显示名称
    avatar_url TEXT,                           -- 头像URL
    game_roles JSONB DEFAULT '["user"]',       -- 游戏内角色 ["user", "premium", "admin", "developer"]
    status VARCHAR(20) DEFAULT 'active',       -- active, suspended, deleted
    preferences JSONB DEFAULT '{}',            -- 用户偏好设置
    idp_claims JSONB DEFAULT '{}',             -- 从IDP获取的额外声明
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,

    -- 约束
    CONSTRAINT unique_external_user UNIQUE (external_id, external_provider)
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_external_provider ON users(external_provider);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 创建用户统计信息表
CREATE TABLE user_stats (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    total_play_time INTEGER DEFAULT 0, -- 总游戏时间(分钟)
    worlds_created INTEGER DEFAULT 0, -- 创建的世界数量
    worlds_joined INTEGER DEFAULT 0, -- 加入的世界数量
    achievements JSONB DEFAULT '[]', -- 成就列表
    level INTEGER DEFAULT 1, -- 用户等级
    experience INTEGER DEFAULT 0, -- 经验值
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_user_stats_level ON user_stats(level);
CREATE INDEX idx_user_stats_experience ON user_stats(experience);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为用户表创建更新时间触发器
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 为用户统计表创建更新时间触发器
CREATE TRIGGER update_user_stats_updated_at 
    BEFORE UPDATE ON user_stats 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
