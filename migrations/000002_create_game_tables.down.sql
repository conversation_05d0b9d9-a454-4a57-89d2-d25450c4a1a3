-- 删除触发器
DROP TRIGGER IF EXISTS update_events_updated_at ON events;
DROP TRIGGER IF EXISTS update_entities_updated_at ON entities;
DROP TRIGGER IF EXISTS update_characters_updated_at ON characters;
DROP TRIGGER IF EXISTS update_scenes_updated_at ON scenes;
DROP TRIGGER IF EXISTS update_worlds_updated_at ON worlds;

-- 删除表（注意删除顺序，先删除有外键依赖的表）
DROP TABLE IF EXISTS ai_interactions;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS entities;
DROP TABLE IF EXISTS characters;
DROP TABLE IF EXISTS scenes;
DROP TABLE IF EXISTS worlds;
