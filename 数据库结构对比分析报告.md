# 数据库结构对比分析报告

**版本**: v1.0
**分析日期**: 2025-01-08
**基于**: 现有代码结构 vs 数据库表设计文档v4.0

## 分析概述

本报告详细对比了现有数据库结构与目标设计文档中的表结构，识别了需要进行的变更和迁移策略。

## 第一部分：现有数据库结构分析

### 1. 现有表结构总览

| 表名 | 模型文件 | 主要字段 | JSON字段 | 状态 |
|------|----------|----------|----------|------|
| users | user.go | id, external_id, email, display_name | preferences, idp_claims, game_roles | ✅ 已实现 |
| user_stats | user.go | user_id, level, experience | achievements | ✅ 已实现 |
| worlds | world.go | id, name, creator_id | world_config, world_state | ✅ 已实现 |
| scenes | scene.go | id, world_id, name, scene_type | properties, environment, connected_scenes | ✅ 已实现 |
| characters | character.go | id, world_id, user_id, name | traits, memories, experiences, relationships | ✅ 已实现 |
| entities | entity.go | id, world_id, entity_type | properties, traits | ✅ 已实现 |
| events | entity.go | id, world_id, event_type | event_data, participants, result | ✅ 已实现 |
| ai_interactions | entity.go | id, interaction_type, prompt | response_schema | ✅ 已实现 |

### 2. 现有JSON字段结构

#### users表JSON字段
- **preferences**: 用户偏好设置 (已实现)
- **idp_claims**: IDP声明信息 (已实现)
- **game_roles**: 游戏角色数组 (已实现)

#### worlds表JSON字段
- **world_config**: 世界配置 (已实现，但结构简单)
- **world_state**: 世界状态 (已实现，但结构简单)

#### scenes表JSON字段
- **properties**: 场景属性 (已实现)
- **environment**: 环境信息 (已实现)
- **connected_scenes**: 连接场景 (已实现，但结构简单)

#### characters表JSON字段
- **traits**: 特质数组 (已实现，但为字符串数组)
- **memories**: 记忆系统 (已实现，但结构简单)
- **experiences**: 阅历系统 (已实现，但结构简单)
- **relationships**: 关系网络 (已实现，但结构简单)

## 第二部分：目标结构对比分析

### 1. 需要新增的表

| 表名 | 用途 | 优先级 | 复杂度 |
|------|------|--------|--------|
| user_sessions | 用户会话管理 | 高 | 中 |
| character_memories | 独立的角色记忆表 | 高 | 高 |
| character_experiences | 独立的角色阅历表 | 高 | 高 |
| specialized_entities | 专门实体扩展表 | 中 | 中 |
| game_events | 游戏事件日志表 (替代events) | 高 | 中 |

### 2. 需要重构的表

#### users表重构需求
- **新增字段**: profile (合并display_name, avatar_url等)
- **字段调整**: 将display_name, avatar_url合并到profile JSON字段
- **索引优化**: 添加JSON字段索引

#### worlds表重构需求
- **字段调整**:
  - 新增 access_settings JSON字段
  - 新增 tags JSON数组字段
  - 新增 time_config JSON字段
  - 移除 is_public, max_players (合并到access_settings)
- **JSON结构增强**: world_config和world_state需要更复杂的结构

#### scenes表重构需求
- **字段调整**:
  - 移除 scene_type (使用tags替代)
  - 移除 entities_present (改为基于entities表查询)
  - 重构 connected_scenes 为 connections (更复杂的结构)
  - 新增 access_rules JSON字段
- **索引优化**: 添加JSON字段索引

#### characters表重构需求
- **字段调整**:
  - traits从StringArray改为复杂的JSON结构
  - 移除memories, experiences, relationships (拆分为独立表)
  - 新增 characteristics JSON字段
  - 新增 is_primary, display_order字段
- **关联调整**: 与新的独立表建立关联

#### entities表重构需求
- **字段调整**:
  - 新增 container_entity_id字段 (支持容器关系)
  - 新增 version字段 (版本控制)
  - 调整位置约束 (三选一约束)
- **关联调整**: 与specialized_entities表建立关联

### 3. 需要移除的表
- 无需移除现有表，但events表需要重构为game_events

## 第三部分：字段映射和数据迁移策略

### 1. users表迁移策略

```sql
-- 数据迁移示例
UPDATE users SET
  profile = jsonb_build_object(
    'display_name', display_name,
    'avatar', jsonb_build_object(
      'url', avatar_url,
      'source', 'external'
    ),
    'locale', 'zh-CN'
  )
WHERE display_name IS NOT NULL OR avatar_url IS NOT NULL;
```

### 2. worlds表迁移策略

```sql
-- 数据迁移示例
UPDATE worlds SET
  access_settings = jsonb_build_object(
    'is_public', is_public,
    'max_players', max_players,
    'join_policy', CASE WHEN is_public THEN 'open' ELSE 'invite' END
  ),
  tags = '[]'::jsonb,
  time_config = jsonb_build_object(
    'time_multiplier', 1.0,
    'pause_when_empty', true
  );
```

### 3. scenes表迁移策略

```sql
-- 数据迁移示例
UPDATE scenes SET
  tags = jsonb_build_array(scene_type),
  access_rules = jsonb_build_object(
    'visibility', 'public',
    'entry_requirements', '[]'::jsonb
  );
```

## 第四部分：索引优化策略

### 1. PostgreSQL索引策略

```sql
-- 用户表JSON索引
CREATE INDEX idx_users_profile_gin ON users USING GIN (profile);
CREATE INDEX idx_users_profile_display_name ON users ((profile->>'display_name'));

-- 世界表JSON索引
CREATE INDEX idx_worlds_config_gin ON worlds USING GIN (world_config);
CREATE INDEX idx_worlds_tags_gin ON worlds USING GIN (tags);

-- 场景表JSON索引
CREATE INDEX idx_scenes_properties_gin ON scenes USING GIN (properties);
CREATE INDEX idx_scenes_tags_gin ON scenes USING GIN (tags);
```

### 2. SQLite索引策略

```sql
-- 用户表JSON索引
CREATE INDEX idx_users_profile_display_name ON users (json_extract(profile, '$.display_name'));

-- 世界表JSON索引
CREATE INDEX idx_worlds_config_genre ON worlds (json_extract(world_config, '$.theme.genre'));

-- 场景表JSON索引
CREATE INDEX idx_scenes_properties_type ON scenes (json_extract(properties, '$.physical.terrain_type'));
```

## 第五部分：迁移风险评估

### 1. 高风险项目
- **字段结构变更**: traits从字符串数组改为复杂JSON结构
- **表拆分**: characters表的memories/experiences拆分为独立表
- **约束变更**: entities表的位置约束调整

### 2. 中风险项目
- **JSON字段合并**: users表的profile字段合并
- **字段重命名**: scenes表的connected_scenes改为connections
- **新表创建**: user_sessions等新表的创建

### 3. 低风险项目
- **索引添加**: 新增JSON字段索引
- **默认值调整**: 字段默认值的修改
- **注释更新**: 表和字段注释的更新

## 第六部分：迁移时间估算

| 阶段 | 任务 | 预估时间 | 依赖关系 |
|------|------|----------|----------|
| 1 | 表结构分析和设计 | 1天 | 无 |
| 2 | 迁移脚本开发 | 2-3天 | 阶段1 |
| 3 | GORM模型更新 | 2天 | 阶段2 |
| 4 | 测试和验证 | 1-2天 | 阶段3 |
| 5 | 文档和部署 | 1天 | 阶段4 |

**总计**: 7-9个工作日

## 第七部分：建议的实施顺序

1. **第一批**: 低风险的字段添加和索引创建
2. **第二批**: 中风险的JSON字段重构
3. **第三批**: 高风险的表拆分和约束变更
4. **第四批**: 新表创建和关联建立
5. **第五批**: 数据验证和清理

这种分批实施的方式可以最大程度降低风险，确保每个阶段都可以独立验证和回滚。