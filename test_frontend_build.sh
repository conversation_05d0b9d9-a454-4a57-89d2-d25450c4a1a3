#!/bin/bash

# 前端构建测试脚本
# 用于验证前端API集成后的构建状态

echo "🚀 开始前端API集成测试..."
echo "=================================="

# 检查当前目录
if [ ! -d "web/frontend" ]; then
    echo "❌ 错误: 未找到 web/frontend 目录"
    echo "请确保在项目根目录下运行此脚本"
    exit 1
fi

cd web/frontend

echo "📦 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "📥 安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
else
    echo "✅ 依赖已存在"
fi

echo ""
echo "🔍 检查TypeScript类型..."
npx tsc --noEmit
if [ $? -eq 0 ]; then
    echo "✅ TypeScript类型检查通过"
else
    echo "❌ TypeScript类型检查失败"
    echo "请检查类型错误并修复"
fi

echo ""
echo "🏗️  尝试构建前端..."
npm run build
if [ $? -eq 0 ]; then
    echo "✅ 前端构建成功"
    echo "📁 构建文件位于: web/frontend/dist/"
    
    # 检查构建文件
    if [ -f "dist/index.html" ]; then
        echo "✅ index.html 已生成"
    else
        echo "❌ index.html 未找到"
    fi
    
    if [ -d "dist/assets" ]; then
        echo "✅ assets 目录已生成"
        echo "📊 构建文件大小:"
        du -sh dist/assets/*
    else
        echo "❌ assets 目录未找到"
    fi
else
    echo "❌ 前端构建失败"
    echo "请检查构建错误并修复"
    exit 1
fi

echo ""
echo "📋 API集成检查清单:"
echo "=================================="

# 检查API文件是否存在
api_files=(
    "src/store/api/apiSlice.ts"
    "src/store/api/authApi.ts"
    "src/store/api/worldApi.ts"
    "src/store/api/characterApi.ts"
    "src/store/api/gameInteractionApi.ts"
    "src/store/api/aiApi.ts"
    "src/store/api/index.ts"
)

for file in "${api_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺失)"
    fi
done

echo ""
echo "🔧 服务文件检查:"
service_files=(
    "src/services/authService.ts"
    "src/services/gameService.ts"
    "src/services/index.ts"
)

for file in "${service_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺失)"
    fi
done

echo ""
echo "📱 页面组件检查:"
page_files=(
    "src/pages/LoginPage.tsx"
    "src/pages/AuthCallbackPage.tsx"
    "src/pages/GameLobbyPage.tsx"
    "src/components/AppInitializer.tsx"
)

for file in "${page_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺失)"
    fi
done

echo ""
echo "🎯 测试建议:"
echo "=================================="
echo "1. 启动后端服务器:"
echo "   cd ../../"
echo "   go run cmd/main.go"
echo ""
echo "2. 启动前端开发服务器:"
echo "   cd web/frontend"
echo "   npm run dev"
echo ""
echo "3. 在浏览器中访问 http://localhost:5173"
echo ""
echo "4. 测试完整的用户流程:"
echo "   - OAuth登录"
echo "   - 世界列表加载"
echo "   - 世界创建和管理"
echo "   - 角色创建和管理"
echo "   - 游戏交互功能"
echo ""
echo "✅ 前端API集成测试完成!"
echo "📄 详细信息请查看: test_frontend_api_integration.html"
