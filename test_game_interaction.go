package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/joho/godotenv"
)

// 测试游戏交互API的简单脚本

type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	Error   string      `json:"error,omitempty"`
}

type ActionRequest struct {
	WorldID    string                 `json:"world_id"`
	ActionType string                 `json:"action_type"`
	TargetType string                 `json:"target_type"`
	TargetID   string                 `json:"target_id"`
	Parameters map[string]interface{} `json:"parameters"`
}

type SpeakRequest struct {
	WorldID    string `json:"world_id"`
	Content    string `json:"content"`
	SpeechType string `json:"speech_type"`
	Volume     string `json:"volume"`
	Emotion    string `json:"emotion"`
}

type TriggerEventRequest struct {
	WorldID            string                 `json:"world_id"`
	EventType          string                 `json:"event_type"`
	Name               string                 `json:"name"`
	Description        string                 `json:"description"`
	Priority           int                    `json:"priority"`
	Participants       []string               `json:"participants"`
	EventData          map[string]interface{} `json:"event_data"`
	ProcessImmediately bool                   `json:"process_immediately"`
}

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Printf("警告: 无法加载.env文件: %v", err)
	}

	baseURL := os.Getenv("API_BASE_URL")
	if baseURL == "" {
		baseURL = "http://localhost:8080"
	}

	token := os.Getenv("TEST_TOKEN")
	if token == "" {
		log.Fatal("请设置TEST_TOKEN环境变量")
	}

	// 测试用的世界ID和角色ID（需要先创建）
	worldID := os.Getenv("TEST_WORLD_ID")
	characterID := os.Getenv("TEST_CHARACTER_ID")
	
	if worldID == "" || characterID == "" {
		log.Fatal("请设置TEST_WORLD_ID和TEST_CHARACTER_ID环境变量")
	}

	client := &http.Client{Timeout: 30 * time.Second}

	fmt.Println("🎮 开始测试游戏交互API...")
	fmt.Println("=====================================")

	// 1. 测试获取世界状态
	fmt.Println("\n1. 测试获取世界状态")
	testGetWorldState(client, baseURL, token, worldID)

	// 2. 测试角色说话
	fmt.Println("\n2. 测试角色说话")
	testCharacterSpeak(client, baseURL, token, characterID, worldID)

	// 3. 测试执行角色行动
	fmt.Println("\n3. 测试执行角色行动")
	testPerformAction(client, baseURL, token, characterID, worldID)

	// 4. 测试触发事件
	fmt.Println("\n4. 测试触发事件")
	testTriggerEvent(client, baseURL, token, worldID, characterID)

	// 5. 测试更新世界时间
	fmt.Println("\n5. 测试更新世界时间")
	testUpdateWorldTime(client, baseURL, token, worldID)

	// 6. 测试世界时钟周期
	fmt.Println("\n6. 测试世界时钟周期")
	testWorldTick(client, baseURL, token, worldID)

	fmt.Println("\n=====================================")
	fmt.Println("✅ 游戏交互API测试完成！")
}

func testGetWorldState(client *http.Client, baseURL, token, worldID string) {
	url := fmt.Sprintf("%s/api/v1/game/worlds/%s/state", baseURL, worldID)
	
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("❌ 创建请求失败: %v", err)
		return
	}
	
	req.Header.Set("Authorization", "Bearer "+token)
	
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("❌ 请求失败: %v", err)
		return
	}
	defer resp.Body.Close()
	
	var result Response
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		log.Printf("❌ 解析响应失败: %v", err)
		return
	}
	
	if result.Success {
		fmt.Printf("✅ 获取世界状态成功: %s", result.Message)
		if data, ok := result.Data.(map[string]interface{}); ok {
			fmt.Printf(" (天气: %v, 季节: %v)", data["weather"], data["season"])
		}
		fmt.Println()
	} else {
		fmt.Printf("❌ 获取世界状态失败: %s\n", result.Message)
	}
}

func testCharacterSpeak(client *http.Client, baseURL, token, characterID, worldID string) {
	url := fmt.Sprintf("%s/api/v1/game/characters/%s/speak", baseURL, characterID)
	
	speakReq := SpeakRequest{
		WorldID:    worldID,
		Content:    "大家好！我是来测试游戏交互API的！",
		SpeechType: "say",
		Volume:     "normal",
		Emotion:    "happy",
	}
	
	jsonData, err := json.Marshal(speakReq)
	if err != nil {
		log.Printf("❌ 序列化请求失败: %v", err)
		return
	}
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("❌ 创建请求失败: %v", err)
		return
	}
	
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("❌ 请求失败: %v", err)
		return
	}
	defer resp.Body.Close()
	
	var result Response
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		log.Printf("❌ 解析响应失败: %v", err)
		return
	}
	
	if result.Success {
		fmt.Printf("✅ 角色说话成功: %s\n", result.Message)
	} else {
		fmt.Printf("❌ 角色说话失败: %s\n", result.Message)
	}
}

func testPerformAction(client *http.Client, baseURL, token, characterID, worldID string) {
	url := fmt.Sprintf("%s/api/v1/game/characters/%s/actions", baseURL, characterID)
	
	actionReq := ActionRequest{
		WorldID:    worldID,
		ActionType: "explore",
		TargetType: "scene",
		Parameters: map[string]interface{}{
			"intensity": "thorough",
			"focus":     "items",
		},
	}
	
	jsonData, err := json.Marshal(actionReq)
	if err != nil {
		log.Printf("❌ 序列化请求失败: %v", err)
		return
	}
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("❌ 创建请求失败: %v", err)
		return
	}
	
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("❌ 请求失败: %v", err)
		return
	}
	defer resp.Body.Close()
	
	var result Response
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		log.Printf("❌ 解析响应失败: %v", err)
		return
	}
	
	if result.Success {
		fmt.Printf("✅ 执行角色行动成功: %s\n", result.Message)
	} else {
		fmt.Printf("❌ 执行角色行动失败: %s\n", result.Message)
	}
}

func testTriggerEvent(client *http.Client, baseURL, token, worldID, characterID string) {
	url := fmt.Sprintf("%s/api/v1/game/events/trigger", baseURL)
	
	eventReq := TriggerEventRequest{
		WorldID:     worldID,
		EventType:   "world_event",
		Name:        "测试事件",
		Description: "这是一个用于测试API的事件",
		Priority:    5,
		Participants: []string{characterID},
		EventData: map[string]interface{}{
			"sub_type":    "test",
			"test_mode":   true,
			"description": "API测试触发的事件",
		},
		ProcessImmediately: true,
	}
	
	jsonData, err := json.Marshal(eventReq)
	if err != nil {
		log.Printf("❌ 序列化请求失败: %v", err)
		return
	}
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("❌ 创建请求失败: %v", err)
		return
	}
	
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("❌ 请求失败: %v", err)
		return
	}
	defer resp.Body.Close()
	
	var result Response
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		log.Printf("❌ 解析响应失败: %v", err)
		return
	}
	
	if result.Success {
		fmt.Printf("✅ 触发事件成功: %s\n", result.Message)
	} else {
		fmt.Printf("❌ 触发事件失败: %s\n", result.Message)
	}
}

func testUpdateWorldTime(client *http.Client, baseURL, token, worldID string) {
	url := fmt.Sprintf("%s/api/v1/game/worlds/%s/time", baseURL, worldID)
	
	timeReq := map[string]interface{}{
		"minutes": 30,
	}
	
	jsonData, err := json.Marshal(timeReq)
	if err != nil {
		log.Printf("❌ 序列化请求失败: %v", err)
		return
	}
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("❌ 创建请求失败: %v", err)
		return
	}
	
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("❌ 请求失败: %v", err)
		return
	}
	defer resp.Body.Close()
	
	var result Response
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		log.Printf("❌ 解析响应失败: %v", err)
		return
	}
	
	if result.Success {
		fmt.Printf("✅ 更新世界时间成功: %s\n", result.Message)
	} else {
		fmt.Printf("❌ 更新世界时间失败: %s\n", result.Message)
	}
}

func testWorldTick(client *http.Client, baseURL, token, worldID string) {
	url := fmt.Sprintf("%s/api/v1/game/worlds/%s/tick", baseURL, worldID)
	
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		log.Printf("❌ 创建请求失败: %v", err)
		return
	}
	
	req.Header.Set("Authorization", "Bearer "+token)
	
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("❌ 请求失败: %v", err)
		return
	}
	defer resp.Body.Close()
	
	var result Response
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		log.Printf("❌ 解析响应失败: %v", err)
		return
	}
	
	if result.Success {
		fmt.Printf("✅ 世界时钟周期处理成功: %s\n", result.Message)
	} else {
		fmt.Printf("❌ 世界时钟周期处理失败: %s\n", result.Message)
	}
}
