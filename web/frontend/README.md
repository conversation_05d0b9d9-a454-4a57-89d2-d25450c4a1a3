# AI文本游戏前端应用

这是 AI文本游戏 "I am NPC" 的前端应用，基于 React + TypeScript 构建。

## 🚀 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **状态管理**: Redux Toolkit + RTK Query
- **UI组件库**: Ant Design
- **样式方案**: Styled-components
- **动画库**: Framer Motion
- **图标库**: Ant Design Icons + Lucide React
- **路由**: React Router DOM

## 📁 项目结构

```
src/
├── components/          # 可复用组件
│   └── layout/         # 布局组件
├── pages/              # 页面组件
├── store/              # Redux 状态管理
│   ├── slices/         # Redux slices
│   └── api/            # RTK Query API
├── types/              # TypeScript 类型定义
├── hooks/              # 自定义 hooks
├── services/           # API 服务
├── utils/              # 工具函数
└── styles/             # 全局样式
```

## 🛠️ 开发命令

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 类型检查
npm run type-check

# 代码检查
npm run lint
```

## 🔧 配置说明

### 开发环境
- 开发服务器运行在 `http://localhost:3000`
- API 请求会代理到后端服务器 `http://localhost:8080`

### 构建配置
- 构建产物输出到 `../static/dist/` 目录
- 自动分包优化，分离 vendor、antd、redux 等库
- 生成 sourcemap 便于调试

### 主题配置
- 支持亮色/暗色主题切换
- 自定义 Ant Design 主题色彩
- 响应式设计，支持移动端

## 🎨 设计系统

### 色彩方案
- **主色调**: #6366f1 (神秘紫色)
- **辅助色**: #f59e0b (温暖金色)
- **成功色**: #10b981
- **警告色**: #f59e0b
- **错误色**: #ef4444

### 字体系统
- 系统默认字体栈
- 游戏文本使用等宽字体 (Courier New)

## 🔗 API 集成

前端通过 RTK Query 与后端 API 集成：
- 自动处理认证 token
- 支持请求缓存和重新验证
- 错误处理和重试机制
- 类型安全的 API 调用

## 📱 响应式设计

- 桌面端：1200px+ 完整功能
- 平板端：768px-1199px 适配布局
- 移动端：<768px 移动优化

## 🚀 部署

构建完成后，静态文件会输出到 `../static/dist/` 目录，由 Go 后端服务器提供静态文件服务。

## 🔒 安全特性

- XSS 防护
- CSRF 保护
- 安全的 token 存储
- 内容校验集成

## 📝 开发规范

- 使用 TypeScript 严格模式
- 遵循 ESLint 规则
- 组件使用函数式组件 + Hooks
- 状态管理使用 Redux Toolkit
- 样式使用 styled-components
- 提交前进行类型检查和代码检查
