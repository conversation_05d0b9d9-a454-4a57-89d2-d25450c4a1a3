import { store } from '../store'
import { 
  setCurrentWorld, 
  setCurrentCharacter, 
  setCurrentScene,
  enterGame,
  leaveGame,
  addChatMessage,
  setLoading,
  setError
} from '../store/slices/gameSlice'
import type { GameWorld, GameCharacter } from '../store/slices/gameSlice'

/**
 * 游戏服务类
 * 处理游戏状态管理和游戏逻辑
 */
export class GameService {
  /**
   * 进入游戏世界
   * @param world 游戏世界
   * @param character 玩家角色
   */
  static async enterWorld(world: GameWorld, character: <PERSON>Character): Promise<boolean> {
    try {
      store.dispatch(setLoading(true))
      store.dispatch(setError(null))
      
      // 设置当前世界和角色
      store.dispatch(setCurrentWorld(world))
      store.dispatch(setCurrentCharacter(character))
      
      // 获取角色当前所在场景
      if (character.currentSceneId) {
        const response = await fetch(`/api/v1/game/scenes/${character.currentSceneId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${GameService.getAuthToken()}`,
            'Content-Type': 'application/json',
          },
        })
        
        if (response.ok) {
          const data = await response.json()
          if (data.success && data.data) {
            store.dispatch(setCurrentScene(data.data))
          }
        }
      }
      
      // 进入游戏状态
      store.dispatch(enterGame())
      
      // 添加欢迎消息
      store.dispatch(addChatMessage({
        type: 'system',
        content: `欢迎来到 ${world.name}！你正在扮演 ${character.name}。`,
      }))
      
      store.dispatch(setLoading(false))
      return true
    } catch (error) {
      console.error('进入游戏世界失败:', error)
      store.dispatch(setError(error instanceof Error ? error.message : '进入游戏失败'))
      store.dispatch(setLoading(false))
      return false
    }
  }
  
  /**
   * 离开游戏世界
   */
  static leaveWorld(): void {
    store.dispatch(leaveGame())
    store.dispatch(addChatMessage({
      type: 'system',
      content: '你已离开游戏世界。',
    }))
  }
  
  /**
   * 执行角色行动
   * @param actionType 行动类型
   * @param targetType 目标类型
   * @param targetId 目标ID
   * @param parameters 行动参数
   */
  static async performAction(
    actionType: string,
    targetType?: string,
    targetId?: string,
    parameters?: Record<string, any>
  ): Promise<boolean> {
    try {
      const state = store.getState()
      const character = state.game.currentCharacter
      const world = state.game.currentWorld
      
      if (!character || !world) {
        throw new Error('没有当前角色或世界')
      }
      
      store.dispatch(setLoading(true))
      
      const response = await fetch(`/api/v1/game/characters/${character.id}/actions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${GameService.getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          world_id: world.id,
          action_type: actionType,
          target_type: targetType,
          target_id: targetId,
          parameters: parameters || {},
        }),
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.success && data.data) {
        // 添加行动结果到聊天
        store.dispatch(addChatMessage({
          type: 'system',
          content: data.data.message,
        }))
        
        store.dispatch(setLoading(false))
        return true
      } else {
        throw new Error(data.message || '行动执行失败')
      }
    } catch (error) {
      console.error('执行行动失败:', error)
      store.dispatch(setError(error instanceof Error ? error.message : '行动执行失败'))
      store.dispatch(setLoading(false))
      return false
    }
  }
  
  /**
   * 角色说话
   * @param content 说话内容
   * @param speechType 说话类型
   * @param volume 音量
   * @param emotion 情感
   */
  static async speak(
    content: string,
    speechType: 'say' | 'whisper' | 'shout' | 'think' = 'say',
    volume: 'quiet' | 'normal' | 'loud' = 'normal',
    emotion: 'neutral' | 'happy' | 'sad' | 'angry' | 'curious' | 'worried' = 'neutral'
  ): Promise<boolean> {
    try {
      const state = store.getState()
      const character = state.game.currentCharacter
      const world = state.game.currentWorld
      
      if (!character || !world) {
        throw new Error('没有当前角色或世界')
      }
      
      // 立即添加用户消息到聊天
      store.dispatch(addChatMessage({
        type: 'user',
        content: content,
      }))
      
      store.dispatch(setLoading(true))
      
      const response = await fetch(`/api/v1/game/characters/${character.id}/speak`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${GameService.getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          world_id: world.id,
          content,
          speech_type: speechType,
          volume,
          emotion,
        }),
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.success && data.data) {
        // 添加系统反馈
        if (data.data.listeners && data.data.listeners.length > 0) {
          store.dispatch(addChatMessage({
            type: 'system',
            content: `有 ${data.data.listeners.length} 个角色听到了你的话。`,
          }))
        }
        
        store.dispatch(setLoading(false))
        return true
      } else {
        throw new Error(data.message || '说话失败')
      }
    } catch (error) {
      console.error('说话失败:', error)
      store.dispatch(setError(error instanceof Error ? error.message : '说话失败'))
      store.dispatch(setLoading(false))
      return false
    }
  }
  
  /**
   * 与其他角色交互
   * @param targetCharacterId 目标角色ID
   * @param interactionType 交互类型
   * @param content 交互内容
   */
  static async interactWithCharacter(
    targetCharacterId: string,
    interactionType: 'greet' | 'talk' | 'trade' | 'help' | 'challenge' | 'follow',
    content?: string
  ): Promise<boolean> {
    try {
      const state = store.getState()
      const character = state.game.currentCharacter
      const world = state.game.currentWorld
      
      if (!character || !world) {
        throw new Error('没有当前角色或世界')
      }
      
      store.dispatch(setLoading(true))
      
      const response = await fetch(
        `/api/v1/game/characters/${character.id}/interact/${targetCharacterId}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${GameService.getAuthToken()}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            world_id: world.id,
            interaction_type: interactionType,
            content: content || '',
          }),
        }
      )
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.success && data.data) {
        // 添加交互结果到聊天
        store.dispatch(addChatMessage({
          type: 'system',
          content: data.data.message,
        }))
        
        // 如果有回应，也添加到聊天
        if (data.data.response) {
          store.dispatch(addChatMessage({
            type: 'ai',
            content: data.data.response,
          }))
        }
        
        store.dispatch(setLoading(false))
        return true
      } else {
        throw new Error(data.message || '交互失败')
      }
    } catch (error) {
      console.error('角色交互失败:', error)
      store.dispatch(setError(error instanceof Error ? error.message : '交互失败'))
      store.dispatch(setLoading(false))
      return false
    }
  }
  
  /**
   * 获取认证令牌
   */
  private static getAuthToken(): string {
    const state = store.getState()
    return state.auth.token || ''
  }
  
  /**
   * 获取当前游戏状态
   */
  static getCurrentGameState() {
    const state = store.getState()
    return {
      world: state.game.currentWorld,
      character: state.game.currentCharacter,
      scene: state.game.currentScene,
      isInGame: state.game.isInGame,
      isLoading: state.game.isLoading,
      error: state.game.error,
      chatMessages: state.game.chatMessages,
    }
  }
  
  /**
   * 清除游戏错误
   */
  static clearError(): void {
    store.dispatch(setError(null))
  }
}

// 导出默认实例
export default GameService
