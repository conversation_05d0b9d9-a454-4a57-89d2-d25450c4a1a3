import { store } from '../store'
import { loginStart, loginSuccess, loginFailure, logout } from '../store/slices/authSlice'

/**
 * 认证服务类
 * 处理OAuth认证流程和用户会话管理
 */
export class AuthService {
  /**
   * 启动OAuth认证流程
   * @param provider OAuth提供商 ('google' | 'github')
   */
  static async startOAuthFlow(provider: 'google' | 'github'): Promise<void> {
    try {
      store.dispatch(loginStart())
      
      // 获取认证URL
      const response = await fetch(`/api/v1/auth/${provider}/url`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.success && data.data.auth_url) {
        // 保存state到sessionStorage用于验证
        sessionStorage.setItem('oauth_state', data.data.state)
        sessionStorage.setItem('oauth_provider', provider)
        
        // 重定向到OAuth提供商
        window.location.href = data.data.auth_url
      } else {
        throw new Error(data.message || '获取认证URL失败')
      }
    } catch (error) {
      console.error('OAuth认证启动失败:', error)
      store.dispatch(loginFailure(error instanceof Error ? error.message : '认证启动失败'))
    }
  }
  
  /**
   * 处理OAuth回调
   * @param code 授权码
   * @param state 状态参数
   * @param provider OAuth提供商
   */
  static async handleOAuthCallback(
    code: string, 
    state: string, 
    provider?: string
  ): Promise<boolean> {
    try {
      store.dispatch(loginStart())
      
      // 验证state参数
      const savedState = sessionStorage.getItem('oauth_state')
      const savedProvider = sessionStorage.getItem('oauth_provider')
      
      if (!savedState || savedState !== state) {
        throw new Error('无效的state参数，可能存在CSRF攻击')
      }
      
      const actualProvider = provider || savedProvider
      if (!actualProvider) {
        throw new Error('缺少OAuth提供商信息')
      }
      
      // 发送回调请求
      const response = await fetch(
        `/api/v1/auth/${actualProvider}/callback?code=${code}&state=${state}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.success && data.data) {
        // 登录成功
        store.dispatch(loginSuccess({
          user: data.data.user,
          token: data.data.token,
          refreshToken: data.data.refresh_token,
        }))
        
        // 清除临时存储
        sessionStorage.removeItem('oauth_state')
        sessionStorage.removeItem('oauth_provider')
        
        return true
      } else {
        throw new Error(data.message || '认证失败')
      }
    } catch (error) {
      console.error('OAuth回调处理失败:', error)
      store.dispatch(loginFailure(error instanceof Error ? error.message : '认证失败'))
      
      // 清除临时存储
      sessionStorage.removeItem('oauth_state')
      sessionStorage.removeItem('oauth_provider')
      
      return false
    }
  }
  
  /**
   * 刷新访问令牌
   */
  static async refreshToken(): Promise<boolean> {
    try {
      const state = store.getState()
      const refreshToken = state.auth.refreshToken
      
      if (!refreshToken) {
        throw new Error('没有刷新令牌')
      }
      
      const response = await fetch('/api/v1/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.success && data.data) {
        // 更新令牌
        const currentUser = state.auth.user
        if (currentUser) {
          store.dispatch(loginSuccess({
            user: currentUser,
            token: data.data.token,
            refreshToken: data.data.refresh_token,
          }))
        }
        return true
      } else {
        throw new Error(data.message || '刷新令牌失败')
      }
    } catch (error) {
      console.error('刷新令牌失败:', error)
      // 刷新失败，登出用户
      AuthService.logout()
      return false
    }
  }
  
  /**
   * 登出用户
   */
  static logout(): void {
    store.dispatch(logout())
    
    // 清除所有相关的存储
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    sessionStorage.removeItem('oauth_state')
    sessionStorage.removeItem('oauth_provider')
    
    // 可选：通知服务器登出（如果有相关端点）
    // 这里我们只是清除客户端状态
  }
  
  /**
   * 检查用户是否已认证
   */
  static isAuthenticated(): boolean {
    const state = store.getState()
    return state.auth.isAuthenticated && !!state.auth.token
  }
  
  /**
   * 获取当前用户
   */
  static getCurrentUser() {
    const state = store.getState()
    return state.auth.user
  }
  
  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | null {
    const state = store.getState()
    return state.auth.token
  }
  
  /**
   * 检查是否为开发模式
   * 使用多重安全检查确保只在真正的开发环境中启用
   */
  static isDevelopmentMode(): boolean {
    // 构建时检查：如果是生产构建，直接返回false
    if (typeof __DEV_MODE_ENABLED__ !== 'undefined' && !__DEV_MODE_ENABLED__) {
      return false
    }

    // 运行时检查：生产环境强制禁用
    if (process.env.NODE_ENV === 'production' || process.env.DISABLE_DEV_MODE === 'true') {
      return false
    }

    // 只有在明确的开发环境条件下才启用
    const isDev = process.env.NODE_ENV === 'development' &&
                  (window.location.hostname === 'localhost' ||
                   window.location.hostname === '127.0.0.1' ||
                   window.location.hostname.endsWith('.local'))

    // 额外的安全检查：确保不在生产域名上
    const isProductionDomain = window.location.hostname.includes('prod') ||
                              window.location.hostname.includes('api') ||
                              (window.location.protocol === 'https:' &&
                               !window.location.hostname.includes('localhost') &&
                               !window.location.hostname.includes('127.0.0.1'))

    return isDev && !isProductionDomain
  }

  /**
   * 开发模式自动登录
   * 模拟用户登录，无需真实的OAuth流程
   * 包含多重安全检查，确保只在开发环境中使用
   */
  static async developmentAutoLogin(): Promise<boolean> {
    // 双重安全检查
    if (!AuthService.isDevelopmentMode()) {
      console.error('🚫 安全警告：developmentAutoLogin 只能在开发模式下使用')
      return false
    }

    // 检查是否为生产构建
    if (process.env.NODE_ENV === 'production') {
      console.error('🚫 安全警告：生产环境禁止使用开发模式登录')
      return false
    }

    try {
      console.log('🔧 开发模式：自动登录开发用户')

      // 模拟开发用户数据
      const mockUser = {
        id: '00000000-0000-0000-0000-000000000001',
        username: '开发测试用户',
        email: '<EMAIL>',
        avatar: '',
        provider: 'development',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // 模拟token（开发环境后端会忽略）
      const mockToken = 'dev-mock-token-' + Date.now()
      const mockRefreshToken = 'dev-mock-refresh-token-' + Date.now()

      // 设置登录状态
      store.dispatch(loginSuccess({
        user: mockUser,
        token: mockToken,
        refreshToken: mockRefreshToken,
      }))

      console.log('✅ 开发模式：自动登录成功', mockUser)
      return true
    } catch (error) {
      console.error('❌ 开发模式：自动登录失败', error)
      return false
    }
  }

  /**
   * 初始化认证状态
   * 在应用启动时调用，检查本地存储的令牌
   */
  static async initializeAuth(): Promise<void> {
    // 开发模式检查
    if (AuthService.isDevelopmentMode()) {
      console.log('🔧 检测到开发模式，检查后端开发模式状态...')

      try {
        // 检查后端是否也是开发模式
        const healthResponse = await fetch('/api/health', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        })

        if (healthResponse.ok) {
          const contentType = healthResponse.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const healthData = await healthResponse.json()
            if (healthData.auth_mode === 'disabled') {
              console.log('✅ 后端开发模式已启用，执行自动登录')
              await AuthService.developmentAutoLogin()
              return
            } else {
              console.log('ℹ️ 后端未启用开发模式，使用正常认证流程')
            }
          } else {
            console.warn('⚠️ 后端返回非JSON响应，可能后端服务未启动')
            // 如果后端没有启动，但前端是开发模式，仍然可以使用开发模式
            console.log('🔧 后端未启动，但前端开发模式仍可用，执行自动登录')
            await AuthService.developmentAutoLogin()
            return
          }
        } else {
          console.warn(`⚠️ 后端健康检查失败，状态码: ${healthResponse.status}`)
          // 开发模式下，即使后端检查失败也允许自动登录
          console.log('🔧 开发模式：后端检查失败，但仍执行自动登录')
          await AuthService.developmentAutoLogin()
          return
        }
      } catch (error) {
        console.warn('⚠️ 无法连接到后端服务，可能后端未启动', error)
        // 开发模式下，网络错误时仍然允许前端开发
        console.log('🔧 开发模式：后端连接失败，但仍执行前端自动登录')
        await AuthService.developmentAutoLogin()
        return
      }
    }

    const token = localStorage.getItem('token')
    const refreshToken = localStorage.getItem('refreshToken')

    if (token && refreshToken) {
      try {
        // 尝试获取用户信息来验证令牌
        const response = await fetch('/api/v1/user/profile', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success && data.data) {
            // 令牌有效，恢复登录状态
            store.dispatch(loginSuccess({
              user: data.data,
              token,
              refreshToken,
            }))
            return
          }
        }

        // 令牌无效，尝试刷新
        if (response.status === 401) {
          const refreshSuccess = await AuthService.refreshToken()
          if (refreshSuccess) {
            return
          }
        }
      } catch (error) {
        console.error('初始化认证状态失败:', error)
      }

      // 如果所有尝试都失败，清除无效的令牌
      AuthService.logout()
    }
  }
}

// 导出默认实例
export default AuthService
