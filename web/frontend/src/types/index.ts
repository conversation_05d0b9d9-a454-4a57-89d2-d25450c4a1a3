// API 响应基础接口
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: number
}

// 分页响应接口
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  provider: string
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  provider: string
  code?: string
  redirectUri?: string
}

export interface LoginResponse {
  user: User
  token: string
  refreshToken: string
}

// 游戏世界相关类型
export interface GameWorld {
  id: string
  name: string
  description: string
  theme: string
  isPublic: boolean
  maxPlayers: number
  currentPlayers: number
  creatorId: string
  createdAt: string
  updatedAt: string
}

export interface CreateWorldRequest {
  name: string
  description: string
  theme: string
  isPublic: boolean
  maxPlayers: number
}

// 游戏角色相关类型
export interface GameCharacter {
  id: string
  name: string
  description: string
  attributes: Record<string, any>
  worldId: string
  userId: string
  currentSceneId: string
  createdAt: string
  updatedAt: string
}

export interface CreateCharacterRequest {
  name: string
  description: string
  attributes?: Record<string, any>
  worldId: string
}

// 游戏场景相关类型
export interface GameScene {
  id: string
  name: string
  description: string
  worldId: string
  connections: string[]
  characters: string[]
  createdAt: string
  updatedAt: string
}

export interface CreateSceneRequest {
  name: string
  description: string
  worldId: string
  connections?: string[]
}

// 游戏事件相关类型
export interface GameEvent {
  id: string
  type: 'action' | 'dialogue' | 'system' | 'ai_generated'
  content: string
  worldId: string
  sceneId?: string
  characterId?: string
  userId?: string
  timestamp: string
  metadata?: Record<string, any>
}

export interface CreateEventRequest {
  type: string
  content: string
  worldId: string
  sceneId?: string
  characterId?: string
  metadata?: Record<string, any>
}

// AI 生成相关类型
export interface AIGenerateRequest {
  type: 'scene' | 'character' | 'dialogue' | 'event'
  prompt: string
  context?: Record<string, any>
  worldId?: string
  sceneId?: string
  characterId?: string
}

export interface AIGenerateResponse {
  content: string
  type: string
  metadata?: Record<string, any>
}

// 内容校验相关类型
export interface ValidationRequest {
  content: string
  type: 'text' | 'name' | 'description'
  context?: Record<string, any>
}

export interface ValidationResponse {
  isValid: boolean
  issues: Array<{
    type: 'sensitive_word' | 'sql_injection' | 'xss' | 'inappropriate'
    message: string
    severity: 'low' | 'medium' | 'high'
  }>
  suggestions?: string[]
}

// 聊天消息类型
export interface ChatMessage {
  id: string
  type: 'user' | 'system' | 'ai' | 'character'
  content: string
  sender?: string
  timestamp: string
  metadata?: Record<string, any>
}

// 游戏动作类型
export interface GameAction {
  type: 'move' | 'interact' | 'speak' | 'examine' | 'use'
  target?: string
  content?: string
  parameters?: Record<string, any>
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: Record<string, any>
}

// 通知类型
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: string
  read: boolean
  actions?: Array<{
    label: string
    action: string
  }>
}

// 主题类型
export type Theme = 'light' | 'dark'

// 游戏状态类型
export type GameStatus = 'waiting' | 'active' | 'paused' | 'ended'

// 角色状态类型
export type CharacterStatus = 'active' | 'inactive' | 'dead'

// 场景类型
export type SceneType = 'indoor' | 'outdoor' | 'dungeon' | 'town' | 'wilderness' | 'special'

// 事件类型
export type EventType = 'action' | 'dialogue' | 'system' | 'ai_generated' | 'combat' | 'exploration'
