import { createSlice, PayloadAction, createSelector } from '@reduxjs/toolkit'

// UI 状态接口
export interface UIState {
  // 主题设置
  theme: 'light' | 'dark'
  
  // 侧边栏状态
  sidebarCollapsed: boolean
  
  // 模态框状态
  modals: {
    createWorld: boolean
    createCharacter: boolean
    settings: boolean
    help: boolean
  }
  
  // 通知消息
  notifications: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message: string
    timestamp: string
    read: boolean
  }>
  
  // 加载状态
  globalLoading: boolean
  
  // 错误状态
  globalError: string | null
  
  // 页面标题
  pageTitle: string
}

// 初始状态
const initialState: UIState = {
  theme: (localStorage.getItem('theme') as 'light' | 'dark') || 'light',
  sidebarCollapsed: false,
  modals: {
    createWorld: false,
    createCharacter: false,
    settings: false,
    help: false,
  },
  notifications: [],
  globalLoading: false,
  globalError: null,
  pageTitle: 'AI文本游戏',
}

// 创建 UI slice
export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // 切换主题
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light'
      localStorage.setItem('theme', state.theme)
    },
    
    // 设置主题
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload
      localStorage.setItem('theme', state.theme)
    },
    
    // 切换侧边栏
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed
    },
    
    // 设置侧边栏状态
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload
    },
    
    // 打开模态框
    openModal: (state, action: PayloadAction<keyof UIState['modals']>) => {
      state.modals[action.payload] = true
    },
    
    // 关闭模态框
    closeModal: (state, action: PayloadAction<keyof UIState['modals']>) => {
      state.modals[action.payload] = false
    },
    
    // 关闭所有模态框
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach((key) => {
        state.modals[key as keyof UIState['modals']] = false
      })
    },
    
    // 添加通知
    addNotification: (state, action: PayloadAction<{
      type: 'success' | 'error' | 'warning' | 'info'
      title: string
      message: string
    }>) => {
      const notification = {
        id: Date.now().toString(),
        ...action.payload,
        timestamp: new Date().toISOString(),
        read: false,
      }
      state.notifications.unshift(notification)
      
      // 限制通知数量
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50)
      }
    },
    
    // 标记通知为已读
    markNotificationRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload)
      if (notification) {
        notification.read = true
      }
    },
    
    // 删除通知
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload)
    },
    
    // 清除所有通知
    clearNotifications: (state) => {
      state.notifications = []
    },
    
    // 设置全局加载状态
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.globalLoading = action.payload
    },
    
    // 设置全局错误
    setGlobalError: (state, action: PayloadAction<string | null>) => {
      state.globalError = action.payload
    },
    
    // 清除全局错误
    clearGlobalError: (state) => {
      state.globalError = null
    },
    
    // 设置页面标题
    setPageTitle: (state, action: PayloadAction<string>) => {
      state.pageTitle = action.payload
      document.title = `${action.payload} - AI文本游戏`
    },
  },
})

// 导出 actions
export const {
  toggleTheme,
  setTheme,
  toggleSidebar,
  setSidebarCollapsed,
  openModal,
  closeModal,
  closeAllModals,
  addNotification,
  markNotificationRead,
  removeNotification,
  clearNotifications,
  setGlobalLoading,
  setGlobalError,
  clearGlobalError,
  setPageTitle,
} = uiSlice.actions

// 导出 selectors
export const selectUI = (state: { ui: UIState }) => state.ui
export const selectTheme = (state: { ui: UIState }) => state.ui.theme
export const selectSidebarCollapsed = (state: { ui: UIState }) => state.ui.sidebarCollapsed
export const selectModals = (state: { ui: UIState }) => state.ui.modals
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications

// 使用 createSelector 优化未读通知选择器，避免不必要的重新渲染
export const selectUnreadNotifications = createSelector(
  [selectNotifications],
  (notifications) => notifications.filter(n => !n.read)
)

export const selectGlobalLoading = (state: { ui: UIState }) => state.ui.globalLoading
export const selectGlobalError = (state: { ui: UIState }) => state.ui.globalError
export const selectPageTitle = (state: { ui: UIState }) => state.ui.pageTitle

export default uiSlice.reducer
