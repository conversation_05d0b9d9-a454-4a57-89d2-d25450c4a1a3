import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// 用户信息接口
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  provider: string
  createdAt: string
  updatedAt: string
}

// 认证状态接口
export interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

// 初始状态
const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('token'),
  refreshToken: localStorage.getItem('refreshToken'),
  isAuthenticated: false,
  isLoading: false,
  error: null,
}

// 创建认证 slice
export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // 开始登录
    loginStart: (state) => {
      state.isLoading = true
      state.error = null
    },
    
    // 登录成功
    loginSuccess: (state, action: PayloadAction<{ user: User; token: string; refreshToken: string }>) => {
      const { user, token, refreshToken } = action.payload
      state.user = user
      state.token = token
      state.refreshToken = refreshToken
      state.isAuthenticated = true
      state.isLoading = false
      state.error = null
      
      // 保存到 localStorage
      localStorage.setItem('token', token)
      localStorage.setItem('refreshToken', refreshToken)
    },
    
    // 登录失败
    loginFailure: (state, action: PayloadAction<string>) => {
      state.user = null
      state.token = null
      state.refreshToken = null
      state.isAuthenticated = false
      state.isLoading = false
      state.error = action.payload
      
      // 清除 localStorage
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
    },
    
    // 登出
    logout: (state) => {
      state.user = null
      state.token = null
      state.refreshToken = null
      state.isAuthenticated = false
      state.isLoading = false
      state.error = null
      
      // 清除 localStorage
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
    },
    
    // 更新用户信息
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload }
      }
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null
    },
  },
})

// 导出 actions
export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  updateUser,
  clearError,
} = authSlice.actions

// 导出 selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth
export const selectUser = (state: { auth: AuthState }) => state.auth.user
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated
export const selectAuthLoading = (state: { auth: AuthState }) => state.auth.isLoading
export const selectAuthError = (state: { auth: AuthState }) => state.auth.error

export default authSlice.reducer
