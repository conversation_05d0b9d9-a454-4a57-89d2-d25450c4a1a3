import gameReducer, {
  setCurrentWorld,
  setCurrentCharacter,
  setCharacters,
  addEvent,
  addChatMessage,
  clearChatMessages,
  GameState
} from './slices/gameSlice';

// Mock数据
const mockWorld = {
  id: 'test-world-id',
  name: '测试世界',
  description: '这是一个测试世界',
  theme: '奇幻',
  isPublic: true,
  maxPlayers: 10,
  currentPlayers: 1,
  creatorId: 'test-user-id',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
};

const mockCharacter = {
  id: 'test-character-id',
  name: '测试角色',
  description: '这是一个测试角色',
  attributes: { level: 1, health: 100, traits: ['勇敢', '智慧'] },
  worldId: 'test-world-id',
  userId: 'test-user-id',
  currentSceneId: 'test-scene-id',
  characterType: 'player' as const,
  traits: ['勇敢', '智慧'],
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
};

describe('gameSlice', () => {
  const initialState: GameState = {
    currentWorld: null,
    currentCharacter: null,
    currentScene: null,
    worlds: [],
    characters: [],
    scenes: [],
    events: [],
    isInGame: false,
    isLoading: false,
    error: null,
    chatMessages: [],
  };

  it('应该返回初始状态', () => {
    expect(gameReducer(undefined, { type: 'unknown' })).toEqual(initialState);
  });

  it('应该处理setCurrentWorld', () => {
    const actual = gameReducer(initialState, setCurrentWorld(mockWorld));
    expect(actual.currentWorld).toEqual(mockWorld);
  });

  it('应该处理setCurrentCharacter', () => {
    const actual = gameReducer(initialState, setCurrentCharacter(mockCharacter));
    expect(actual.currentCharacter).toEqual(mockCharacter);
  });

  it('应该处理setCharacters', () => {
    const characters = [mockCharacter];
    const actual = gameReducer(initialState, setCharacters(characters));
    expect(actual.characters).toEqual(characters);
  });

  it('应该处理addChatMessage', () => {
    const message = {
      id: 'msg-1',
      type: 'user' as const,
      content: '你执行了一个行动',
      timestamp: '2023-01-01T00:00:00Z'
    };

    const actual = gameReducer(initialState, addChatMessage(message));
    expect(actual.chatMessages).toHaveLength(1);
    expect(actual.chatMessages[0]).toEqual(message);
  });

  it('应该处理clearChatMessages', () => {
    const stateWithMessages: GameState = {
      ...initialState,
      chatMessages: [
        {
          id: 'msg-1',
          type: 'system',
          content: '游戏开始',
          timestamp: '2023-01-01T00:00:00Z'
        }
      ]
    };

    const actual = gameReducer(stateWithMessages, clearChatMessages());
    expect(actual.chatMessages).toHaveLength(0);
  });

  it('应该能够添加多条聊天消息', () => {
    const message1 = {
      id: 'msg-1',
      type: 'user' as const,
      content: '第一条消息',
      timestamp: '2023-01-01T00:00:00Z'
    };

    const message2 = {
      id: 'msg-2',
      type: 'ai' as const,
      content: '第二条消息',
      timestamp: '2023-01-01T00:01:00Z'
    };

    let state = gameReducer(initialState, addChatMessage(message1));
    state = gameReducer(state, addChatMessage(message2));

    expect(state.chatMessages).toHaveLength(2);
    expect(state.chatMessages[0]).toEqual(message1);
    expect(state.chatMessages[1]).toEqual(message2);
  });

  it('应该能够更新游戏状态中的角色列表', () => {
    const character2 = {
      ...mockCharacter,
      id: 'character-2',
      name: '第二个角色'
    };

    const actual = gameReducer(initialState, setCharacters([mockCharacter, character2]));
    expect(actual.characters).toHaveLength(2);
    expect(actual.characters[0]).toEqual(mockCharacter);
    expect(actual.characters[1]).toEqual(character2);
  });

  it('应该能够更新游戏状态中的事件列表', () => {
    const event1 = {
      id: 'event-1',
      type: 'action',
      content: '第一个事件',
      worldId: 'test-world-id',
      timestamp: '2023-01-01T00:00:00Z'
    };

    const event2 = {
      id: 'event-2',
      type: 'interaction',
      content: '第二个事件',
      worldId: 'test-world-id',
      timestamp: '2023-01-01T00:01:00Z'
    };

    // 添加第一个事件
    let actual = gameReducer(initialState, addEvent(event1));
    // 添加第二个事件
    actual = gameReducer(actual, addEvent(event2));

    expect(actual.events).toHaveLength(2);
    expect(actual.events[0]).toEqual(event1);
    expect(actual.events[1]).toEqual(event2);
  });
});
