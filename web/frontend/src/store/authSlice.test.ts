import authReducer, {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  updateUser,
  clearError,
  AuthState
} from './slices/authSlice';

// Mock用户数据
const mockUser = {
  id: 'test-user-id',
  username: 'testuser',
  email: '<EMAIL>',
  avatar: '',
  provider: 'google',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
};

describe('authSlice', () => {
  const initialState: AuthState = {
    user: null,
    token: null,
    refreshToken: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  };

  it('应该返回初始状态', () => {
    expect(authReducer(undefined, { type: 'unknown' })).toEqual(initialState);
  });

  it('应该处理loginSuccess', () => {
    const loginData = {
      user: mockUser,
      token: 'test-token',
      refreshToken: 'test-refresh-token'
    };
    const actual = authReducer(initialState, loginSuccess(loginData));
    expect(actual.user).toEqual(mockUser);
    expect(actual.token).toBe('test-token');
    expect(actual.refreshToken).toBe('test-refresh-token');
    expect(actual.isAuthenticated).toBe(true);
    expect(actual.isLoading).toBe(false);
    expect(actual.error).toBe(null);
  });

  it('应该处理loginStart', () => {
    const actual = authReducer(initialState, loginStart());
    expect(actual.isLoading).toBe(true);
    expect(actual.error).toBe(null);
  });

  it('应该处理logout', () => {
    const authenticatedState: AuthState = {
      user: mockUser,
      token: 'test-token',
      refreshToken: 'test-refresh-token',
      isAuthenticated: true,
      isLoading: false,
      error: null,
    };

    const actual = authReducer(authenticatedState, logout());
    expect(actual).toEqual(initialState);
  });

  it('应该处理loginFailure', () => {
    const error = '认证失败';
    const actual = authReducer(initialState, loginFailure(error));
    expect(actual.error).toBe(error);
    expect(actual.isLoading).toBe(false);
    expect(actual.isAuthenticated).toBe(false);
  });

  it('应该处理updateUser', () => {
    const authenticatedState: AuthState = {
      user: mockUser,
      token: 'test-token',
      refreshToken: 'test-refresh-token',
      isAuthenticated: true,
      isLoading: false,
      error: null,
    };

    const updatedUser = { ...mockUser, email: '<EMAIL>' };
    const actual = authReducer(authenticatedState, updateUser(updatedUser));
    expect(actual.user).toEqual(updatedUser);
    expect(actual.isAuthenticated).toBe(true);
  });

  it('应该处理clearError', () => {
    const stateWithError: AuthState = {
      ...initialState,
      error: '之前的错误',
    };

    const actual = authReducer(stateWithError, clearError());
    expect(actual.error).toBe(null);
  });

  it('应该在登录失败时停止加载', () => {
    const loadingState: AuthState = {
      ...initialState,
      isLoading: true,
    };

    const actual = authReducer(loadingState, loginFailure('新错误'));
    expect(actual.isLoading).toBe(false);
    expect(actual.error).toBe('新错误');
  });
});
