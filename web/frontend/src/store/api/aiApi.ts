import { apiSlice } from './apiSlice'

// AI内容生成相关的类型定义
export interface GenerateSceneRequest {
  world_id: string
  scene_name?: string
  scene_type?: string
  theme?: string
  mood?: string
  connected_scenes?: string[]
  special_requirements?: string
}

export interface GenerateCharacterRequest {
  world_id: string
  character_name?: string
  character_type: 'npc' | 'player'
  role?: string
  personality_traits?: string[]
  background_context?: string
  special_requirements?: string
}

export interface GenerateEventRequest {
  world_id: string
  event_type: 'random' | 'plot' | 'character' | 'environmental'
  context?: string
  participants?: string[]
  intensity?: 'low' | 'medium' | 'high'
  special_requirements?: string
}

export interface GenerateDialogueRequest {
  world_id: string
  character_id: string
  context: string
  dialogue_type: 'greeting' | 'conversation' | 'reaction' | 'monologue'
  target_character_id?: string
  mood?: string
  special_requirements?: string
}

// AI生成响应的基础结构
export interface AIGenerateResponse {
  content: string
  structured_data?: Record<string, any>
  token_usage?: number
  response_time?: number
}

export interface GeneratedScene {
  name: string
  description: string
  atmosphere: string
  key_features: string[]
  possible_actions: string[]
  connections: Array<{
    direction: string
    description: string
    scene_name: string
  }>
}

export interface GeneratedCharacter {
  name: string
  description: string
  personality: Record<string, any>
  appearance: Record<string, any>
  background: string
  attributes: Record<string, any>
  dialogue_style: string
  motivations: string[]
}

export interface GeneratedEvent {
  name: string
  description: string
  event_type: string
  consequences: string[]
  required_actions: string[]
  duration_minutes: number
  participants: string[]
}

export interface GeneratedDialogue {
  content: string
  emotion: string
  tone: string
  context_awareness: string
  follow_up_suggestions: string[]
}

export interface AIResponse<T> {
  success: boolean
  message: string
  data: T
  metadata?: {
    model_used: string
    tokens_used: number
    generation_time_ms: number
  }
}

export interface TokenUsageResponse {
  success: boolean
  message: string
  data: {
    total_tokens_used: number
    tokens_by_type: Record<string, number>
    cost_estimate: number
    period_start: string
    period_end: string
  }
}

export interface InteractionHistoryResponse {
  success: boolean
  message: string
  data: {
    interactions: Array<{
      id: string
      type: string
      request_summary: string
      response_summary: string
      tokens_used: number
      timestamp: string
    }>
    total: number
    page: number
    limit: number
  }
}

// 扩展 API slice 添加AI内容生成端点
export const aiApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // 生成场景描述 - 使用更长的超时时间以适应Windmill异步接口
    generateScene: builder.mutation<AIResponse<AIGenerateResponse>, GenerateSceneRequest>({
      query: (data) => ({
        url: '/ai/generate/scene',
        method: 'POST',
        body: data,
        // 设置更长的超时时间，因为Windmill接口需要异步处理和轮询
        timeout: 120000, // 2分钟超时
      }),
    }),

    // 生成角色 - 使用更长的超时时间以适应Windmill异步接口
    generateCharacter: builder.mutation<AIResponse<GeneratedCharacter>, GenerateCharacterRequest>({
      query: (data) => ({
        url: '/ai/generate/character',
        method: 'POST',
        body: data,
        // 设置更长的超时时间，因为Windmill接口需要异步处理和轮询
        timeout: 120000, // 2分钟超时
      }),
    }),

    // 生成事件 - 使用更长的超时时间以适应Windmill异步接口
    generateEvent: builder.mutation<AIResponse<GeneratedEvent>, GenerateEventRequest>({
      query: (data) => ({
        url: '/ai/generate/event',
        method: 'POST',
        body: data,
        // 设置更长的超时时间，因为Windmill接口需要异步处理和轮询
        timeout: 120000, // 2分钟超时
      }),
    }),

    // 生成对话 - 使用更长的超时时间以适应Windmill异步接口
    generateDialogue: builder.mutation<AIResponse<GeneratedDialogue>, GenerateDialogueRequest>({
      query: (data) => ({
        url: '/ai/generate/dialogue',
        method: 'POST',
        body: data,
        // 设置更长的超时时间，因为Windmill接口需要异步处理和轮询
        timeout: 120000, // 2分钟超时
      }),
    }),

    // 获取Token使用统计
    getTokenUsage: builder.query<TokenUsageResponse, { 
      start_date?: string
      end_date?: string 
    }>({
      query: ({ start_date, end_date } = {}) => {
        let url = '/ai/usage/tokens'
        const params = new URLSearchParams()
        if (start_date) params.append('start_date', start_date)
        if (end_date) params.append('end_date', end_date)
        if (params.toString()) url += `?${params.toString()}`
        
        return {
          url,
          method: 'GET',
        }
      },
    }),

    // 获取交互历史
    getInteractionHistory: builder.query<InteractionHistoryResponse, {
      page?: number
      limit?: number
      type?: string
    }>({
      query: ({ page = 1, limit = 10, type } = {}) => {
        let url = `/ai/usage/history?page=${page}&limit=${limit}`
        if (type) url += `&type=${type}`
        
        return {
          url,
          method: 'GET',
        }
      },
    }),
  }),
})

// 导出生成的hooks
export const {
  useGenerateSceneMutation,
  useGenerateCharacterMutation,
  useGenerateEventMutation,
  useGenerateDialogueMutation,
  useGetTokenUsageQuery,
  useLazyGetTokenUsageQuery,
  useGetInteractionHistoryQuery,
  useLazyGetInteractionHistoryQuery,
} = aiApi
