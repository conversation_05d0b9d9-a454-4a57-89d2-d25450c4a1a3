import { apiSlice } from './apiSlice'
import type { User } from '../slices/authSlice'

// 认证相关的类型定义
export interface AuthResponse {
  success: boolean
  message: string
  data: {
    user: User
    token: string
    refresh_token: string
  }
}

export interface AuthUrlResponse {
  success: boolean
  message: string
  data: {
    auth_url: string
    state: string
  }
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface RefreshTokenResponse {
  success: boolean
  message: string
  data: {
    token: string
    refresh_token: string
  }
}

export interface UserProfileResponse {
  success: boolean
  message: string
  data: User
}

export interface UpdateProfileRequest {
  username?: string
  email?: string
  avatar?: string
}

// 扩展 API slice 添加认证端点
export const authApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // 获取OAuth认证URL
    getAuthUrl: builder.query<AuthUrlResponse, { provider: 'google' | 'github' }>({
      query: ({ provider }) => ({
        url: `/auth/${provider}/url`,
        method: 'GET',
      }),
    }),

    // 处理OAuth回调
    handleAuthCallback: builder.mutation<AuthResponse, { 
      provider: 'google' | 'github'
      code: string
      state: string
    }>({
      query: ({ provider, code, state }) => ({
        url: `/auth/${provider}/callback?code=${code}&state=${state}`,
        method: 'GET',
      }),
      invalidatesTags: ['User'],
    }),

    // 刷新访问令牌
    refreshToken: builder.mutation<RefreshTokenResponse, RefreshTokenRequest>({
      query: (body) => ({
        url: '/auth/refresh',
        method: 'POST',
        body,
      }),
    }),

    // 获取用户资料
    getUserProfile: builder.query<UserProfileResponse, void>({
      query: () => ({
        url: '/user/profile',
        method: 'GET',
      }),
      providesTags: ['User'],
    }),

    // 更新用户资料
    updateUserProfile: builder.mutation<UserProfileResponse, UpdateProfileRequest>({
      query: (body) => ({
        url: '/user/profile',
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['User'],
    }),

    // 登出（可选，主要是清除客户端状态）
    logout: builder.mutation<{ success: boolean }, void>({
      queryFn: () => ({ data: { success: true } }),
      invalidatesTags: ['User', 'World', 'Character', 'Scene', 'Event'],
    }),
  }),
})

// 导出生成的hooks
export const {
  useGetAuthUrlQuery,
  useLazyGetAuthUrlQuery,
  useHandleAuthCallbackMutation,
  useRefreshTokenMutation,
  useGetUserProfileQuery,
  useLazyGetUserProfileQuery,
  useUpdateUserProfileMutation,
  useLogoutMutation,
} = authApi
