import { apiSlice } from './apiSlice'
import type { GameCharacter } from '../slices/gameSlice'

// 角色相关的类型定义
export interface CreateCharacterRequest {
  world_id: string
  name: string
  description: string
  character_type: 'player' | 'npc'
  attributes?: Record<string, any>
  appearance?: Record<string, any>
  personality?: Record<string, any>
  background?: string
}

export interface UpdateCharacterRequest {
  name?: string
  description?: string
  character_type?: 'player' | 'npc'
  traits?: string[]
  attributes?: Record<string, any>
  appearance?: Record<string, any>
  personality?: Record<string, any>
  background?: string
}

export interface CharacterResponse {
  success: boolean
  message: string
  data: GameCharacter
}

export interface CharacterListResponse {
  success: boolean
  message: string
  data: {
    items: GameCharacter[]
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

export interface AddTraitRequest {
  trait_name: string
  trait_value: any
  description?: string
}

export interface AddMemoryRequest {
  memory_type: string
  content: string
  importance: number
  related_characters?: string[]
  related_events?: string[]
}

export interface AddExperienceRequest {
  experience_type: string
  amount: number
  description?: string
}

// 扩展 API slice 添加角色管理端点
export const characterApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // 创建角色
    createCharacter: builder.mutation<CharacterResponse, CreateCharacterRequest>({
      query: (body) => ({
        url: '/game/characters',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Character'],
    }),

    // 获取角色详情
    getCharacter: builder.query<CharacterResponse, string>({
      query: (characterId) => ({
        url: `/game/characters/${characterId}`,
        method: 'GET',
      }),
      providesTags: (_result, _error, characterId) => [{ type: 'Character', id: characterId }],
    }),

    // 更新角色
    updateCharacter: builder.mutation<CharacterResponse, { 
      characterId: string
      data: UpdateCharacterRequest 
    }>({
      query: ({ characterId, data }) => ({
        url: `/game/characters/${characterId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (_result, _error, { characterId }) => [{ type: 'Character', id: characterId }],
    }),

    // 删除角色
    deleteCharacter: builder.mutation<{ success: boolean; message: string }, string>({
      query: (characterId) => ({
        url: `/game/characters/${characterId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Character'],
    }),

    // 获取世界中的角色列表
    getWorldCharacters: builder.query<CharacterListResponse, { 
      worldId: string
      page?: number
      limit?: number
      character_type?: 'player' | 'npc'
    }>({
      query: ({ worldId, page = 1, limit = 10, character_type }) => {
        let url = `/game/world/${worldId}/characters?page=${page}&limit=${limit}`
        if (character_type) {
          url += `&character_type=${character_type}`
        }
        return {
          url,
          method: 'GET',
        }
      },
      providesTags: ['Character'],
    }),

    // 获取我的角色列表
    getMyCharacters: builder.query<CharacterListResponse, { 
      page?: number
      limit?: number
      world_id?: string
    }>({
      query: ({ page = 1, limit = 10, world_id }) => {
        let url = `/game/my-characters?page=${page}&limit=${limit}`
        if (world_id) {
          url += `&world_id=${world_id}`
        }
        return {
          url,
          method: 'GET',
        }
      },
      providesTags: ['Character'],
    }),

    // 添加角色特质
    addCharacterTrait: builder.mutation<{ success: boolean; message: string }, {
      characterId: string
      data: AddTraitRequest
    }>({
      query: ({ characterId, data }) => ({
        url: `/game/characters/${characterId}/traits`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (_result, _error, { characterId }) => [{ type: 'Character', id: characterId }],
    }),

    // 添加角色记忆
    addCharacterMemory: builder.mutation<{ success: boolean; message: string }, {
      characterId: string
      data: AddMemoryRequest
    }>({
      query: ({ characterId, data }) => ({
        url: `/game/characters/${characterId}/memories`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (_result, _error, { characterId }) => [{ type: 'Character', id: characterId }],
    }),

    // 添加角色经验
    addCharacterExperience: builder.mutation<{ success: boolean; message: string }, {
      characterId: string
      data: AddExperienceRequest
    }>({
      query: ({ characterId, data }) => ({
        url: `/game/characters/${characterId}/experiences`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (_result, _error, { characterId }) => [{ type: 'Character', id: characterId }],
    }),
  }),
})

// 导出生成的hooks
export const {
  useCreateCharacterMutation,
  useGetCharacterQuery,
  useLazyGetCharacterQuery,
  useUpdateCharacterMutation,
  useDeleteCharacterMutation,
  useGetWorldCharactersQuery,
  useLazyGetWorldCharactersQuery,
  useGetMyCharactersQuery,
  useLazyGetMyCharactersQuery,
  useAddCharacterTraitMutation,
  useAddCharacterMemoryMutation,
  useAddCharacterExperienceMutation,
} = characterApi
