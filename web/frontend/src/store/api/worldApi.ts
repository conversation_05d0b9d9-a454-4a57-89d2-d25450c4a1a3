import { apiSlice } from './apiSlice'
import type { GameWorld } from '../slices/gameSlice'

// 世界相关的类型定义
export interface CreateWorldRequest {
  name: string
  description: string
  theme: string
  is_public: boolean
  max_players: number
  world_settings?: Record<string, any>
}

export interface UpdateWorldRequest {
  name?: string
  description?: string
  theme?: string
  is_public?: boolean
  max_players?: number
  world_settings?: Record<string, any>
}

export interface WorldResponse {
  success: boolean
  message: string
  data: GameWorld
}

export interface WorldListResponse {
  success: boolean
  message: string
  data: {
    worlds: GameWorld[]
    total: number
    page: number
    limit: number
  }
}

export interface WorldStateResponse {
  success: boolean
  message: string
  data: {
    world_id: string
    current_time: string
    game_time: number
    weather: string
    season: string
    global_events: Array<{
      id: string
      event_type: string
      name: string
      status: string
      priority: number
    }>
    active_scenes: Array<{
      id: string
      name: string
      status: string
      character_count: number
    }>
    online_characters: Array<{
      id: string
      name: string
      character_type: string
      current_scene_id: string
    }>
    variables: Record<string, any>
  }
}

export interface UpdateWorldTimeRequest {
  minutes: number
}

// AI生成世界配置相关类型
export interface GenerateWorldsRequest {
  worldName: string
  worldSettings?: Record<string, any>
}

export interface WorldRule {
  name: string
  description: string
  category: string
  severity: string
  enforcement: string
}

export interface ClimateConfig {
  type: string
  seasons: string[]
  weatherPatterns: string[]
}

export interface Landmark {
  name: string
  type: string
  description?: string
}

export interface TerrainConfig {
  primaryTerrain: string
  landmarks?: Landmark[]
}

export interface RareResource {
  name: string
  rarity: string
  description?: string
}

export interface ResourcesConfig {
  commonResources?: string[]
  rareResources?: RareResource[]
}

export interface EnvironmentConfig {
  climate?: ClimateConfig
  terrain?: TerrainConfig
  resources?: ResourcesConfig
}

export interface Society {
  name: string
  type: string
  description?: string
  traits?: string[]
}

export interface Language {
  name: string
  speakers: string[]
  script?: string
}

export interface Tradition {
  name: string
  description: string
  participants?: string[]
  frequency?: string
}

export interface CultureConfig {
  societies?: Society[]
  languages?: Language[]
  traditions?: Tradition[]
}

export interface Era {
  name: string
  description: string
  duration?: string
  keyEvents?: string[]
}

export interface Legend {
  title: string
  summary: string
  characters?: string[]
  truthLevel?: string
}

export interface HistoryConfig {
  eras?: Era[]
  legends?: Legend[]
}

export interface Region {
  name: string
  type: string
  description?: string
  climate?: string
  population?: string
  governance?: string
}

export interface Connection {
  from: string
  to: string
  method: string
  difficulty?: string
  description?: string
}

export interface GeographyConfig {
  worldType: string
  size: string
  regions?: Region[]
  connections?: Connection[]
}

export interface WorldConfiguration {
  worldDescription: string
  worldRules?: WorldRule[]
  environment?: EnvironmentConfig
  culture?: CultureConfig
  history?: HistoryConfig
  geography?: GeographyConfig
}

export interface WorldPreview {
  shortDescription: string
  mainFeatures: string[]
  theme: string
  difficulty: string
}

export interface WorldCandidate {
  id: string
  name: string
  configuration: WorldConfiguration
  preview: WorldPreview
}

export interface GenerateWorldsResponse {
  success: boolean
  message: string
  data: {
    candidates: WorldCandidate[]
  }
}

// 扩展 API slice 添加世界管理端点
export const worldApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // AI生成世界配置
    generateWorlds: builder.mutation<GenerateWorldsResponse, GenerateWorldsRequest>({
      query: (body) => ({
        url: '/game/worlds/generate',
        method: 'POST',
        body,
        // 设置更长的超时时间，因为AI生成需要时间
        timeout: 120000, // 2分钟超时
      }),
    }),

    // 创建世界
    createWorld: builder.mutation<WorldResponse, CreateWorldRequest>({
      query: (body) => ({
        url: '/game/worlds',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['World'],
    }),

    // 获取世界详情
    getWorld: builder.query<WorldResponse, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}`,
        method: 'GET',
      }),
      providesTags: (_result, _error, worldId) => [{ type: 'World', id: worldId }],
    }),

    // 更新世界
    updateWorld: builder.mutation<WorldResponse, { worldId: string; data: UpdateWorldRequest }>({
      query: ({ worldId, data }) => ({
        url: `/game/worlds/${worldId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (_result, _error, { worldId }) => [{ type: 'World', id: worldId }],
    }),

    // 删除世界
    deleteWorld: builder.mutation<{ success: boolean; message: string }, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['World'],
    }),

    // 加入世界
    joinWorld: builder.mutation<{ success: boolean; message: string }, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}/join`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, worldId) => [{ type: 'World', id: worldId }],
    }),

    // 离开世界
    leaveWorld: builder.mutation<{ success: boolean; message: string }, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}/leave`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, worldId) => [{ type: 'World', id: worldId }],
    }),

    // 获取我的世界列表
    getMyWorlds: builder.query<WorldListResponse, { page?: number; limit?: number }>({
      query: ({ page = 1, limit = 10 } = {}) => ({
        url: `/game/my-worlds?page=${page}&limit=${limit}`,
        method: 'GET',
      }),
      providesTags: ['World'],
    }),

    // 获取公开世界列表
    getPublicWorlds: builder.query<WorldListResponse, { page?: number; limit?: number }>({
      query: ({ page = 1, limit = 10 } = {}) => ({
        url: `/game/public-worlds?page=${page}&limit=${limit}`,
        method: 'GET',
      }),
      providesTags: ['World'],
    }),

    // 获取世界状态
    getWorldState: builder.query<WorldStateResponse, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}/state`,
        method: 'GET',
      }),
      providesTags: (_result, _error, worldId) => [{ type: 'World', id: `${worldId}-state` }],
    }),

    // 更新世界时间
    updateWorldTime: builder.mutation<{ success: boolean; message: string }, { 
      worldId: string
      data: UpdateWorldTimeRequest 
    }>({
      query: ({ worldId, data }) => ({
        url: `/game/worlds/${worldId}/time`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (_result, _error, { worldId }) => [
        { type: 'World', id: worldId },
        { type: 'World', id: `${worldId}-state` }
      ],
    }),

    // 处理世界时钟周期
    processWorldTick: builder.mutation<{ success: boolean; message: string }, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}/tick`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, worldId) => [
        { type: 'World', id: worldId },
        { type: 'World', id: `${worldId}-state` },
        'Character',
        'Scene',
        'Event'
      ],
    }),
  }),
})

// 导出生成的hooks
export const {
  useGenerateWorldsMutation,
  useCreateWorldMutation,
  useGetWorldQuery,
  useLazyGetWorldQuery,
  useUpdateWorldMutation,
  useDeleteWorldMutation,
  useJoinWorldMutation,
  useLeaveWorldMutation,
  useGetMyWorldsQuery,
  useLazyGetMyWorldsQuery,
  useGetPublicWorldsQuery,
  useLazyGetPublicWorldsQuery,
  useGetWorldStateQuery,
  useLazyGetWorldStateQuery,
  useUpdateWorldTimeMutation,
  useProcessWorldTickMutation,
} = worldApi
