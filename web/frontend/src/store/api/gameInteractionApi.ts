import { apiSlice } from './apiSlice'

// 游戏交互相关的类型定义
export interface PerformActionRequest {
  world_id: string
  action_type: 'move' | 'explore' | 'use' | 'rest' | 'search'
  target_type?: 'scene' | 'character' | 'entity' | 'item'
  target_id?: string
  parameters?: Record<string, any>
}

export interface ActionResult {
  event_id: string
  action_type: string
  success: boolean
  message: string
  effects?: Record<string, any>
  timestamp: string
}

export interface InteractionRequest {
  world_id: string
  interaction_type: 'greet' | 'talk' | 'trade' | 'help' | 'challenge' | 'follow'
  content?: string
  parameters?: Record<string, any>
}

export interface InteractionResult {
  event_id: string
  interaction_type: string
  success: boolean
  message: string
  response?: string
  effects?: Record<string, any>
  timestamp: string
}

export interface SpeakRequest {
  world_id: string
  content: string
  speech_type: 'say' | 'whisper' | 'shout' | 'think'
  target_character_id?: string
  volume: 'quiet' | 'normal' | 'loud'
  emotion: 'neutral' | 'happy' | 'sad' | 'angry' | 'curious' | 'worried'
}

export interface SpeechResult {
  event_id: string
  content: string
  speech_type: string
  volume: string
  emotion: string
  listeners: string[]
  success: boolean
  message: string
  timestamp: string
}

export interface TriggerEventRequest {
  world_id: string
  event_type: 'character_action' | 'world_event' | 'scene_event' | 'system_event'
  name: string
  description: string
  priority: number
  participants?: string[]
  event_data?: Record<string, any>
  process_immediately?: boolean
}

export interface EventResult {
  event_id: string
  event_type: string
  name: string
  status: string
  success: boolean
  message: string
  effects?: Record<string, any>
  timestamp: string
}

export interface GameInteractionResponse<T> {
  success: boolean
  message: string
  data: T
}

// 扩展 API slice 添加游戏交互端点
export const gameInteractionApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // 执行角色行动
    performAction: builder.mutation<GameInteractionResponse<ActionResult>, {
      characterId: string
      data: PerformActionRequest
    }>({
      query: ({ characterId, data }) => ({
        url: `/game/characters/${characterId}/actions`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Character', 'Scene', 'Event'],
    }),

    // 角色间交互
    interactWithCharacter: builder.mutation<GameInteractionResponse<InteractionResult>, {
      characterId: string
      targetCharacterId: string
      data: InteractionRequest
    }>({
      query: ({ characterId, targetCharacterId, data }) => ({
        url: `/game/characters/${characterId}/interact/${targetCharacterId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Character', 'Event'],
    }),

    // 场景中说话
    speakInScene: builder.mutation<GameInteractionResponse<SpeechResult>, {
      characterId: string
      data: SpeakRequest
    }>({
      query: ({ characterId, data }) => ({
        url: `/game/characters/${characterId}/speak`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Scene', 'Event'],
    }),

    // 触发事件
    triggerEvent: builder.mutation<GameInteractionResponse<EventResult>, TriggerEventRequest>({
      query: (data) => ({
        url: '/game/events/trigger',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Event', 'World', 'Scene', 'Character'],
    }),
  }),
})

// 导出生成的hooks
export const {
  usePerformActionMutation,
  useInteractWithCharacterMutation,
  useSpeakInSceneMutation,
  useTriggerEventMutation,
} = gameInteractionApi
