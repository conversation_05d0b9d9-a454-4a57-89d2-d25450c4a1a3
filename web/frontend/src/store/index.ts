import { configureStore } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query'
import { authSlice } from './slices/authSlice'
import { gameSlice } from './slices/gameSlice'
import { uiSlice } from './slices/uiSlice'
import { apiSlice } from './api/apiSlice'

// 配置 Redux store
export const store = configureStore({
  reducer: {
    // API slice
    api: apiSlice.reducer,
    
    // 功能 slices
    auth: authSlice.reducer,
    game: gameSlice.reducer,
    ui: uiSlice.reducer,
  },
  
  // 添加 RTK Query 中间件
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略 RTK Query 的 action types
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).concat(apiSlice.middleware),
  
  // 开发环境启用 Redux DevTools
  devTools: process.env.NODE_ENV !== 'production',
})

// 设置监听器以启用 refetchOnFocus/refetchOnReconnect 行为
setupListeners(store.dispatch)

// 导出类型
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// 导出 hooks
export { useAppDispatch, useAppSelector } from './hooks'
