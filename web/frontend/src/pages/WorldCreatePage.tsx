import React, { useEffect, useState } from 'react'
import { Card, Typography, Form, Input, Select, Switch, Button, message, Space, Divider, Modal, List, Tag } from 'antd'
import { PlusOutlined, RobotOutlined, EyeOutlined, CheckOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch } from '../store/hooks'
import { setPageTitle } from '../store/slices/uiSlice'
import { useCreateWorldMutation, useGenerateWorldsMutation } from '../store/api'
import { useGenerateSceneMutation } from '../store/api'
import type { WorldCandidate } from '../store/api/worldApi'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

const Container = styled.div`
  min-height: calc(100vh - 64px - 70px);
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
`

const StyledCard = styled(Card)`
  width: 100%;
  max-width: 800px;

  .ant-card-body {
    padding: 32px;
  }
`

const AIButton = styled(Button)`
  border-color: #6366f1;
  color: #6366f1;

  &:hover {
    border-color: #5856eb;
    color: #5856eb;
  }
`

const GenerateButton = styled(Button)`
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  color: white;
  font-weight: 500;
  height: 48px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);

  &:hover {
    background: linear-gradient(135deg, #5856eb 0%, #7c3aed 100%);
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`

const CandidateCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .ant-card-body {
    padding: 20px;
  }
`

const FeatureTag = styled(Tag)`
  margin: 2px 4px;
  border-radius: 12px;
  padding: 2px 8px;
`

const PreviewModal = styled(Modal)`
  .ant-modal-content {
    border-radius: 12px;
  }

  .ant-modal-header {
    border-radius: 12px 12px 0 0;
  }
`

interface WorldFormData {
  name: string
  description: string
  theme: string
  setting: string
  isPublic: boolean
  maxPlayers: number
  rules?: string
}

const WorldCreatePage: React.FC = () => {
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const [form] = Form.useForm<WorldFormData>()
  const [loading, setLoading] = useState(false)
  const [aiGenerating, setAiGenerating] = useState(false)

  // 新增状态
  const [showCandidates, setShowCandidates] = useState(false)
  const [candidates, setCandidates] = useState<WorldCandidate[]>([])
  const [selectedCandidate, setSelectedCandidate] = useState<WorldCandidate | null>(null)
  const [previewVisible, setPreviewVisible] = useState(false)
  const [previewCandidate, setPreviewCandidate] = useState<WorldCandidate | null>(null)

  const [createWorld] = useCreateWorldMutation()
  const [generateScene] = useGenerateSceneMutation()
  const [generateWorlds] = useGenerateWorldsMutation()

  useEffect(() => {
    dispatch(setPageTitle('创建世界'))
  }, [dispatch])

  // AI辅助生成世界描述
  const handleAIGenerate = async () => {
    const name = form.getFieldValue('name')
    const theme = form.getFieldValue('theme')
    const setting = form.getFieldValue('setting')

    if (!name || !theme) {
      message.warning('请先填写世界名称和类型')
      return
    }

    // 显示加载提示，提醒用户Windmill接口需要更长时间
    const loadingMessage = message.loading('正在调用Windmill AI生成世界描述，请耐心等待...', 0)
    setAiGenerating(true)

    try {
      const result = await generateScene({
        world_id: 'temp', // 临时ID，因为世界还未创建
        scene_name: name,
        scene_type: 'main',
        theme: theme,
        mood: setting || 'neutral',
        special_requirements: `为名为"${name}"的${theme}类型世界生成详细描述，设定为${setting || '未指定'}。请生成一个引人入胜的世界背景，包含环境特色、文化背景、历史设定等元素。`
      }).unwrap()

      // 关闭加载提示
      loadingMessage()

      // 修复AI生成内容的格式验证逻辑
      // 优先使用structured_data中的description，然后使用content
      let description = ''

      // 类型断言以访问实际的响应结构
      const aiResponse = result.data as any

      if (aiResponse?.structured_data?.description) {
        description = aiResponse.structured_data.description
      } else if (aiResponse?.content) {
        description = aiResponse.content
      } else if ((result as any).content) {
        // 兼容旧格式
        description = (result as any).content
      }

      if (description) {
        form.setFieldsValue({
          description: description
        })
        message.success('Windmill AI生成完成！世界描述已自动填入')
      } else {
        console.warn('AI响应格式:', result)
        message.warning('AI生成的内容格式异常，请手动填写描述')
      }
    } catch (error: any) {
      // 关闭加载提示
      loadingMessage()

      console.error('Windmill AI生成失败:', error)

      // 根据错误类型显示不同的提示，特别处理Windmill接口的错误
      if (error?.status === 401) {
        message.error('Windmill AI服务认证失败，请联系管理员')
      } else if (error?.status === 429) {
        message.error('Windmill AI服务请求过于频繁，请稍后重试')
      } else if (error?.status >= 500) {
        message.error('Windmill AI服务暂时不可用，请稍后重试')
      } else if (error?.message?.includes('timeout') || error?.status === 'TIMEOUT_ERROR') {
        message.error('Windmill AI生成超时，接口处理时间较长，请稍后重试')
      } else if (error?.status === 'FETCH_ERROR') {
        message.error('网络连接错误，请检查网络连接后重试')
      } else {
        message.error('Windmill AI生成失败，请稍后重试或手动填写描述')
      }
    } finally {
      setAiGenerating(false)
    }
  }

  // AI生成世界配置
  const handleGenerateWorlds = async () => {
    const name = form.getFieldValue('name')
    const theme = form.getFieldValue('theme')
    const setting = form.getFieldValue('setting')

    if (!name) {
      message.warning('请先填写世界名称')
      return
    }

    // 显示生成提示
    const loadingMessage = message.loading('正在生成世界配置，请稍候...', 0)
    setAiGenerating(true)

    try {
      const result = await generateWorlds({
        worldName: name,
        worldSettings: {
          theme: theme || 'fantasy',
          style: setting || '',
          difficulty: 'medium'
        }
      }).unwrap()

      // 关闭加载提示
      loadingMessage()

      if (result.success && result.data.candidates.length > 0) {
        setCandidates(result.data.candidates)
        setShowCandidates(true)
        message.success(`成功生成${result.data.candidates.length}个世界配置选项`)
      } else {
        message.error('未能生成有效的世界配置，请稍后重试')
      }
    } catch (error: any) {
      // 关闭加载提示
      loadingMessage()

      console.error('AI生成世界配置失败:', error)

      // 根据错误类型显示不同的错误信息
      if (error?.status === 401) {
        message.error('认证失败，请重新登录')
      } else if (error?.status === 429) {
        message.error('AI服务请求过于频繁，请稍后重试')
      } else if (error?.status >= 500) {
        message.error('AI服务暂时不可用，请稍后重试')
      } else if (error?.message?.includes('timeout') || error?.status === 'TIMEOUT_ERROR') {
        message.error('AI生成超时，请稍后重试')
      } else if (error?.status === 'FETCH_ERROR') {
        message.error('网络连接错误，请检查网络连接后重试')
      } else {
        message.error('AI生成失败，请稍后重试')
      }
    } finally {
      setAiGenerating(false)
    }
  }

  // 选择世界配置
  const handleSelectCandidate = (candidate: WorldCandidate) => {
    setSelectedCandidate(candidate)

    // 自动填充表单
    const config = candidate.configuration

    // 构建详细的世界描述，包含各个方面的信息
    let enhancedDescription = config.worldDescription

    // 添加环境信息
    if (config.environment) {
      enhancedDescription += '\n\n【环境设定】'
      if (config.environment.climate) {
        enhancedDescription += `\n气候类型：${config.environment.climate.type}`
        if (config.environment.climate.seasons?.length) {
          enhancedDescription += `，季节：${config.environment.climate.seasons.join('、')}`
        }
      }
      if (config.environment.terrain) {
        enhancedDescription += `\n地形特征：${config.environment.terrain.primaryTerrain}`
        if (config.environment.terrain.landmarks?.length) {
          const landmarkNames = config.environment.terrain.landmarks.slice(0, 3).map(l => l.name).join('、')
          enhancedDescription += `，主要地标：${landmarkNames}`
          if (config.environment.terrain.landmarks.length > 3) {
            enhancedDescription += ` 等${config.environment.terrain.landmarks.length}个地标`
          }
        }
      }
    }

    // 添加文化信息
    if (config.culture?.societies?.length) {
      enhancedDescription += '\n\n【文化背景】'
      const societyNames = config.culture.societies.slice(0, 3).map(s => s.name).join('、')
      enhancedDescription += `\n主要种族：${societyNames}`
      if (config.culture.societies.length > 3) {
        enhancedDescription += ` 等${config.culture.societies.length}个种族`
      }
    }

    // 添加地理信息
    if (config.geography) {
      enhancedDescription += '\n\n【地理结构】'
      enhancedDescription += `\n世界类型：${config.geography.worldType}，规模：${config.geography.size}`
      if (config.geography.regions?.length) {
        const regionNames = config.geography.regions.slice(0, 3).map(r => r.name).join('、')
        enhancedDescription += `\n主要区域：${regionNames}`
        if (config.geography.regions.length > 3) {
          enhancedDescription += ` 等${config.geography.regions.length}个区域`
        }
      }
    }

    // 构建规则文本
    let rulesText = ''
    if (config.worldRules?.length) {
      rulesText = config.worldRules.map(rule => {
        let ruleText = `【${rule.name}】${rule.description}`
        if (rule.category !== 'other') {
          ruleText += ` (类别：${rule.category})`
        }
        if (rule.severity !== 'medium') {
          ruleText += ` (严重性：${rule.severity})`
        }
        return ruleText
      }).join('\n\n')
    }

    // 自动推断主题
    let inferredTheme = candidate.preview.theme || 'fantasy'

    // 根据世界描述和配置推断更准确的主题
    const description = config.worldDescription.toLowerCase()
    if (description.includes('科技') || description.includes('机器') || description.includes('太空')) {
      inferredTheme = 'sci-fi'
    } else if (description.includes('现代') || description.includes('都市')) {
      inferredTheme = 'modern'
    } else if (description.includes('历史') || description.includes('古代')) {
      inferredTheme = 'historical'
    } else if (description.includes('末世') || description.includes('废土')) {
      inferredTheme = 'post-apocalyptic'
    } else if (description.includes('赛博') || description.includes('网络')) {
      inferredTheme = 'cyberpunk'
    } else if (description.includes('蒸汽') || description.includes('机械')) {
      inferredTheme = 'steampunk'
    } else if (description.includes('恐怖') || description.includes('黑暗')) {
      inferredTheme = 'horror'
    } else if (description.includes('悬疑') || description.includes('推理')) {
      inferredTheme = 'mystery'
    } else if (description.includes('冒险') || description.includes('探索')) {
      inferredTheme = 'adventure'
    }

    // 填充表单字段
    form.setFieldsValue({
      description: enhancedDescription,
      rules: rulesText,
      theme: inferredTheme
    })

    setShowCandidates(false)
    message.success('已选择世界配置并自动填充表单，您可以继续编辑完善')
  }

  // 预览世界配置
  const handlePreviewCandidate = (candidate: WorldCandidate) => {
    setPreviewCandidate(candidate)
    setPreviewVisible(true)
  }

  // 重置表单
  const handleResetForm = () => {
    Modal.confirm({
      title: '确认重置',
      content: '这将清除所有已填写的内容，包括AI生成的配置。确定要重置吗？',
      onOk: () => {
        form.resetFields()
        setSelectedCandidate(null)
        setCandidates([])
        setShowCandidates(false)
        setPreviewVisible(false)
        setPreviewCandidate(null)
        message.success('表单已重置')
      }
    })
  }

  // 提交表单
  const handleSubmit = async (values: WorldFormData) => {
    // 验证表单数据
    if (!values.name?.trim()) {
      message.error('请输入世界名称')
      return
    }

    if (!values.description?.trim()) {
      message.error('请输入世界描述')
      return
    }

    // 显示创建世界的加载提示
    const loadingMessage = message.loading('正在创建世界...', 0)
    setLoading(true)

    try {
      // 构建世界设置，包含AI生成的配置信息
      const worldSettings: Record<string, any> = {
        setting: values.setting || '',
        rules: values.rules || ''
      }

      // 如果有选中的AI生成配置，添加到设置中
      if (selectedCandidate) {
        worldSettings.aiGenerated = true
        worldSettings.candidateId = selectedCandidate.id
        worldSettings.originalConfiguration = selectedCandidate.configuration

        // 添加AI生成的详细配置信息
        if (selectedCandidate.configuration.environment) {
          worldSettings.environment = selectedCandidate.configuration.environment
        }
        if (selectedCandidate.configuration.culture) {
          worldSettings.culture = selectedCandidate.configuration.culture
        }
        if (selectedCandidate.configuration.history) {
          worldSettings.history = selectedCandidate.configuration.history
        }
        if (selectedCandidate.configuration.geography) {
          worldSettings.geography = selectedCandidate.configuration.geography
        }
      }

      const result = await createWorld({
        name: values.name.trim(),
        description: values.description.trim(),
        theme: values.theme,
        is_public: values.isPublic,
        max_players: values.maxPlayers,
        world_settings: worldSettings
      }).unwrap()

      // 关闭加载提示
      loadingMessage()

      // 显示成功消息
      if (selectedCandidate) {
        message.success('基于AI生成配置的世界创建成功！正在进入游戏...')
      } else {
        message.success('世界创建成功！正在进入游戏...')
      }

      // 清理状态
      setSelectedCandidate(null)
      setCandidates([])
      setShowCandidates(false)

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        navigate(`/game/${result.data.id}`)
      }, 1500)

    } catch (error: any) {
      // 关闭加载提示
      loadingMessage()

      console.error('创建世界失败:', error)

      // 详细的错误处理
      if (error?.status === 401) {
        message.error('用户认证失败，请重新登录')
        // 可以考虑跳转到登录页面
        setTimeout(() => {
          navigate('/login')
        }, 2000)
      } else if (error?.status === 403) {
        message.error('没有权限创建世界，请联系管理员')
      } else if (error?.status === 400) {
        if (error?.data?.message) {
          message.error(`输入数据有误：${error.data.message}`)
        } else {
          message.error('输入数据格式不正确，请检查后重试')
        }
      } else if (error?.status === 409) {
        message.error('世界名称已存在，请使用其他名称')
      } else if (error?.status === 429) {
        message.error('创建请求过于频繁，请稍后重试')
      } else if (error?.status >= 500) {
        message.error('服务器内部错误，请稍后重试或联系技术支持')
      } else if (error?.status === 'FETCH_ERROR') {
        message.error('网络连接错误，请检查网络连接后重试')
      } else if (error?.data?.message) {
        message.error(`创建失败：${error.data.message}`)
      } else {
        message.error('创建世界失败，请稍后重试')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <Container>
      <StyledCard>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
          <PlusOutlined style={{ marginRight: 8 }} />
          创建新世界
        </Title>

        {/* AI配置状态指示器 */}
        {selectedCandidate && (
          <div style={{
            marginBottom: 16,
            padding: 12,
            background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
            border: '1px solid #0ea5e9',
            borderRadius: 8,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <RobotOutlined style={{ color: '#0ea5e9', marginRight: 8, fontSize: 16 }} />
              <Text strong style={{ color: '#0369a1' }}>
                已应用AI生成的世界配置：{selectedCandidate.name}
              </Text>
            </div>
            <Button
              type="link"
              size="small"
              onClick={() => handlePreviewCandidate(selectedCandidate)}
              style={{ color: '#0ea5e9' }}
            >
              查看详情
            </Button>
          </div>
        )}

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          disabled={loading || aiGenerating}
          initialValues={{
            isPublic: false,
            maxPlayers: 10,
            theme: 'fantasy'
          }}
        >
          <Form.Item
            name="name"
            label="世界名称"
            rules={[
              { required: true, message: '请输入世界名称' },
              { min: 2, max: 50, message: '世界名称长度应在2-50个字符之间' }
            ]}
          >
            <Input
              placeholder="为你的世界起一个独特的名字"
              disabled={loading || aiGenerating}
            />
          </Form.Item>

          <Form.Item
            name="theme"
            label="世界类型"
            rules={[{ required: true, message: '请选择世界类型' }]}
          >
            <Select
              placeholder="选择世界的类型风格"
              disabled={loading || aiGenerating}
            >
              <Option value="fantasy">奇幻</Option>
              <Option value="sci-fi">科幻</Option>
              <Option value="modern">现代</Option>
              <Option value="historical">历史</Option>
              <Option value="post-apocalyptic">末世</Option>
              <Option value="cyberpunk">赛博朋克</Option>
              <Option value="steampunk">蒸汽朋克</Option>
              <Option value="horror">恐怖</Option>
              <Option value="mystery">悬疑</Option>
              <Option value="adventure">冒险</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="setting"
            label="世界设定"
          >
            <Input
              placeholder="简要描述世界的背景设定（可选）"
              disabled={loading || aiGenerating}
            />
          </Form.Item>

          {/* AI生成世界配置按钮 */}
          <div style={{ textAlign: 'center', margin: '24px 0' }}>
            <GenerateButton
              size="large"
              icon={<RobotOutlined />}
              loading={aiGenerating}
              disabled={loading || aiGenerating}
              onClick={handleGenerateWorlds}
            >
              {aiGenerating ? '正在生成世界配置...' : 'AI生成世界配置'}
            </GenerateButton>
            <div style={{ marginTop: 8, color: '#666', fontSize: '12px' }}>
              基于世界名称和设定生成多个完整的世界配置供您选择
            </div>
          </div>

          <Divider>或手动填写</Divider>

          <Form.Item
            name="description"
            label={
              <Space>
                <span>世界描述</span>
                <AIButton
                  size="small"
                  icon={<RobotOutlined />}
                  loading={aiGenerating}
                  disabled={loading || aiGenerating}
                  onClick={handleAIGenerate}
                >
                  {aiGenerating ? '正在生成...' : 'AI辅助生成'}
                </AIButton>
              </Space>
            }
            rules={[
              { required: true, message: '请输入世界描述' },
              { min: 10, max: 1000, message: '世界描述长度应在10-1000个字符之间' }
            ]}
          >
            <TextArea
              rows={6}
              placeholder="详细描述你的世界，包括环境、文化、历史背景等..."
              disabled={loading || aiGenerating}
            />
          </Form.Item>

          <Divider />

          <Form.Item
            name="maxPlayers"
            label="最大玩家数"
            rules={[
              { required: true, message: '请设置最大玩家数' },
              { type: 'number', min: 1, max: 100, message: '玩家数应在1-100之间' }
            ]}
          >
            <Input
              type="number"
              min={1}
              max={100}
              disabled={loading || aiGenerating}
            />
          </Form.Item>

          <Form.Item
            name="isPublic"
            label="公开世界"
            valuePropName="checked"
          >
            <Switch disabled={loading || aiGenerating} />
          </Form.Item>

          <Text type="secondary" style={{ fontSize: 12 }}>
            公开世界将显示在世界列表中，其他玩家可以申请加入
          </Text>

          <Form.Item
            name="rules"
            label="世界规则"
            style={{ marginTop: 16 }}
          >
            <TextArea
              rows={4}
              placeholder="设定世界的游戏规则和限制（可选）"
              disabled={loading || aiGenerating}
            />
          </Form.Item>

          <Form.Item style={{ marginTop: 32, textAlign: 'center' }}>
            <Space size="large">
              <Button
                onClick={() => navigate('/lobby')}
                disabled={loading || aiGenerating}
              >
                取消
              </Button>
              <Button
                onClick={handleResetForm}
                disabled={loading || aiGenerating}
              >
                重置表单
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                disabled={aiGenerating}
                size="large"
              >
                {loading ? '正在创建...' : '创建世界'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </StyledCard>

      {/* 候选世界配置选择模态框 */}
      <Modal
        title="选择世界配置"
        open={showCandidates}
        onCancel={() => setShowCandidates(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
          {candidates.map((candidate) => (
            <CandidateCard
              key={candidate.id}
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span>{candidate.name}</span>
                  <Space>
                    <FeatureTag color="blue">{candidate.preview.theme}</FeatureTag>
                    <FeatureTag color="green">{candidate.preview.difficulty}</FeatureTag>
                  </Space>
                </div>
              }
              actions={[
                <Button
                  key="preview"
                  icon={<EyeOutlined />}
                  onClick={() => handlePreviewCandidate(candidate)}
                >
                  预览详情
                </Button>,
                <Button
                  key="select"
                  type="primary"
                  icon={<CheckOutlined />}
                  onClick={() => handleSelectCandidate(candidate)}
                >
                  选择此配置
                </Button>
              ]}
            >
              <div style={{ marginBottom: 12 }}>
                <Text>{candidate.preview.shortDescription}</Text>
              </div>
              <div>
                <Text strong style={{ marginRight: 8 }}>主要特征:</Text>
                {candidate.preview.mainFeatures.map((feature, index) => (
                  <FeatureTag key={index} color="purple">
                    {feature}
                  </FeatureTag>
                ))}
              </div>
            </CandidateCard>
          ))}
        </div>
      </Modal>

      {/* 世界配置预览模态框 */}
      <PreviewModal
        title={`世界配置预览 - ${previewCandidate?.name}`}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
          <Button
            key="select"
            type="primary"
            onClick={() => {
              if (previewCandidate) {
                handleSelectCandidate(previewCandidate)
                setPreviewVisible(false)
              }
            }}
          >
            选择此配置
          </Button>
        ]}
        width={900}
        style={{ top: 20 }}
      >
        {previewCandidate && (
          <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
            <div style={{ marginBottom: 16 }}>
              <Title level={4}>世界描述</Title>
              <Text>{previewCandidate.configuration.worldDescription}</Text>
            </div>

            {previewCandidate.configuration.worldRules && previewCandidate.configuration.worldRules.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <Title level={4}>世界规则</Title>
                <List
                  size="small"
                  dataSource={previewCandidate.configuration.worldRules}
                  renderItem={(rule) => (
                    <List.Item>
                      <div>
                        <Text strong>{rule.name}</Text>
                        <Tag color="blue" style={{ marginLeft: 8 }}>{rule.category}</Tag>
                        <Tag color="orange">{rule.severity}</Tag>
                        <div style={{ marginTop: 4 }}>
                          <Text type="secondary">{rule.description}</Text>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              </div>
            )}

            {previewCandidate.configuration.environment && (
              <div style={{ marginBottom: 16 }}>
                <Title level={4}>环境设定</Title>
                {previewCandidate.configuration.environment.climate && (
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>气候: </Text>
                    <Tag color="cyan">{previewCandidate.configuration.environment.climate.type}</Tag>
                    {previewCandidate.configuration.environment.climate.seasons?.map((season, index) => (
                      <Tag key={index} color="green">{season}</Tag>
                    ))}
                  </div>
                )}
                {previewCandidate.configuration.environment.terrain && (
                  <div>
                    <Text strong>地形: </Text>
                    <Tag color="volcano">{previewCandidate.configuration.environment.terrain.primaryTerrain}</Tag>
                  </div>
                )}
              </div>
            )}

            {previewCandidate.configuration.culture?.societies && previewCandidate.configuration.culture.societies.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <Title level={4}>文化群体</Title>
                {previewCandidate.configuration.culture.societies.map((society, index) => (
                  <div key={index} style={{ marginBottom: 8 }}>
                    <Text strong>{society.name}</Text>
                    <Tag color="purple" style={{ marginLeft: 8 }}>{society.type}</Tag>
                    {society.description && (
                      <div style={{ marginTop: 4 }}>
                        <Text type="secondary">{society.description}</Text>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {previewCandidate.configuration.geography && (
              <div>
                <Title level={4}>地理信息</Title>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>世界类型: </Text>
                  <Tag color="geekblue">{previewCandidate.configuration.geography.worldType}</Tag>
                  <Text strong style={{ marginLeft: 16 }}>规模: </Text>
                  <Tag color="magenta">{previewCandidate.configuration.geography.size}</Tag>
                </div>
                {previewCandidate.configuration.geography.regions && previewCandidate.configuration.geography.regions.length > 0 && (
                  <div>
                    <Text strong>主要区域: </Text>
                    {previewCandidate.configuration.geography.regions.slice(0, 5).map((region, index) => (
                      <Tag key={index} color="gold">{region.name}</Tag>
                    ))}
                    {previewCandidate.configuration.geography.regions.length > 5 && (
                      <Tag>+{previewCandidate.configuration.geography.regions.length - 5} 更多</Tag>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </PreviewModal>
    </Container>
  )
}

export default WorldCreatePage
