import React, { useEffect, useState } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { Card, Spin, Result, Button } from 'antd'
import { LoadingOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { AuthService } from '../services/authService'
import { useAppSelector } from '../store/hooks'
import { selectIsAuthenticated } from '../store/slices/authSlice'

const CallbackContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`

const CallbackCard = styled(Card)`
  width: 100%;
  max-width: 500px;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
`

const SpinIcon = styled(LoadingOutlined)`
  font-size: 48px;
  color: #6366f1;
`

type AuthStatus = 'processing' | 'success' | 'error'

const AuthCallbackPage: React.FC = () => {
  const navigate = useNavigate()
  const { provider } = useParams<{ provider: string }>()
  const [searchParams] = useSearchParams()
  const [status, setStatus] = useState<AuthStatus>('processing')
  const [errorMessage, setErrorMessage] = useState<string>('')
  
  const isAuthenticated = useAppSelector(selectIsAuthenticated)
  
  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code')
        const state = searchParams.get('state')
        const error = searchParams.get('error')
        
        // 检查是否有错误参数
        if (error) {
          throw new Error(`OAuth认证失败: ${error}`)
        }
        
        // 检查必要参数
        if (!code || !state) {
          throw new Error('缺少必要的认证参数')
        }
        
        // 处理OAuth回调
        const success = await AuthService.handleOAuthCallback(code, state, provider)
        
        if (success) {
          setStatus('success')
          // 延迟跳转，让用户看到成功消息
          setTimeout(() => {
            navigate('/lobby', { replace: true })
          }, 2000)
        } else {
          throw new Error('认证处理失败')
        }
      } catch (error) {
        console.error('OAuth回调处理失败:', error)
        setStatus('error')
        setErrorMessage(error instanceof Error ? error.message : '认证失败')
      }
    }
    
    // 如果已经认证，直接跳转
    if (isAuthenticated) {
      navigate('/lobby', { replace: true })
      return
    }
    
    handleCallback()
  }, [searchParams, provider, navigate, isAuthenticated])
  
  const handleRetry = () => {
    navigate('/login', { replace: true })
  }
  
  const renderContent = () => {
    switch (status) {
      case 'processing':
        return (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Spin indicator={<SpinIcon />} />
            <div style={{ marginTop: 24, fontSize: 18, fontWeight: 500 }}>
              正在处理认证...
            </div>
            <div style={{ marginTop: 8, color: '#6b7280' }}>
              请稍候，我们正在验证您的身份
            </div>
          </motion.div>
        )
      
      case 'success':
        return (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Result
              icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              title="认证成功！"
              subTitle="正在跳转到游戏大厅..."
              extra={
                <Button type="primary" onClick={() => navigate('/lobby')}>
                  立即进入
                </Button>
              }
            />
          </motion.div>
        )
      
      case 'error':
        return (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Result
              icon={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              title="认证失败"
              subTitle={errorMessage || '认证过程中发生错误，请重试'}
              extra={[
                <Button type="primary" key="retry" onClick={handleRetry}>
                  重新登录
                </Button>,
                <Button key="home" onClick={() => navigate('/')}>
                  返回首页
                </Button>,
              ]}
            />
          </motion.div>
        )
      
      default:
        return null
    }
  }
  
  return (
    <CallbackContainer>
      <CallbackCard>
        {renderContent()}
      </CallbackCard>
    </CallbackContainer>
  )
}

export default AuthCallbackPage
