import React, { useEffect } from 'react'
import { Card, Button, Space, Typography, Divider } from 'antd'
import { useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { GoogleOutlined, GithubOutlined, PlayCircleOutlined } from '@ant-design/icons'
import { useAppSelector, useAppDispatch } from '../store/hooks'
import { selectIsAuthenticated, selectAuthLoading } from '../store/slices/authSlice'
import { setPageTitle } from '../store/slices/uiSlice'
import { AuthService } from '../services/authService'

const { Title, Paragraph } = Typography

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`

const LogoSection = styled.div`
  text-align: center;
  margin-bottom: 32px;
  
  .logo-icon {
    font-size: 48px;
    color: #6366f1;
    margin-bottom: 16px;
  }
`

const LoginButton = styled(Button)`
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  
  &.google-btn {
    background: #4285f4;
    border-color: #4285f4;
    
    &:hover {
      background: #3367d6;
      border-color: #3367d6;
    }
  }
  
  &.github-btn {
    background: #333;
    border-color: #333;
    
    &:hover {
      background: #24292e;
      border-color: #24292e;
    }
  }
`

const FeatureList = styled.div`
  margin-top: 24px;
  
  .feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #6b7280;
    font-size: 14px;
    
    &::before {
      content: '✨';
      margin-right: 8px;
    }
  }
`

const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  
  const isAuthenticated = useAppSelector(selectIsAuthenticated)
  const isLoading = useAppSelector(selectAuthLoading)

  useEffect(() => {
    dispatch(setPageTitle('登录'))
    
    // 如果已经登录，重定向到游戏大厅
    if (isAuthenticated) {
      navigate('/lobby')
    }
  }, [isAuthenticated, navigate, dispatch])

  // 处理 OAuth 登录
  const handleOAuthLogin = async (provider: 'google' | 'github') => {
    try {
      await AuthService.startOAuthFlow(provider)
    } catch (error) {
      console.error('OAuth登录失败:', error)
    }
  }

  // 开发模式快速登录
  const handleDevLogin = async () => {
    try {
      const success = await AuthService.developmentAutoLogin()
      if (success) {
        navigate('/lobby')
      }
    } catch (error) {
      console.error('开发模式登录失败:', error)
    }
  }

  // 检查是否为开发模式
  const isDevelopmentMode = AuthService.isDevelopmentMode()

  return (
    <LoginContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <LoginCard>
          <LogoSection>
            <PlayCircleOutlined className="logo-icon" />
            <Title level={2} style={{ margin: 0, color: '#1f2937' }}>
              AI文本游戏
            </Title>
            <Paragraph style={{ margin: '8px 0 0 0', color: '#6b7280' }}>
              I am NPC - 沉浸式文本冒险体验
            </Paragraph>
          </LogoSection>

          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div>
              <Title level={4} style={{ textAlign: 'center', marginBottom: 16 }}>
                选择登录方式
              </Title>
              
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {/* 开发模式快速登录按钮 */}
                {isDevelopmentMode && (
                  <>
                    <LoginButton
                      type="primary"
                      size="large"
                      icon={<PlayCircleOutlined />}
                      className="dev-btn"
                      block
                      onClick={handleDevLogin}
                      style={{
                        background: 'linear-gradient(45deg, #ff6b6b, #ee5a24)',
                        border: 'none',
                        boxShadow: '0 4px 15px rgba(255, 107, 107, 0.4)'
                      }}
                    >
                      🚀 开发模式快速登录
                    </LoginButton>
                    <Divider>
                      <span style={{ color: '#666', fontSize: '12px' }}>
                        或使用 OAuth 登录
                      </span>
                    </Divider>
                  </>
                )}

                <LoginButton
                  type="primary"
                  size="large"
                  icon={<GoogleOutlined />}
                  className="google-btn"
                  block
                  loading={isLoading}
                  onClick={() => handleOAuthLogin('google')}
                >
                  使用 Google 登录
                </LoginButton>

                <LoginButton
                  type="primary"
                  size="large"
                  icon={<GithubOutlined />}
                  className="github-btn"
                  block
                  loading={isLoading}
                  onClick={() => handleOAuthLogin('github')}
                >
                  使用 GitHub 登录
                </LoginButton>
              </Space>
            </div>

            <Divider>游戏特色</Divider>

            <FeatureList>
              <div className="feature-item">AI 驱动的动态故事生成</div>
              <div className="feature-item">多玩家协作冒险体验</div>
              <div className="feature-item">丰富的角色定制系统</div>
              <div className="feature-item">智能内容安全保障</div>
            </FeatureList>
          </Space>
        </LoginCard>
      </motion.div>
    </LoginContainer>
  )
}

export default LoginPage
