/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f9fafb;
}

#root {
  height: 100%;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 文本选择样式 */
::selection {
  background: #6366f1;
  color: white;
}

/* 链接样式 */
a {
  color: #6366f1;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #4f46e5;
}

/* 按钮动画 */
.ant-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 卡片动画 */
.ant-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 输入框聚焦效果 */
.ant-input:focus,
.ant-input-focused {
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* 游戏主题样式 */
.game-container {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: #f8fafc;
  min-height: 100vh;
}

.game-text {
  font-family: 'Courier New', monospace;
  line-height: 1.6;
  letter-spacing: 0.5px;
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px;
  }
  
  .ant-card {
    margin: 8px;
  }
}
