import React from 'react'
import { Layout, Space, Typography } from 'antd'
import styled from 'styled-components'
import { GithubOutlined, HeartFilled } from '@ant-design/icons'

const { Footer } = Layout
const { Text, Link } = Typography

const StyledFooter = styled(Footer)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid #f0f0f0;
  text-align: center;
  padding: 24px 50px;
  margin-top: auto;
`

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`

const FooterLinks = styled(Space)`
  margin-bottom: 16px;
  
  .ant-typography {
    color: #6b7280;
    transition: color 0.2s ease;
    
    &:hover {
      color: #6366f1;
    }
  }
`

const Copyright = styled(Text)`
  color: #9ca3af;
  font-size: 14px;
  
  .heart {
    color: #ef4444;
    margin: 0 4px;
  }
`

const AppFooter: React.FC = () => {
  const currentYear = new Date().getFullYear()

  return (
    <StyledFooter>
      <FooterContent>
        <FooterLinks size="large" split="|">
          <Link href="/about" target="_blank">
            关于我们
          </Link>
          <Link href="/privacy" target="_blank">
            隐私政策
          </Link>
          <Link href="/terms" target="_blank">
            服务条款
          </Link>
          <Link href="/help" target="_blank">
            帮助中心
          </Link>
          <Link href="https://github.com/your-repo" target="_blank">
            <GithubOutlined /> 开源代码
          </Link>
        </FooterLinks>
        
        <Copyright>
          © {currentYear} AI文本游戏 - I am NPC. 
          Made with <HeartFilled className="heart" /> by AI Team.
          All rights reserved.
        </Copyright>
      </FooterContent>
    </StyledFooter>
  )
}

export default AppFooter
