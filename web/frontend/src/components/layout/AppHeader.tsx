import React from 'react'
import { Layout, Menu, Avatar, Dropdown, Button, Space, Badge } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import styled from 'styled-components'
import {
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  HomeOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons'
import { useAppSelector, useAppDispatch } from '../../store/hooks'
import { selectUser, selectIsAuthenticated, logout } from '../../store/slices/authSlice'
import { selectUnreadNotifications, toggleSidebar, selectSidebarCollapsed } from '../../store/slices/uiSlice'

const { Header } = Layout

const StyledHeader = styled(Header)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #f0f0f0;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
`

const Logo = styled.div`
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #6366f1;
  cursor: pointer;
  
  .logo-icon {
    margin-right: 12px;
    font-size: 24px;
  }
`

const NavSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`

const AppHeader: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useAppDispatch()
  
  const user = useAppSelector(selectUser)
  const isAuthenticated = useAppSelector(selectIsAuthenticated)
  const unreadNotifications = useAppSelector(selectUnreadNotifications)
  const sidebarCollapsed = useAppSelector(selectSidebarCollapsed)

  // 处理登出
  const handleLogout = () => {
    dispatch(logout())
    navigate('/login')
  }

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => {
        // TODO: 打开设置模态框
      },
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ]

  // 主导航菜单项
  const navMenuItems = [
    {
      key: '/lobby',
      icon: <HomeOutlined />,
      label: '游戏大厅',
    },
    {
      key: '/world/create',
      icon: <PlayCircleOutlined />,
      label: '创建世界',
    },
  ]

  return (
    <StyledHeader>
      <NavSection>
        {/* 侧边栏切换按钮 */}
        {isAuthenticated && (
          <Button
            type="text"
            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => dispatch(toggleSidebar())}
          />
        )}
        
        {/* Logo */}
        <Logo onClick={() => navigate(isAuthenticated ? '/lobby' : '/login')}>
          <PlayCircleOutlined className="logo-icon" />
          AI文本游戏
        </Logo>
      </NavSection>

      {/* 导航菜单 */}
      {isAuthenticated && (
        <Menu
          mode="horizontal"
          selectedKeys={[location.pathname]}
          items={navMenuItems}
          style={{ 
            border: 'none', 
            background: 'transparent',
            minWidth: 200,
          }}
          onClick={({ key }) => navigate(key)}
        />
      )}

      {/* 用户区域 */}
      {isAuthenticated && user ? (
        <NavSection>
          {/* 通知铃铛 */}
          <Badge count={unreadNotifications.length} size="small">
            <Button
              type="text"
              icon={<BellOutlined />}
              onClick={() => {
                // TODO: 打开通知面板
              }}
            />
          </Badge>

          {/* 用户头像和菜单 */}
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <Space style={{ cursor: 'pointer' }}>
              <Avatar
                src={user.avatar}
                icon={<UserOutlined />}
                size="default"
              />
              <span>{user.username}</span>
            </Space>
          </Dropdown>
        </NavSection>
      ) : (
        <NavSection>
          <Button type="primary" onClick={() => navigate('/login')}>
            登录
          </Button>
        </NavSection>
      )}
    </StyledHeader>
  )
}

export default AppHeader
