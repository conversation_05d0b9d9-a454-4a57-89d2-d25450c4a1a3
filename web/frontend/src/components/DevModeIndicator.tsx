import React, { useState, useEffect } from 'react'
import { Badge, Tooltip, Button, Modal, Descriptions, Tag } from 'antd'
import { BugOutlined, InfoCircleOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { useAppSelector } from '../store/hooks'
import { selectIsAuthenticated } from '../store/slices/authSlice'
import { AuthService } from '../services/authService'

const DevIndicator = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  
  .dev-badge {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% {
      box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    }
    50% {
      box-shadow: 0 4px 16px rgba(255, 107, 107, 0.6);
    }
    100% {
      box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    }
  }
`

interface BackendStatus {
  status: string
  environment?: string
  auth_mode?: string
  dev_features?: string[]
  message?: string
}

/**
 * 开发模式指示器组件
 * 显示当前开发模式状态和相关信息
 */
const DevModeIndicator: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [backendStatus, setBackendStatus] = useState<BackendStatus | null>(null)
  const [loading, setLoading] = useState(false)
  
  const authState = useAppSelector(state => state.auth)
  const isAuthenticated = useAppSelector(selectIsAuthenticated)
  const user = authState.user
  const isDevelopmentMode = AuthService.isDevelopmentMode()

  // 获取后端状态
  const fetchBackendStatus = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/health', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const contentType = response.headers.get('content-type')
        if (contentType && contentType.includes('application/json')) {
          const data = await response.json()
          setBackendStatus(data)
        } else {
          console.warn('后端返回非JSON响应，可能后端服务未启动')
          setBackendStatus({
            status: 'disconnected',
            message: '后端服务未启动或不可用',
            auth_mode: 'unknown'
          })
        }
      } else {
        console.warn(`后端健康检查失败，状态码: ${response.status}`)
        setBackendStatus({
          status: 'error',
          message: `后端服务错误 (${response.status})`,
          auth_mode: 'unknown'
        })
      }
    } catch (error) {
      console.warn('无法连接到后端服务:', error)
      setBackendStatus({
        status: 'disconnected',
        message: '无法连接到后端服务',
        auth_mode: 'unknown'
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isDevelopmentMode) {
      fetchBackendStatus()
    }
  }, [isDevelopmentMode])

  // 如果不是开发模式，不显示指示器
  if (!isDevelopmentMode) {
    return null
  }

  const showModal = () => {
    setIsModalVisible(true)
    fetchBackendStatus() // 刷新状态
  }

  const handleCancel = () => {
    setIsModalVisible(false)
  }

  // 快速登录
  const handleQuickLogin = async () => {
    try {
      await AuthService.developmentAutoLogin()
      setIsModalVisible(false)
    } catch (error) {
      console.error('快速登录失败:', error)
    }
  }

  // 快速登出
  const handleQuickLogout = () => {
    AuthService.logout()
    setIsModalVisible(false)
  }

  return (
    <>
      <DevIndicator>
        <Tooltip title="开发模式已启用 - 点击查看详情">
          <Badge count="DEV" className="dev-badge">
            <Button
              type="primary"
              shape="circle"
              icon={<BugOutlined />}
              onClick={showModal}
              style={{
                background: 'rgba(255, 107, 107, 0.1)',
                border: '2px solid #ff6b6b',
                color: '#ff6b6b'
              }}
            />
          </Badge>
        </Tooltip>
      </DevIndicator>

      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <BugOutlined style={{ color: '#ff6b6b' }} />
            <span>开发模式状态</span>
          </div>
        }
        open={isModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="refresh" onClick={fetchBackendStatus} loading={loading}>
            刷新状态
          </Button>,
          !isAuthenticated ? (
            <Button key="login" type="primary" onClick={handleQuickLogin}>
              快速登录
            </Button>
          ) : (
            <Button key="logout" onClick={handleQuickLogout}>
              快速登出
            </Button>
          ),
          <Button key="close" onClick={handleCancel}>
            关闭
          </Button>,
        ]}
        width={600}
      >
        <Descriptions column={1} bordered size="small">
          <Descriptions.Item label="前端环境">
            <Tag color="orange">开发模式</Tag>
            <span style={{ marginLeft: 8 }}>
              {window.location.hostname}:{window.location.port}
            </span>
          </Descriptions.Item>
          
          <Descriptions.Item label="后端状态">
            {backendStatus ? (
              <div>
                <Tag color={backendStatus.status === 'ok' ? 'green' : 'red'}>
                  {backendStatus.status}
                </Tag>
                <Tag color="blue">{backendStatus.environment}</Tag>
                {backendStatus.auth_mode && (
                  <Tag color={backendStatus.auth_mode === 'disabled' ? 'orange' : 'green'}>
                    认证: {backendStatus.auth_mode === 'disabled' ? '已跳过' : '正常'}
                  </Tag>
                )}
              </div>
            ) : (
              <Tag color="red">无法连接</Tag>
            )}
          </Descriptions.Item>

          <Descriptions.Item label="认证状态">
            <div>
              <Tag color={isAuthenticated ? 'green' : 'red'}>
                {isAuthenticated ? '已登录' : '未登录'}
              </Tag>
              {user && (
                <span style={{ marginLeft: 8 }}>
                  {user.email} ({user.provider})
                </span>
              )}
            </div>
          </Descriptions.Item>

          {backendStatus?.dev_features && (
            <Descriptions.Item label="开发特性">
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                {backendStatus.dev_features.map((feature, index) => (
                  <Tag key={index} color="purple" style={{ margin: '2px' }}>
                    {feature}
                  </Tag>
                ))}
              </div>
            </Descriptions.Item>
          )}

          <Descriptions.Item label="API测试">
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <div>
                <strong>健康检查:</strong>
                <code style={{ marginLeft: 8, padding: '2px 6px', background: '#f5f5f5' }}>
                  GET /health
                </code>
              </div>
              <div>
                <strong>用户信息:</strong>
                <code style={{ marginLeft: 8, padding: '2px 6px', background: '#f5f5f5' }}>
                  GET /api/v1/user/profile
                </code>
              </div>
              <div>
                <strong>游戏世界:</strong>
                <code style={{ marginLeft: 8, padding: '2px 6px', background: '#f5f5f5' }}>
                  GET /api/v1/game/my-worlds
                </code>
              </div>
            </div>
          </Descriptions.Item>

          <Descriptions.Item label="注意事项">
            <div style={{ color: '#ff6b6b', fontSize: '12px' }}>
              <InfoCircleOutlined style={{ marginRight: 4 }} />
              此模式仅用于开发测试，请勿在生产环境使用！
            </div>
          </Descriptions.Item>
        </Descriptions>
      </Modal>
    </>
  )
}

export default DevModeIndicator
