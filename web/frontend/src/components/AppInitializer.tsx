import React, { useEffect, useState } from 'react'
import { Spin } from 'antd'
import styled from 'styled-components'
import { AuthService } from '../services/authService'

const InitializerContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 9999;
`

const InitializerContent = styled.div`
  text-align: center;
  color: white;
  
  .ant-spin {
    .ant-spin-dot-item {
      background-color: white;
    }
  }
  
  .loading-text {
    margin-top: 16px;
    font-size: 16px;
    font-weight: 500;
  }
`

interface AppInitializerProps {
  children: React.ReactNode
}

/**
 * 应用初始化组件
 * 负责在应用启动时初始化认证状态和其他必要的设置
 */
const AppInitializer: React.FC<AppInitializerProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false)
  
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // 初始化认证状态
        await AuthService.initializeAuth()
        
        // 可以在这里添加其他初始化逻辑
        // 例如：加载用户偏好设置、检查应用版本等
        
        // 模拟一些初始化时间，确保用户能看到加载状态
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.error('应用初始化失败:', error)
        // 即使初始化失败，也要继续加载应用
      } finally {
        setIsInitialized(true)
      }
    }
    
    initializeApp()
  }, [])
  
  if (!isInitialized) {
    return (
      <InitializerContainer>
        <InitializerContent>
          <Spin size="large" />
          <div className="loading-text">
            正在初始化应用...
          </div>
        </InitializerContent>
      </InitializerContainer>
    )
  }
  
  return <>{children}</>
}

export default AppInitializer
