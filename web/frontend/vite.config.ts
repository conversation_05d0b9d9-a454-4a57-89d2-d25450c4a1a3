import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [react()],
  define: {
    // 在生产构建时移除开发模式功能
    __DEV_MODE_ENABLED__: mode === 'development',
    // 确保生产环境下完全禁用开发模式
    'process.env.DISABLE_DEV_MODE': mode === 'production' ? 'true' : 'false',
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    host: true, // 允许外部访问
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        // 启用代理日志
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('🔴 [PROXY ERROR]', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('🔄 [PROXY REQ]', req.method, req.url, '→', options.target + req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('✅ [PROXY RES]', req.method, req.url, '←', proxyRes.statusCode);
          });
        },
      },
    },
    // 启用详细的开发服务器日志
    logLevel: mode === 'development' ? 'info' : 'warn',
  },
  build: {
    outDir: '../static/dist',
    emptyOutDir: true,
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          redux: ['@reduxjs/toolkit', 'react-redux'],
        },
      },
    },
  },
}))
