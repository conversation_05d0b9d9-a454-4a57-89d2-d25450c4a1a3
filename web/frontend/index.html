<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI文本游戏 - I am NPC</title>
    <meta name="description" content="沉浸式AI驱动的文本冒险游戏，体验无限可能的虚拟世界" />
    <meta name="keywords" content="AI游戏,文本游戏,角色扮演,冒险游戏,NPC" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://ai-text-game.com/" />
    <meta property="og:title" content="AI文本游戏 - I am NPC" />
    <meta property="og:description" content="沉浸式AI驱动的文本冒险游戏，体验无限可能的虚拟世界" />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://ai-text-game.com/" />
    <meta property="twitter:title" content="AI文本游戏 - I am NPC" />
    <meta property="twitter:description" content="沉浸式AI驱动的文本冒险游戏，体验无限可能的虚拟世界" />
    <meta property="twitter:image" content="/og-image.png" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 主题色 -->
    <meta name="theme-color" content="#6366f1" />
    
    <!-- PWA 支持 -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- 防止页面缩放 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    
    <style>
      /* 加载动画样式 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.3s ease-out;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      .loading-text {
        color: white;
        font-size: 18px;
        font-weight: 500;
        margin-top: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* 隐藏加载动画 */
      #loading.hidden {
        opacity: 0;
        pointer-events: none;
      }
      
      /* 基础样式重置 */
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f8fafc;
      }
      
      #root {
        min-height: 100vh;
      }
    </style>
  </head>
  <body>
    <!-- 加载动画 -->
    <div id="loading">
      <div style="text-align: center;">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载AI文本游戏...</div>
      </div>
    </div>
    
    <!-- React 应用挂载点 -->
    <div id="root"></div>
    
    <!-- 主应用脚本 -->
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- 隐藏加载动画的脚本 -->
    <script>
      // 当页面加载完成后隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('hidden');
            setTimeout(function() {
              loading.style.display = 'none';
            }, 300);
          }
        }, 500);
      });
      
      // 错误处理
      window.addEventListener('error', function(e) {
        console.error('应用加载错误:', e.error);
        const loading = document.getElementById('loading');
        if (loading) {
          loading.innerHTML = `
            <div style="text-align: center; color: white;">
              <div style="font-size: 24px; margin-bottom: 16px;">⚠️</div>
              <div style="font-size: 18px; margin-bottom: 8px;">应用加载失败</div>
              <div style="font-size: 14px; opacity: 0.8;">请刷新页面重试</div>
            </div>
          `;
        }
      });
    </script>
  </body>
</html>
