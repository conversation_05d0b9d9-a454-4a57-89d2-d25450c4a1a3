# AI文本游戏前端页面原型设计

## 1. 设计概述

### 1.1. 设计原则
- **用户体验优先**: 简洁直观的界面设计，降低学习成本
- **响应式设计**: 支持桌面端、平板和移动端的自适应布局
- **沉浸式体验**: 营造文本冒险游戏的氛围感
- **高效交互**: 快速响应用户操作，流畅的动画过渡
- **可访问性**: 支持键盘导航和屏幕阅读器

### 1.2. 技术栈
- **框架**: React 18 + TypeScript
- **状态管理**: Redux Toolkit + RTK Query
- **UI组件库**: Ant Design + 自定义组件
- **样式方案**: Styled-components + CSS Modules
- **动画库**: Framer Motion
- **图标库**: Ant Design Icons + Lucide React

### 1.3. 设计系统

#### 色彩方案
```css
/* 主色调 - 神秘紫色系 */
--primary-color: #6366f1;
--primary-light: #8b5cf6;
--primary-dark: #4f46e5;

/* 辅助色 - 温暖金色系 */
--secondary-color: #f59e0b;
--secondary-light: #fbbf24;
--secondary-dark: #d97706;

/* 中性色 */
--text-primary: #1f2937;
--text-secondary: #6b7280;
--text-muted: #9ca3af;
--background: #f9fafb;
--surface: #ffffff;
--border: #e5e7eb;

/* 深色主题 */
--dark-background: #111827;
--dark-surface: #1f2937;
--dark-text: #f9fafb;
```

#### 字体系统
```css
/* 标题字体 */
--font-heading: 'Inter', 'PingFang SC', sans-serif;
/* 正文字体 */
--font-body: 'Inter', 'PingFang SC', sans-serif;
/* 等宽字体 */
--font-mono: 'JetBrains Mono', 'Consolas', monospace;

/* 字体大小 */
--text-xs: 0.75rem;
--text-sm: 0.875rem;
--text-base: 1rem;
--text-lg: 1.125rem;
--text-xl: 1.25rem;
--text-2xl: 1.5rem;
--text-3xl: 1.875rem;
```

## 2. 页面结构设计

### 2.1. 整体布局
```
┌─────────────────────────────────────────────────────────────┐
│                        Header                               │
├─────────────────────────────────────────────────────────────┤
│          │                                    │             │
│          │                                    │             │
│ Sidebar  │            Main Content            │  Right Panel│
│          │                                    │             │
│          │                                    │             │
├─────────────────────────────────────────────────────────────┤
│                        Footer                               │
└─────────────────────────────────────────────────────────────┘
```

### 2.2. 响应式断点
```css
/* 移动端 */
@media (max-width: 768px) {
  /* 单列布局，侧边栏折叠 */
}

/* 平板端 */
@media (min-width: 769px) and (max-width: 1024px) {
  /* 两列布局，右侧面板可折叠 */
}

/* 桌面端 */
@media (min-width: 1025px) {
  /* 三列布局，完整功能 */
}
```

## 3. 核心页面设计

### 3.1. 登录页面 (外部IDP认证)

#### 页面布局
```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│    ┌─────────────────┐                                      │
│    │                 │         AI Text Game                 │
│    │   游戏Logo      │         欢迎来到AI文本冒险世界        │
│    │   动画效果      │                                      │
│    │                 │    ┌─────────────────────────────┐   │
│    └─────────────────┘    │                             │   │
│                           │      选择登录方式            │   │
│                           │                             │   │
│                           │  ┌─────────────────────────┐ │   │
│                           │  │  🔐 Auth0 登录         │ │   │
│                           │  └─────────────────────────┘ │   │
│                           │                             │   │
│                           │  ┌─────────────────────────┐ │   │
│                           │  │  🔍 Google 登录        │ │   │
│                           │  └─────────────────────────┘ │   │
│                           │                             │   │
│                           │  ┌─────────────────────────┐ │   │
│                           │  │  🐙 GitHub 登录        │ │   │
│                           │  └─────────────────────────┘ │   │
│                           │                             │   │
│                           │  ┌─────────────────────────┐ │   │
│                           │  │  🏢 企业SSO登录        │ │   │
│                           │  └─────────────────────────┘ │   │
│                           │                             │   │
│                           │     选择其他登录方式...      │   │
│                           └─────────────────────────────┘   │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### 关键功能
- 支持多种外部IDP提供商
- OAuth 2.0 / OpenID Connect 认证流程
- 自动检测可用的认证提供商
- 安全的认证回调处理
- 深色/浅色主题切换
- 响应式设计适配移动端

#### React组件设计
```typescript
interface LoginPageProps {
  availableProviders: IDPProvider[];
  onProviderSelect: (provider: string) => void;
  loading?: boolean;
}

interface IDPProvider {
  name: string;
  displayName: string;
  iconUrl: string;
  enabled: boolean;
}

const LoginPage: React.FC<LoginPageProps> = ({
  availableProviders,
  onProviderSelect,
  loading = false
}) => {
  return (
    <div className="login-container">
      <div className="login-form">
        <h1>选择登录方式</h1>
        {availableProviders.map(provider => (
          <Button
            key={provider.name}
            onClick={() => onProviderSelect(provider.name)}
            disabled={!provider.enabled || loading}
            className="idp-button"
          >
            <img src={provider.iconUrl} alt={provider.displayName} />
            {provider.displayName} 登录
          </Button>
        ))}
      </div>
    </div>
  );
};
```

### 3.2. 世界选择页面

#### 页面布局
```
┌─────────────────────────────────────────────────────────────┐
│  [用户头像] 用户名                    [设置] [通知] [退出]    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │  [+ 创建新世界]              [我的世界] [公共世界]   │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │                 │  │                 │  │             │  │
│  │   魔法王国      │  │   赛博朋克2077  │  │  古代中国   │  │
│  │                 │  │                 │  │             │  │
│  │ 创建者: 玩家A    │  │ 创建者: 玩家B    │  │ 创建者: 我  │  │
│  │ 玩家: 3/10      │  │ 玩家: 7/8       │  │ 玩家: 1/5   │  │
│  │ 状态: 活跃      │  │ 状态: 活跃      │  │ 状态: 暂停  │  │
│  │                 │  │                 │  │             │  │
│  │    [加入]       │  │    [加入]       │  │   [继续]    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   更多世界...   │  │   更多世界...   │  │  更多世界.. │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### 关键功能
- 世界卡片展示(缩略图、名称、描述、统计信息)
- 筛选和搜索功能
- 分页加载
- 世界预览功能
- 收藏和推荐系统

### 3.3. 世界创建页面

#### 页面布局
```
┌─────────────────────────────────────────────────────────────┐
│  [← 返回]                创建新世界                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                   基础设置                          │    │
│  │                                                     │    │
│  │  世界名称: [________________________]               │    │
│  │  世界描述: [________________________]               │    │
│  │           [________________________]               │    │
│  │           [________________________]               │    │
│  │                                                     │    │
│  │  世界类型: [奇幻] [科幻] [现代] [历史] [自定义]      │    │
│  │                                                     │    │
│  │  最大玩家数: [____] (1-20)                          │    │
│  │  是否公开: [ ] 允许其他玩家加入                      │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                   AI生成设置                        │    │
│  │                                                     │    │
│  │  世界背景提示:                                       │    │
│  │  [____________________________________________]     │    │
│  │  [____________________________________________]     │    │
│  │  [____________________________________________]     │    │
│  │                                                     │    │
│  │  时间倍率: [1x] [10x] [60x] [自定义: ___]           │    │
│  │  难度等级: [简单] [普通] [困难] [专家]               │    │
│  │                                                     │    │
│  │  多玩家设置:                                        │    │
│  │  ☑ 启用多玩家模式                                   │    │
│  │  最大玩家数: [2] [5] [10] [20] [自定义: ___]        │    │
│  │  玩家分配策略: [分散初始化] [集中起点] [自定义]      │    │
│  │                                                     │    │
│  │  内容安全等级:                                      │    │
│  │  ○ 宽松 ○ 标准 ● 严格                             │    │
│  │  ☑ 启用AI内容审核                                   │    │
│  │  ☑ 过滤不当内容                                     │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│                    [取消]        [创建世界]                 │
└─────────────────────────────────────────────────────────────┘
```

#### 关键功能
- 实时预览功能
- 内容安全检查
- 多玩家配置向导
- 模板选择
- 高级设置选项
- 创建进度指示
- 错误处理和重试机制

### 3.4. 游戏主界面

#### 页面布局
```
┌─────────────────────────────────────────────────────────────┐
│ [世界] 魔法王国  [时间] 第3天 10:30  [玩家] 3/10  [设置] [退出]│
├─────────────────────────────────────────────────────────────┤
│          │                                    │             │
│  角色信息  │              叙事区域              │   场景信息   │
│          │                                    │             │
│ ┌────────┐│  ┌─────────────────────────────┐   │ ┌─────────┐ │
│ │ 艾莉亚  ││  │你沿着森林小径缓缓前行，阳光  │   │ │ 森林小径 │ │
│ │        ││  │透过茂密的树叶洒在地面上，   │   │ │         │ │
│ │ 精灵法师││  │形成斑驳的光影。远处传来潺   │   │ │ 实体:   │ │
│ │        ││  │潺的流水声...              │   │ │ • 古橡树 │ │
│ └────────┘│  │                           │   │ │ • 小溪   │ │
│          │  │[10:25] 你向前走去           │   │ │ • 野花   │ │
│ 特质:     │  │[10:27] 你遇到了神秘商人     │   │ │         │ │
│ • 魔法天赋 │  │[10:30] 商人对你微笑...      │   │ │ 角色:   │ │
│ • 好奇心强 │  └─────────────────────────────┘   │ │ • 艾莉亚 │ │
│          │                                    │ │ • 神秘商人│ │
│ 物品栏:   │  ┌─────────────────────────────┐   │ └─────────┘ │
│ • 法师袍  │  │                             │   │             │
│ • 魔法杖  │  │     行动输入区域             │   │   快捷行动   │
│ • 治疗药水│  │                             │   │             │
│          │  │ [_________________________] │   │ [查看周围] │
│          │  │                             │   │ [检查物品] │
│          │  │        [执行行动]            │   │ [与NPC交谈]│
│          │  └─────────────────────────────┘   │ [使用技能] │
│          │                                    │             │
└──────────┴────────────────────────────────────┴─────────────┘
```

#### 关键功能
- 实时叙事更新
- 智能行动建议
- 角色状态监控
- 物品管理系统
- 快捷操作面板
- 历史记录查看
- 自动保存功能

### 3.5. 移动端适配

#### 移动端游戏界面
```
┌─────────────────────────────────────┐
│ [≡] 魔法王国    第3天 10:30  [⚙] [×]│
├─────────────────────────────────────┤
│                                     │
│  ┌─────────────────────────────────┐ │
│  │你沿着森林小径缓缓前行，阳光透过  │ │
│  │茂密的树叶洒在地面上，形成斑驳的  │ │
│  │光影。远处传来潺潺的流水声...    │ │
│  │                               │ │
│  │[10:25] 你向前走去              │ │
│  │[10:27] 你遇到了神秘商人        │ │
│  │[10:30] 商人对你微笑...         │ │
│  └─────────────────────────────────┘ │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ [_________________________]    │ │
│  │                               │ │
│  │          [执行行动]            │ │
│  └─────────────────────────────────┘ │
│                                     │
│  [角色] [场景] [物品] [历史] [更多]   │
└─────────────────────────────────────┘
```

## 4. 交互设计

### 4.1. 动画效果
- **页面切换**: 平滑的淡入淡出效果
- **内容更新**: 打字机效果展示新的叙事内容
- **状态变化**: 微妙的颜色和大小变化动画
- **加载状态**: 优雅的骨架屏和加载指示器

### 4.2. 快捷键支持
- `Enter`: 执行当前行动
- `Tab`: 在输入框间切换
- `Esc`: 关闭模态框
- `Ctrl+Z`: 撤销上一个行动(如果支持)
- `Ctrl+S`: 手动保存游戏

### 4.3. 无障碍设计
- 完整的键盘导航支持
- 屏幕阅读器友好的语义化标签
- 高对比度模式支持
- 字体大小调节功能

## 5. 认证系统集成

### 5.1. 认证状态管理
```typescript
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  tokenExpiry: number | null;
  loading: boolean;
  error: string | null;
}

interface User {
  id: string;
  externalId: string;
  externalProvider: string;
  email: string;
  displayName: string;
  avatarUrl?: string;
  gameRoles: string[];
  preferences: UserPreferences;
}

// Redux Toolkit Slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.accessToken = action.payload.accessToken;
      state.refreshToken = action.payload.refreshToken;
      state.tokenExpiry = action.payload.expiresIn + Date.now();
      state.loading = false;
    },
    loginFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.tokenExpiry = null;
    }
  }
});
```

### 5.2. API集成设计
```typescript
// RTK Query API定义
export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/v1/auth',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.accessToken;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getProviders: builder.query<IDPProvider[], void>({
      query: () => '/providers',
    }),
    initiateLogin: builder.mutation<{ redirectUrl: string }, { provider: string; redirectUri: string }>({
      query: ({ provider, redirectUri }) => ({
        url: `/providers/${provider}/authorize`,
        method: 'GET',
        params: { redirect_uri: redirectUri },
      }),
    }),
    handleCallback: builder.mutation<AuthResponse, CallbackRequest>({
      query: (data) => ({
        url: '/callback',
        method: 'POST',
        body: data,
      }),
    }),
    refreshToken: builder.mutation<{ accessToken: string; expiresIn: number }, { refreshToken: string }>({
      query: ({ refreshToken }) => ({
        url: '/refresh',
        method: 'POST',
        body: { refresh_token: refreshToken },
      }),
    }),
    logout: builder.mutation<void, { refreshToken: string }>({
      query: ({ refreshToken }) => ({
        url: '/logout',
        method: 'POST',
        body: { refresh_token: refreshToken },
      }),
    }),
  }),
});
```

### 5.3. 认证流程处理
```typescript
// 认证Hook
export const useAuth = () => {
  const dispatch = useAppDispatch();
  const authState = useAppSelector(state => state.auth);
  const [initiateLogin] = authApi.useInitiateLoginMutation();
  const [handleCallback] = authApi.useHandleCallbackMutation();
  const [refreshTokenMutation] = authApi.useRefreshTokenMutation();

  const login = useCallback(async (provider: string) => {
    try {
      dispatch(authSlice.actions.loginStart());

      const redirectUri = `${window.location.origin}/auth/callback`;
      const result = await initiateLogin({ provider, redirectUri }).unwrap();

      // 重定向到IDP认证页面
      window.location.href = result.redirectUrl;
    } catch (error) {
      dispatch(authSlice.actions.loginFailure(error.message));
    }
  }, [dispatch, initiateLogin]);

  const processCallback = useCallback(async (code: string, state: string, provider: string) => {
    try {
      const result = await handleCallback({ provider, code, state }).unwrap();
      dispatch(authSlice.actions.loginSuccess(result));

      // 重定向到主页面
      window.location.href = '/worlds';
    } catch (error) {
      dispatch(authSlice.actions.loginFailure(error.message));
    }
  }, [dispatch, handleCallback]);

  const refreshAccessToken = useCallback(async () => {
    if (!authState.refreshToken) return false;

    try {
      const result = await refreshTokenMutation({
        refreshToken: authState.refreshToken
      }).unwrap();

      dispatch(authSlice.actions.loginSuccess({
        ...authState,
        accessToken: result.accessToken,
        expiresIn: result.expiresIn
      }));

      return true;
    } catch (error) {
      dispatch(authSlice.actions.logout());
      return false;
    }
  }, [authState, refreshTokenMutation, dispatch]);

  return {
    ...authState,
    login,
    processCallback,
    refreshAccessToken,
    logout: () => dispatch(authSlice.actions.logout())
  };
};
```

## 6. 内容安全UI组件

### 6.1. 内容输入验证组件
```typescript
interface ContentInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  safetyLevel?: 'loose' | 'standard' | 'strict';
  onValidationResult?: (result: ValidationResult) => void;
}

const ContentInput: React.FC<ContentInputProps> = ({
  value,
  onChange,
  placeholder,
  maxLength = 500,
  safetyLevel = 'standard',
  onValidationResult
}) => {
  const [validationState, setValidationState] = useState<'idle' | 'validating' | 'valid' | 'invalid'>('idle');
  const [validationMessage, setValidationMessage] = useState<string>('');

  const validateContent = useCallback(
    debounce(async (content: string) => {
      if (!content.trim()) {
        setValidationState('idle');
        return;
      }

      setValidationState('validating');

      try {
        const result = await validateUserInput({
          content,
          safetyLevel,
          context: { /* 当前上下文 */ }
        });

        if (result.isValid) {
          setValidationState('valid');
          setValidationMessage('内容通过安全检查');
        } else {
          setValidationState('invalid');
          setValidationMessage(result.violations.join(', '));
        }

        onValidationResult?.(result);
      } catch (error) {
        setValidationState('invalid');
        setValidationMessage('验证失败，请重试');
      }
    }, 500),
    [safetyLevel, onValidationResult]
  );

  useEffect(() => {
    validateContent(value);
  }, [value, validateContent]);

  return (
    <div className="content-input-wrapper">
      <TextArea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        maxLength={maxLength}
        className={`content-input ${validationState}`}
        suffix={
          <div className="validation-indicator">
            {validationState === 'validating' && <LoadingOutlined />}
            {validationState === 'valid' && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
            {validationState === 'invalid' && <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
          </div>
        }
      />
      {validationMessage && (
        <div className={`validation-message ${validationState}`}>
          {validationMessage}
        </div>
      )}
      <div className="character-count">
        {value.length}/{maxLength}
      </div>
    </div>
  );
};
```

### 6.2. 多玩家世界配置组件
```typescript
interface MultiplayerConfigProps {
  config: MultiplayerConfig;
  onChange: (config: MultiplayerConfig) => void;
  worldDescription: string;
}

const MultiplayerConfig: React.FC<MultiplayerConfigProps> = ({
  config,
  onChange,
  worldDescription
}) => {
  const [frameworkPreview, setFrameworkPreview] = useState<WorldFramework | null>(null);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);

  const generateFrameworkPreview = useCallback(async () => {
    if (!worldDescription.trim()) return;

    setIsGeneratingPreview(true);
    try {
      const framework = await generateWorldFramework({
        description: worldDescription,
        maxPlayers: config.maxPlayers
      });
      setFrameworkPreview(framework);
    } catch (error) {
      message.error('生成世界框架预览失败');
    } finally {
      setIsGeneratingPreview(false);
    }
  }, [worldDescription, config.maxPlayers]);

  return (
    <div className="multiplayer-config">
      <div className="config-section">
        <h4>多玩家设置</h4>

        <Form.Item label="启用多玩家模式">
          <Switch
            checked={config.enabled}
            onChange={(enabled) => onChange({ ...config, enabled })}
          />
        </Form.Item>

        {config.enabled && (
          <>
            <Form.Item label="最大玩家数">
              <Slider
                min={2}
                max={20}
                value={config.maxPlayers}
                onChange={(maxPlayers) => onChange({ ...config, maxPlayers })}
                marks={{
                  2: '2人',
                  5: '5人',
                  10: '10人',
                  20: '20人'
                }}
              />
            </Form.Item>

            <Form.Item label="玩家分配策略">
              <Radio.Group
                value={config.allocationStrategy}
                onChange={(e) => onChange({ ...config, allocationStrategy: e.target.value })}
              >
                <Radio value="scattered">分散初始化</Radio>
                <Radio value="clustered">集中起点</Radio>
                <Radio value="custom">自定义</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item>
              <Button
                type="dashed"
                icon={<EyeOutlined />}
                loading={isGeneratingPreview}
                onClick={generateFrameworkPreview}
                disabled={!worldDescription.trim()}
              >
                预览世界框架
              </Button>
            </Form.Item>

            {frameworkPreview && (
              <div className="framework-preview">
                <h5>世界框架预览</h5>
                <div className="regions-preview">
                  {frameworkPreview.regions.map((region, index) => (
                    <Tag key={index} color="blue">
                      {region.name} ({region.playerSlots}人)
                    </Tag>
                  ))}
                </div>
                <div className="landmarks-preview">
                  <strong>汇聚点:</strong>
                  {frameworkPreview.convergencePoints.map((point, index) => (
                    <Tag key={index} color="gold">{point}</Tag>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
```

## 7. 状态管理

### 6.1. 全局状态结构
```typescript
interface AppState {
  auth: AuthState;
  game: GameState;
  ui: UIState;
  settings: SettingsState;
}

interface GameState {
  currentWorld: World | null;
  character: Character | null;
  scene: Scene | null;
  narrative: NarrativeEntry[];
  pendingActions: Action[];
}
```

### 5.2. 数据流设计
```
用户操作 → Action → Reducer → State → Component → UI更新
                     ↓
                 API调用 → 服务器响应 → State更新
```

## 6. 性能优化

### 6.1. 代码分割
- 路由级别的懒加载
- 组件级别的动态导入
- 第三方库的按需加载

### 6.2. 渲染优化
- React.memo优化组件重渲染
- useMemo和useCallback缓存计算结果
- 虚拟滚动处理长列表

### 6.3. 资源优化
- 图片懒加载和WebP格式支持
- CSS和JS文件压缩
- CDN加速静态资源

本前端设计确保了良好的用户体验、高性能和可维护性，为AI文本游戏提供了完整的交互界面。
