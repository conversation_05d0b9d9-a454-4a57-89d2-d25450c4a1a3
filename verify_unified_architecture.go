package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"strings"
)

// 验证统一架构的实现
func main() {
	fmt.Println("🔍 验证统一架构实现")
	
	// 1. 验证simple-server不再包含独立的AI业务逻辑
	fmt.Println("\n📋 检查1: 验证simple-server已移除独立AI逻辑")
	if checkSimpleServerCleanup() {
		fmt.Println("✅ simple-server已成功移除独立AI逻辑")
	} else {
		fmt.Println("❌ simple-server仍包含独立AI逻辑")
		return
	}
	
	// 2. 验证统一处理器存在
	fmt.Println("\n📋 检查2: 验证统一处理器存在")
	if checkUnifiedHandlerExists() {
		fmt.Println("✅ 统一处理器已正确创建")
	} else {
		fmt.Println("❌ 统一处理器不存在")
		return
	}
	
	// 3. 验证simple-server使用统一处理器
	fmt.Println("\n📋 检查3: 验证simple-server使用统一处理器")
	if checkSimpleServerUsesUnifiedHandler() {
		fmt.Println("✅ simple-server正确使用统一处理器")
	} else {
		fmt.Println("❌ simple-server未使用统一处理器")
		return
	}
	
	// 4. 验证配置驱动架构
	fmt.Println("\n📋 检查4: 验证配置驱动架构")
	if checkConfigDrivenArchitecture() {
		fmt.Println("✅ 配置驱动架构已正确实现")
	} else {
		fmt.Println("❌ 配置驱动架构实现有问题")
		return
	}
	
	fmt.Println("\n🎉 统一架构验证通过！")
	fmt.Println("📝 架构特点:")
	fmt.Println("   - 单一代码路径：开发和生产环境使用相同的业务逻辑")
	fmt.Println("   - 配置驱动：环境差异仅通过配置控制")
	fmt.Println("   - Mock下沉：Mock逻辑在AI服务层处理")
	fmt.Println("   - 统一响应：所有环境使用相同的响应格式")
}

// checkSimpleServerCleanup 检查simple-server是否已移除独立AI逻辑
func checkSimpleServerCleanup() bool {
	filePath := "cmd/simple-server/main.go"
	
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		fmt.Printf("❌ 文件不存在: %s\n", filePath)
		return false
	}
	
	// 解析文件
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, filePath, nil, parser.ParseComments)
	if err != nil {
		fmt.Printf("❌ 解析文件失败: %v\n", err)
		return false
	}
	
	// 检查是否还有独立的AI处理函数
	forbiddenFunctions := []string{
		"generateScene",
		"generateFallbackScene", 
		"getInteractionHistory",
		"getTokenUsageStats",
	}
	
	for _, decl := range node.Decls {
		if fn, ok := decl.(*ast.FuncDecl); ok {
			for _, forbidden := range forbiddenFunctions {
				if fn.Name.Name == forbidden {
					fmt.Printf("❌ 发现禁止的独立函数: %s\n", forbidden)
					return false
				}
			}
		}
	}
	
	fmt.Println("✅ 已移除所有独立AI处理函数")
	return true
}

// checkUnifiedHandlerExists 检查统一处理器是否存在
func checkUnifiedHandlerExists() bool {
	filePath := "internal/handlers/ai_unified.go"
	
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		fmt.Printf("❌ 统一处理器文件不存在: %s\n", filePath)
		return false
	}
	
	// 解析文件检查关键结构
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, filePath, nil, parser.ParseComments)
	if err != nil {
		fmt.Printf("❌ 解析统一处理器文件失败: %v\n", err)
		return false
	}
	
	hasUnifiedHandler := false
	hasUnifiedResponse := false
	requiredMethods := map[string]bool{
		"GenerateScene":          false,
		"GenerateCharacter":      false,
		"GetInteractionHistory":  false,
		"GetTokenUsageStats":     false,
	}
	
	for _, decl := range node.Decls {
		switch d := decl.(type) {
		case *ast.GenDecl:
			for _, spec := range d.Specs {
				if ts, ok := spec.(*ast.TypeSpec); ok {
					if ts.Name.Name == "UnifiedAIHandler" {
						hasUnifiedHandler = true
					}
					if ts.Name.Name == "UnifiedAPIResponse" {
						hasUnifiedResponse = true
					}
				}
			}
		case *ast.FuncDecl:
			if d.Recv != nil && len(d.Recv.List) > 0 {
				if methodName := d.Name.Name; methodName != "" {
					if _, exists := requiredMethods[methodName]; exists {
						requiredMethods[methodName] = true
					}
				}
			}
		}
	}
	
	if !hasUnifiedHandler {
		fmt.Println("❌ 缺少 UnifiedAIHandler 结构")
		return false
	}
	
	if !hasUnifiedResponse {
		fmt.Println("❌ 缺少 UnifiedAPIResponse 结构")
		return false
	}
	
	for method, found := range requiredMethods {
		if !found {
			fmt.Printf("❌ 缺少必需的方法: %s\n", method)
			return false
		}
	}
	
	fmt.Println("✅ 统一处理器结构完整")
	return true
}

// checkSimpleServerUsesUnifiedHandler 检查simple-server是否使用统一处理器
func checkSimpleServerUsesUnifiedHandler() bool {
	filePath := "cmd/simple-server/main.go"
	
	content, err := os.ReadFile(filePath)
	if err != nil {
		fmt.Printf("❌ 读取文件失败: %v\n", err)
		return false
	}
	
	fileContent := string(content)
	
	// 检查是否导入了统一处理器
	if !strings.Contains(fileContent, "ai-text-game-iam-npc/internal/handlers") {
		fmt.Println("❌ 未导入统一处理器包")
		return false
	}
	
	// 检查是否使用了统一处理器
	if !strings.Contains(fileContent, "unifiedAIHandler") {
		fmt.Println("❌ 未使用统一处理器变量")
		return false
	}
	
	// 检查路由是否使用统一处理器的方法
	requiredRoutes := []string{
		"unifiedAIHandler.GenerateScene",
		"unifiedAIHandler.GenerateCharacter",
		"unifiedAIHandler.GetInteractionHistory",
		"unifiedAIHandler.GetTokenUsageStats",
	}
	
	for _, route := range requiredRoutes {
		if !strings.Contains(fileContent, route) {
			fmt.Printf("❌ 路由未使用统一处理器方法: %s\n", route)
			return false
		}
	}
	
	fmt.Println("✅ simple-server正确使用统一处理器")
	return true
}

// checkConfigDrivenArchitecture 检查配置驱动架构
func checkConfigDrivenArchitecture() bool {
	filePath := "cmd/simple-server/main.go"
	
	content, err := os.ReadFile(filePath)
	if err != nil {
		fmt.Printf("❌ 读取文件失败: %v\n", err)
		return false
	}
	
	fileContent := string(content)
	
	// 检查配置初始化
	if !strings.Contains(fileContent, "initSimpleConfig") {
		fmt.Println("❌ 缺少配置初始化函数")
		return false
	}
	
	// 检查是否有环境变量配置
	envVars := []string{
		"AI_BASE_URL",
		"AI_TOKEN", 
		"WINDMILL_WORKSPACE",
	}
	
	for _, envVar := range envVars {
		if !strings.Contains(fileContent, envVar) {
			fmt.Printf("❌ 缺少环境变量配置: %s\n", envVar)
			return false
		}
	}
	
	// 检查Mock配置逻辑
	if !strings.Contains(fileContent, "MockEnabled") {
		fmt.Println("❌ 缺少Mock配置逻辑")
		return false
	}
	
	fmt.Println("✅ 配置驱动架构实现正确")
	return true
}
