<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话接口测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .stream-button {
            background-color: #e74c3c;
        }
        .stream-button:hover {
            background-color: #c0392b;
        }
        .response {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .stream {
            background-color: #e2f3ff;
            border: 1px solid #b8daff;
            color: #004085;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .status.connecting {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.connected {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.disconnected {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI对话接口测试工具</h1>
        
        <!-- 配置区域 -->
        <div class="test-section">
            <h3>📋 配置参数</h3>
            <div class="input-group">
                <label for="apiUrl">API地址:</label>
                <input type="text" id="apiUrl" value="http://localhost:8080/api/chat" placeholder="输入API地址">
            </div>
            <div class="input-group">
                <label for="model">AI模型:</label>
                <select id="model">
                    <option value="deepseek">DeepSeek</option>
                    <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                    <option value="gpt-4">GPT-4</option>
                </select>
            </div>
            <div class="input-group">
                <label for="conversationId">对话ID (可选):</label>
                <input type="text" id="conversationId" placeholder="留空将创建新对话">
            </div>
            <div class="checkbox-group">
                <input type="checkbox" id="streamMode">
                <label for="streamMode">启用流式响应 (SSE)</label>
            </div>
        </div>

        <!-- 消息输入区域 -->
        <div class="test-section">
            <h3>💬 发送消息</h3>
            <div class="input-group">
                <label for="userMessage">用户消息:</label>
                <textarea id="userMessage" placeholder="输入您的消息...">你好，我是一个测试用户。请介绍一下你自己，并告诉我你能做什么。</textarea>
            </div>
            <button onclick="sendMessage()">📤 发送普通消息</button>
            <button onclick="sendStreamMessage()" class="stream-button">🌊 发送流式消息</button>
            <button onclick="clearResponse()">🗑️ 清空响应</button>
        </div>

        <!-- 预设测试用例 -->
        <div class="test-section">
            <h3>🧪 预设测试用例</h3>
            <button onclick="testBasicChat()">基础对话测试</button>
            <button onclick="testJSONStructure()">JSON结构测试</button>
            <button onclick="testStreamResponse()">流式响应测试</button>
            <button onclick="testConversationContext()">上下文测试</button>
        </div>

        <!-- 连接状态 -->
        <div id="connectionStatus" class="status disconnected" style="display: none;">
            连接状态: 未连接
        </div>

        <!-- 响应显示区域 -->
        <div class="test-section">
            <h3>📨 响应结果</h3>
            <div id="response" class="response">等待响应...</div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let currentConversationId = null;

        // 发送普通消息
        async function sendMessage() {
            const apiUrl = document.getElementById('apiUrl').value;
            const model = document.getElementById('model').value;
            const conversationId = document.getElementById('conversationId').value;
            const userMessage = document.getElementById('userMessage').value;

            if (!userMessage.trim()) {
                showResponse('请输入消息内容', 'error');
                return;
            }

            const requestData = {
                messages: [
                    {
                        role: "user",
                        content: userMessage
                    }
                ],
                model: model,
                stream: false
            };

            if (conversationId) {
                requestData.conversation_id = conversationId;
            }

            try {
                showResponse('正在发送请求...', 'stream');
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                
                if (response.ok) {
                    currentConversationId = data.conversation_id;
                    document.getElementById('conversationId').value = currentConversationId;
                    showResponse(JSON.stringify(data, null, 2), 'success');
                } else {
                    showResponse(`错误: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResponse(`网络错误: ${error.message}`, 'error');
            }
        }

        // 发送流式消息
        function sendStreamMessage() {
            const apiUrl = document.getElementById('apiUrl').value;
            const model = document.getElementById('model').value;
            const conversationId = document.getElementById('conversationId').value;
            const userMessage = document.getElementById('userMessage').value;

            if (!userMessage.trim()) {
                showResponse('请输入消息内容', 'error');
                return;
            }

            // 关闭之前的连接
            if (eventSource) {
                eventSource.close();
            }

            const requestData = {
                messages: [
                    {
                        role: "user",
                        content: userMessage
                    }
                ],
                model: model,
                stream: true
            };

            if (conversationId) {
                requestData.conversation_id = conversationId;
            }

            try {
                showConnectionStatus('connecting');
                showResponse('正在建立流式连接...', 'stream');

                // 使用fetch发送POST请求，然后处理流式响应
                fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream',
                    },
                    body: JSON.stringify(requestData)
                }).then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    showConnectionStatus('connected');
                    showResponse('流式连接已建立，等待数据...\n', 'stream');

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';

                    function readStream() {
                        reader.read().then(({ done, value }) => {
                            if (done) {
                                showConnectionStatus('disconnected');
                                appendResponse('\n\n✅ 流式响应完成');
                                return;
                            }

                            buffer += decoder.decode(value, { stream: true });
                            const lines = buffer.split('\n');
                            buffer = lines.pop(); // 保留不完整的行

                            for (const line of lines) {
                                if (line.startsWith('data: ')) {
                                    const data = line.slice(6);
                                    if (data === '[DONE]') {
                                        showConnectionStatus('disconnected');
                                        appendResponse('\n\n✅ 流式响应完成');
                                        return;
                                    }
                                    
                                    try {
                                        const jsonData = JSON.parse(data);
                                        appendResponse(`\n📦 数据块: ${JSON.stringify(jsonData, null, 2)}\n`);
                                        
                                        // 提取对话ID
                                        if (jsonData.conversation_id && !currentConversationId) {
                                            currentConversationId = jsonData.conversation_id;
                                            document.getElementById('conversationId').value = currentConversationId;
                                        }
                                    } catch (e) {
                                        appendResponse(`\n⚠️ 解析JSON失败: ${data}\n`);
                                    }
                                } else if (line.startsWith('event: ')) {
                                    const eventType = line.slice(7);
                                    appendResponse(`\n🎯 事件: ${eventType}\n`);
                                }
                            }

                            readStream();
                        }).catch(error => {
                            showConnectionStatus('disconnected');
                            appendResponse(`\n❌ 流式读取错误: ${error.message}`);
                        });
                    }

                    readStream();
                }).catch(error => {
                    showConnectionStatus('disconnected');
                    showResponse(`流式连接错误: ${error.message}`, 'error');
                });

            } catch (error) {
                showConnectionStatus('disconnected');
                showResponse(`流式连接失败: ${error.message}`, 'error');
            }
        }

        // 显示响应
        function showResponse(text, type = 'success') {
            const responseDiv = document.getElementById('response');
            responseDiv.textContent = text;
            responseDiv.className = `response ${type}`;
        }

        // 追加响应内容
        function appendResponse(text) {
            const responseDiv = document.getElementById('response');
            responseDiv.textContent += text;
        }

        // 清空响应
        function clearResponse() {
            showResponse('等待响应...', 'success');
        }

        // 显示连接状态
        function showConnectionStatus(status) {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = `status ${status}`;
            
            switch (status) {
                case 'connecting':
                    statusDiv.textContent = '连接状态: 正在连接...';
                    break;
                case 'connected':
                    statusDiv.textContent = '连接状态: 已连接';
                    break;
                case 'disconnected':
                    statusDiv.textContent = '连接状态: 已断开';
                    break;
            }
        }

        // 预设测试用例
        function testBasicChat() {
            document.getElementById('userMessage').value = '你好，请介绍一下你自己。';
            document.getElementById('streamMode').checked = false;
            sendMessage();
        }

        function testJSONStructure() {
            document.getElementById('userMessage').value = '请返回一个包含content、emotion、intent和confidence字段的JSON响应。';
            document.getElementById('streamMode').checked = false;
            sendMessage();
        }

        function testStreamResponse() {
            document.getElementById('userMessage').value = '请用流式方式回答：什么是人工智能？请详细解释。';
            document.getElementById('streamMode').checked = true;
            sendStreamMessage();
        }

        function testConversationContext() {
            document.getElementById('userMessage').value = '我刚才问了什么问题？请回顾我们的对话历史。';
            document.getElementById('streamMode').checked = false;
            sendMessage();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI对话接口测试工具已加载');
            
            // 监听流式模式复选框变化
            document.getElementById('streamMode').addEventListener('change', function() {
                if (this.checked) {
                    showResponse('已启用流式模式，点击"发送流式消息"按钮进行测试', 'stream');
                }
            });
        });
    </script>
</body>
</html>
