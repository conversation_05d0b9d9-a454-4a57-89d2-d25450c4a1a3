package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// 测试结构体
type TestResult struct {
	Name    string
	Success bool
	Message string
	Error   string
}

// API响应结构
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	Error   string      `json:"error,omitempty"`
}

// 世界创建请求
type CreateWorldRequest struct {
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	Theme         string                 `json:"theme"`
	IsPublic      bool                   `json:"is_public"`
	MaxPlayers    int                    `json:"max_players"`
	WorldSettings map[string]interface{} `json:"world_settings"`
}

// AI生成场景请求
type GenerateSceneRequest struct {
	WorldID              string   `json:"world_id"`
	SceneName            string   `json:"scene_name,omitempty"`
	SceneType            string   `json:"scene_type,omitempty"`
	Theme                string   `json:"theme,omitempty"`
	Mood                 string   `json:"mood,omitempty"`
	ConnectedScenes      []string `json:"connected_scenes,omitempty"`
	SpecialRequirements  string   `json:"special_requirements,omitempty"`
}

func main() {
	fmt.Println("🧪 开始集成测试 - 验证修复效果")
	fmt.Println(strings.Repeat("=", 50))

	var results []TestResult

	// 测试1: AI生成内容格式验证修复
	fmt.Println("\n🔧 测试1: AI生成内容格式验证修复")
	result1 := testAIGenerationFormat()
	results = append(results, result1)
	printTestResult(result1)

	// 测试2: 世界创建流程修复
	fmt.Println("\n🔧 测试2: 世界创建流程修复")
	result2 := testWorldCreationFlow()
	results = append(results, result2)
	printTestResult(result2)

	// 测试3: 世界查询功能
	fmt.Println("\n🔧 测试3: 世界查询功能")
	result3 := testWorldQuery()
	results = append(results, result3)
	printTestResult(result3)

	// 测试4: 数据库完整性
	fmt.Println("\n🔧 测试4: 数据库完整性")
	result4 := testDatabaseIntegrity()
	results = append(results, result4)
	printTestResult(result4)

	// 汇总结果
	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("📊 测试结果汇总:")
	
	successCount := 0
	for i, result := range results {
		status := "❌"
		if result.Success {
			status = "✅"
			successCount++
		}
		fmt.Printf("%s 测试%d: %s\n", status, i+1, result.Name)
		if !result.Success && result.Error != "" {
			fmt.Printf("   错误: %s\n", result.Error)
		}
	}

	fmt.Printf("\n🎯 总体结果: %d/%d 测试通过\n", successCount, len(results))
	
	if successCount == len(results) {
		fmt.Println("🎉 所有测试通过！修复成功！")
	} else {
		fmt.Println("⚠️ 部分测试失败，需要进一步修复")
	}
}

func testAIGenerationFormat() TestResult {
	req := GenerateSceneRequest{
		WorldID:             "temp",
		SceneName:           "格式测试世界",
		SceneType:           "main",
		Theme:               "fantasy",
		Mood:                "mysterious",
		SpecialRequirements: "测试AI生成内容的格式验证",
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post("http://localhost:8080/api/v1/ai/generate/scene", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return TestResult{
			Name:    "AI生成内容格式验证",
			Success: false,
			Error:   fmt.Sprintf("请求失败: %v", err),
		}
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return TestResult{
			Name:    "AI生成内容格式验证",
			Success: false,
			Error:   fmt.Sprintf("解析响应失败: %v", err),
		}
	}

	if !apiResp.Success {
		return TestResult{
			Name:    "AI生成内容格式验证",
			Success: false,
			Error:   fmt.Sprintf("API调用失败: %s", apiResp.Message),
		}
	}

	// 检查响应格式
	data, ok := apiResp.Data.(map[string]interface{})
	if !ok {
		return TestResult{
			Name:    "AI生成内容格式验证",
			Success: false,
			Error:   "响应数据格式错误",
		}
	}

	// 检查必要字段
	hasContent := data["content"] != nil
	hasStructuredData := data["structured_data"] != nil

	if !hasContent && !hasStructuredData {
		return TestResult{
			Name:    "AI生成内容格式验证",
			Success: false,
			Error:   "响应缺少content或structured_data字段",
		}
	}

	return TestResult{
		Name:    "AI生成内容格式验证",
		Success: true,
		Message: "AI生成内容格式正确，包含必要字段",
	}
}

func testWorldCreationFlow() TestResult {
	req := CreateWorldRequest{
		Name:        "修复测试世界",
		Description: "用于验证世界创建修复的测试世界",
		Theme:       "fantasy",
		IsPublic:    true,
		MaxPlayers:  10,
		WorldSettings: map[string]interface{}{
			"difficulty": "normal",
		},
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post("http://localhost:8080/api/v1/game/worlds", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return TestResult{
			Name:    "世界创建流程",
			Success: false,
			Error:   fmt.Sprintf("请求失败: %v", err),
		}
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return TestResult{
			Name:    "世界创建流程",
			Success: false,
			Error:   fmt.Sprintf("解析响应失败: %v", err),
		}
	}

	if !apiResp.Success {
		return TestResult{
			Name:    "世界创建流程",
			Success: false,
			Error:   fmt.Sprintf("世界创建失败: %s", apiResp.Message),
		}
	}

	// 检查返回的世界数据
	data, ok := apiResp.Data.(map[string]interface{})
	if !ok || data["id"] == nil {
		return TestResult{
			Name:    "世界创建流程",
			Success: false,
			Error:   "世界创建成功但返回数据格式错误",
		}
	}

	return TestResult{
		Name:    "世界创建流程",
		Success: true,
		Message: fmt.Sprintf("世界创建成功，ID: %v", data["id"]),
	}
}

func testWorldQuery() TestResult {
	// 先创建一个世界
	createReq := CreateWorldRequest{
		Name:        "查询测试世界",
		Description: "用于测试世界查询功能",
		Theme:       "fantasy",
		IsPublic:    true,
		MaxPlayers:  5,
	}

	jsonData, _ := json.Marshal(createReq)
	createResp, err := http.Post("http://localhost:8080/api/v1/game/worlds", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return TestResult{
			Name:    "世界查询功能",
			Success: false,
			Error:   fmt.Sprintf("创建测试世界失败: %v", err),
		}
	}
	defer createResp.Body.Close()

	createBody, _ := io.ReadAll(createResp.Body)
	var createApiResp APIResponse
	if err := json.Unmarshal(createBody, &createApiResp); err != nil || !createApiResp.Success {
		return TestResult{
			Name:    "世界查询功能",
			Success: false,
			Error:   "创建测试世界失败",
		}
	}

	// 提取世界ID
	createData, _ := createApiResp.Data.(map[string]interface{})
	worldID := fmt.Sprintf("%v", createData["id"])

	// 等待一秒确保数据已保存
	time.Sleep(1 * time.Second)

	// 查询世界
	queryURL := fmt.Sprintf("http://localhost:8080/api/v1/game/worlds/%s", worldID)
	queryResp, err := http.Get(queryURL)
	if err != nil {
		return TestResult{
			Name:    "世界查询功能",
			Success: false,
			Error:   fmt.Sprintf("查询世界失败: %v", err),
		}
	}
	defer queryResp.Body.Close()

	queryBody, _ := io.ReadAll(queryResp.Body)
	var queryApiResp APIResponse
	if err := json.Unmarshal(queryBody, &queryApiResp); err != nil {
		return TestResult{
			Name:    "世界查询功能",
			Success: false,
			Error:   fmt.Sprintf("解析查询响应失败: %v", err),
		}
	}

	if !queryApiResp.Success {
		return TestResult{
			Name:    "世界查询功能",
			Success: false,
			Error:   fmt.Sprintf("查询世界失败: %s", queryApiResp.Message),
		}
	}

	return TestResult{
		Name:    "世界查询功能",
		Success: true,
		Message: fmt.Sprintf("世界查询成功，ID: %s", worldID),
	}
}

func testDatabaseIntegrity() TestResult {
	// 这里可以添加数据库完整性检查
	// 由于我们没有直接的数据库访问，我们通过API来验证

	// 测试获取公开世界列表（这个API存在）
	resp, err := http.Get("http://localhost:8080/api/v1/game/public-worlds")
	if err != nil {
		return TestResult{
			Name:    "数据库完整性",
			Success: false,
			Error:   fmt.Sprintf("获取公开世界列表失败: %v", err),
		}
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return TestResult{
			Name:    "数据库完整性",
			Success: false,
			Error:   fmt.Sprintf("解析响应失败: %v", err),
		}
	}

	if apiResp.Success {
		return TestResult{
			Name:    "数据库完整性",
			Success: true,
			Message: "数据库连接正常，API响应正常",
		}
	} else {
		return TestResult{
			Name:    "数据库完整性",
			Success: false,
			Error:   fmt.Sprintf("API调用失败: %s", apiResp.Message),
		}
	}
}

func printTestResult(result TestResult) {
	if result.Success {
		fmt.Printf("✅ %s: %s\n", result.Name, result.Message)
	} else {
		fmt.Printf("❌ %s: %s\n", result.Name, result.Error)
	}
}
