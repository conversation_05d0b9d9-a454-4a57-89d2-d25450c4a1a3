package service

import (
	"fmt"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"gorm.io/gorm"
)

// ConversationServiceImpl 对话服务实现
type ConversationServiceImpl struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewConversationService 创建对话服务实例
func NewConversationService(db *gorm.DB, logger logger.Logger) models.ConversationService {
	return &ConversationServiceImpl{
		db:     db,
		logger: logger,
	}
}

// CreateConversation 创建新对话
func (s *ConversationServiceImpl) CreateConversation(userID string, worldID *string, title string, model string) (*models.Conversation, error) {
	s.logger.Info("创建新对话", "user_id", userID, "world_id", worldID, "title", title, "model", model)

	// 如果没有提供标题，生成默认标题
	if title == "" {
		title = fmt.Sprintf("对话 - %s", time.Now().Format("2006-01-02 15:04"))
	}

	conversation := &models.Conversation{
		UserID:       userID,
		WorldID:      worldID,
		Title:        title,
		Model:        model,
		Status:       "active",
		MessageCount: 0,
		TokenUsage:   0,
		Metadata:     models.JSON("{}"),
	}

	if err := s.db.Create(conversation).Error; err != nil {
		s.logger.Error("创建对话失败", "error", err)
		return nil, fmt.Errorf("创建对话失败: %w", err)
	}

	s.logger.Info("对话创建成功", "conversation_id", conversation.ID)
	return conversation, nil
}

// GetConversation 获取对话详情
func (s *ConversationServiceImpl) GetConversation(conversationID string) (*models.Conversation, error) {
	s.logger.Debug("获取对话详情", "conversation_id", conversationID)

	var conversation models.Conversation
	if err := s.db.Preload("Messages").First(&conversation, "id = ?", conversationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("对话不存在: %s", conversationID)
		}
		s.logger.Error("获取对话失败", "error", err, "conversation_id", conversationID)
		return nil, fmt.Errorf("获取对话失败: %w", err)
	}

	return &conversation, nil
}

// AddMessage 添加消息到对话
func (s *ConversationServiceImpl) AddMessage(conversationID string, role string, content string, tokenUsage int, model string) (*models.Message, error) {
	s.logger.Debug("添加消息到对话", "conversation_id", conversationID, "role", role, "token_usage", tokenUsage)

	// 验证对话是否存在
	var conversation models.Conversation
	if err := s.db.First(&conversation, "id = ?", conversationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("对话不存在: %s", conversationID)
		}
		return nil, fmt.Errorf("查询对话失败: %w", err)
	}

	// 创建新消息
	message := &models.Message{
		ConversationID: conversationID,
		Role:           role,
		Content:        content,
		TokenUsage:     tokenUsage,
		Model:          model,
		Metadata:       models.JSON("{}"),
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 保存消息
	if err := tx.Create(message).Error; err != nil {
		tx.Rollback()
		s.logger.Error("保存消息失败", "error", err)
		return nil, fmt.Errorf("保存消息失败: %w", err)
	}

	// 更新对话统计
	if err := tx.Model(&conversation).Updates(map[string]interface{}{
		"message_count": gorm.Expr("message_count + 1"),
		"token_usage":   gorm.Expr("token_usage + ?", tokenUsage),
		"updated_at":    time.Now(),
	}).Error; err != nil {
		tx.Rollback()
		s.logger.Error("更新对话统计失败", "error", err)
		return nil, fmt.Errorf("更新对话统计失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		s.logger.Error("提交事务失败", "error", err)
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("消息添加成功", "message_id", message.ID, "conversation_id", conversationID)
	return message, nil
}

// GetConversationHistory 获取对话历史
func (s *ConversationServiceImpl) GetConversationHistory(conversationID string, limit int) ([]models.Message, error) {
	s.logger.Debug("获取对话历史", "conversation_id", conversationID, "limit", limit)

	var messages []models.Message
	query := s.db.Where("conversation_id = ?", conversationID).Order("created_at ASC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&messages).Error; err != nil {
		s.logger.Error("获取对话历史失败", "error", err, "conversation_id", conversationID)
		return nil, fmt.Errorf("获取对话历史失败: %w", err)
	}

	s.logger.Debug("对话历史获取成功", "conversation_id", conversationID, "message_count", len(messages))
	return messages, nil
}

// UpdateConversationStats 更新对话统计信息
func (s *ConversationServiceImpl) UpdateConversationStats(conversationID string, messageCount int, tokenUsage int) error {
	s.logger.Debug("更新对话统计", "conversation_id", conversationID, "message_count", messageCount, "token_usage", tokenUsage)

	result := s.db.Model(&models.Conversation{}).Where("id = ?", conversationID).Updates(map[string]interface{}{
		"message_count": messageCount,
		"token_usage":   tokenUsage,
		"updated_at":    time.Now(),
	})

	if result.Error != nil {
		s.logger.Error("更新对话统计失败", "error", result.Error, "conversation_id", conversationID)
		return fmt.Errorf("更新对话统计失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("对话不存在: %s", conversationID)
	}

	s.logger.Debug("对话统计更新成功", "conversation_id", conversationID)
	return nil
}

// ListUserConversations 获取用户的对话列表
func (s *ConversationServiceImpl) ListUserConversations(userID string, limit int, offset int) ([]models.Conversation, error) {
	s.logger.Debug("获取用户对话列表", "user_id", userID, "limit", limit, "offset", offset)

	var conversations []models.Conversation
	query := s.db.Where("user_id = ? AND status != ?", userID, "deleted").
		Order("updated_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&conversations).Error; err != nil {
		s.logger.Error("获取用户对话列表失败", "error", err, "user_id", userID)
		return nil, fmt.Errorf("获取用户对话列表失败: %w", err)
	}

	s.logger.Debug("用户对话列表获取成功", "user_id", userID, "conversation_count", len(conversations))
	return conversations, nil
}

// GetConversationWithMessages 获取对话及其消息（带分页）
func (s *ConversationServiceImpl) GetConversationWithMessages(conversationID string, messageLimit int, messageOffset int) (*models.Conversation, error) {
	s.logger.Debug("获取对话及消息", "conversation_id", conversationID, "message_limit", messageLimit, "message_offset", messageOffset)

	// 获取对话基本信息
	var conversation models.Conversation
	if err := s.db.First(&conversation, "id = ?", conversationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("对话不存在: %s", conversationID)
		}
		return nil, fmt.Errorf("获取对话失败: %w", err)
	}

	// 获取消息列表
	var messages []models.Message
	query := s.db.Where("conversation_id = ?", conversationID).Order("created_at ASC")
	
	if messageLimit > 0 {
		query = query.Limit(messageLimit)
	}
	if messageOffset > 0 {
		query = query.Offset(messageOffset)
	}

	if err := query.Find(&messages).Error; err != nil {
		return nil, fmt.Errorf("获取消息列表失败: %w", err)
	}

	conversation.Messages = messages
	return &conversation, nil
}

// DeleteConversation 删除对话（软删除）
func (s *ConversationServiceImpl) DeleteConversation(conversationID string) error {
	s.logger.Info("删除对话", "conversation_id", conversationID)

	result := s.db.Model(&models.Conversation{}).Where("id = ?", conversationID).Update("status", "deleted")
	if result.Error != nil {
		s.logger.Error("删除对话失败", "error", result.Error, "conversation_id", conversationID)
		return fmt.Errorf("删除对话失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("对话不存在: %s", conversationID)
	}

	s.logger.Info("对话删除成功", "conversation_id", conversationID)
	return nil
}
