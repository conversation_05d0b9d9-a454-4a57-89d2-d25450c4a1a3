package service

import (
	"context"
	"fmt"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/repository"
	"ai-text-game-iam-npc/pkg/database"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Logger 日志接口
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
}

// EnhancedUserService 增强的用户服务
type EnhancedUserService struct {
	repo          *repository.EnhancedUserRepository
	compatibility *database.EnhancedCompatibility
	logger        Logger
}

// NewEnhancedUserService 创建增强的用户服务
func NewEnhancedUserService(repo *repository.EnhancedUserRepository, logger Logger) *EnhancedUserService {
	return &EnhancedUserService{
		repo:          repo,
		compatibility: repo.GetCompatibility(),
		logger:        logger,
	}
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	ExternalID       string                 `json:"external_id" binding:"required"`
	ExternalProvider string                 `json:"external_provider" binding:"required"`
	Email            string                 `json:"email" binding:"required,email"`
	DisplayName      string                 `json:"display_name"`
	AvatarURL        string                 `json:"avatar_url"`
	Preferences      map[string]interface{} `json:"preferences"`
}

// CreateUser 创建用户（AI友好）
func (s *EnhancedUserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*models.User, error) {
	// 检查用户是否已存在
	existingUser, err := s.repo.FindByExternalID(ctx, req.ExternalID, req.ExternalProvider)
	if err == nil && existingUser != nil {
		s.logger.Info("用户已存在", "external_id", req.ExternalID, "provider", req.ExternalProvider)
		return existingUser, nil
	}
	
	// 设置默认偏好
	defaultPreferences := map[string]interface{}{
		"ui": map[string]interface{}{
			"theme":      "auto",
			"language":   "zh-CN",
			"font_size":  "medium",
			"animations": true,
		},
		"game": map[string]interface{}{
			"ai_speed":     "balanced",
			"auto_save":    true,
			"notifications": true,
			"sound_effects": true,
		},
		"privacy": map[string]interface{}{
			"profile_visible": true,
			"allow_invites":   true,
		},
	}
	
	// 合并用户提供的偏好
	if req.Preferences != nil {
		for key, value := range req.Preferences {
			defaultPreferences[key] = value
		}
	}
	
	user := &models.User{
		ID:               uuid.New().String(),
		ExternalID:       req.ExternalID,
		ExternalProvider: req.ExternalProvider,
		Email:            req.Email,
		DisplayName:      &req.DisplayName,
		AvatarURL:        &req.AvatarURL,
		GameRoles:        models.StringArray{"user"},
		Status:           "active",
		Preferences:      models.JSON(defaultPreferences),
	}
	
	if err := s.repo.Create(ctx, user); err != nil {
		s.logger.Error("创建用户失败", "error", err, "external_id", req.ExternalID)
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}
	
	s.logger.Info("用户创建成功", "user_id", user.ID, "external_id", req.ExternalID)
	return user, nil
}

// UpdateUserPreferences 更新用户偏好
func (s *EnhancedUserService) UpdateUserPreferences(ctx context.Context, userID string, updates map[string]interface{}) error {
	// 验证更新数据
	validationHelper := s.compatibility.NewValidationHelper()
	sanitizedUpdates := validationHelper.SanitizeJSONData(updates)
	
	// 逐个更新字段
	for field, value := range sanitizedUpdates {
		if err := s.repo.UpdatePreferenceField(ctx, userID, field, value); err != nil {
			s.logger.Error("更新用户偏好失败", "error", err, "user_id", userID, "field", field)
			return fmt.Errorf("更新用户偏好失败: %w", err)
		}
	}
	
	s.logger.Info("用户偏好更新成功", "user_id", userID, "updated_fields", len(sanitizedUpdates))
	return nil
}

// SearchUsersByPreferences AI友好的用户搜索
func (s *EnhancedUserService) SearchUsersByPreferences(ctx context.Context, searchCriteria map[string]interface{}) ([]*models.User, error) {
	users, err := s.repo.SearchUsersByPreferences(ctx, searchCriteria)
	if err != nil {
		s.logger.Error("搜索用户失败", "error", err, "criteria", searchCriteria)
		return nil, fmt.Errorf("搜索用户失败: %w", err)
	}
	
	s.logger.Info("用户搜索完成", "found_count", len(users), "criteria", searchCriteria)
	return users, nil
}

// GetUserByID 根据ID获取用户
func (s *EnhancedUserService) GetUserByID(ctx context.Context, userID string) (*models.User, error) {
	user, err := s.repo.FindByID(ctx, userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在")
		}
		s.logger.Error("获取用户失败", "error", err, "user_id", userID)
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}
	
	return user, nil
}

// EnhancedWorldService 增强的世界服务
type EnhancedWorldService struct {
	repo          *repository.EnhancedWorldRepository
	compatibility *database.EnhancedCompatibility
	logger        Logger
}

// NewEnhancedWorldService 创建增强的世界服务
func NewEnhancedWorldService(repo *repository.EnhancedWorldRepository, logger Logger) *EnhancedWorldService {
	return &EnhancedWorldService{
		repo:          repo,
		compatibility: repo.GetCompatibility(),
		logger:        logger,
	}
}

// CreateWorldRequest 创建世界请求
type CreateWorldRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	CreatorID   string                 `json:"creator_id" binding:"required"`
	WorldConfig map[string]interface{} `json:"world_config"`
	Tags        []string               `json:"tags"`
}

// CreateWorld 创建世界（AI友好）
func (s *EnhancedWorldService) CreateWorld(ctx context.Context, req *CreateWorldRequest) (*models.World, error) {
	// 设置默认世界配置
	defaultConfig := map[string]interface{}{
		"theme": map[string]interface{}{
			"genre":      "fantasy",
			"mood":       "neutral",
			"complexity": "medium",
		},
		"rules": map[string]interface{}{
			"narrative_style": "descriptive",
			"content_rating":  "teen",
			"ai_creativity":   "balanced",
		},
		"mechanics": map[string]interface{}{
			"time_rate":           1.0,
			"tick_interval":       30,
			"max_memory_per_char": 100,
		},
		"language": "zh-CN",
	}
	
	// 合并用户提供的配置
	if req.WorldConfig != nil {
		for key, value := range req.WorldConfig {
			defaultConfig[key] = value
		}
	}
	
	// 设置默认世界状态
	defaultState := map[string]interface{}{
		"current_tick":     0,
		"last_tick_at":     time.Now(),
		"active_events":    []string{},
		"global_variables": map[string]interface{}{},
		"weather": map[string]interface{}{
			"type":        "clear",
			"temperature": 20,
		},
		"season":      "spring",
		"world_goals": []string{},
	}
	
	world := &models.World{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: &req.Description,
		CreatorID:   req.CreatorID,
		WorldConfig: models.JSON(defaultConfig),
		WorldState:  models.JSON(defaultState),
		Status:      "active",
		Tags:        models.StringArray(req.Tags),
		GameTime:    0,
	}
	
	if err := s.repo.Create(ctx, world); err != nil {
		s.logger.Error("创建世界失败", "error", err, "name", req.Name, "creator_id", req.CreatorID)
		return nil, fmt.Errorf("创建世界失败: %w", err)
	}
	
	s.logger.Info("世界创建成功", "world_id", world.ID, "name", req.Name, "creator_id", req.CreatorID)
	return world, nil
}

// UpdateWorldConfig 更新世界配置
func (s *EnhancedWorldService) UpdateWorldConfig(ctx context.Context, worldID string, configUpdates map[string]interface{}) error {
	// 验证更新数据
	validationHelper := s.compatibility.NewValidationHelper()
	sanitizedUpdates := validationHelper.SanitizeJSONData(configUpdates)
	
	if err := s.repo.UpdateWorldConfig(ctx, worldID, sanitizedUpdates); err != nil {
		s.logger.Error("更新世界配置失败", "error", err, "world_id", worldID)
		return fmt.Errorf("更新世界配置失败: %w", err)
	}
	
	s.logger.Info("世界配置更新成功", "world_id", worldID, "updated_fields", len(sanitizedUpdates))
	return nil
}

// FindWorldsByGenre 根据类型查找世界
func (s *EnhancedWorldService) FindWorldsByGenre(ctx context.Context, genre string) ([]*models.World, error) {
	worlds, err := s.repo.FindByThemeGenre(ctx, genre)
	if err != nil {
		s.logger.Error("根据类型查找世界失败", "error", err, "genre", genre)
		return nil, fmt.Errorf("根据类型查找世界失败: %w", err)
	}
	
	s.logger.Info("根据类型查找世界完成", "genre", genre, "found_count", len(worlds))
	return worlds, nil
}

// GetWorldByID 根据ID获取世界
func (s *EnhancedWorldService) GetWorldByID(ctx context.Context, worldID string) (*models.World, error) {
	world, err := s.repo.FindByID(ctx, worldID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("世界不存在")
		}
		s.logger.Error("获取世界失败", "error", err, "world_id", worldID)
		return nil, fmt.Errorf("获取世界失败: %w", err)
	}
	
	return world, nil
}

// ServiceManager 服务管理器
type ServiceManager struct {
	db            *gorm.DB
	repoManager   *repository.RepositoryManager
	compatibility *database.EnhancedCompatibility
	logger        Logger
	
	User  *EnhancedUserService
	World *EnhancedWorldService
}

// NewServiceManager 创建服务管理器
func NewServiceManager(db *gorm.DB, logger Logger) *ServiceManager {
	repoManager := repository.NewRepositoryManager(db)
	
	return &ServiceManager{
		db:            db,
		repoManager:   repoManager,
		compatibility: database.NewEnhancedCompatibility(db),
		logger:        logger,
		User:          NewEnhancedUserService(repoManager.User, logger),
		World:         NewEnhancedWorldService(repoManager.World, logger),
	}
}

// Initialize 初始化服务管理器
func (sm *ServiceManager) Initialize(ctx context.Context) error {
	// 初始化索引
	if err := sm.repoManager.InitializeIndexes(ctx); err != nil {
		sm.logger.Error("初始化索引失败", "error", err)
		return fmt.Errorf("初始化索引失败: %w", err)
	}
	
	sm.logger.Info("服务管理器初始化成功", "database_type", sm.compatibility.GetDatabaseType())
	return nil
}

// GetDatabaseInfo 获取数据库信息
func (sm *ServiceManager) GetDatabaseInfo() map[string]interface{} {
	return sm.repoManager.GetDatabaseInfo()
}

// HealthCheck 健康检查
func (sm *ServiceManager) HealthCheck(ctx context.Context) error {
	// 测试数据库连接
	sqlDB, err := sm.db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}
	
	if err := sqlDB.PingContext(ctx); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}
	
	return nil
}
