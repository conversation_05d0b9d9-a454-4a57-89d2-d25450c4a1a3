package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/database"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseRepository 基础仓库，提供通用的数据库操作
type BaseRepository struct {
	db            *gorm.DB
	compatibility *database.EnhancedCompatibility
}

// NewBaseRepository 创建基础仓库
func NewBaseRepository(db *gorm.DB) *BaseRepository {
	return &BaseRepository{
		db:            db,
		compatibility: database.NewEnhancedCompatibility(db),
	}
}

// GetDB 获取数据库连接
func (br *BaseRepository) GetDB() *gorm.DB {
	return br.db
}

// GetCompatibility 获取兼容性管理器
func (br *BaseRepository) GetCompatibility() *database.EnhancedCompatibility {
	return br.compatibility
}

// EnhancedUserRepository 增强的用户仓库
type EnhancedUserRepository struct {
	*BaseRepository
}

// NewEnhancedUserRepository 创建增强的用户仓库
func NewEnhancedUserRepository(db *gorm.DB) *EnhancedUserRepository {
	return &EnhancedUserRepository{
		BaseRepository: NewBaseRepository(db),
	}
}

// Create 创建用户
func (r *EnhancedUserRepository) Create(ctx context.Context, user *models.User) error {
	return r.db.WithContext(ctx).Create(user).Error
}

// FindByID 根据ID查找用户
func (r *EnhancedUserRepository) FindByID(ctx context.Context, id string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindByExternalID 根据外部ID查找用户
func (r *EnhancedUserRepository) FindByExternalID(ctx context.Context, externalID, provider string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Where("external_id = ? AND external_provider = ?", externalID, provider).
		First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindByPreferenceField 根据偏好字段查找用户
func (r *EnhancedUserRepository) FindByPreferenceField(ctx context.Context, field string, value interface{}) ([]*models.User, error) {
	var users []*models.User
	
	queryBuilder := r.compatibility.NewJSONQueryBuilder("users", "preferences")
	condition, args := queryBuilder.FieldEquals(field, value)
	
	err := r.db.WithContext(ctx).
		Where(condition, args...).
		Find(&users).Error
	
	return users, err
}

// UpdatePreferenceField 更新偏好字段
func (r *EnhancedUserRepository) UpdatePreferenceField(ctx context.Context, userID string, field string, value interface{}) error {
	if r.compatibility.IsPostgreSQL() {
		// PostgreSQL使用jsonb_set函数
		valueJSON, _ := json.Marshal(value)
		return r.db.WithContext(ctx).
			Model(&models.User{}).
			Where("id = ?", userID).
			Update("preferences", gorm.Expr("jsonb_set(preferences, ?, ?)", 
				fmt.Sprintf("{%s}", field), string(valueJSON))).Error
	} else {
		// SQLite需要读取-修改-写入
		var user models.User
		if err := r.db.WithContext(ctx).First(&user, "id = ?", userID).Error; err != nil {
			return err
		}
		
		if user.Preferences == nil {
			user.Preferences = make(models.JSON)
		}
		user.Preferences[field] = value
		
		return r.db.WithContext(ctx).Save(&user).Error
	}
}

// SearchUsersByPreferences 根据偏好搜索用户
func (r *EnhancedUserRepository) SearchUsersByPreferences(ctx context.Context, searchCriteria map[string]interface{}) ([]*models.User, error) {
	var users []*models.User
	
	queryBuilder := r.compatibility.NewJSONQueryBuilder("users", "preferences")
	condition, args := queryBuilder.ContainsObject(searchCriteria)
	
	err := r.db.WithContext(ctx).
		Where(condition, args...).
		Find(&users).Error
	
	return users, err
}

// EnhancedWorldRepository 增强的世界仓库
type EnhancedWorldRepository struct {
	*BaseRepository
}

// NewEnhancedWorldRepository 创建增强的世界仓库
func NewEnhancedWorldRepository(db *gorm.DB) *EnhancedWorldRepository {
	return &EnhancedWorldRepository{
		BaseRepository: NewBaseRepository(db),
	}
}

// Create 创建世界
func (r *EnhancedWorldRepository) Create(ctx context.Context, world *models.World) error {
	return r.db.WithContext(ctx).Create(world).Error
}

// FindByID 根据ID查找世界
func (r *EnhancedWorldRepository) FindByID(ctx context.Context, id string) (*models.World, error) {
	var world models.World
	err := r.db.WithContext(ctx).
		Preload("Creator").
		First(&world, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &world, nil
}

// FindByConfigField 根据配置字段查找世界
func (r *EnhancedWorldRepository) FindByConfigField(ctx context.Context, field string, value interface{}) ([]*models.World, error) {
	var worlds []*models.World
	
	queryBuilder := r.compatibility.NewJSONQueryBuilder("worlds", "world_config")
	condition, args := queryBuilder.FieldEquals(field, value)
	
	err := r.db.WithContext(ctx).
		Where(condition, args...).
		Find(&worlds).Error
	
	return worlds, err
}

// FindByThemeGenre 根据主题类型查找世界
func (r *EnhancedWorldRepository) FindByThemeGenre(ctx context.Context, genre string) ([]*models.World, error) {
	var worlds []*models.World
	
	queryBuilder := r.compatibility.NewJSONQueryBuilder("worlds", "world_config")
	condition, args := queryBuilder.NestedFieldEquals("theme.genre", genre)
	
	err := r.db.WithContext(ctx).
		Where(condition, args...).
		Find(&worlds).Error
	
	return worlds, err
}

// UpdateWorldConfig 更新世界配置
func (r *EnhancedWorldRepository) UpdateWorldConfig(ctx context.Context, worldID string, configUpdates map[string]interface{}) error {
	transactionManager := r.compatibility.NewTransactionManager()
	
	return transactionManager.WithTransaction(ctx, func(tx *gorm.DB) error {
		if r.compatibility.IsPostgreSQL() {
			// PostgreSQL批量更新
			for field, value := range configUpdates {
				valueJSON, _ := json.Marshal(value)
				query := "UPDATE worlds SET world_config = jsonb_set(world_config, ?, ?) WHERE id = ?"
				if err := tx.Exec(query, fmt.Sprintf("{%s}", field), string(valueJSON), worldID).Error; err != nil {
					return fmt.Errorf("更新配置字段 %s 失败: %w", field, err)
				}
			}
		} else {
			// SQLite读取-修改-写入
			var world models.World
			if err := tx.First(&world, "id = ?", worldID).Error; err != nil {
				return err
			}
			
			if world.WorldConfig == nil {
				world.WorldConfig = make(models.JSON)
			}
			
			for field, value := range configUpdates {
				world.WorldConfig[field] = value
			}
			
			return tx.Save(&world).Error
		}
		return nil
	})
}

// EnhancedSceneRepository 增强的场景仓库
type EnhancedSceneRepository struct {
	*BaseRepository
}

// NewEnhancedSceneRepository 创建增强的场景仓库
func NewEnhancedSceneRepository(db *gorm.DB) *EnhancedSceneRepository {
	return &EnhancedSceneRepository{
		BaseRepository: NewBaseRepository(db),
	}
}

// Create 创建场景
func (r *EnhancedSceneRepository) Create(ctx context.Context, scene *models.Scene) error {
	return r.db.WithContext(ctx).Create(scene).Error
}

// FindByID 根据ID查找场景
func (r *EnhancedSceneRepository) FindByID(ctx context.Context, id string) (*models.Scene, error) {
	var scene models.Scene
	err := r.db.WithContext(ctx).
		Preload("World").
		First(&scene, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &scene, nil
}

// FindByWorldID 根据世界ID查找场景
func (r *EnhancedSceneRepository) FindByWorldID(ctx context.Context, worldID string) ([]*models.Scene, error) {
	var scenes []*models.Scene
	err := r.db.WithContext(ctx).
		Where("world_id = ?", worldID).
		Find(&scenes).Error
	return scenes, err
}

// FindByPropertyField 根据属性字段查找场景
func (r *EnhancedSceneRepository) FindByPropertyField(ctx context.Context, field string, value interface{}) ([]*models.Scene, error) {
	var scenes []*models.Scene
	
	queryBuilder := r.compatibility.NewJSONQueryBuilder("scenes", "properties")
	condition, args := queryBuilder.FieldEquals(field, value)
	
	err := r.db.WithContext(ctx).
		Where(condition, args...).
		Find(&scenes).Error
	
	return scenes, err
}

// UpdateSceneProperties 更新场景属性
func (r *EnhancedSceneRepository) UpdateSceneProperties(ctx context.Context, sceneID string, properties map[string]interface{}) error {
	if r.compatibility.IsPostgreSQL() {
		// PostgreSQL使用jsonb_set
		for field, value := range properties {
			valueJSON, _ := json.Marshal(value)
			query := "UPDATE scenes SET properties = jsonb_set(properties, ?, ?) WHERE id = ?"
			if err := r.db.WithContext(ctx).Exec(query, fmt.Sprintf("{%s}", field), string(valueJSON), sceneID).Error; err != nil {
				return fmt.Errorf("更新属性字段 %s 失败: %w", field, err)
			}
		}
		return nil
	} else {
		// SQLite读取-修改-写入
		var scene models.Scene
		if err := r.db.WithContext(ctx).First(&scene, "id = ?", sceneID).Error; err != nil {
			return err
		}
		
		if scene.Properties == nil {
			scene.Properties = make(models.JSON)
		}
		
		for field, value := range properties {
			scene.Properties[field] = value
		}
		
		return r.db.WithContext(ctx).Save(&scene).Error
	}
}

// RepositoryManager 仓库管理器
type RepositoryManager struct {
	db            *gorm.DB
	compatibility *database.EnhancedCompatibility
	
	User  *EnhancedUserRepository
	World *EnhancedWorldRepository
	Scene *EnhancedSceneRepository
}

// NewRepositoryManager 创建仓库管理器
func NewRepositoryManager(db *gorm.DB) *RepositoryManager {
	return &RepositoryManager{
		db:            db,
		compatibility: database.NewEnhancedCompatibility(db),
		User:          NewEnhancedUserRepository(db),
		World:         NewEnhancedWorldRepository(db),
		Scene:         NewEnhancedSceneRepository(db),
	}
}

// InitializeIndexes 初始化索引
func (rm *RepositoryManager) InitializeIndexes(ctx context.Context) error {
	indexManager := rm.compatibility.NewIndexManager()
	
	// 用户表索引
	if err := indexManager.CreateJSONIndex("users", "preferences", []string{
		"ui.theme", "ui.language", "game.ai_speed", "game.language",
	}); err != nil {
		return fmt.Errorf("创建用户偏好索引失败: %w", err)
	}
	
	// 世界表索引
	if err := indexManager.CreateJSONIndex("worlds", "world_config", []string{
		"theme.genre", "theme.mood", "rules.content_rating", "language",
	}); err != nil {
		return fmt.Errorf("创建世界配置索引失败: %w", err)
	}
	
	// 场景表索引
	if err := indexManager.CreateJSONIndex("scenes", "properties", []string{
		"environment.weather", "environment.time", "accessibility.is_accessible",
	}); err != nil {
		return fmt.Errorf("创建场景属性索引失败: %w", err)
	}
	
	// 复合索引
	if err := indexManager.CreateCompositeIndex("users", []string{"status", "external_provider"}, ""); err != nil {
		return fmt.Errorf("创建用户复合索引失败: %w", err)
	}
	
	if err := indexManager.CreateCompositeIndex("worlds", []string{"creator_id", "status"}, ""); err != nil {
		return fmt.Errorf("创建世界复合索引失败: %w", err)
	}
	
	return nil
}

// GetDatabaseInfo 获取数据库信息
func (rm *RepositoryManager) GetDatabaseInfo() map[string]interface{} {
	return map[string]interface{}{
		"database_type":  string(rm.compatibility.GetDatabaseType()),
		"is_postgresql":  rm.compatibility.IsPostgreSQL(),
		"is_sqlite":      rm.compatibility.IsSQLite(),
		"supports_jsonb": rm.compatibility.SupportsJSONB(),
		"supports_uuid":  rm.compatibility.SupportsUUIDType(),
	}
}
