package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"ai-text-game-iam-npc/internal/ai"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/service"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ChatHandler 对话处理器
type ChatHandler struct {
	aiService           *ai.Service
	conversationService models.ConversationService
	sseHandler          *SSEHandler
	logger              logger.Logger
}

// NewChatHandler 创建对话处理器
func NewChatHandler(aiService *ai.Service, conversationService models.ConversationService, logger logger.Logger) *ChatHandler {
	return &ChatHandler{
		aiService:           aiService,
		conversationService: conversationService,
		sseHandler:          NewSSEHandler(logger),
		logger:              logger,
	}
}

// HandleChat 处理对话请求
// @Summary 处理AI对话请求
// @Description 处理用户的对话请求，支持流式和非流式响应
// @Tags 对话
// @Accept json
// @Produce json
// @Param request body ai.ChatRequest true "对话请求"
// @Success 200 {object} models.ConversationResponse "对话响应"
// @Failure 400 {object} gin.H "请求参数错误"
// @Failure 500 {object} gin.H "服务器内部错误"
// @Router /api/chat [post]
func (h *ChatHandler) HandleChat(c *gin.Context) {
	h.logger.Info("收到对话请求", "client_ip", c.ClientIP())

	// 解析请求参数
	var req ai.ChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析对话请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数格式错误: " + err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := h.validateChatRequest(&req); err != nil {
		h.logger.Error("对话请求参数验证失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数验证失败: " + err.Error(),
		})
		return
	}

	// 设置默认用户ID（在开发环境中）
	if req.UserID == "" {
		req.UserID = "test-user-123"
	}

	h.logger.Info("对话请求验证通过", 
		"user_id", req.UserID,
		"conversation_id", req.ConversationID,
		"model", req.Model,
		"stream", req.Stream,
		"message_count", len(req.Messages))

	// 根据是否流式响应选择处理方式
	if req.Stream {
		h.handleStreamChat(c, &req)
	} else {
		h.handleNormalChat(c, &req)
	}
}

// handleNormalChat 处理普通对话请求
func (h *ChatHandler) handleNormalChat(c *gin.Context, req *ai.ChatRequest) {
	h.logger.Info("处理普通对话请求", "user_id", req.UserID)

	// 获取或创建对话
	conversation, err := h.getOrCreateConversation(req)
	if err != nil {
		h.logger.Error("获取或创建对话失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "对话处理失败: " + err.Error(),
		})
		return
	}

	// 保存用户消息
	userMessage, err := h.saveUserMessage(conversation.ID, req.Messages)
	if err != nil {
		h.logger.Error("保存用户消息失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "保存消息失败: " + err.Error(),
		})
		return
	}

	// 调用AI生成响应
	chatResponse, err := h.aiService.GenerateChat(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("AI对话生成失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "AI生成失败: " + err.Error(),
		})
		return
	}

	// 保存AI响应消息
	aiMessage, err := h.conversationService.AddMessage(
		conversation.ID,
		"assistant",
		chatResponse.Content,
		chatResponse.TokenUsage,
		chatResponse.Model,
	)
	if err != nil {
		h.logger.Error("保存AI消息失败", "error", err)
		// 不返回错误，因为AI响应已经生成成功
	}

	// 构建响应
	response := &models.ConversationResponse{
		Success:        true,
		ConversationID: conversation.ID,
		MessageID:      aiMessage.ID,
		Content:        chatResponse.Content,
		Model:          chatResponse.Model,
		TokenUsage:     chatResponse.TokenUsage,
		FinishReason:   chatResponse.FinishReason,
	}

	h.logger.Info("普通对话处理成功", 
		"conversation_id", conversation.ID,
		"message_id", aiMessage.ID,
		"token_usage", chatResponse.TokenUsage)

	c.JSON(http.StatusOK, response)
}

// handleStreamChat 处理流式对话请求
func (h *ChatHandler) handleStreamChat(c *gin.Context, req *ai.ChatRequest) {
	h.logger.Info("处理流式对话请求", "user_id", req.UserID)

	// 获取或创建对话
	conversation, err := h.getOrCreateConversation(req)
	if err != nil {
		h.logger.Error("获取或创建对话失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "对话处理失败: " + err.Error(),
		})
		return
	}

	// 保存用户消息
	_, err = h.saveUserMessage(conversation.ID, req.Messages)
	if err != nil {
		h.logger.Error("保存用户消息失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "保存消息失败: " + err.Error(),
		})
		return
	}

	// 更新请求中的对话ID
	req.ConversationID = &conversation.ID

	// 使用SSE处理器处理流式响应
	h.sseHandler.HandleSSEConnection(c, func(writer *SSEWriter) error {
		return h.aiService.GenerateChatStream(c.Request.Context(), req, writer)
	})
}

// validateChatRequest 验证对话请求参数
func (h *ChatHandler) validateChatRequest(req *ai.ChatRequest) error {
	if len(req.Messages) == 0 {
		return fmt.Errorf("消息列表不能为空")
	}

	if req.Model == "" {
		return fmt.Errorf("模型名称不能为空")
	}

	// 验证消息格式
	for i, msg := range req.Messages {
		if msg.Role == "" {
			return fmt.Errorf("第%d条消息的角色不能为空", i+1)
		}
		if msg.Content == "" {
			return fmt.Errorf("第%d条消息的内容不能为空", i+1)
		}
		if msg.Role != "user" && msg.Role != "assistant" && msg.Role != "system" {
			return fmt.Errorf("第%d条消息的角色无效: %s", i+1, msg.Role)
		}
	}

	return nil
}

// getOrCreateConversation 获取或创建对话
func (h *ChatHandler) getOrCreateConversation(req *ai.ChatRequest) (*models.Conversation, error) {
	// 如果提供了对话ID，尝试获取现有对话
	if req.ConversationID != nil && *req.ConversationID != "" {
		conversation, err := h.conversationService.GetConversation(*req.ConversationID)
		if err == nil {
			h.logger.Debug("使用现有对话", "conversation_id", *req.ConversationID)
			return conversation, nil
		}
		h.logger.Warn("获取现有对话失败，将创建新对话", "conversation_id", *req.ConversationID, "error", err)
	}

	// 创建新对话
	title := h.generateConversationTitle(req.Messages)
	conversation, err := h.conversationService.CreateConversation(
		req.UserID,
		req.WorldID,
		title,
		req.Model,
	)
	if err != nil {
		return nil, fmt.Errorf("创建对话失败: %w", err)
	}

	h.logger.Info("创建新对话成功", "conversation_id", conversation.ID, "title", title)
	return conversation, nil
}

// generateConversationTitle 生成对话标题
func (h *ChatHandler) generateConversationTitle(messages []models.MessageRequest) string {
	if len(messages) > 0 && messages[0].Role == "user" {
		content := messages[0].Content
		if len(content) > 30 {
			return content[:30] + "..."
		}
		return content
	}
	return fmt.Sprintf("对话 - %s", time.Now().Format("01-02 15:04"))
}

// saveUserMessage 保存用户消息
func (h *ChatHandler) saveUserMessage(conversationID string, messages []models.MessageRequest) (*models.Message, error) {
	// 只保存最后一条用户消息（最新的）
	var lastUserMessage *models.MessageRequest
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == "user" {
			lastUserMessage = &messages[i]
			break
		}
	}

	if lastUserMessage == nil {
		return nil, fmt.Errorf("未找到用户消息")
	}

	return h.conversationService.AddMessage(
		conversationID,
		lastUserMessage.Role,
		lastUserMessage.Content,
		len(lastUserMessage.Content)/4, // 粗略估算token数量
		"user-input",
	)
}
