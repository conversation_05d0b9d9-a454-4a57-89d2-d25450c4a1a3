package handlers

import (
	"net/http"
	"strconv"

	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/validation"

	"github.com/gin-gonic/gin"
)

// ValidationHandler 校验处理器
type ValidationHandler struct {
	validationService *validation.ValidationService
}

// NewValidationHandler 创建校验处理器
func NewValidationHandler(validationService *validation.ValidationService) *ValidationHandler {
	return &ValidationHandler{
		validationService: validationService,
	}
}

// ValidateContent 校验内容
// @Summary 校验内容
// @Description 对用户输入的内容进行校验和过滤
// @Tags Validation
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body ValidateContentRequest true "校验内容请求"
// @Success 200 {object} Response{data=validation.ValidationResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /validation/validate [post]
func (h *ValidationHandler) ValidateContent(c *gin.Context) {
	var req ValidateContentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 构建校验请求
	validationReq := &validation.ValidationRequest{
		UserID:      userID,
		ContentType: req.ContentType,
		Text:        req.Text,
		Context:     req.Context,
	}

	// 执行校验
	result, err := h.validationService.ValidateContent(c.Request.Context(), validationReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "内容校验失败",
			Error:   err.Error(),
		})
		return
	}

	// 根据校验结果返回不同的HTTP状态码
	statusCode := http.StatusOK
	message := "内容校验完成"

	if !result.IsValid {
		statusCode = http.StatusBadRequest
		message = "内容校验失败"
	} else if len(result.Warnings) > 0 {
		message = "内容校验通过，但有警告"
	}

	c.JSON(statusCode, Response{
		Success: result.IsValid,
		Message: message,
		Data:    result,
	})
}

// BatchValidateContent 批量校验内容
// @Summary 批量校验内容
// @Description 批量校验多个内容项
// @Tags Validation
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body BatchValidateContentRequest true "批量校验内容请求"
// @Success 200 {object} Response{data=BatchValidationResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /validation/batch-validate [post]
func (h *ValidationHandler) BatchValidateContent(c *gin.Context) {
	var req BatchValidateContentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 限制批量处理数量
	if len(req.Items) > 50 {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "批量校验项目数量不能超过50个",
		})
		return
	}

	// 批量校验
	results := make([]validation.ValidationResult, len(req.Items))
	allValid := true

	for i, item := range req.Items {
		validationReq := &validation.ValidationRequest{
			UserID:      userID,
			ContentType: item.ContentType,
			Text:        item.Text,
			Context:     item.Context,
		}

		result, err := h.validationService.ValidateContent(c.Request.Context(), validationReq)
		if err != nil {
			c.JSON(http.StatusInternalServerError, Response{
				Success: false,
				Message: "批量校验失败",
				Error:   err.Error(),
			})
			return
		}

		results[i] = *result
		if !result.IsValid {
			allValid = false
		}
	}

	// 构建批量结果
	batchResult := BatchValidationResult{
		Results:  results,
		AllValid: allValid,
		Summary: BatchValidationSummary{
			Total:   len(results),
			Valid:   0,
			Invalid: 0,
		},
	}

	// 统计结果
	for _, result := range results {
		if result.IsValid {
			batchResult.Summary.Valid++
		} else {
			batchResult.Summary.Invalid++
		}
	}

	message := "批量校验完成"
	if !allValid {
		message = "批量校验完成，部分内容未通过校验"
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: message,
		Data:    batchResult,
	})
}

// GetValidationStats 获取校验统计信息
// @Summary 获取校验统计信息
// @Description 获取用户的内容校验统计信息
// @Tags Validation
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param days query int false "统计天数" default(7)
// @Success 200 {object} Response{data=map[string]interface{}}
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /validation/stats [get]
func (h *ValidationHandler) GetValidationStats(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 解析天数参数
	days, _ := strconv.Atoi(c.DefaultQuery("days", "7"))
	if days < 1 || days > 365 {
		days = 7
	}

	// 获取统计信息
	stats, err := h.validationService.GetValidationStats(c.Request.Context(), userID, days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取统计信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取统计信息成功",
		Data:    stats,
	})
}

// GetValidationConfig 获取校验配置
// @Summary 获取校验配置
// @Description 获取当前的内容校验配置信息
// @Tags Validation
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=ValidationConfigResponse}
// @Router /validation/config [get]
func (h *ValidationHandler) GetValidationConfig(c *gin.Context) {
	// 返回公开的配置信息（不包含敏感信息如敏感词列表）
	config := ValidationConfigResponse{
		MaxTextLength: 10000,
		MinTextLength: 1,
		AllowedContentTypes: []string{
			"character_name", "character_description", "world_name", "world_description",
			"scene_name", "scene_description", "dialogue", "action", "event_description",
		},
		MaxRequestsPerMinute: 60,
		MaxRequestsPerHour:   1000,
		Features: ValidationFeatures{
			ProfanityFilter: true,
			SecurityCheck:   true,
			AIModeration:    false,
			RateLimit:       true,
		},
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取配置信息成功",
		Data:    config,
	})
}

// 请求和响应结构体定义

// ValidateContentRequest 校验内容请求
type ValidateContentRequest struct {
	ContentType string                 `json:"content_type" binding:"required"`
	Text        string                 `json:"text" binding:"required"`
	Context     map[string]interface{} `json:"context"`
}

// BatchValidateContentRequest 批量校验内容请求
type BatchValidateContentRequest struct {
	Items []ValidateContentRequest `json:"items" binding:"required"`
}

// BatchValidationResult 批量校验结果
type BatchValidationResult struct {
	Results  []validation.ValidationResult `json:"results"`
	AllValid bool                          `json:"all_valid"`
	Summary  BatchValidationSummary        `json:"summary"`
}

// BatchValidationSummary 批量校验摘要
type BatchValidationSummary struct {
	Total   int `json:"total"`
	Valid   int `json:"valid"`
	Invalid int `json:"invalid"`
}

// ValidationConfigResponse 校验配置响应
type ValidationConfigResponse struct {
	MaxTextLength        int                `json:"max_text_length"`
	MinTextLength        int                `json:"min_text_length"`
	AllowedContentTypes  []string           `json:"allowed_content_types"`
	MaxRequestsPerMinute int                `json:"max_requests_per_minute"`
	MaxRequestsPerHour   int                `json:"max_requests_per_hour"`
	Features             ValidationFeatures `json:"features"`
}

// ValidationFeatures 校验功能特性
type ValidationFeatures struct {
	ProfanityFilter bool `json:"profanity_filter"`
	SecurityCheck   bool `json:"security_check"`
	AIModeration    bool `json:"ai_moderation"`
	RateLimit       bool `json:"rate_limit"`
}
