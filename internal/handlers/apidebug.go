package handlers

import (
	"net/http"

	"ai-text-game-iam-npc/internal/apidebug"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
)

// APIDebugHandler API调试系统处理器
type APIDebugHandler struct {
	service *apidebug.Service
	logger  logger.Logger
}

// NewAPIDebugHandler 创建新的API调试系统处理器
func NewAPIDebugHandler(service *apidebug.Service, logger logger.Logger) *APIDebugHandler {
	return &APIDebugHandler{
		service: service,
		logger:  logger,
	}
}

// GetSystemStatus 获取系统状态
// @Summary 获取API调试系统状态
// @Description 获取API调试系统的运行状态和统计信息
// @Tags 系统状态
// @Produce json
// @Success 200 {object} Response{data=apidebug.SystemStatus}
// @Failure 500 {object} Response
// @Router /api/v1/system/status [get]
func (h *APIDebugHandler) GetSystemStatus(c *gin.Context) {
	h.logger.Info("获取系统状态")
	
	status, err := h.service.GetSystemStatus(c.Request.Context())
	if err != nil {
		h.logger.Error("获取系统状态失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取系统状态失败",
			Error:   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取系统状态成功",
		Data:    status,
	})
}

// GetSystemConfig 获取系统配置
// @Summary 获取系统配置
// @Description 获取API调试系统的当前配置信息
// @Tags 系统状态
// @Produce json
// @Success 200 {object} Response{data=apidebug.Config}
// @Router /api/v1/system/config [get]
func (h *APIDebugHandler) GetSystemConfig(c *gin.Context) {
	h.logger.Info("获取系统配置")
	
	config := h.service.GetConfig()
	
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取系统配置成功",
		Data:    config,
	})
}

// GetSystemInfo 获取系统信息页面
// @Summary 获取系统信息页面
// @Description 返回系统信息和状态的HTML页面
// @Tags 系统状态
// @Produce html
// @Success 200 {string} string "HTML页面"
// @Router /system [get]
func (h *APIDebugHandler) GetSystemInfo(c *gin.Context) {
	h.logger.Info("获取系统信息页面")
	
	html := h.generateSystemInfoHTML()
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, html)
}

// generateSystemInfoHTML 生成系统信息HTML页面
func (h *APIDebugHandler) generateSystemInfoHTML() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试系统 - 系统状态</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .nav {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .nav a {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .nav a:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 500;
            color: #555;
        }
        
        .status-value {
            color: #2c3e50;
            font-weight: bold;
        }
        
        .status-online {
            color: #27ae60;
        }
        
        .status-offline {
            color: #e74c3c;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .error {
            background: #fdf2f2;
            border: 1px solid #fecaca;
            color: #e74c3c;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .refresh-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
            margin-bottom: 20px;
        }
        
        .refresh-btn:hover {
            background: #229954;
        }
        
        .chart-container {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .chart-title {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            text-align: center;
        }
        
        .method-chart, .tag-chart {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .chart-item {
            background: #3498db;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .chart-item.get { background: #27ae60; }
        .chart-item.post { background: #3498db; }
        .chart-item.put { background: #f39c12; }
        .chart-item.delete { background: #e74c3c; }
        .chart-item.patch { background: #9b59b6; }
        
        @media (max-width: 768px) {
            .status-grid {
                grid-template-columns: 1fr;
            }
            
            .nav a {
                display: block;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 API调试系统</h1>
            <p>系统状态监控面板</p>
        </div>
        
        <div class="nav">
            <a href="/api/v1/docs">📚 API文档</a>
            <a href="/api/v1/docs/swagger">📖 Swagger UI</a>
            <a href="/debug">🔧 调试界面</a>
            <a href="/api/v1/system/status">📊 状态API</a>
        </div>
        
        <button class="refresh-btn" onclick="loadSystemStatus()">🔄 刷新状态</button>
        
        <div id="statusContainer">
            <div class="loading">正在加载系统状态...</div>
        </div>
    </div>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStatus();
            
            // 每30秒自动刷新一次
            setInterval(loadSystemStatus, 30000);
        });
        
        // 加载系统状态
        async function loadSystemStatus() {
            const container = document.getElementById('statusContainer');
            
            try {
                const response = await fetch('/api/v1/system/status');
                const result = await response.json();
                
                if (result.success) {
                    displaySystemStatus(result.data);
                } else {
                    container.innerHTML = '<div class="error">加载系统状态失败: ' + result.message + '</div>';
                }
            } catch (error) {
                container.innerHTML = '<div class="error">网络错误: ' + error.message + '</div>';
            }
        }
        
        // 显示系统状态
        function displaySystemStatus(status) {
            const container = document.getElementById('statusContainer');
            
            const html = ` + "`" + `
                <div class="status-grid">
                    <div class="status-card">
                        <h3>🖥️ 服务状态</h3>
                        <div class="status-item">
                            <span class="status-label">服务名称</span>
                            <span class="status-value">${status.service_name}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">版本</span>
                            <span class="status-value">${status.version}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">状态</span>
                            <span class="status-value status-online">${status.status}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">启动时间</span>
                            <span class="status-value">${new Date(status.start_time).toLocaleString()}</span>
                        </div>
                    </div>
                    
                    <div class="status-card">
                        <h3>📚 文档状态</h3>
                        <div class="status-item">
                            <span class="status-label">API端点总数</span>
                            <span class="status-value">${status.documentation.total_endpoints}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">最后更新</span>
                            <span class="status-value">${new Date(status.documentation.last_updated).toLocaleString()}</span>
                        </div>
                    </div>
                    
                    <div class="status-card">
                        <h3>🔧 调试状态</h3>
                        <div class="status-item">
                            <span class="status-label">历史请求数</span>
                            <span class="status-value">${status.debug.total_requests}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">缓存状态</span>
                            <span class="status-value ${status.debug.cache_enabled ? 'status-online' : 'status-offline'}">
                                ${status.debug.cache_enabled ? '已启用' : '已禁用'}
                            </span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">最大历史记录</span>
                            <span class="status-value">${status.debug.max_history_size}</span>
                        </div>
                    </div>
                    
                    <div class="status-card">
                        <h3>⚙️ 配置信息</h3>
                        <div class="status-item">
                            <span class="status-label">项目根目录</span>
                            <span class="status-value">${status.configuration.project_root}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">基础URL</span>
                            <span class="status-value">${status.configuration.base_url}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">输出目录</span>
                            <span class="status-value">${status.configuration.output_dir}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">自动刷新</span>
                            <span class="status-value ${status.configuration.auto_refresh ? 'status-online' : 'status-offline'}">
                                ${status.configuration.auto_refresh ? '已启用' : '已禁用'}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">HTTP方法分布</div>
                    <div class="method-chart" id="methodChart"></div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">API标签分布</div>
                    <div class="tag-chart" id="tagChart"></div>
                </div>
            ` + "`" + `;
            
            container.innerHTML = html;
            
            // 渲染图表
            renderMethodChart(status.documentation.method_counts);
            renderTagChart(status.documentation.tag_counts);
        }
        
        // 渲染方法图表
        function renderMethodChart(methodCounts) {
            const container = document.getElementById('methodChart');
            let html = '';
            
            for (const [method, count] of Object.entries(methodCounts)) {
                html += ` + "`" + `<div class="chart-item ${method.toLowerCase()}">${method}: ${count}</div>` + "`" + `;
            }
            
            container.innerHTML = html;
        }
        
        // 渲染标签图表
        function renderTagChart(tagCounts) {
            const container = document.getElementById('tagChart');
            let html = '';
            
            for (const [tag, count] of Object.entries(tagCounts)) {
                html += ` + "`" + `<div class="chart-item">${tag}: ${count}</div>` + "`" + `;
            }
            
            container.innerHTML = html;
        }
    </script>
</body>
</html>`
}
