package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"ai-text-game-iam-npc/internal/ai"
	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AIHandler AI处理器
type AIHandler struct {
	aiService *ai.Service
	logger    logger.Logger
}

// NewAIHandler 创建AI处理器
func NewAIHandler(aiService *ai.Service) *AIHandler {
	return &AIHandler{
		aiService: aiService,
		logger:    logger.New("ai-handler"),
	}
}

// GenerateContent 生成内容
// @Summary 生成AI内容
// @Description 使用AI生成游戏内容，支持场景、角色、事件等多种类型的内容生成。系统会根据提供的提示词和上下文信息，调用AI服务生成结构化的游戏内容。
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param type body string true "生成类型" Enums(scene,character,event,dialogue,world,item) example(scene) "指定要生成的内容类型：scene(场景)、character(角色)、event(事件)、dialogue(对话)、world(世界)、item(物品)等"
// @Param prompt body string true "提示词" example("生成一个神秘的森林场景") "描述要生成内容的具体要求和特征，越详细越能生成符合期望的内容"
// @Param context body object false "上下文信息" example({"world_theme":"奇幻","current_location":"精灵王国"}) "提供生成内容所需的背景信息和相关数据，如世界设定、相关角色等"
// @Param schema body object false "期望的响应结构" example({"name":"string","description":"string","type":"string"}) "定义生成内容的数据结构格式，用于约束AI输出的结构"
// @Param max_tokens body integer false "最大token数" default(500) minimum(1) maximum(2000) example(500) "限制生成内容的长度，数值越大生成的内容越详细"
// @Param temperature body number false "温度参数" default(0.7) minimum(0.0) maximum(1.0) example(0.7) "控制生成内容的创造性，0.0最保守，1.0最有创意"
// @Param world_id body string false "世界ID" format(uuid) example("123e4567-e89b-12d3-a456-************") "指定内容所属的游戏世界，用于保持世界观一致性"
// @Success 200 {object} Response{data=ai.GenerateResponse} "生成成功，返回生成的内容和相关信息"
// @Failure 400 {object} Response "请求参数错误，如缺少必填字段或参数格式不正确"
// @Failure 401 {object} Response "未授权访问，需要有效的认证token"
// @Failure 500 {object} Response "服务器内部错误，如AI服务不可用或生成失败"
// @Router /api/v1/ai/generate [post]
func (h *AIHandler) GenerateContent(c *gin.Context) {
	var req ai.GenerateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	if userID, exists := auth.GetCurrentUserID(c); exists {
		userIDStr := userID.String()
		req.UserID = &userIDStr
	}

	// 验证必要参数
	if req.Type == "" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "生成类型不能为空",
		})
		return
	}

	if req.Prompt == "" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "提示词不能为空",
		})
		return
	}

	// 设置默认值
	if req.MaxTokens == 0 {
		req.MaxTokens = 500
	}
	if req.Temperature == 0 {
		req.Temperature = 0.7
	}

	// 生成内容
	response, err := h.aiService.GenerateContent(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "生成内容失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "生成内容成功",
		Data:    response,
	})
}

// GenerateScene 生成场景
// @Summary 生成游戏场景
// @Description 根据场景参数生成游戏场景描述和属性。支持指定场景名称、类型、主题风格、氛围设定等参数，AI会生成包含详细描述、氛围、关键特征和可能行动的完整场景信息。
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id body string true "世界ID" format(uuid) example("123e4567-e89b-12d3-a456-************") "指定要生成场景的游戏世界，必填"
// @Param scene_name body string false "场景名称" example("神秘森林") "指定要生成的场景名称，如'神秘森林'、'古老城堡'等"
// @Param scene_type body string false "场景类型" example("outdoor") Enums(indoor,outdoor,underground,town,dungeon,wilderness) "指定场景的类型"
// @Param theme body string false "主题风格" example("奇幻") Enums(fantasy,sci-fi,horror,modern,medieval,cyberpunk) "指定场景的主题风格"
// @Param mood body string false "氛围设定" example("神秘") Enums(mysterious,tense,peaceful,dangerous,cheerful,gloomy) "指定场景的氛围"
// @Param connected_scenes body array false "连接的场景" example(["精灵村庄","古老神庙"]) "指定与此场景相连的其他场景名称列表"
// @Param special_requirements body string false "特殊要求" example("需要包含一个隐藏的宝箱") "对场景生成的特殊要求或限制条件"
// @Success 200 {object} Response{data=ai.GenerateResponse} "场景生成成功，返回生成的场景描述和结构化数据"
// @Failure 400 {object} Response "请求参数错误，如缺少必填的世界ID"
// @Failure 401 {object} Response "未授权访问，需要有效的认证token"
// @Failure 500 {object} Response "服务器内部错误，如AI服务不可用"
// @Router /api/v1/ai/generate/scene [post]
func (h *AIHandler) GenerateScene(c *gin.Context) {
	var req GenerateSceneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("场景生成请求参数绑定失败", "error", err)
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	h.logger.Info("收到场景生成请求",
		"world_id", req.WorldID,
		"scene_name", req.SceneName,
		"theme", req.Theme,
		"mood", req.Mood)

	// 获取当前用户ID
	var userID *uuid.UUID
	if id, exists := auth.GetCurrentUserID(c); exists {
		userID = &id
	}

	// 构建提示词
	prompt := h.buildScenePrompt(req)
	h.logger.Info("构建的场景生成提示词", "prompt", prompt)

	// 解析世界ID
	var worldID *uuid.UUID
	if req.WorldID != "" && req.WorldID != "temp" {
		if parsedID, err := uuid.Parse(req.WorldID); err == nil {
			worldID = &parsedID
		}
	}

	// 构建AI请求
	aiReq := &ai.GenerateRequest{
		Type:        "scene",
		Prompt:      prompt,
		Context:     h.buildSceneContext(req),
		MaxTokens:   500,
		Temperature: 0.7,
		WorldID:     func() *string { s := worldID.String(); return &s }(),
		UserID:      func() *string { s := userID.String(); return &s }(),
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
			"atmosphere":  "string",
			"connections": "object",
			"entities":    "array",
		},
	}

	h.logger.Info("开始调用AI服务生成场景", "ai_request_type", aiReq.Type)
	response, err := h.aiService.GenerateContent(c.Request.Context(), aiReq)
	if err != nil {
		h.logger.Error("AI场景生成失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "生成场景失败",
			Error:   err.Error(),
		})
		return
	}

	h.logger.Info("AI场景生成成功", "token_usage", response.TokenUsage)
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "生成场景成功",
		Data:    response,
	})
}

// GenerateCharacter 生成角色
// @Summary 生成游戏角色
// @Description 根据提示词生成游戏角色描述和属性。AI会根据提供的角色描述生成包含姓名、外观、性格、技能、背景故事等完整信息的游戏角色。
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param prompt body string true "角色生成提示词" example("一个勇敢的精灵战士，擅长弓箭术") "描述要生成的角色特征，如'一个勇敢的战士'、'神秘的法师'等，越详细越好"
// @Param context body object false "上下文信息" example({"world_theme":"奇幻","faction":"精灵族","level":"高级"}) "提供额外的角色背景信息，如世界设定、相关角色、所属阵营等"
// @Param world_id body string false "世界ID" format(uuid) example("123e4567-e89b-12d3-a456-************") "指定角色所属的游戏世界，用于保持世界观一致性"
// @Success 200 {object} Response{data=ai.GenerateResponse} "角色生成成功，返回生成的角色描述和属性信息"
// @Failure 400 {object} Response "请求参数错误，如缺少必填的提示词"
// @Failure 401 {object} Response "未授权访问，需要有效的认证token"
// @Failure 500 {object} Response "服务器内部错误，如AI服务不可用"
// @Router /api/v1/ai/generate/character [post]
func (h *AIHandler) GenerateCharacter(c *gin.Context) {
	var req GenerateCharacterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	var userID *uuid.UUID
	if id, exists := auth.GetCurrentUserID(c); exists {
		userID = &id
	}

	// 构建AI请求
	aiReq := &ai.GenerateRequest{
		Type:        "character",
		Prompt:      req.Prompt,
		Context:     req.Context,
		MaxTokens:   400,
		Temperature: 0.8,
		WorldID:     func() *string { s := req.WorldID.String(); return &s }(),
		UserID:      func() *string { s := userID.String(); return &s }(),
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
			"personality": "array",
			"skills":      "array",
			"background":  "string",
		},
	}

	response, err := h.aiService.GenerateContent(c.Request.Context(), aiReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "生成角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "生成角色成功",
		Data:    response,
	})
}

// GenerateEvent 生成事件
// @Summary 生成游戏事件
// @Description 根据提示词生成游戏事件描述和效果。AI会生成包含事件名称、详细描述、触发条件、持续时间、影响效果等完整信息的游戏事件。
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param prompt body string true "事件生成提示词" example("一场突然的暴风雨席卷了整个村庄") "描述要生成的事件内容，如'一场突然的暴风雨'、'神秘商人的到来'等"
// @Param context body object false "上下文信息" example({"current_scene":"村庄广场","time_of_day":"傍晚","weather":"晴朗"}) "提供事件相关的背景信息，如当前场景、涉及角色、时间地点等"
// @Param world_id body string false "世界ID" format(uuid) example("123e4567-e89b-12d3-a456-************") "指定事件发生的游戏世界，确保事件与世界设定相符"
// @Success 200 {object} Response{data=ai.GenerateResponse} "事件生成成功，返回生成的事件描述和效果信息"
// @Failure 400 {object} Response "请求参数错误，如缺少必填的提示词"
// @Failure 401 {object} Response "未授权访问，需要有效的认证token"
// @Failure 500 {object} Response "服务器内部错误，如AI服务不可用"
// @Router /api/v1/ai/generate/event [post]
func (h *AIHandler) GenerateEvent(c *gin.Context) {
	var req GenerateEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	var userID *uuid.UUID
	if id, exists := auth.GetCurrentUserID(c); exists {
		userID = &id
	}

	// 构建AI请求
	aiReq := &ai.GenerateRequest{
		Type:        "event",
		Prompt:      req.Prompt,
		Context:     req.Context,
		MaxTokens:   300,
		Temperature: 0.6,
		WorldID:     func() *string { s := req.WorldID.String(); return &s }(),
		UserID:      func() *string { s := userID.String(); return &s }(),
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
			"priority":    "number",
			"duration":    "number",
			"effects":     "object",
		},
	}

	response, err := h.aiService.GenerateContent(c.Request.Context(), aiReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "生成事件失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "生成事件成功",
		Data:    response,
	})
}

// GetInteractionHistory 获取AI交互历史
// @Summary 获取AI交互历史
// @Description 获取用户或世界的AI交互历史记录，包括所有的AI生成请求和响应信息。可以按世界ID过滤，支持分页查询。
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id query string false "世界ID" format(uuid) example("123e4567-e89b-12d3-a456-************") "可选，指定世界ID来获取特定世界的AI交互历史"
// @Param limit query integer false "限制数量" default(50) minimum(1) maximum(200) example(50) "返回记录的最大数量，默认50条，最多200条"
// @Success 200 {object} Response{data=[]models.AIInteraction} "成功返回AI交互历史记录列表"
// @Failure 400 {object} Response "请求参数错误，如无效的世界ID格式"
// @Failure 401 {object} Response "未授权访问，需要有效的认证token"
// @Router /api/v1/ai/history [get]
func (h *AIHandler) GetInteractionHistory(c *gin.Context) {
	// 获取查询参数
	worldIDStr := c.Query("world_id")
	limitStr := c.DefaultQuery("limit", "50")

	var worldID *uuid.UUID
	if worldIDStr != "" {
		if id, err := uuid.Parse(worldIDStr); err == nil {
			worldID = &id
		} else {
			c.JSON(http.StatusBadRequest, Response{
				Success: false,
				Message: "无效的世界ID格式",
			})
			return
		}
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200 // 限制最大数量
	}

	// 获取当前用户ID
	var userID *uuid.UUID
	if id, exists := auth.GetCurrentUserID(c); exists {
		userID = &id
	}

	// 获取交互历史
	interactions, err := h.aiService.GetInteractionHistory(worldID, userID, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取交互历史失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取交互历史成功",
		Data:    interactions,
	})
}

// GetTokenUsageStats 获取token使用统计
// @Summary 获取token使用统计
// @Description 获取用户或世界的token使用统计信息，包括总使用量、日均使用量、按类型分类的使用情况等。用于监控AI服务使用情况和成本控制。
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id query string false "世界ID" format(uuid) example("123e4567-e89b-12d3-a456-************") "可选，指定世界ID来获取特定世界的token使用统计"
// @Param days query integer false "统计天数" default(30) minimum(1) maximum(365) example(30) "统计的天数范围，默认30天，最多365天"
// @Success 200 {object} Response{data=object{total_tokens=integer,daily_average=number,by_type=object}} "成功返回token使用统计信息"
// @Failure 400 {object} Response "请求参数错误，如无效的世界ID格式或天数超出范围"
// @Failure 401 {object} Response "未授权访问，需要有效的认证token"
// @Router /api/v1/ai/stats [get]
func (h *AIHandler) GetTokenUsageStats(c *gin.Context) {
	// 获取查询参数
	worldIDStr := c.Query("world_id")
	daysStr := c.DefaultQuery("days", "30")

	var worldID *uuid.UUID
	if worldIDStr != "" {
		if id, err := uuid.Parse(worldIDStr); err == nil {
			worldID = &id
		} else {
			c.JSON(http.StatusBadRequest, Response{
				Success: false,
				Message: "无效的世界ID格式",
			})
			return
		}
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 30
	}
	if days > 365 {
		days = 365 // 限制最大天数
	}

	// 获取当前用户ID
	var userID *uuid.UUID
	if id, exists := auth.GetCurrentUserID(c); exists {
		userID = &id
	}

	// 获取统计信息
	stats, err := h.aiService.GetTokenUsageStats(worldID, userID, days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取统计信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取统计信息成功",
		Data:    stats,
	})
}

// 请求结构体定义

// GenerateSceneRequest 场景生成请求
// @Description 场景生成请求结构，用于指定要生成的游戏场景的各种参数和要求
type GenerateSceneRequest struct {
	WorldID              string   `json:"world_id" binding:"required" format:"uuid" example:"123e4567-e89b-12d3-a456-************"`              // 世界ID，必填，指定要生成场景的游戏世界
	SceneName            string   `json:"scene_name,omitempty" example:"神秘森林"`                     // 场景名称，可选，指定要生成的场景名称，如"神秘森林"、"古老城堡"等
	SceneType            string   `json:"scene_type,omitempty" example:"outdoor" enums:"indoor,outdoor,underground,town,dungeon,wilderness"`                     // 场景类型，可选，指定场景的类型，如"室内"、"室外"、"地下城"、"城镇"等
	Theme                string   `json:"theme,omitempty" example:"奇幻" enums:"fantasy,sci-fi,horror,modern,medieval,cyberpunk"`                          // 主题风格，可选，指定场景的主题风格，如"奇幻"、"科幻"、"恐怖"、"现代"等
	Mood                 string   `json:"mood,omitempty" example:"神秘" enums:"mysterious,tense,peaceful,dangerous,cheerful,gloomy"`                           // 氛围设定，可选，指定场景的氛围，如"神秘"、"紧张"、"宁静"、"危险"等
	ConnectedScenes      []string `json:"connected_scenes,omitempty" example:"[\"精灵村庄\",\"古老神庙\"]"`               // 连接的场景，可选，指定与此场景相连的其他场景名称列表
	SpecialRequirements  string   `json:"special_requirements,omitempty" example:"需要包含一个隐藏的宝箱"`           // 特殊要求，可选，对场景生成的特殊要求或限制条件
	// 兼容旧格式的字段
	Prompt               string                 `json:"prompt,omitempty" example:"生成一个充满魔法气息的神秘森林"`         // 提示词，可选，用于兼容旧版本API的自定义提示词
	Context              map[string]interface{} `json:"context,omitempty" example:"{\"season\":\"春季\",\"time\":\"黄昏\"}"`        // 上下文信息，可选，用于兼容旧版本API的额外上下文数据
}

// buildScenePrompt 构建场景生成提示词
func (h *AIHandler) buildScenePrompt(req GenerateSceneRequest) string {
	// 如果提供了旧格式的prompt，直接使用
	if req.Prompt != "" {
		return req.Prompt
	}

	// 构建新格式的提示词
	prompt := "请生成一个游戏场景的详细描述。"

	if req.SceneName != "" {
		prompt += fmt.Sprintf("场景名称：%s。", req.SceneName)
	}

	if req.Theme != "" {
		prompt += fmt.Sprintf("主题风格：%s。", req.Theme)
	}

	if req.SceneType != "" {
		prompt += fmt.Sprintf("场景类型：%s。", req.SceneType)
	}

	if req.Mood != "" {
		prompt += fmt.Sprintf("氛围设定：%s。", req.Mood)
	}

	if req.SpecialRequirements != "" {
		prompt += fmt.Sprintf("特殊要求：%s。", req.SpecialRequirements)
	}

	if len(req.ConnectedScenes) > 0 {
		prompt += fmt.Sprintf("需要连接的场景：%s。", strings.Join(req.ConnectedScenes, "、"))
	}

	prompt += "请生成包含场景名称、详细描述、氛围、关键特征和可能的行动的完整场景信息。"

	return prompt
}

// buildSceneContext 构建场景生成上下文
func (h *AIHandler) buildSceneContext(req GenerateSceneRequest) map[string]interface{} {
	// 如果提供了旧格式的context，合并使用
	context := make(map[string]interface{})
	if req.Context != nil {
		for k, v := range req.Context {
			context[k] = v
		}
	}

	// 添加新格式的上下文信息
	if req.WorldID != "" {
		context["world_id"] = req.WorldID
	}
	if req.SceneName != "" {
		context["scene_name"] = req.SceneName
	}
	if req.SceneType != "" {
		context["scene_type"] = req.SceneType
	}
	if req.Theme != "" {
		context["theme"] = req.Theme
	}
	if req.Mood != "" {
		context["mood"] = req.Mood
	}
	if len(req.ConnectedScenes) > 0 {
		context["connected_scenes"] = req.ConnectedScenes
	}
	if req.SpecialRequirements != "" {
		context["special_requirements"] = req.SpecialRequirements
	}

	return context
}

// GenerateCharacterRequest 角色生成请求
// @Description 角色生成请求结构，用于指定要生成的游戏角色的特征和背景信息
type GenerateCharacterRequest struct {
	Prompt  string                 `json:"prompt" binding:"required" example:"一个勇敢的精灵战士，擅长弓箭术，有着翠绿的眼睛"`  // 角色生成提示词，必填，描述要生成的角色特征，如"一个勇敢的战士"、"神秘的法师"等，越详细越好
	Context map[string]interface{} `json:"context,omitempty" example:"{\"world_theme\":\"奇幻\",\"faction\":\"精灵族\",\"level\":\"高级\"}"`                    // 上下文信息，可选，提供额外的角色背景信息，如世界设定、相关角色、所属阵营等
	WorldID *uuid.UUID             `json:"world_id,omitempty" format:"uuid" example:"123e4567-e89b-12d3-a456-************"`                   // 世界ID，可选，指定角色所属的游戏世界，用于保持世界观一致性
}

// GenerateEventRequest 事件生成请求
// @Description 事件生成请求结构，用于指定要生成的游戏事件的内容和背景信息
type GenerateEventRequest struct {
	Prompt  string                 `json:"prompt" binding:"required" example:"一场突然的暴风雨席卷了整个村庄，村民们纷纷寻找避难所"`  // 事件生成提示词，必填，描述要生成的事件内容，如"一场突然的暴风雨"、"神秘商人的到来"等
	Context map[string]interface{} `json:"context,omitempty" example:"{\"current_scene\":\"村庄广场\",\"time_of_day\":\"傍晚\",\"weather\":\"晴朗\"}"`                    // 上下文信息，可选，提供事件相关的背景信息，如当前场景、涉及角色、时间地点等
	WorldID *uuid.UUID             `json:"world_id,omitempty" format:"uuid" example:"123e4567-e89b-12d3-a456-************"`                   // 世界ID，可选，指定事件发生的游戏世界，确保事件与世界设定相符
}
