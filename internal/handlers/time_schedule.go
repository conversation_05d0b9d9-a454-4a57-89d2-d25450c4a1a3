package handlers

import (
	"net/http"
	"time"

	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/game"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// TimeScheduleHandler 时间段配置处理器
type TimeScheduleHandler struct {
	timeConfigService *game.TimeConfigService
	worldService      *game.WorldService
	logger            logger.Logger
}

// NewTimeScheduleHandler 创建时间段配置处理器
func NewTimeScheduleHandler(timeConfigService *game.TimeConfigService, worldService *game.WorldService, logger logger.Logger) *TimeScheduleHandler {
	return &TimeScheduleHandler{
		timeConfigService: timeConfigService,
		worldService:      worldService,
		logger:            logger,
	}
}

// GetTimeScheduleConfig 获取世界的时间段配置
// @Summary 获取世界的时间段配置
// @Description 获取指定世界的时间段配置信息
// @Tags TimeSchedule
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response{data=models.TimeScheduleConfig}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/game/worlds/{world_id}/time-schedule [get]
func (h *TimeScheduleHandler) GetTimeScheduleConfig(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 验证用户权限（只有世界创建者可以查看配置）
	world, err := h.worldService.GetWorld(c.Request.Context(), worldID.String())
	if err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Message: "世界不存在",
		})
		return
	}

	if world.CreatorID != userID.String() {
		c.JSON(http.StatusForbidden, Response{
			Success: false,
			Message: "无权限访问该世界的时间配置",
		})
		return
	}

	// 获取时间段配置
	config, err := h.timeConfigService.GetTimeScheduleConfig(c.Request.Context(), worldID.String())
	if err != nil {
		h.logger.Error("获取时间段配置失败", "world_id", worldID, "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取时间段配置失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取时间段配置成功",
		Data:    config,
	})
}

// UpdateTimeScheduleConfig 更新世界的时间段配置
// @Summary 更新世界的时间段配置
// @Description 更新指定世界的时间段配置，支持热更新
// @Tags TimeSchedule
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Param config body models.TimeScheduleConfig true "时间段配置"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/game/worlds/{world_id}/time-schedule [put]
func (h *TimeScheduleHandler) UpdateTimeScheduleConfig(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 验证用户权限
	world, err := h.worldService.GetWorld(c.Request.Context(), worldID.String())
	if err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Message: "世界不存在",
		})
		return
	}

	if world.CreatorID != userID.String() {
		c.JSON(http.StatusForbidden, Response{
			Success: false,
			Message: "无权限修改该世界的时间配置",
		})
		return
	}

	// 解析请求体
	var config models.TimeScheduleConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数格式错误",
			Error:   err.Error(),
		})
		return
	}

	// 更新时间段配置
	if err := h.timeConfigService.UpdateTimeScheduleConfig(c.Request.Context(), worldID.String(), &config); err != nil {
		h.logger.Error("更新时间段配置失败", "world_id", worldID, "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "更新时间段配置失败",
			Error:   err.Error(),
		})
		return
	}

	h.logger.Info("时间段配置更新成功", "world_id", worldID, "user_id", userID.String())
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "时间段配置更新成功",
	})
}

// GetCurrentTimeRate 获取世界当前的时间速率
// @Summary 获取世界当前的时间速率
// @Description 获取指定世界当前生效的时间速率
// @Tags TimeSchedule
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response{data=map[string]interface{}}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/game/worlds/{world_id}/time-rate [get]
func (h *TimeScheduleHandler) GetCurrentTimeRate(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 获取当前时间速率
	timeRate, err := h.timeConfigService.GetCurrentTimeRate(c.Request.Context(), worldID.String())
	if err != nil {
		h.logger.Error("获取当前时间速率失败", "world_id", worldID, "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取当前时间速率失败",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前生效的时间段
	activeSchedule, err := h.timeConfigService.GetActiveTimeSchedule(c.Request.Context(), worldID.String())
	if err != nil {
		h.logger.Error("获取当前时间段失败", "world_id", worldID, "error", err)
	}

	data := map[string]interface{}{
		"world_id":   worldID.String(),
		"time_rate":  timeRate,
		"timestamp":  h.getCurrentTimestamp(),
	}

	if activeSchedule != nil {
		data["active_schedule"] = map[string]interface{}{
			"name":        activeSchedule.Name,
			"start_time":  activeSchedule.StartTime,
			"end_time":    activeSchedule.EndTime,
			"description": activeSchedule.Description,
		}
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取当前时间速率成功",
		Data:    data,
	})
}

// EnableTimeSchedule 启用世界的时间段配置
// @Summary 启用世界的时间段配置
// @Description 启用指定世界的时间段配置功能
// @Tags TimeSchedule
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/game/worlds/{world_id}/time-schedule/enable [post]
func (h *TimeScheduleHandler) EnableTimeSchedule(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 验证用户权限
	world, err := h.worldService.GetWorld(c.Request.Context(), worldID.String())
	if err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Message: "世界不存在",
		})
		return
	}

	if world.CreatorID != userID.String() {
		c.JSON(http.StatusForbidden, Response{
			Success: false,
			Message: "无权限修改该世界的时间配置",
		})
		return
	}

	// 启用时间段配置
	if err := h.timeConfigService.EnableTimeSchedule(c.Request.Context(), worldID.String()); err != nil {
		h.logger.Error("启用时间段配置失败", "world_id", worldID, "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "启用时间段配置失败",
			Error:   err.Error(),
		})
		return
	}

	h.logger.Info("时间段配置已启用", "world_id", worldID, "user_id", userID.String())
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "时间段配置已启用",
	})
}

// DisableTimeSchedule 禁用世界的时间段配置
// @Summary 禁用世界的时间段配置
// @Description 禁用指定世界的时间段配置功能
// @Tags TimeSchedule
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/game/worlds/{world_id}/time-schedule/disable [post]
func (h *TimeScheduleHandler) DisableTimeSchedule(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 验证用户权限
	world, err := h.worldService.GetWorld(c.Request.Context(), worldID.String())
	if err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Message: "世界不存在",
		})
		return
	}

	if world.CreatorID != userID.String() {
		c.JSON(http.StatusForbidden, Response{
			Success: false,
			Message: "无权限修改该世界的时间配置",
		})
		return
	}

	// 禁用时间段配置
	if err := h.timeConfigService.DisableTimeSchedule(c.Request.Context(), worldID.String()); err != nil {
		h.logger.Error("禁用时间段配置失败", "world_id", worldID, "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "禁用时间段配置失败",
			Error:   err.Error(),
		})
		return
	}

	h.logger.Info("时间段配置已禁用", "world_id", worldID, "user_id", userID.String())
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "时间段配置已禁用",
	})
}

// CreateDefaultTimeSchedule 为世界创建默认时间段配置
// @Summary 为世界创建默认时间段配置
// @Description 为指定世界创建默认的时间段配置
// @Tags TimeSchedule
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/game/worlds/{world_id}/time-schedule/default [post]
func (h *TimeScheduleHandler) CreateDefaultTimeSchedule(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 验证用户权限
	world, err := h.worldService.GetWorld(c.Request.Context(), worldID.String())
	if err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Message: "世界不存在",
		})
		return
	}

	if world.CreatorID != userID.String() {
		c.JSON(http.StatusForbidden, Response{
			Success: false,
			Message: "无权限修改该世界的时间配置",
		})
		return
	}

	// 创建默认时间段配置
	if err := h.timeConfigService.CreateDefaultTimeScheduleConfig(c.Request.Context(), worldID.String()); err != nil {
		h.logger.Error("创建默认时间段配置失败", "world_id", worldID, "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建默认时间段配置失败",
			Error:   err.Error(),
		})
		return
	}

	h.logger.Info("默认时间段配置创建成功", "world_id", worldID, "user_id", userID.String())
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "默认时间段配置创建成功",
	})
}

// ValidateTimeScheduleConfig 验证时间段配置
// @Summary 验证时间段配置
// @Description 验证时间段配置的有效性，不保存到数据库
// @Tags TimeSchedule
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param config body models.TimeScheduleConfig true "时间段配置"
// @Success 200 {object} Response{data=map[string]interface{}}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/game/time-schedule/validate [post]
func (h *TimeScheduleHandler) ValidateTimeScheduleConfig(c *gin.Context) {
	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 解析请求体
	var config models.TimeScheduleConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数格式错误",
			Error:   err.Error(),
		})
		return
	}

	// 验证配置
	if err := h.timeConfigService.ValidateTimeScheduleConfig(&config); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "时间段配置验证失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回验证结果和配置摘要
	data := map[string]interface{}{
		"valid":           true,
		"schedule_count":  len(config.Schedules),
		"enabled":         config.Enabled,
		"timezone":        config.Timezone,
		"default_rate":    config.DefaultRate,
		"current_rate":    config.GetCurrentTimeRate(),
		"active_schedule": nil,
	}

	if activeSchedule := config.GetActiveSchedule(); activeSchedule != nil {
		data["active_schedule"] = map[string]interface{}{
			"name":        activeSchedule.Name,
			"start_time":  activeSchedule.StartTime,
			"end_time":    activeSchedule.EndTime,
			"time_rate":   activeSchedule.TimeRate,
			"description": activeSchedule.Description,
		}
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "时间段配置验证成功",
		Data:    data,
	})
}

// GetTimeScheduleStats 获取时间段配置统计信息
// @Summary 获取时间段配置统计信息
// @Description 获取系统中所有世界的时间段配置统计信息
// @Tags TimeSchedule
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response{data=map[string]interface{}}
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/game/time-schedule/stats [get]
func (h *TimeScheduleHandler) GetTimeScheduleStats(c *gin.Context) {
	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 获取统计信息
	stats, err := h.timeConfigService.GetTimeScheduleStats(c.Request.Context())
	if err != nil {
		h.logger.Error("获取时间段配置统计信息失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取统计信息失败",
			Error:   err.Error(),
		})
		return
	}

	// 添加时间管理器统计信息
	if timeManagerStats := h.worldService.GetTimeManagerStats(); timeManagerStats != nil {
		stats["time_manager"] = timeManagerStats
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取时间段配置统计信息成功",
		Data:    stats,
	})
}

// ResetTimeScheduleConfig 重置世界的时间段配置
// @Summary 重置世界的时间段配置
// @Description 将指定世界的时间段配置重置为默认值
// @Tags TimeSchedule
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/game/worlds/{world_id}/time-schedule/reset [post]
func (h *TimeScheduleHandler) ResetTimeScheduleConfig(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 验证用户权限
	world, err := h.worldService.GetWorld(c.Request.Context(), worldID.String())
	if err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Message: "世界不存在",
		})
		return
	}

	if world.CreatorID != userID.String() {
		c.JSON(http.StatusForbidden, Response{
			Success: false,
			Message: "无权限修改该世界的时间配置",
		})
		return
	}

	// 重置时间段配置
	if err := h.timeConfigService.ResetTimeScheduleConfig(c.Request.Context(), worldID.String()); err != nil {
		h.logger.Error("重置时间段配置失败", "world_id", worldID, "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "重置时间段配置失败",
			Error:   err.Error(),
		})
		return
	}

	h.logger.Info("时间段配置已重置", "world_id", worldID, "user_id", userID.String())
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "时间段配置已重置为默认值",
	})
}

// getCurrentTimestamp 获取当前时间戳
func (h *TimeScheduleHandler) getCurrentTimestamp() string {
	return h.getCurrentTime().Format("2006-01-02 15:04:05")
}

// getCurrentTime 获取当前时间
func (h *TimeScheduleHandler) getCurrentTime() time.Time {
	return time.Now()
}
