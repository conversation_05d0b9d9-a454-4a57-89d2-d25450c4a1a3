package handlers

import (
	"net/http"
	"strconv"

	"ai-text-game-iam-npc/internal/debug"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
)

// DebugHandler API调试处理器
type DebugHandler struct {
	service *debug.Service
	logger  logger.Logger
}

// NewDebugHandler 创建新的调试处理器
func NewDebugHandler(service *debug.Service, logger logger.Logger) *DebugHandler {
	return &DebugHandler{
		service: service,
		logger:  logger,
	}
}

// GetDebugUI 获取调试界面
// @Summary 获取API调试界面
// @Description 返回完整的API调试界面HTML页面
// @Tags API调试
// @Produce html
// @Success 200 {string} string "HTML页面"
// @Router /debug [get]
func (h *DebugHandler) GetDebugUI(c *gin.Context) {
	h.logger.Info("获取API调试界面")
	
	html := h.generateDebugUIHTML()
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, html)
}

// SendRequest 发送调试请求
// @Summary 发送API调试请求
// @Description 发送HTTP请求并返回响应结果
// @Tags API调试
// @Accept json
// @Produce json
// @Param request body debug.DebugRequest true "调试请求"
// @Success 200 {object} Response{data=debug.DebugResponse}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/debug/request [post]
func (h *DebugHandler) SendRequest(c *gin.Context) {
	var req debug.DebugRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("请求参数错误", "error", err)
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}
	
	h.logger.Info("发送调试请求", "method", req.Method, "url", req.URL)
	
	response, err := h.service.SendRequest(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("发送请求失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "发送请求失败",
			Error:   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "请求发送完成",
		Data:    response,
	})
}

// GetHistory 获取请求历史
// @Summary 获取API调试历史记录
// @Description 获取API调试的历史请求记录，支持分页查询。返回的记录按时间倒序排列，包含请求详情和响应信息。
// @Tags API调试
// @Produce json
// @Param limit query integer false "限制数量" default(20) minimum(1) maximum(100) example(20) "每页返回的记录数量，最多100条"
// @Param offset query integer false "偏移量" default(0) minimum(0) example(0) "跳过的记录数量，用于分页"
// @Success 200 {object} Response{data=HistoryResponse} "成功返回历史记录列表"
// @Failure 500 {object} Response "服务器内部错误"
// @Router /api/v1/debug/history [get]
func (h *DebugHandler) GetHistory(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))
	
	if limit < 1 || limit > 100 {
		limit = 20
	}
	if offset < 0 {
		offset = 0
	}
	
	h.logger.Info("获取请求历史", "limit", limit, "offset", offset)
	
	records, total, err := h.service.GetHistory(c.Request.Context(), limit, offset)
	if err != nil {
		h.logger.Error("获取历史记录失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取历史记录失败",
			Error:   err.Error(),
		})
		return
	}
	
	response := HistoryResponse{
		Records: records,
		Pagination: PaginationInfo{
			Page:       (offset / limit) + 1,
			Size:       limit,
			Total:      total,
			TotalPages: (total + limit - 1) / limit,
		},
	}
	
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取历史记录成功",
		Data:    response,
	})
}

// GetHistoryByID 根据ID获取历史记录
// @Summary 根据ID获取历史记录
// @Description 根据请求ID获取特定的历史记录详情
// @Tags API调试
// @Produce json
// @Param id path string true "请求ID"
// @Success 200 {object} Response{data=debug.RequestRecord}
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/debug/history/{id} [get]
func (h *DebugHandler) GetHistoryByID(c *gin.Context) {
	id := c.Param("id")
	
	h.logger.Info("获取历史记录详情", "id", id)
	
	record, err := h.service.GetHistoryByID(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("获取历史记录失败", "error", err, "id", id)
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Message: "未找到指定的历史记录",
			Error:   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取历史记录成功",
		Data:    record,
	})
}

// ClearHistory 清空历史记录
// @Summary 清空请求历史记录
// @Description 清空所有的API调试历史记录
// @Tags API调试
// @Produce json
// @Success 200 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/debug/history [delete]
func (h *DebugHandler) ClearHistory(c *gin.Context) {
	h.logger.Info("清空请求历史记录")
	
	if err := h.service.ClearHistory(c.Request.Context()); err != nil {
		h.logger.Error("清空历史记录失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "清空历史记录失败",
			Error:   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "历史记录已清空",
	})
}

// GetEndpointTemplate 获取端点模板
// @Summary 获取API端点模板
// @Description 根据方法和路径获取API端点的调试模板
// @Tags API调试
// @Produce json
// @Param method query string true "HTTP方法"
// @Param path query string true "API路径"
// @Success 200 {object} Response{data=debug.EndpointTemplate}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/debug/template [get]
func (h *DebugHandler) GetEndpointTemplate(c *gin.Context) {
	method := c.Query("method")
	path := c.Query("path")
	
	if method == "" || path == "" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "缺少必需的参数: method 和 path",
		})
		return
	}
	
	h.logger.Info("获取端点模板", "method", method, "path", path)
	
	template, err := h.service.GetEndpointTemplate(c.Request.Context(), method, path)
	if err != nil {
		h.logger.Error("获取端点模板失败", "error", err)
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Message: "获取端点模板失败",
			Error:   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取端点模板成功",
		Data:    template,
	})
}

// generateDebugUIHTML 生成调试界面HTML
func (h *DebugHandler) generateDebugUIHTML() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            height: calc(100vh - 200px);
        }
        
        .left-panel, .right-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .panel-header {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        
        .panel-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        /* 特别为URL输入框增加最小宽度 */
        #url {
            min-width: 400px;
        }
        
        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .method-select {
            width: 120px;
            margin-right: 10px;
        }
        
        .url-input {
            flex: 1;
        }
        
        .url-row {
            display: flex;
            align-items: center;
        }

        .template-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .template-select {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }

        .template-info {
            margin-top: 8px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }

        .template-description {
            color: #666;
            font-style: italic;
            margin: 0;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .response-area {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: #3498db;
            color: #3498db;
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .history-item {
            border: 1px solid #e9ecef;
            border-radius: 4px;
            margin-bottom: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .history-item:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }
        
        .history-method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            margin-right: 10px;
        }
        
        .method-GET { background: #27ae60; }
        .method-POST { background: #3498db; }
        .method-PUT { background: #f39c12; }
        .method-DELETE { background: #e74c3c; }
        .method-PATCH { background: #9b59b6; }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            border: 1px solid #fecaca;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .success {
            color: #27ae60;
            background: #f0f9f4;
            border: 1px solid #a7f3d0;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .param-item {
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .param-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .param-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .param-required {
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }

        .param-optional {
            background: #95a5a6;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }

        .param-type {
            background: #3498db;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            margin-left: 5px;
        }

        .param-location {
            background: #9b59b6;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            margin-left: 5px;
        }

        .param-description {
            color: #7f8c8d;
            font-size: 13px;
            margin-bottom: 10px;
        }

        .param-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .param-example {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .info-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 API调试工具</h1>
            <p>完整的API调试和测试界面</p>
        </div>
        
        <div class="main-content">
            <!-- 左侧面板 - 请求构建 -->
            <div class="left-panel">
                <div class="panel-header">
                    📝 构建请求
                </div>
                <div class="panel-content">
                    <form id="requestForm">
                        <div class="form-group">
                            <label>🎯 API模板选择</label>
                            <div class="template-row">
                                <select id="apiTemplate" class="template-select" onchange="loadSelectedTemplate()">
                                    <option value="">-- 选择API端点模板 --</option>
                                </select>
                                <button type="button" class="btn btn-small" onclick="refreshApiList()">🔄</button>
                            </div>
                            <div class="template-info" id="templateInfo" style="display: none;">
                                <small class="template-description" id="templateDescription"></small>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>请求方法和URL</label>
                            <div class="url-row">
                                <select id="method" class="method-select">
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                    <option value="PATCH">PATCH</option>
                                </select>
                                <input type="text" id="url" class="url-input" placeholder="输入API路径，如: /api/v1/users" value="/api/v1/health">
                            </div>
                        </div>
                        
                        <div class="tabs">
                            <div class="tab active" onclick="switchTab('params', event)">参数</div>
                            <div class="tab" onclick="switchTab('headers', event)">请求头</div>
                            <div class="tab" onclick="switchTab('body', event)">请求体</div>
                            <div class="tab" onclick="switchTab('settings', event)">设置</div>
                        </div>
                        
                        <div id="params-tab" class="tab-content active">
                            <div id="paramsList">
                                <div class="info-message">
                                    <p>📋 点击"加载模板"按钮自动获取API参数信息</p>
                                    <p>或手动输入API路径后点击"分析参数"</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <button type="button" class="btn" onclick="analyzeParameters()">🔍 分析参数</button>
                                <button type="button" class="btn" onclick="clearParameters()">🗑️ 清空参数</button>
                            </div>
                        </div>

                        <div id="headers-tab" class="tab-content">
                            <div class="form-group">
                                <label>请求头 (JSON格式)</label>
                                <textarea id="headers" placeholder='{"Content-Type": "application/json", "Authorization": "Bearer your-token"}'></textarea>
                            </div>
                        </div>
                        
                        <div id="body-tab" class="tab-content">
                            <div class="form-group">
                                <label>请求体 (JSON格式)</label>
                                <textarea id="body" placeholder='{"key": "value"}'></textarea>
                            </div>
                        </div>
                        
                        <div id="settings-tab" class="tab-content">
                            <div class="form-group">
                                <label>描述</label>
                                <input type="text" id="description" placeholder="请求描述">
                            </div>
                            <div class="form-group">
                                <label>标签</label>
                                <input type="text" id="tags" placeholder="标签1,标签2">
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="saveToHistory" checked> 保存到历史记录
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-success">🚀 发送请求</button>
                            <button type="button" class="btn" onclick="loadTemplate()">📋 加载模板</button>
                            <button type="button" class="btn" onclick="clearForm()">🗑️ 清空表单</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 右侧面板 - 响应和历史 -->
            <div class="right-panel">
                <div class="panel-header">
                    📊 响应结果
                </div>
                <div class="panel-content">
                    <div class="tabs">
                        <div class="tab active" onclick="switchRightTab('response', event)">响应</div>
                        <div class="tab" onclick="switchRightTab('history', event)">历史记录</div>
                    </div>
                    
                    <div id="response-tab" class="tab-content active">
                        <div id="responseArea" class="response-area">
                            点击"发送请求"按钮开始调试API...
                        </div>
                    </div>
                    
                    <div id="history-tab" class="tab-content">
                        <div style="margin-bottom: 15px;">
                            <button class="btn" onclick="loadHistory()">🔄 刷新历史</button>
                            <button class="btn btn-danger" onclick="clearHistory()">🗑️ 清空历史</button>
                        </div>
                        <div id="historyArea">
                            <div class="loading">加载历史记录...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let currentRequest = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadHistory();
            loadApiList(); // 加载API端点列表

            // 绑定表单提交事件
            document.getElementById('requestForm').addEventListener('submit', function(e) {
                e.preventDefault();
                sendRequest();
            });
        });
        
        // 切换左侧标签
        function switchTab(tabName, event) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的激活状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 激活选中的标签
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event，通过tabName找到对应的标签
                document.querySelector(` + "`" + `.tab[onclick*="${tabName}"]` + "`" + `).classList.add('active');
            }
        }
        
        // 切换右侧标签
        function switchRightTab(tabName, event) {
            // 隐藏所有标签内容
            document.querySelectorAll('#response-tab, #history-tab').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的激活状态
            document.querySelectorAll('.right-panel .tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 激活选中的标签
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有event，通过tabName找到对应的标签
                document.querySelector(` + "`" + `.right-panel .tab[onclick*="${tabName}"]` + "`" + `).classList.add('active');
            }

            // 如果切换到历史记录标签，加载历史记录
            if (tabName === 'history') {
                loadHistory();
            }
        }
        
        // 发送请求
        async function sendRequest() {
            const method = document.getElementById('method').value;
            const url = document.getElementById('url').value;
            const headersText = document.getElementById('headers').value;
            const bodyText = document.getElementById('body').value;
            const description = document.getElementById('description').value;
            const tagsText = document.getElementById('tags').value;
            const saveToHistory = document.getElementById('saveToHistory').checked;
            
            // 解析请求头
            let headers = {};
            if (headersText.trim()) {
                try {
                    headers = JSON.parse(headersText);
                } catch (e) {
                    showError('请求头格式错误，请使用有效的JSON格式');
                    return;
                }
            }
            
            // 解析请求体
            let body = null;
            if (bodyText.trim() && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
                try {
                    body = JSON.parse(bodyText);
                } catch (e) {
                    showError('请求体格式错误，请使用有效的JSON格式');
                    return;
                }
            }
            
            // 解析标签
            const tags = tagsText.split(',').map(tag => tag.trim()).filter(tag => tag);
            
            const requestData = {
                method,
                url,
                headers,
                body,
                description,
                tags,
                save_to_history: saveToHistory
            };
            
            // 显示加载状态
            showLoading();
            
            try {
                const response = await fetch('/api/v1/debug/request', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResponse(result.data);
                    if (saveToHistory) {
                        loadHistory(); // 刷新历史记录
                    }
                } else {
                    showError(result.message || '请求失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }
        
        // 显示响应
        function showResponse(data) {
            const responseArea = document.getElementById('responseArea');
            const request = data.request;
            
            let html = '';
            
            // 请求信息
            html += '=== 请求信息 ===\\n';
            html += '方法: ' + request.method + '\\n';
            html += 'URL: ' + request.url + '\\n';
            html += '时间: ' + new Date(request.timestamp).toLocaleString() + '\\n';
            html += '耗时: ' + Math.round(request.duration / 1000000) + 'ms\\n\\n';
            
            // 请求头
            if (request.headers && Object.keys(request.headers).length > 0) {
                html += '=== 请求头 ===\\n';
                for (const [key, value] of Object.entries(request.headers)) {
                    html += key + ': ' + value + '\\n';
                }
                html += '\\n';
            }
            
            // 请求体
            if (request.body) {
                html += '=== 请求体 ===\\n';
                html += JSON.stringify(request.body, null, 2) + '\\n\\n';
            }
            
            // 响应信息
            if (request.response) {
                html += '=== 响应信息 ===\\n';
                html += '状态码: ' + request.response.status_code + '\\n';
                html += '响应大小: ' + request.response.size + ' bytes\\n\\n';
                
                // 响应头
                if (request.response.headers && Object.keys(request.response.headers).length > 0) {
                    html += '=== 响应头 ===\\n';
                    for (const [key, values] of Object.entries(request.response.headers)) {
                        html += key + ': ' + (Array.isArray(values) ? values.join(', ') : values) + '\\n';
                    }
                    html += '\\n';
                }
                
                // 响应体
                html += '=== 响应体 ===\\n';
                if (typeof request.response.body === 'object') {
                    html += JSON.stringify(request.response.body, null, 2);
                } else {
                    html += request.response.body;
                }
            } else if (request.error) {
                html += '=== 错误信息 ===\\n';
                html += request.error;
            }
            
            responseArea.textContent = html;
            
            // 切换到响应标签
            switchRightTab('response');
        }
        
        // 显示加载状态
        function showLoading() {
            const responseArea = document.getElementById('responseArea');
            responseArea.textContent = '正在发送请求，请稍候...';
        }
        
        // 显示错误
        function showError(message) {
            const responseArea = document.getElementById('responseArea');
            responseArea.textContent = '错误: ' + message;
        }
        
        // 加载历史记录
        async function loadHistory() {
            const historyArea = document.getElementById('historyArea');
            historyArea.innerHTML = '<div class="loading">加载历史记录...</div>';
            
            try {
                const response = await fetch('/api/v1/debug/history?limit=50');
                const result = await response.json();
                
                if (result.success && result.data.records) {
                    displayHistory(result.data.records);
                } else {
                    historyArea.innerHTML = '<div class="error">加载历史记录失败</div>';
                }
            } catch (error) {
                historyArea.innerHTML = '<div class="error">网络错误: ' + error.message + '</div>';
            }
        }
        
        // 显示历史记录
        function displayHistory(records) {
            const historyArea = document.getElementById('historyArea');
            
            if (records.length === 0) {
                historyArea.innerHTML = '<div class="loading">暂无历史记录</div>';
                return;
            }
            
            let html = '';
            records.forEach(record => {
                const statusClass = record.response ? 
                    (record.response.status_code < 400 ? 'success' : 'error') : 
                    'error';
                
                html += '<div class="history-item" onclick="loadHistoryItem(\'' + record.id + '\')">';
                html += '<div>';
                html += '<span class="history-method method-' + record.method + '">' + record.method + '</span>';
                html += '<span>' + record.url + '</span>';
                html += '</div>';
                html += '<div style="font-size: 12px; color: #7f8c8d; margin-top: 5px;">';
                html += new Date(record.timestamp).toLocaleString();
                if (record.response) {
                    html += ' • 状态码: ' + record.response.status_code;
                }
                html += ' • 耗时: ' + Math.round(record.duration / 1000000) + 'ms';
                html += '</div>';
                if (record.description) {
                    html += '<div style="font-size: 12px; color: #555; margin-top: 3px;">' + record.description + '</div>';
                }
                html += '</div>';
            });
            
            historyArea.innerHTML = html;
        }
        
        // 加载历史记录项
        async function loadHistoryItem(id) {
            try {
                const response = await fetch('/api/v1/debug/history/' + id);
                const result = await response.json();
                
                if (result.success) {
                    const record = result.data;
                    
                    // 填充表单
                    document.getElementById('method').value = record.method;
                    document.getElementById('url').value = record.url.replace(window.location.origin, '');
                    document.getElementById('headers').value = record.headers ? JSON.stringify(record.headers, null, 2) : '';
                    document.getElementById('body').value = record.body ? JSON.stringify(record.body, null, 2) : '';
                    document.getElementById('description').value = record.description || '';
                    document.getElementById('tags').value = record.tags ? record.tags.join(', ') : '';
                    
                    // 显示响应
                    showResponse({request: record});
                } else {
                    showError('加载历史记录失败: ' + result.message);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }
        
        // 清空历史记录
        async function clearHistory() {
            if (!confirm('确定要清空所有历史记录吗？')) {
                return;
            }
            
            try {
                const response = await fetch('/api/v1/debug/history', {
                    method: 'DELETE'
                });
                const result = await response.json();
                
                if (result.success) {
                    loadHistory();
                } else {
                    alert('清空历史记录失败: ' + result.message);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }
        
        // 清空表单
        function clearForm() {
            document.getElementById('method').value = 'GET';
            document.getElementById('url').value = '';
            document.getElementById('headers').value = '';
            document.getElementById('body').value = '';
            document.getElementById('description').value = '';
            document.getElementById('tags').value = '';
            document.getElementById('saveToHistory').checked = true;
            
            const responseArea = document.getElementById('responseArea');
            responseArea.textContent = '点击"发送请求"按钮开始调试API...';
        }
        
        // 加载模板
        async function loadTemplate() {
            const method = document.getElementById('method').value;
            const url = document.getElementById('url').value;
            
            if (!url) {
                alert('请先输入API路径');
                return;
            }
            
            try {
                const response = await fetch('/api/v1/debug/template?method=' + method + '&path=' + encodeURIComponent(url));
                const result = await response.json();
                
                if (result.success) {
                    const template = result.data;

                    // 显示参数信息
                    displayParameters(template.parameters || []);

                    // 填充表单
                    if (template.headers) {
                        document.getElementById('headers').value = JSON.stringify(template.headers, null, 2);
                    }

                    if (template.body_schema) {
                        document.getElementById('body').value = JSON.stringify(template.body_schema, null, 2);
                    }

                    if (template.description) {
                        document.getElementById('description').value = template.description;
                    }

                    alert('模板加载成功！');
                } else {
                    alert('加载模板失败: ' + result.message);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        // 分析参数
        async function analyzeParameters() {
            const method = document.getElementById('method').value;
            const url = document.getElementById('url').value;

            if (!url) {
                alert('请先输入API路径');
                return;
            }

            try {
                const response = await fetch('/api/v1/debug/template?method=' + method + '&path=' + encodeURIComponent(url));
                const result = await response.json();

                if (result.success) {
                    const template = result.data;
                    displayParameters(template.parameters || []);
                } else {
                    alert('分析参数失败: ' + result.message);
                }
            } catch (error) {
                console.error('分析参数失败:', error);
                alert('分析参数失败: ' + error.message);
            }
        }

        // 显示参数
        function displayParameters(parameters) {
            const paramsList = document.getElementById('paramsList');

            // 添加调试信息
            console.log('显示参数:', parameters);

            if (!parameters || parameters.length === 0) {
                paramsList.innerHTML = ` + "`" + `
                    <div class="info-message">
                        <p>📝 此API端点没有参数</p>
                        <p>可以直接发送请求进行测试</p>
                    </div>
                ` + "`" + `;
                return;
            }

            let html = '<h4>📋 API参数详情</h4>';

            parameters.forEach((param, index) => {
                // 添加调试信息
                console.log('处理参数:', index, param);

                const requiredBadge = param.required ?
                    '<span class="param-required">必填</span>' :
                    '<span class="param-optional">可选</span>';

                const typeBadge = '<span class="param-type">' + (param.type || 'string') + '</span>';
                const locationBadge = '<span class="param-location">' + (param.in || 'query') + '</span>';

                const exampleText = param.example ?
                    '<div class="param-example">示例值: ' + param.example + '</div>' : '';

                html += ` + "`" + `
                    <div class="param-item">
                        <div class="param-header">
                            <span class="param-name">` + "${param.name || 'request'}" + `</span>
                            <div>
                                ` + "${requiredBadge}" + `
                                ` + "${typeBadge}" + `
                                ` + "${locationBadge}" + `
                            </div>
                        </div>
                        <div class="param-description">` + "${param.description || '无描述'}" + `</div>
                        <input type="text"
                               class="param-input"
                               id="param_` + "${index}" + `"
                               placeholder="输入` + "${param.name || 'request'}" + `的值"
                               data-param-name="` + "${param.name || 'request'}" + `"
                               data-param-in="` + "${param.in || 'query'}" + `"
                               data-param-required="` + "${param.required || false}" + `">
                        ` + "${exampleText}" + `
                    </div>
                ` + "`" + `;
            });

            html += ` + "`" + `
                <div class="form-group">
                    <button type="button" class="btn btn-success" onclick="applyParameters()">✅ 应用参数到请求</button>
                </div>
            ` + "`" + `;

            paramsList.innerHTML = html;
        }

        // 应用参数到请求
        function applyParameters() {
            const paramInputs = document.querySelectorAll('.param-input');
            const pathParams = {};
            const queryParams = {};
            const bodyParams = {};
            const headerParams = {};

            paramInputs.forEach(input => {
                const value = input.value.trim();
                const paramName = input.dataset.paramName;
                const paramIn = input.dataset.paramIn;
                const isRequired = input.dataset.paramRequired === 'true';

                if (isRequired && !value) {
                    alert('参数 ' + paramName + ' 是必填的，请输入值');
                    input.focus();
                    return;
                }

                if (value) {
                    switch (paramIn) {
                        case 'path':
                            pathParams[paramName] = value;
                            break;
                        case 'query':
                            queryParams[paramName] = value;
                            break;
                        case 'body':
                            bodyParams[paramName] = value;
                            break;
                        case 'header':
                            headerParams[paramName] = value;
                            break;
                    }
                }
            });

            // 更新URL中的路径参数
            let url = document.getElementById('url').value;
            Object.keys(pathParams).forEach(paramName => {
                url = url.replace(':' + paramName, pathParams[paramName]);
                url = url.replace('{' + paramName + '}', pathParams[paramName]);
            });
            document.getElementById('url').value = url;

            // 添加查询参数
            if (Object.keys(queryParams).length > 0) {
                const queryString = new URLSearchParams(queryParams).toString();
                url += (url.includes('?') ? '&' : '?') + queryString;
                document.getElementById('url').value = url;
            }

            // 更新请求头
            if (Object.keys(headerParams).length > 0) {
                try {
                    const existingHeaders = JSON.parse(document.getElementById('headers').value || '{}');
                    const updatedHeaders = { ...existingHeaders, ...headerParams };
                    document.getElementById('headers').value = JSON.stringify(updatedHeaders, null, 2);
                } catch (e) {
                    document.getElementById('headers').value = JSON.stringify(headerParams, null, 2);
                }
            }

            // 更新请求体
            if (Object.keys(bodyParams).length > 0) {
                document.getElementById('body').value = JSON.stringify(bodyParams, null, 2);
            }

            alert('参数已应用到请求中！');
        }

        // 清空参数
        function clearParameters() {
            const paramsList = document.getElementById('paramsList');
            paramsList.innerHTML = ` + "`" + `
                <div class="info-message">
                    <p>📋 点击"加载模板"按钮自动获取API参数信息</p>
                    <p>或手动输入API路径后点击"分析参数"</p>
                </div>
            ` + "`" + `;
        }

        // 加载API端点列表
        async function loadApiList() {
            try {
                const response = await fetch('/api/v1/docs/endpoints');
                const result = await response.json();

                if (result.success && result.data && result.data.endpoints) {
                    const apiTemplate = document.getElementById('apiTemplate');

                    // 清空现有选项
                    apiTemplate.innerHTML = '<option value="">-- 选择API端点模板 --</option>';

                    // 按方法和路径排序
                    const endpoints = result.data.endpoints.sort((a, b) => {
                        if (a.method !== b.method) {
                            return a.method.localeCompare(b.method);
                        }
                        return a.path.localeCompare(b.path);
                    });

                    // 添加端点选项
                    endpoints.forEach(endpoint => {
                        const option = document.createElement('option');
                        option.value = JSON.stringify({
                            method: endpoint.method,
                            path: endpoint.path,
                            summary: endpoint.summary || '',
                            parameters: endpoint.parameters || []
                        });

                        const paramCount = endpoint.parameters ? endpoint.parameters.length : 0;
                        const summary = endpoint.summary || '无描述';
                        option.textContent = ` + "`" + `${endpoint.method} ${endpoint.path}` + "`" + ` + (paramCount > 0 ? ` + "`" + ` (${paramCount}个参数)` + "`" + ` : '');
                        option.title = summary;

                        apiTemplate.appendChild(option);
                    });

                    console.log('API端点列表加载成功，共', endpoints.length, '个端点');
                } else {
                    console.error('加载API端点列表失败:', result.message);
                }
            } catch (error) {
                console.error('加载API端点列表时发生错误:', error);
            }
        }

        // 刷新API列表
        function refreshApiList() {
            loadApiList();
        }

        // 加载选中的模板
        function loadSelectedTemplate() {
            const apiTemplate = document.getElementById('apiTemplate');
            const templateInfo = document.getElementById('templateInfo');
            const templateDescription = document.getElementById('templateDescription');

            if (!apiTemplate.value) {
                templateInfo.style.display = 'none';
                return;
            }

            try {
                const template = JSON.parse(apiTemplate.value);

                // 更新方法和路径
                document.getElementById('method').value = template.method;
                document.getElementById('url').value = template.path;

                // 显示模板描述
                if (template.summary) {
                    templateDescription.textContent = template.summary;
                    templateInfo.style.display = 'block';
                } else {
                    templateInfo.style.display = 'none';
                }

                // 自动加载参数信息
                if (template.parameters && template.parameters.length > 0) {
                    displayParameters(template.parameters);
                } else {
                    // 尝试从服务器获取参数信息
                    analyzeParameters();
                }

                console.log('模板加载成功:', template.method, template.path);
            } catch (error) {
                console.error('解析模板数据失败:', error);
                templateInfo.style.display = 'none';
            }
        }
    </script>
</body>
</html>`
}

// 响应结构体

// HistoryResponse 历史记录响应
type HistoryResponse struct {
	Records    []debug.RequestRecord `json:"records"`
	Pagination PaginationInfo        `json:"pagination"`
}
