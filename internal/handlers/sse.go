package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
)

// SSEWriter Server-Sent Events写入器
// 实现StreamWriter接口，用于向客户端发送流式数据
type SSEWriter struct {
	ctx      *gin.Context
	writer   gin.ResponseWriter
	flusher  http.Flusher
	logger   logger.Logger
	closed   bool
	doneChan chan struct{}
}

// NewSSEWriter 创建SSE写入器
func NewSSEWriter(c *gin.Context, logger logger.Logger) (*SSEWriter, error) {
	// 检查是否支持流式响应
	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		return nil, fmt.Errorf("流式响应不被支持")
	}

	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.<PERSON>er("Cache-Control", "no-cache")
	c.<PERSON>("Connection", "keep-alive")
	c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
	c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Cache-Control")

	writer := &SSEWriter{
		ctx:      c,
		writer:   c.Writer,
		flusher:  flusher,
		logger:   logger,
		closed:   false,
		doneChan: make(chan struct{}),
	}

	return writer, nil
}

// WriteChunk 写入流式数据块
func (w *SSEWriter) WriteChunk(chunk *models.StreamChunk) error {
	if w.closed {
		return fmt.Errorf("SSE连接已关闭")
	}

	// 将数据块序列化为JSON
	data, err := json.Marshal(chunk)
	if err != nil {
		w.logger.Error("序列化流式数据失败", "error", err)
		return fmt.Errorf("序列化数据失败: %w", err)
	}

	// 写入SSE格式的数据
	if _, err := fmt.Fprintf(w.writer, "data: %s\n\n", string(data)); err != nil {
		w.logger.Error("写入SSE数据失败", "error", err)
		return fmt.Errorf("写入数据失败: %w", err)
	}

	// 立即刷新缓冲区
	w.flusher.Flush()

	w.logger.Debug("SSE数据块发送成功", "chunk_id", chunk.ID, "content_length", len(chunk.Choices[0].Delta.Content))
	return nil
}

// WriteEvent 写入自定义事件
func (w *SSEWriter) WriteEvent(eventType string, data interface{}) error {
	if w.closed {
		return fmt.Errorf("SSE连接已关闭")
	}

	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化事件数据失败: %w", err)
	}

	// 写入SSE事件格式
	if _, err := fmt.Fprintf(w.writer, "event: %s\ndata: %s\n\n", eventType, string(jsonData)); err != nil {
		return fmt.Errorf("写入事件失败: %w", err)
	}

	w.flusher.Flush()
	return nil
}

// WriteError 写入错误信息
func (w *SSEWriter) WriteError(errorMsg string) error {
	if w.closed {
		return fmt.Errorf("SSE连接已关闭")
	}

	errorData := map[string]interface{}{
		"error":     errorMsg,
		"timestamp": time.Now().Unix(),
	}

	return w.WriteEvent("error", errorData)
}

// WriteDone 写入完成标记
func (w *SSEWriter) WriteDone() error {
	if w.closed {
		return fmt.Errorf("SSE连接已关闭")
	}

	// 写入标准的完成标记
	if _, err := fmt.Fprintf(w.writer, "data: [DONE]\n\n"); err != nil {
		return fmt.Errorf("写入完成标记失败: %w", err)
	}

	w.flusher.Flush()
	w.logger.Debug("SSE完成标记发送成功")
	return nil
}

// Close 关闭SSE连接
func (w *SSEWriter) Close() error {
	if w.closed {
		return nil
	}

	w.closed = true
	close(w.doneChan)

	// 发送完成标记
	if err := w.WriteDone(); err != nil {
		w.logger.Warn("发送完成标记失败", "error", err)
	}

	w.logger.Debug("SSE连接已关闭")
	return nil
}

// IsClosed 检查连接是否已关闭
func (w *SSEWriter) IsClosed() bool {
	return w.closed
}

// Done 返回完成通道
func (w *SSEWriter) Done() <-chan struct{} {
	return w.doneChan
}

// SSEHandler SSE处理器
type SSEHandler struct {
	logger logger.Logger
}

// NewSSEHandler 创建SSE处理器
func NewSSEHandler(logger logger.Logger) *SSEHandler {
	return &SSEHandler{
		logger: logger,
	}
}

// HandleSSEConnection 处理SSE连接
func (h *SSEHandler) HandleSSEConnection(c *gin.Context, handler func(*SSEWriter) error) {
	h.logger.Info("建立SSE连接", "client_ip", c.ClientIP(), "user_agent", c.GetHeader("User-Agent"))

	// 创建SSE写入器
	writer, err := NewSSEWriter(c, h.logger)
	if err != nil {
		h.logger.Error("创建SSE写入器失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "不支持流式响应",
		})
		return
	}

	// 设置连接超时
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Minute)
	defer cancel()

	// 监听客户端断开连接
	go func() {
		select {
		case <-ctx.Done():
			h.logger.Info("SSE连接超时")
			writer.Close()
		case <-c.Request.Context().Done():
			h.logger.Info("客户端断开SSE连接")
			writer.Close()
		case <-writer.Done():
			h.logger.Info("SSE连接正常关闭")
		}
	}()

	// 发送连接建立事件
	if err := writer.WriteEvent("connected", map[string]interface{}{
		"message":   "SSE连接已建立",
		"timestamp": time.Now().Unix(),
	}); err != nil {
		h.logger.Error("发送连接事件失败", "error", err)
		return
	}

	// 执行业务处理逻辑
	if err := handler(writer); err != nil {
		h.logger.Error("SSE处理器执行失败", "error", err)
		writer.WriteError(fmt.Sprintf("处理失败: %v", err))
	}

	// 确保连接关闭
	writer.Close()
	h.logger.Info("SSE连接处理完成")
}

// SendHeartbeat 发送心跳包
func (w *SSEWriter) SendHeartbeat() error {
	return w.WriteEvent("heartbeat", map[string]interface{}{
		"timestamp": time.Now().Unix(),
		"status":    "alive",
	})
}

// StartHeartbeat 启动心跳包发送
func (w *SSEWriter) StartHeartbeat(interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	go func() {
		for {
			select {
			case <-ticker.C:
				if w.closed {
					return
				}
				if err := w.SendHeartbeat(); err != nil {
					w.logger.Warn("发送心跳包失败", "error", err)
					return
				}
			case <-w.doneChan:
				return
			}
		}
	}()
}

// ValidateChunk 验证流式数据块的格式
func ValidateChunk(chunk *models.StreamChunk) error {
	if chunk == nil {
		return fmt.Errorf("数据块不能为空")
	}

	if chunk.ID == "" {
		return fmt.Errorf("数据块ID不能为空")
	}

	if chunk.Object == "" {
		return fmt.Errorf("数据块对象类型不能为空")
	}

	if len(chunk.Choices) == 0 {
		return fmt.Errorf("数据块必须包含至少一个选择项")
	}

	return nil
}

// FormatSSEData 格式化SSE数据
func FormatSSEData(data interface{}) (string, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("序列化数据失败: %w", err)
	}

	return fmt.Sprintf("data: %s\n\n", string(jsonData)), nil
}
