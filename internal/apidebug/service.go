package apidebug

import (
	"context"
	"fmt"
	"time"

	"ai-text-game-iam-npc/internal/apidoc"
	"ai-text-game-iam-npc/internal/debug"
	"ai-text-game-iam-npc/pkg/logger"
)

// Service API调试系统服务
type Service struct {
	apiDocService *apidoc.Service
	debugService  *debug.Service
	logger        logger.Logger
	config        Config
}

// Config 服务配置
type Config struct {
	// API文档配置
	ProjectRoot     string
	OutputDir       string
	CacheEnabled    bool
	CacheTTL        time.Duration
	AutoRefresh     bool
	RefreshInterval time.Duration
	
	// 调试配置
	BaseURL         string
	RequestTimeout  time.Duration
	MaxHistorySize  int
	DefaultHeaders  map[string]string
	
	// 生成器配置
	APITitle       string
	APIDescription string
	APIVersion     string
	ServerURL      string
	ContactName    string
	ContactEmail   string
}

// NewService 创建新的API调试系统服务
func NewService(config Config, logger logger.Logger) (*Service, error) {
	// 验证配置
	if err := validateConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}
	
	// 创建日志适配器
	apiDocLogger := apidoc.NewLoggerAdapter(logger)
	
	// 创建API文档服务
	apiDocConfig := apidoc.ServiceConfig{
		ProjectRoot:     config.ProjectRoot,
		CacheEnabled:    config.CacheEnabled,
		CacheTTL:        config.CacheTTL,
		AutoRefresh:     config.AutoRefresh,
		RefreshInterval: config.RefreshInterval,
		OutputDir:       config.OutputDir,
		GeneratorConfig: apidoc.GeneratorConfig{
			Title:             config.APITitle,
			Description:       config.APIDescription,
			Version:           config.APIVersion,
			ServerURL:         config.ServerURL,
			ServerDescription: "开发环境服务器",
			ContactName:       config.ContactName,
			ContactEmail:      config.ContactEmail,
		},
	}
	
	apiDocService := apidoc.NewService(apiDocConfig, apiDocLogger)
	
	// 创建调试服务
	debugConfig := debug.ServiceConfig{
		BaseURL:        config.BaseURL,
		Timeout:        config.RequestTimeout,
		MaxHistorySize: config.MaxHistorySize,
		DefaultHeaders: config.DefaultHeaders,
	}
	
	debugService := debug.NewService(apiDocService, debugConfig, logger)
	
	service := &Service{
		apiDocService: apiDocService,
		debugService:  debugService,
		logger:        logger,
		config:        config,
	}
	
	logger.Info("API调试系统服务初始化完成", 
		"project_root", config.ProjectRoot,
		"base_url", config.BaseURL,
		"cache_enabled", config.CacheEnabled)
	
	return service, nil
}

// GetAPIDocService 获取API文档服务
func (s *Service) GetAPIDocService() *apidoc.Service {
	return s.apiDocService
}

// GetDebugService 获取调试服务
func (s *Service) GetDebugService() *debug.Service {
	return s.debugService
}

// Initialize 初始化服务
func (s *Service) Initialize(ctx context.Context) error {
	s.logger.Info("初始化API调试系统")
	
	// 生成初始API文档
	_, err := s.apiDocService.GenerateDocumentation(ctx)
	if err != nil {
		s.logger.Error("生成初始API文档失败", "error", err)
		return fmt.Errorf("生成初始API文档失败: %w", err)
	}
	
	s.logger.Info("API调试系统初始化完成")
	return nil
}

// GetSystemStatus 获取系统状态
func (s *Service) GetSystemStatus(ctx context.Context) (*SystemStatus, error) {
	// 获取文档统计
	docStats, err := s.apiDocService.GetDocumentationStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取文档统计失败: %w", err)
	}
	
	// 获取调试历史统计
	_, historyTotal, err := s.debugService.GetHistory(ctx, 1, 0)
	if err != nil {
		return nil, fmt.Errorf("获取历史统计失败: %w", err)
	}
	
	status := &SystemStatus{
		ServiceName:     "API调试系统",
		Version:         s.config.APIVersion,
		Status:          "运行中",
		StartTime:       time.Now(), // 这里应该记录实际启动时间
		Documentation: DocumentationStatus{
			TotalEndpoints: docStats.TotalEndpoints,
			MethodCounts:   docStats.MethodCounts,
			TagCounts:      docStats.TagCounts,
			LastUpdated:    docStats.LastUpdated,
		},
		Debug: DebugStatus{
			TotalRequests:  historyTotal,
			CacheEnabled:   s.config.CacheEnabled,
			MaxHistorySize: s.config.MaxHistorySize,
		},
		Configuration: ConfigurationStatus{
			ProjectRoot:     s.config.ProjectRoot,
			BaseURL:         s.config.BaseURL,
			OutputDir:       s.config.OutputDir,
			AutoRefresh:     s.config.AutoRefresh,
			RefreshInterval: s.config.RefreshInterval,
		},
	}
	
	return status, nil
}

// SystemStatus 系统状态
type SystemStatus struct {
	ServiceName   string                `json:"service_name"`
	Version       string                `json:"version"`
	Status        string                `json:"status"`
	StartTime     time.Time             `json:"start_time"`
	Documentation DocumentationStatus   `json:"documentation"`
	Debug         DebugStatus           `json:"debug"`
	Configuration ConfigurationStatus   `json:"configuration"`
}

// DocumentationStatus 文档状态
type DocumentationStatus struct {
	TotalEndpoints int               `json:"total_endpoints"`
	MethodCounts   map[string]int    `json:"method_counts"`
	TagCounts      map[string]int    `json:"tag_counts"`
	LastUpdated    time.Time         `json:"last_updated"`
}

// DebugStatus 调试状态
type DebugStatus struct {
	TotalRequests  int  `json:"total_requests"`
	CacheEnabled   bool `json:"cache_enabled"`
	MaxHistorySize int  `json:"max_history_size"`
}

// ConfigurationStatus 配置状态
type ConfigurationStatus struct {
	ProjectRoot     string        `json:"project_root"`
	BaseURL         string        `json:"base_url"`
	OutputDir       string        `json:"output_dir"`
	AutoRefresh     bool          `json:"auto_refresh"`
	RefreshInterval time.Duration `json:"refresh_interval"`
}

// DefaultConfig 默认配置
func DefaultConfig() Config {
	return Config{
		// API文档配置
		ProjectRoot:     ".",
		OutputDir:       "docs/api",
		CacheEnabled:    true,
		CacheTTL:        5 * time.Minute,
		AutoRefresh:     false,
		RefreshInterval: 10 * time.Minute,
		
		// 调试配置
		BaseURL:        "http://localhost:8080",
		RequestTimeout: 30 * time.Second,
		MaxHistorySize: 1000,
		DefaultHeaders: map[string]string{
			"User-Agent": "API-Debug-Tool/1.0",
		},
		
		// 生成器配置
		APITitle:       "AI文本游戏API",
		APIDescription: "AI文本游戏后端API调试系统",
		APIVersion:     "1.0.0",
		ServerURL:      "http://localhost:8080",
		ContactName:    "开发团队",
		ContactEmail:   "<EMAIL>",
	}
}

// validateConfig 验证配置
func validateConfig(config Config) error {
	if config.ProjectRoot == "" {
		return fmt.Errorf("项目根目录不能为空")
	}
	
	if config.BaseURL == "" {
		return fmt.Errorf("基础URL不能为空")
	}
	
	if config.RequestTimeout <= 0 {
		return fmt.Errorf("请求超时时间必须大于0")
	}
	
	if config.MaxHistorySize <= 0 {
		return fmt.Errorf("最大历史记录数必须大于0")
	}
	
	if config.APITitle == "" {
		return fmt.Errorf("API标题不能为空")
	}
	
	if config.APIVersion == "" {
		return fmt.Errorf("API版本不能为空")
	}
	
	if config.CacheTTL < 0 {
		return fmt.Errorf("缓存TTL不能为负数")
	}
	
	if config.RefreshInterval < time.Minute {
		return fmt.Errorf("刷新间隔不能少于1分钟")
	}
	
	return nil
}

// GetConfig 获取配置
func (s *Service) GetConfig() Config {
	return s.config
}

// UpdateConfig 更新配置
func (s *Service) UpdateConfig(ctx context.Context, newConfig Config) error {
	if err := validateConfig(newConfig); err != nil {
		return fmt.Errorf("新配置验证失败: %w", err)
	}
	
	s.config = newConfig
	s.logger.Info("配置已更新")
	
	// 如果需要，可以在这里重新初始化服务
	return nil
}

// Shutdown 关闭服务
func (s *Service) Shutdown(ctx context.Context) error {
	s.logger.Info("正在关闭API调试系统服务")
	
	// 这里可以添加清理逻辑
	// 例如：保存缓存、关闭连接等
	
	s.logger.Info("API调试系统服务已关闭")
	return nil
}
