package validation

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"ai-text-game-iam-npc/internal/auth"

	"github.com/gin-gonic/gin"
)

// ValidationMiddleware 校验中间件配置
type ValidationMiddleware struct {
	service *ValidationService
	config  *MiddlewareConfig
}

// MiddlewareConfig 中间件配置
type MiddlewareConfig struct {
	// 需要校验的路径
	ValidatedPaths []string `json:"validated_paths"`

	// 需要校验的字段映射
	FieldMappings map[string]string `json:"field_mappings"`

	// 跳过校验的路径
	SkipPaths []string `json:"skip_paths"`

	// 是否阻止无效请求
	BlockInvalidRequests bool `json:"block_invalid_requests"`

	// 是否记录校验日志
	LogValidation bool `json:"log_validation"`
}

// NewValidationMiddleware 创建校验中间件
func NewValidationMiddleware(service *ValidationService, config *MiddlewareConfig) *ValidationMiddleware {
	if config == nil {
		config = getDefaultMiddlewareConfig()
	}

	return &ValidationMiddleware{
		service: service,
		config:  config,
	}
}

// getDefaultMiddlewareConfig 获取默认中间件配置
func getDefaultMiddlewareConfig() *MiddlewareConfig {
	return &MiddlewareConfig{
		ValidatedPaths: []string{
			"/api/v1/game/worlds",
			"/api/v1/game/characters",
			"/api/v1/game/scenes",
			"/api/v1/game/events",
			"/api/v1/ai/generate",
		},
		FieldMappings: map[string]string{
			"name":        "name",
			"description": "description",
			"content":     "content",
			"text":        "text",
			"message":     "message",
		},
		SkipPaths: []string{
			"/api/v1/auth",
			"/api/v1/validation",
			"/health",
		},
		BlockInvalidRequests: true,
		LogValidation:        true,
	}
}

// Middleware 返回Gin中间件函数
func (vm *ValidationMiddleware) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否需要跳过校验
		if vm.shouldSkipValidation(c) {
			c.Next()
			return
		}

		// 检查是否需要校验此路径
		if !vm.shouldValidatePath(c) {
			c.Next()
			return
		}

		// 只对POST和PUT请求进行校验
		if c.Request.Method != http.MethodPost && c.Request.Method != http.MethodPut {
			c.Next()
			return
		}

		// 读取请求体
		body, err := vm.readRequestBody(c)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "读取请求体失败",
				"error":   err.Error(),
			})
			c.Abort()
			return
		}

		// 解析JSON
		var requestData map[string]interface{}
		if err := json.Unmarshal(body, &requestData); err != nil {
			// 如果不是JSON格式，跳过校验
			c.Next()
			return
		}

		// 获取用户ID
		userID, exists := auth.GetCurrentUserID(c)
		if !exists {
			// 如果没有用户ID，跳过校验（让认证中间件处理）
			c.Next()
			return
		}

		// 校验请求中的文本字段
		validationErrors := []string{}
		validationWarnings := []string{}

		for fieldName, fieldValue := range requestData {
			if vm.shouldValidateField(fieldName) {
				if textValue, ok := fieldValue.(string); ok && textValue != "" {
					// 确定内容类型
					contentType := vm.getContentType(c.Request.URL.Path, fieldName)

					// 创建校验请求
					validationReq := &ValidationRequest{
						UserID:      userID,
						ContentType: contentType,
						Text:        textValue,
						Context: map[string]interface{}{
							"field_name": fieldName,
							"path":       c.Request.URL.Path,
							"method":     c.Request.Method,
						},
					}

					// 执行校验
					result, err := vm.service.ValidateContent(c.Request.Context(), validationReq)
					if err != nil {
						if vm.config.LogValidation {
							vm.service.logger.Error("中间件校验失败", "error", err, "field", fieldName)
						}
						continue
					}

					// 收集错误和警告
					validationErrors = append(validationErrors, result.Errors...)
					validationWarnings = append(validationWarnings, result.Warnings...)

					// 如果文本被过滤，更新请求数据
					if result.FilteredText != textValue {
						requestData[fieldName] = result.FilteredText
					}
				}
			}
		}

		// 如果有校验错误且配置为阻止无效请求
		if len(validationErrors) > 0 && vm.config.BlockInvalidRequests {
			c.JSON(http.StatusBadRequest, gin.H{
				"success":  false,
				"message":  "内容校验失败",
				"errors":   validationErrors,
				"warnings": validationWarnings,
			})
			c.Abort()
			return
		}

		// 如果有警告，添加到响应头
		if len(validationWarnings) > 0 {
			c.Header("X-Validation-Warnings", strings.Join(validationWarnings, "; "))
		}

		// 如果文本被修改，更新请求体
		if vm.hasModifiedText(requestData, body) {
			modifiedBody, err := json.Marshal(requestData)
			if err == nil {
				c.Request.Body = io.NopCloser(bytes.NewReader(modifiedBody))
				c.Request.ContentLength = int64(len(modifiedBody))
			}
		}

		c.Next()
	}
}

// shouldSkipValidation 检查是否应该跳过校验
func (vm *ValidationMiddleware) shouldSkipValidation(c *gin.Context) bool {
	path := c.Request.URL.Path

	for _, skipPath := range vm.config.SkipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}

	return false
}

// shouldValidatePath 检查是否应该校验此路径
func (vm *ValidationMiddleware) shouldValidatePath(c *gin.Context) bool {
	path := c.Request.URL.Path

	for _, validatedPath := range vm.config.ValidatedPaths {
		if strings.HasPrefix(path, validatedPath) {
			return true
		}
	}

	return false
}

// shouldValidateField 检查是否应该校验此字段
func (vm *ValidationMiddleware) shouldValidateField(fieldName string) bool {
	_, exists := vm.config.FieldMappings[fieldName]
	return exists
}

// getContentType 根据路径和字段名确定内容类型
func (vm *ValidationMiddleware) getContentType(path, fieldName string) string {
	// 根据路径确定基础类型
	var baseType string

	switch {
	case strings.Contains(path, "/worlds"):
		baseType = "world"
	case strings.Contains(path, "/characters"):
		baseType = "character"
	case strings.Contains(path, "/scenes"):
		baseType = "scene"
	case strings.Contains(path, "/events"):
		baseType = "event"
	case strings.Contains(path, "/ai/generate"):
		baseType = "ai_content"
	default:
		baseType = "general"
	}

	// 根据字段名确定具体类型
	switch fieldName {
	case "name":
		return baseType + "_name"
	case "description":
		return baseType + "_description"
	case "content", "text":
		return baseType + "_content"
	case "message":
		return "message"
	default:
		return baseType + "_" + fieldName
	}
}

// readRequestBody 读取请求体
func (vm *ValidationMiddleware) readRequestBody(c *gin.Context) ([]byte, error) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return nil, err
	}

	// 重新设置请求体，以便后续处理器可以读取
	c.Request.Body = io.NopCloser(bytes.NewReader(body))

	return body, nil
}

// hasModifiedText 检查文本是否被修改
func (vm *ValidationMiddleware) hasModifiedText(requestData map[string]interface{}, originalBody []byte) bool {
	// 简单的检查方法：重新序列化并比较
	modifiedBody, err := json.Marshal(requestData)
	if err != nil {
		return false
	}

	return !bytes.Equal(originalBody, modifiedBody)
}

// ContentValidationMiddleware 内容校验中间件（简化版本）
func ContentValidationMiddleware(service *ValidationService) gin.HandlerFunc {
	middleware := NewValidationMiddleware(service, nil)
	return middleware.Middleware()
}

// CustomValidationMiddleware 自定义校验中间件
func CustomValidationMiddleware(service *ValidationService, config *MiddlewareConfig) gin.HandlerFunc {
	middleware := NewValidationMiddleware(service, config)
	return middleware.Middleware()
}
