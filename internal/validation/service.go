package validation

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ValidationService 内容校验服务
type ValidationService struct {
	db     *gorm.DB
	logger logger.Logger
	config *Config
}

// Config 校验配置
type Config struct {
	// 文本长度限制
	MaxTextLength int `json:"max_text_length"`
	MinTextLength int `json:"min_text_length"`

	// 敏感词检测
	EnableProfanityFilter bool     `json:"enable_profanity_filter"`
	ProfanityWords        []string `json:"profanity_words"`

	// 内容类型限制
	AllowedContentTypes []string `json:"allowed_content_types"`

	// 频率限制
	MaxRequestsPerMinute int `json:"max_requests_per_minute"`
	MaxRequestsPerHour   int `json:"max_requests_per_hour"`

	// AI内容审核
	EnableAIModeration bool `json:"enable_ai_moderation"`

	// 安全检查
	EnableSecurityCheck bool `json:"enable_security_check"`
}

// ValidationResult 校验结果
type ValidationResult struct {
	IsValid      bool                   `json:"is_valid"`
	Errors       []string               `json:"errors"`
	Warnings     []string               `json:"warnings"`
	FilteredText string                 `json:"filtered_text"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// ValidationRequest 校验请求
type ValidationRequest struct {
	UserID      uuid.UUID              `json:"user_id"`
	ContentType string                 `json:"content_type"`
	Text        string                 `json:"text"`
	Context     map[string]interface{} `json:"context"`
}

// NewValidationService 创建校验服务
func NewValidationService(db *gorm.DB, log logger.Logger, config *Config) *ValidationService {
	if config == nil {
		config = getDefaultConfig()
	}

	return &ValidationService{
		db:     db,
		logger: log,
		config: config,
	}
}

// getDefaultConfig 获取默认配置
func getDefaultConfig() *Config {
	return &Config{
		MaxTextLength:         10000,
		MinTextLength:         1,
		EnableProfanityFilter: true,
		ProfanityWords: []string{
			// 基础敏感词列表（实际使用时应该从配置文件或数据库加载）
			"fuck", "shit", "damn", "bitch", "asshole",
			"傻逼", "操你妈", "去死", "滚蛋", "白痴",
		},
		AllowedContentTypes: []string{
			"character_name", "character_description", "world_name", "world_description",
			"scene_name", "scene_description", "dialogue", "action", "event_description",
		},
		MaxRequestsPerMinute: 60,
		MaxRequestsPerHour:   1000,
		EnableAIModeration:   false, // 在测试环境中禁用AI审核
		EnableSecurityCheck:  true,
	}
}

// ValidateContent 校验内容
func (s *ValidationService) ValidateContent(ctx context.Context, req *ValidationRequest) (*ValidationResult, error) {
	result := &ValidationResult{
		IsValid:      true,
		Errors:       []string{},
		Warnings:     []string{},
		FilteredText: req.Text,
		Metadata:     make(map[string]interface{}),
	}

	// 1. 基础校验
	if err := s.validateBasic(req, result); err != nil {
		return result, err
	}

	// 2. 长度校验
	s.validateLength(req, result)

	// 3. 内容类型校验
	s.validateContentType(req, result)

	// 4. 敏感词过滤
	if s.config.EnableProfanityFilter {
		s.filterProfanity(req, result)
	}

	// 5. 安全检查
	if s.config.EnableSecurityCheck {
		s.performSecurityCheck(req, result)
	}

	// 6. 频率限制检查
	if err := s.checkRateLimit(ctx, req, result); err != nil {
		return result, err
	}

	// 7. AI内容审核（如果启用）
	if s.config.EnableAIModeration {
		if err := s.performAIModeration(ctx, req, result); err != nil {
			s.logger.Warn("AI内容审核失败", "error", err, "user_id", req.UserID)
			result.Warnings = append(result.Warnings, "AI内容审核暂时不可用")
		}
	}

	// 8. 记录校验日志
	s.logValidation(req, result)

	// 如果有错误，标记为无效
	if len(result.Errors) > 0 {
		result.IsValid = false
	}

	return result, nil
}

// validateBasic 基础校验
func (s *ValidationService) validateBasic(req *ValidationRequest, result *ValidationResult) error {
	if req.UserID == uuid.Nil {
		result.Errors = append(result.Errors, "用户ID不能为空")
	}

	if req.ContentType == "" {
		result.Errors = append(result.Errors, "内容类型不能为空")
	}

	if req.Text == "" {
		result.Errors = append(result.Errors, "文本内容不能为空")
	}

	return nil
}

// validateLength 长度校验
func (s *ValidationService) validateLength(req *ValidationRequest, result *ValidationResult) {
	textLength := len([]rune(req.Text))

	if textLength < s.config.MinTextLength {
		result.Errors = append(result.Errors, fmt.Sprintf("文本长度不能少于%d个字符", s.config.MinTextLength))
	}

	if textLength > s.config.MaxTextLength {
		result.Errors = append(result.Errors, fmt.Sprintf("文本长度不能超过%d个字符", s.config.MaxTextLength))
	}

	result.Metadata["text_length"] = textLength
}

// validateContentType 内容类型校验
func (s *ValidationService) validateContentType(req *ValidationRequest, result *ValidationResult) {
	isValidType := false
	for _, allowedType := range s.config.AllowedContentTypes {
		if req.ContentType == allowedType {
			isValidType = true
			break
		}
	}

	if !isValidType {
		result.Errors = append(result.Errors, fmt.Sprintf("不支持的内容类型: %s", req.ContentType))
	}
}

// filterProfanity 敏感词过滤
func (s *ValidationService) filterProfanity(req *ValidationRequest, result *ValidationResult) {
	filteredText := req.Text
	foundProfanity := false

	for _, word := range s.config.ProfanityWords {
		if strings.Contains(strings.ToLower(filteredText), strings.ToLower(word)) {
			foundProfanity = true
			// 用星号替换敏感词
			replacement := strings.Repeat("*", len([]rune(word)))
			re := regexp.MustCompile("(?i)" + regexp.QuoteMeta(word))
			filteredText = re.ReplaceAllString(filteredText, replacement)
		}
	}

	if foundProfanity {
		result.Warnings = append(result.Warnings, "检测到敏感词，已自动过滤")
		result.FilteredText = filteredText
		result.Metadata["profanity_filtered"] = true
	}
}

// performSecurityCheck 安全检查
func (s *ValidationService) performSecurityCheck(req *ValidationRequest, result *ValidationResult) {
	// 检查SQL注入
	sqlPatterns := []string{
		`(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute)`,
		`(?i)(script|javascript|vbscript|onload|onerror|onclick)`,
		`(?i)(<script|</script|<iframe|</iframe)`,
	}

	for _, pattern := range sqlPatterns {
		if matched, _ := regexp.MatchString(pattern, req.Text); matched {
			result.Errors = append(result.Errors, "检测到潜在的安全威胁")
			result.Metadata["security_threat"] = true
			break
		}
	}

	// 检查XSS攻击
	xssPatterns := []string{
		`<[^>]*script[^>]*>`,
		`javascript:`,
		`on\w+\s*=`,
	}

	for _, pattern := range xssPatterns {
		if matched, _ := regexp.MatchString(pattern, req.Text); matched {
			result.Warnings = append(result.Warnings, "检测到可能的XSS攻击模式")
			result.Metadata["xss_risk"] = true
			break
		}
	}
}

// checkRateLimit 检查频率限制
func (s *ValidationService) checkRateLimit(ctx context.Context, req *ValidationRequest, result *ValidationResult) error {
	// 这里应该使用Redis来实现分布式频率限制
	// 为了简化，这里只做基本的数据库查询检查

	now := time.Now()
	oneMinuteAgo := now.Add(-time.Minute)
	oneHourAgo := now.Add(-time.Hour)

	// 检查每分钟请求数
	var minuteCount int64
	err := s.db.WithContext(ctx).
		Table("validation_logs").
		Where("user_id = ? AND created_at > ?", req.UserID, oneMinuteAgo).
		Count(&minuteCount).Error

	if err != nil {
		s.logger.Error("检查分钟频率限制失败", "error", err, "user_id", req.UserID)
		return fmt.Errorf("频率限制检查失败: %w", err)
	}

	if minuteCount >= int64(s.config.MaxRequestsPerMinute) {
		result.Errors = append(result.Errors, "请求过于频繁，请稍后再试")
		result.Metadata["rate_limit_exceeded"] = "minute"
		return nil
	}

	// 检查每小时请求数
	var hourCount int64
	err = s.db.WithContext(ctx).
		Table("validation_logs").
		Where("user_id = ? AND created_at > ?", req.UserID, oneHourAgo).
		Count(&hourCount).Error

	if err != nil {
		s.logger.Error("检查小时频率限制失败", "error", err, "user_id", req.UserID)
		return fmt.Errorf("频率限制检查失败: %w", err)
	}

	if hourCount >= int64(s.config.MaxRequestsPerHour) {
		result.Errors = append(result.Errors, "小时请求次数已达上限")
		result.Metadata["rate_limit_exceeded"] = "hour"
		return nil
	}

	result.Metadata["requests_this_minute"] = minuteCount
	result.Metadata["requests_this_hour"] = hourCount

	return nil
}

// performAIModeration AI内容审核（模拟实现）
func (s *ValidationService) performAIModeration(ctx context.Context, req *ValidationRequest, result *ValidationResult) error {
	// 在测试环境中使用模拟逻辑
	s.logger.Info("执行AI内容审核（模拟）", "user_id", req.UserID, "content_type", req.ContentType)

	// 模拟AI审核逻辑
	textLength := len([]rune(req.Text))

	// 模拟检测规则
	if textLength > 1000 {
		result.Warnings = append(result.Warnings, "内容较长，建议简化")
	}

	// 模拟情感分析
	negativeWords := []string{"hate", "angry", "sad", "terrible", "awful", "恨", "愤怒", "悲伤", "糟糕"}
	negativeCount := 0
	for _, word := range negativeWords {
		if strings.Contains(strings.ToLower(req.Text), strings.ToLower(word)) {
			negativeCount++
		}
	}

	if negativeCount > 2 {
		result.Warnings = append(result.Warnings, "检测到较多负面情绪词汇")
		result.Metadata["sentiment"] = "negative"
	} else {
		result.Metadata["sentiment"] = "neutral"
	}

	result.Metadata["ai_moderation_completed"] = true
	return nil
}

// logValidation 记录校验日志
func (s *ValidationService) logValidation(req *ValidationRequest, result *ValidationResult) {
	// 创建校验日志记录
	log := ValidationLog{
		UserID:       req.UserID,
		ContentType:  req.ContentType,
		TextLength:   len([]rune(req.Text)),
		IsValid:      result.IsValid,
		ErrorCount:   len(result.Errors),
		WarningCount: len(result.Warnings),
		Metadata:     result.Metadata,
	}

	// 异步保存日志
	go func() {
		if err := s.db.Create(&log).Error; err != nil {
			s.logger.Error("保存校验日志失败", "error", err, "user_id", req.UserID)
		}
	}()

	s.logger.Info("内容校验完成",
		"user_id", req.UserID,
		"content_type", req.ContentType,
		"is_valid", result.IsValid,
		"errors", len(result.Errors),
		"warnings", len(result.Warnings),
	)
}

// GetValidationStats 获取校验统计信息
func (s *ValidationService) GetValidationStats(ctx context.Context, userID uuid.UUID, days int) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	startTime := time.Now().AddDate(0, 0, -days)

	// 总请求数
	var totalRequests int64
	err := s.db.WithContext(ctx).
		Model(&ValidationLog{}).
		Where("user_id = ? AND created_at > ?", userID, startTime).
		Count(&totalRequests).Error

	if err != nil {
		return nil, fmt.Errorf("获取总请求数失败: %w", err)
	}

	// 有效请求数
	var validRequests int64
	err = s.db.WithContext(ctx).
		Model(&ValidationLog{}).
		Where("user_id = ? AND created_at > ? AND is_valid = ?", userID, startTime, true).
		Count(&validRequests).Error

	if err != nil {
		return nil, fmt.Errorf("获取有效请求数失败: %w", err)
	}

	// 按内容类型统计
	var typeStats []struct {
		ContentType string `json:"content_type"`
		Count       int64  `json:"count"`
	}

	err = s.db.WithContext(ctx).
		Model(&ValidationLog{}).
		Select("content_type, COUNT(*) as count").
		Where("user_id = ? AND created_at > ?", userID, startTime).
		Group("content_type").
		Scan(&typeStats).Error

	if err != nil {
		return nil, fmt.Errorf("获取类型统计失败: %w", err)
	}

	stats["total_requests"] = totalRequests
	stats["valid_requests"] = validRequests
	stats["invalid_requests"] = totalRequests - validRequests
	stats["success_rate"] = float64(validRequests) / float64(totalRequests) * 100
	stats["by_content_type"] = typeStats
	stats["period_days"] = days

	return stats, nil
}

// ValidationLog 校验日志模型
type ValidationLog struct {
	ID           uuid.UUID              `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID       uuid.UUID              `gorm:"type:uuid;not null;index" json:"user_id"`
	ContentType  string                 `gorm:"size:50;not null" json:"content_type"`
	TextLength   int                    `gorm:"not null" json:"text_length"`
	IsValid      bool                   `gorm:"not null;default:false" json:"is_valid"`
	ErrorCount   int                    `gorm:"not null;default:0" json:"error_count"`
	WarningCount int                    `gorm:"not null;default:0" json:"warning_count"`
	Metadata     map[string]interface{} `gorm:"type:jsonb" json:"metadata"`
	CreatedAt    time.Time              `gorm:"autoCreateTime" json:"created_at"`
}
