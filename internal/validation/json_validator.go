package validation

import (
	"encoding/json"
	"fmt"
	"reflect"

	"ai-text-game-iam-npc/pkg/logger"
)

// JSONValidator JSON结构验证器
type JSONValidator struct {
	logger logger.Logger
}

// NewJSONValidator 创建JSON验证器
func NewJSONValidator(logger logger.Logger) *JSONValidator {
	return &JSONValidator{
		logger: logger,
	}
}

// JSONValidationResult JSON验证结果
type JSONValidationResult struct {
	Valid   bool     `json:"valid"`           // 是否有效
	Errors  []string `json:"errors"`          // 错误列表
	Missing []string `json:"missing"`         // 缺失字段
	Extra   []string `json:"extra"`           // 额外字段
	Score   float64  `json:"score"`           // 匹配分数 (0-1)
}

// ValidateJSON 验证JSON数据是否符合指定的结构
func (v *JSONValidator) ValidateJSON(data interface{}, schema map[string]interface{}) *JSONValidationResult {
	v.logger.Debug("开始验证JSON结构")

	result := &JSONValidationResult{
		Valid:   true,
		Errors:  []string{},
		Missing: []string{},
		Extra:   []string{},
		Score:   1.0,
	}

	// 将数据转换为map
	dataMap, err := v.toMap(data)
	if err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, fmt.Sprintf("数据格式错误: %v", err))
		result.Score = 0.0
		return result
	}

	// 验证结构
	v.validateStructure(dataMap, schema, "", result)

	// 计算最终分数
	result.Score = v.calculateScore(result)
	result.Valid = result.Score >= 0.8 // 80%以上认为有效

	v.logger.Debug("JSON结构验证完成", 
		"valid", result.Valid, 
		"score", result.Score,
		"errors", len(result.Errors))

	return result
}

// ValidateChatResponse 验证对话响应的JSON结构
func (v *JSONValidator) ValidateChatResponse(data interface{}) *JSONValidationResult {
	// 定义对话响应的期望结构
	schema := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"content": map[string]interface{}{
				"type":        "string",
				"description": "AI生成的回复内容",
				"required":    true,
			},
			"emotion": map[string]interface{}{
				"type":        "string",
				"description": "回复的情感色彩",
				"enum":        []string{"neutral", "happy", "sad", "excited", "confused", "helpful", "friendly"},
				"required":    false,
			},
			"intent": map[string]interface{}{
				"type":        "string",
				"description": "回复的意图",
				"enum":        []string{"answer", "question", "suggestion", "clarification", "greeting", "farewell"},
				"required":    false,
			},
			"confidence": map[string]interface{}{
				"type":        "number",
				"description": "回复的置信度",
				"minimum":     0.0,
				"maximum":     1.0,
				"required":    false,
			},
		},
		"required": []string{"content"},
	}

	return v.ValidateJSON(data, schema)
}

// validateStructure 验证数据结构
func (v *JSONValidator) validateStructure(data map[string]interface{}, schema map[string]interface{}, path string, result *JSONValidationResult) {
	// 获取schema的properties
	properties, ok := schema["properties"].(map[string]interface{})
	if !ok {
		return
	}

	// 获取required字段
	required := []string{}
	if req, ok := schema["required"].([]interface{}); ok {
		for _, r := range req {
			if str, ok := r.(string); ok {
				required = append(required, str)
			}
		}
	} else if req, ok := schema["required"].([]string); ok {
		required = req
	}

	// 检查required字段是否存在
	for _, field := range required {
		if _, exists := data[field]; !exists {
			fieldPath := v.buildPath(path, field)
			result.Missing = append(result.Missing, fieldPath)
			result.Errors = append(result.Errors, fmt.Sprintf("缺失必需字段: %s", fieldPath))
		}
	}

	// 验证每个字段
	for field, value := range data {
		fieldPath := v.buildPath(path, field)
		
		// 检查字段是否在schema中定义
		fieldSchema, exists := properties[field]
		if !exists {
			result.Extra = append(result.Extra, fieldPath)
			continue
		}

		// 验证字段类型和值
		v.validateField(value, fieldSchema.(map[string]interface{}), fieldPath, result)
	}
}

// validateField 验证单个字段
func (v *JSONValidator) validateField(value interface{}, fieldSchema map[string]interface{}, path string, result *JSONValidationResult) {
	// 获取期望类型
	expectedType, ok := fieldSchema["type"].(string)
	if !ok {
		return
	}

	// 验证类型
	actualType := v.getJSONType(value)
	if actualType != expectedType {
		result.Errors = append(result.Errors, 
			fmt.Sprintf("字段 %s 类型错误: 期望 %s, 实际 %s", path, expectedType, actualType))
		return
	}

	// 验证枚举值
	if enum, ok := fieldSchema["enum"].([]interface{}); ok {
		if !v.isInEnum(value, enum) {
			result.Errors = append(result.Errors, 
				fmt.Sprintf("字段 %s 值不在允许范围内: %v", path, value))
		}
	} else if enum, ok := fieldSchema["enum"].([]string); ok {
		enumInterface := make([]interface{}, len(enum))
		for i, e := range enum {
			enumInterface[i] = e
		}
		if !v.isInEnum(value, enumInterface) {
			result.Errors = append(result.Errors, 
				fmt.Sprintf("字段 %s 值不在允许范围内: %v", path, value))
		}
	}

	// 验证数值范围
	if expectedType == "number" {
		if min, ok := fieldSchema["minimum"].(float64); ok {
			if num, ok := value.(float64); ok && num < min {
				result.Errors = append(result.Errors, 
					fmt.Sprintf("字段 %s 值小于最小值 %f: %f", path, min, num))
			}
		}
		if max, ok := fieldSchema["maximum"].(float64); ok {
			if num, ok := value.(float64); ok && num > max {
				result.Errors = append(result.Errors, 
					fmt.Sprintf("字段 %s 值大于最大值 %f: %f", path, max, num))
			}
		}
	}

	// 验证字符串长度
	if expectedType == "string" {
		if minLen, ok := fieldSchema["minLength"].(int); ok {
			if str, ok := value.(string); ok && len(str) < minLen {
				result.Errors = append(result.Errors, 
					fmt.Sprintf("字段 %s 长度小于最小值 %d: %d", path, minLen, len(str)))
			}
		}
		if maxLen, ok := fieldSchema["maxLength"].(int); ok {
			if str, ok := value.(string); ok && len(str) > maxLen {
				result.Errors = append(result.Errors, 
					fmt.Sprintf("字段 %s 长度大于最大值 %d: %d", path, maxLen, len(str)))
			}
		}
	}
}

// toMap 将数据转换为map
func (v *JSONValidator) toMap(data interface{}) (map[string]interface{}, error) {
	switch d := data.(type) {
	case map[string]interface{}:
		return d, nil
	case string:
		var result map[string]interface{}
		if err := json.Unmarshal([]byte(d), &result); err != nil {
			return nil, err
		}
		return result, nil
	default:
		// 尝试通过JSON序列化/反序列化转换
		jsonData, err := json.Marshal(data)
		if err != nil {
			return nil, err
		}
		var result map[string]interface{}
		if err := json.Unmarshal(jsonData, &result); err != nil {
			return nil, err
		}
		return result, nil
	}
}

// getJSONType 获取JSON类型
func (v *JSONValidator) getJSONType(value interface{}) string {
	if value == nil {
		return "null"
	}

	switch value.(type) {
	case bool:
		return "boolean"
	case float64, int, int64, float32:
		return "number"
	case string:
		return "string"
	case []interface{}:
		return "array"
	case map[string]interface{}:
		return "object"
	default:
		return reflect.TypeOf(value).String()
	}
}

// isInEnum 检查值是否在枚举中
func (v *JSONValidator) isInEnum(value interface{}, enum []interface{}) bool {
	for _, e := range enum {
		if value == e {
			return true
		}
	}
	return false
}

// buildPath 构建字段路径
func (v *JSONValidator) buildPath(parent, field string) string {
	if parent == "" {
		return field
	}
	return parent + "." + field
}

// calculateScore 计算匹配分数
func (v *JSONValidator) calculateScore(result *JSONValidationResult) float64 {
	if len(result.Errors) == 0 && len(result.Missing) == 0 {
		return 1.0
	}

	// 基础分数
	score := 1.0

	// 每个错误扣分
	score -= float64(len(result.Errors)) * 0.2

	// 每个缺失字段扣分
	score -= float64(len(result.Missing)) * 0.3

	// 额外字段轻微扣分
	score -= float64(len(result.Extra)) * 0.1

	if score < 0 {
		score = 0
	}

	return score
}

// FixJSONStructure 尝试修复JSON结构
func (v *JSONValidator) FixJSONStructure(data map[string]interface{}, schema map[string]interface{}) map[string]interface{} {
	v.logger.Debug("尝试修复JSON结构")

	fixed := make(map[string]interface{})
	
	// 复制现有的有效字段
	properties, ok := schema["properties"].(map[string]interface{})
	if !ok {
		return data
	}

	for field, value := range data {
		if _, exists := properties[field]; exists {
			fixed[field] = value
		}
	}

	// 添加缺失的必需字段的默认值
	if required, ok := schema["required"].([]string); ok {
		for _, field := range required {
			if _, exists := fixed[field]; !exists {
				if fieldSchema, exists := properties[field].(map[string]interface{}); exists {
					fixed[field] = v.getDefaultValue(fieldSchema)
				}
			}
		}
	}

	v.logger.Debug("JSON结构修复完成", "original_fields", len(data), "fixed_fields", len(fixed))
	return fixed
}

// getDefaultValue 获取字段的默认值
func (v *JSONValidator) getDefaultValue(fieldSchema map[string]interface{}) interface{} {
	fieldType, ok := fieldSchema["type"].(string)
	if !ok {
		return nil
	}

	switch fieldType {
	case "string":
		return ""
	case "number":
		return 0.0
	case "boolean":
		return false
	case "array":
		return []interface{}{}
	case "object":
		return map[string]interface{}{}
	default:
		return nil
	}
}
