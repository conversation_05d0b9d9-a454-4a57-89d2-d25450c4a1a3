package apidoc

import (
	"log"
	"os"
)

// SimpleLogger 简单的日志记录器实现
type SimpleLogger struct {
	logger *log.Logger
}

// NewSimpleLogger 创建新的简单日志记录器
func NewSimpleLogger() *SimpleLogger {
	return &SimpleLogger{
		logger: log.New(os.Stdout, "[API-DOC] ", log.LstdFlags),
	}
}

// Info 记录信息日志
func (l *SimpleLogger) Info(msg string, args ...interface{}) {
	if len(args) > 0 {
		l.logger.Printf("[INFO] "+msg+" %v", args)
	} else {
		l.logger.Printf("[INFO] " + msg)
	}
}

// Error 记录错误日志
func (l *SimpleLogger) Error(msg string, args ...interface{}) {
	if len(args) > 0 {
		l.logger.Printf("[ERROR] "+msg+" %v", args)
	} else {
		l.logger.Printf("[ERROR] " + msg)
	}
}

// Debug 记录调试日志
func (l *SimpleLogger) Debug(msg string, args ...interface{}) {
	if len(args) > 0 {
		l.logger.Printf("[DEBUG] "+msg+" %v", args)
	} else {
		l.logger.Printf("[DEBUG] " + msg)
	}
}

// Warn 记录警告日志
func (l *SimpleLogger) Warn(msg string, args ...interface{}) {
	if len(args) > 0 {
		l.logger.Printf("[WARN] "+msg+" %v", args)
	} else {
		l.logger.Printf("[WARN] " + msg)
	}
}

// LoggerAdapter 日志适配器，将pkg/logger.Logger适配为apidoc.Logger
type LoggerAdapter struct {
	logger interface {
		Info(msg string, args ...interface{})
		Error(msg string, args ...interface{})
		Debug(msg string, args ...interface{})
		Warn(msg string, args ...interface{})
		Fatal(msg string, args ...interface{})
	}
}

// NewLoggerAdapter 创建日志适配器
func NewLoggerAdapter(logger interface {
	Info(msg string, args ...interface{})
	Error(msg string, args ...interface{})
	Debug(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Fatal(msg string, args ...interface{})
}) *LoggerAdapter {
	return &LoggerAdapter{logger: logger}
}

// Info 记录信息日志
func (a *LoggerAdapter) Info(msg string, args ...interface{}) {
	a.logger.Info(msg, args...)
}

// Error 记录错误日志
func (a *LoggerAdapter) Error(msg string, args ...interface{}) {
	a.logger.Error(msg, args...)
}

// Debug 记录调试日志
func (a *LoggerAdapter) Debug(msg string, args ...interface{}) {
	a.logger.Debug(msg, args...)
}

// Warn 记录警告日志
func (a *LoggerAdapter) Warn(msg string, args ...interface{}) {
	a.logger.Warn(msg, args...)
}
