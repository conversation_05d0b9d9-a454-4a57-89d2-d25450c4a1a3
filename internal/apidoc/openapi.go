package apidoc

import (
	"encoding/json"
	"fmt"
	"strings"
)

// OpenAPISpec OpenAPI 3.0 规范结构
type OpenAPISpec struct {
	OpenAPI    string                 `json:"openapi"`
	Info       Info                   `json:"info"`
	Servers    []Server               `json:"servers"`
	Paths      map[string]PathItem    `json:"paths"`
	Components Components             `json:"components"`
	Security   []map[string][]string  `json:"security,omitempty"`
	Tags       []Tag                  `json:"tags,omitempty"`
}

// Info API信息
type Info struct {
	Title          string  `json:"title"`
	Description    string  `json:"description"`
	Version        string  `json:"version"`
	TermsOfService string  `json:"termsOfService,omitempty"`
	Contact        Contact `json:"contact,omitempty"`
	License        License `json:"license,omitempty"`
}

// Contact 联系信息
type Contact struct {
	Name  string `json:"name,omitempty"`
	URL   string `json:"url,omitempty"`
	Email string `json:"email,omitempty"`
}

// License 许可证信息
type License struct {
	Name string `json:"name"`
	URL  string `json:"url,omitempty"`
}

// Server 服务器信息
type Server struct {
	URL         string                    `json:"url"`
	Description string                    `json:"description,omitempty"`
	Variables   map[string]ServerVariable `json:"variables,omitempty"`
}

// ServerVariable 服务器变量
type ServerVariable struct {
	Enum        []string `json:"enum,omitempty"`
	Default     string   `json:"default"`
	Description string   `json:"description,omitempty"`
}

// PathItem 路径项
type PathItem struct {
	Summary     string     `json:"summary,omitempty"`
	Description string     `json:"description,omitempty"`
	Get         *Operation `json:"get,omitempty"`
	Post        *Operation `json:"post,omitempty"`
	Put         *Operation `json:"put,omitempty"`
	Delete      *Operation `json:"delete,omitempty"`
	Options     *Operation `json:"options,omitempty"`
	Head        *Operation `json:"head,omitempty"`
	Patch       *Operation `json:"patch,omitempty"`
	Trace       *Operation `json:"trace,omitempty"`
	Parameters  []Parameter `json:"parameters,omitempty"`
}

// Operation 操作
type Operation struct {
	Tags         []string               `json:"tags,omitempty"`
	Summary      string                 `json:"summary,omitempty"`
	Description  string                 `json:"description,omitempty"`
	OperationID  string                 `json:"operationId,omitempty"`
	Parameters   []Parameter            `json:"parameters,omitempty"`
	RequestBody  *RequestBody           `json:"requestBody,omitempty"`
	Responses    map[string]Response    `json:"responses"`
	Callbacks    map[string]interface{} `json:"callbacks,omitempty"`
	Deprecated   bool                   `json:"deprecated,omitempty"`
	Security     []map[string][]string  `json:"security,omitempty"`
	Servers      []Server               `json:"servers,omitempty"`
}

// RequestBody 请求体
type RequestBody struct {
	Description string                `json:"description,omitempty"`
	Content     map[string]MediaType  `json:"content"`
	Required    bool                  `json:"required,omitempty"`
}

// MediaType 媒体类型
type MediaType struct {
	Schema   *Schema                `json:"schema,omitempty"`
	Example  interface{}            `json:"example,omitempty"`
	Examples map[string]interface{} `json:"examples,omitempty"`
	Encoding map[string]interface{} `json:"encoding,omitempty"`
}

// Components 组件
type Components struct {
	Schemas         map[string]*Schema             `json:"schemas,omitempty"`
	Responses       map[string]Response            `json:"responses,omitempty"`
	Parameters      map[string]Parameter           `json:"parameters,omitempty"`
	Examples        map[string]interface{}         `json:"examples,omitempty"`
	RequestBodies   map[string]RequestBody         `json:"requestBodies,omitempty"`
	Headers         map[string]interface{}         `json:"headers,omitempty"`
	SecuritySchemes map[string]SecurityScheme      `json:"securitySchemes,omitempty"`
	Links           map[string]interface{}         `json:"links,omitempty"`
	Callbacks       map[string]interface{}         `json:"callbacks,omitempty"`
}

// SecurityScheme 安全方案
type SecurityScheme struct {
	Type             string `json:"type"`
	Description      string `json:"description,omitempty"`
	Name             string `json:"name,omitempty"`
	In               string `json:"in,omitempty"`
	Scheme           string `json:"scheme,omitempty"`
	BearerFormat     string `json:"bearerFormat,omitempty"`
	Flows            *Flows `json:"flows,omitempty"`
	OpenIDConnectURL string `json:"openIdConnectUrl,omitempty"`
}

// Flows OAuth流程
type Flows struct {
	Implicit          *Flow `json:"implicit,omitempty"`
	Password          *Flow `json:"password,omitempty"`
	ClientCredentials *Flow `json:"clientCredentials,omitempty"`
	AuthorizationCode *Flow `json:"authorizationCode,omitempty"`
}

// Flow OAuth流程详情
type Flow struct {
	AuthorizationURL string            `json:"authorizationUrl,omitempty"`
	TokenURL         string            `json:"tokenUrl,omitempty"`
	RefreshURL       string            `json:"refreshUrl,omitempty"`
	Scopes           map[string]string `json:"scopes"`
}

// Tag 标签
type Tag struct {
	Name         string `json:"name"`
	Description  string `json:"description,omitempty"`
	ExternalDocs *ExternalDocumentation `json:"externalDocs,omitempty"`
}

// ExternalDocumentation 外部文档
type ExternalDocumentation struct {
	Description string `json:"description,omitempty"`
	URL         string `json:"url"`
}

// Generator OpenAPI文档生成器
type Generator struct {
	logger Logger
}

// NewGenerator 创建新的OpenAPI文档生成器
func NewGenerator(logger Logger) *Generator {
	return &Generator{
		logger: logger,
	}
}

// GenerateSpec 生成OpenAPI规范
func (g *Generator) GenerateSpec(endpoints []APIEndpoint, config GeneratorConfig, structs map[string]*StructInfo) (*OpenAPISpec, error) {
	g.logger.Info("开始生成OpenAPI规范", "endpoints_count", len(endpoints))
	
	spec := &OpenAPISpec{
		OpenAPI: "3.0.3",
		Info: Info{
			Title:       config.Title,
			Description: config.Description,
			Version:     config.Version,
			Contact: Contact{
				Name:  config.ContactName,
				Email: config.ContactEmail,
			},
		},
		Servers: []Server{
			{
				URL:         config.ServerURL,
				Description: config.ServerDescription,
			},
		},
		Paths:      make(map[string]PathItem),
		Components: Components{
			SecuritySchemes: map[string]SecurityScheme{
				"BearerAuth": {
					Type:         "http",
					Scheme:       "bearer",
					BearerFormat: "JWT",
					Description:  "JWT Bearer token认证",
				},
				"ApiKeyAuth": {
					Type:        "apiKey",
					In:          "header",
					Name:        "X-API-Key",
					Description: "API Key认证",
				},
			},
			Schemas: make(map[string]*Schema),
		},
		Tags: g.generateTags(endpoints),
	}
	
	// 转换端点为OpenAPI路径
	for _, endpoint := range endpoints {
		g.addEndpointToSpec(spec, endpoint)
	}
	
	// 添加通用响应模式
	g.addCommonSchemas(spec)

	// 添加解析的结构体schemas
	g.addStructSchemas(spec, structs)

	g.logger.Info("OpenAPI规范生成完成", "paths_count", len(spec.Paths), "schemas_count", len(spec.Components.Schemas))
	return spec, nil
}

// GeneratorConfig 生成器配置
type GeneratorConfig struct {
	Title             string
	Description       string
	Version           string
	ServerURL         string
	ServerDescription string
	ContactName       string
	ContactEmail      string
}

// addEndpointToSpec 将端点添加到规范中
func (g *Generator) addEndpointToSpec(spec *OpenAPISpec, endpoint APIEndpoint) {
	path := endpoint.Path
	
	// 获取或创建路径项
	pathItem, exists := spec.Paths[path]
	if !exists {
		pathItem = PathItem{}
	}
	
	// 创建操作
	operation := &Operation{
		Tags:        endpoint.Tags,
		Summary:     endpoint.Summary,
		Description: endpoint.Description,
		OperationID: g.generateOperationID(endpoint),
		Parameters:  g.convertParameters(endpoint.Parameters),
		Responses:   g.convertResponses(endpoint.Responses),
	}

	// 添加安全要求
	if len(endpoint.Security) > 0 {
		operation.Security = endpoint.Security
	}

	// 根据HTTP方法设置操作
	switch strings.ToUpper(endpoint.Method) {
	case "GET":
		pathItem.Get = operation
	case "POST":
		pathItem.Post = operation
		// POST请求通常有请求体
		operation.RequestBody = g.generateRequestBody(endpoint.Parameters)
	case "PUT":
		pathItem.Put = operation
		operation.RequestBody = g.generateRequestBody(endpoint.Parameters)
	case "DELETE":
		pathItem.Delete = operation
	case "PATCH":
		pathItem.Patch = operation
		operation.RequestBody = g.generateRequestBody(endpoint.Parameters)
	case "HEAD":
		pathItem.Head = operation
	case "OPTIONS":
		pathItem.Options = operation
	}
	
	spec.Paths[path] = pathItem
}

// generateOperationID 生成操作ID
func (g *Generator) generateOperationID(endpoint APIEndpoint) string {
	method := strings.ToLower(endpoint.Method)
	path := strings.ReplaceAll(endpoint.Path, "/", "_")
	path = strings.ReplaceAll(path, "{", "")
	path = strings.ReplaceAll(path, "}", "")
	path = strings.Trim(path, "_")
	
	if path == "" {
		return method + "_root"
	}
	
	return method + "_" + path
}

// convertParameters 转换参数
func (g *Generator) convertParameters(params []Parameter) []Parameter {
	// 过滤掉body参数，因为在OpenAPI 3.0中body参数应该在requestBody中
	var result []Parameter
	for _, param := range params {
		if param.In != "body" {
			result = append(result, param)
		}
	}
	return result
}

// convertResponses 转换响应
func (g *Generator) convertResponses(responses map[string]Response) map[string]Response {
	if len(responses) == 0 {
		// 提供默认响应
		return map[string]Response{
			"200": {
				Description: "成功响应",
				Schema: &Schema{
					Type: "object",
					Properties: map[string]*Schema{
						"success": {Type: "boolean"},
						"message": {Type: "string"},
						"data":    {Type: "object"},
					},
				},
			},
			"400": {
				Description: "请求错误",
				Schema: &Schema{
					Type: "object",
					Properties: map[string]*Schema{
						"success": {Type: "boolean"},
						"message": {Type: "string"},
						"error":   {Type: "string"},
					},
				},
			},
			"500": {
				Description: "服务器错误",
				Schema: &Schema{
					Type: "object",
					Properties: map[string]*Schema{
						"success": {Type: "boolean"},
						"message": {Type: "string"},
						"error":   {Type: "string"},
					},
				},
			},
		}
	}
	
	return responses
}

// generateTags 生成标签
func (g *Generator) generateTags(endpoints []APIEndpoint) []Tag {
	tagMap := make(map[string]bool)
	var tags []Tag
	
	for _, endpoint := range endpoints {
		for _, tag := range endpoint.Tags {
			if !tagMap[tag] {
				tagMap[tag] = true
				tags = append(tags, Tag{
					Name:        tag,
					Description: fmt.Sprintf("%s相关API", tag),
				})
			}
		}
	}
	
	return tags
}

// generateRequestBody 生成请求体
func (g *Generator) generateRequestBody(parameters []Parameter) *RequestBody {
	// 查找body参数
	var bodyParams []Parameter
	for _, param := range parameters {
		if param.In == "body" {
			bodyParams = append(bodyParams, param)
		}
	}

	// 如果没有body参数，返回默认请求体
	if len(bodyParams) == 0 {
		return g.generateDefaultRequestBody()
	}

	// 如果只有一个body参数，直接使用其schema
	if len(bodyParams) == 1 {
		param := bodyParams[0]
		return &RequestBody{
			Description: param.Description,
			Content: map[string]MediaType{
				"application/json": {
					Schema: param.Schema,
				},
			},
			Required: param.Required,
		}
	}

	// 如果有多个body参数，合并为一个对象
	schema := &Schema{
		Type:       "object",
		Properties: make(map[string]*Schema),
		Required:   []string{},
	}

	description := "请求数据"
	for i, param := range bodyParams {
		if param.Schema != nil {
			schema.Properties[param.Name] = param.Schema
		} else {
			// 如果没有schema，创建一个基本的
			schema.Properties[param.Name] = &Schema{
				Type:        "object",
				Description: param.Description,
			}
		}

		if param.Required {
			schema.Required = append(schema.Required, param.Name)
		}

		if i == 0 {
			description = param.Description
		}
	}

	return &RequestBody{
		Description: description,
		Content: map[string]MediaType{
			"application/json": {
				Schema: schema,
			},
		},
		Required: true,
	}
}

// generateDefaultRequestBody 生成默认请求体
func (g *Generator) generateDefaultRequestBody() *RequestBody {
	return &RequestBody{
		Description: "请求数据",
		Content: map[string]MediaType{
			"application/json": {
				Schema: &Schema{
					Type: "object",
				},
			},
		},
		Required: true,
	}
}

// addCommonSchemas 添加通用模式
func (g *Generator) addCommonSchemas(spec *OpenAPISpec) {
	spec.Components.Schemas["Response"] = &Schema{
		Type: "object",
		Properties: map[string]*Schema{
			"success": {
				Type:        "boolean",
				Description: "请求是否成功",
			},
			"message": {
				Type:        "string",
				Description: "响应消息",
			},
			"data": {
				Type:        "object",
				Description: "响应数据",
			},
			"error": {
				Type:        "string",
				Description: "错误信息",
			},
			"timestamp": {
				Type:        "string",
				Description: "时间戳",
			},
		},
		Required: []string{"success"},
	}
	
	spec.Components.Schemas["Error"] = &Schema{
		Type: "object",
		Properties: map[string]*Schema{
			"code": {
				Type:        "string",
				Description: "错误代码",
			},
			"message": {
				Type:        "string",
				Description: "错误消息",
			},
			"details": {
				Type:        "object",
				Description: "错误详情",
			},
		},
		Required: []string{"code", "message"},
	}
}

// ToJSON 将规范转换为JSON
func (spec *OpenAPISpec) ToJSON() ([]byte, error) {
	return json.MarshalIndent(spec, "", "  ")
}

// ToYAML 将规范转换为YAML (简单实现)
func (spec *OpenAPISpec) ToYAML() ([]byte, error) {
	// 这里简化实现，实际项目中可以使用yaml库
	jsonData, err := spec.ToJSON()
	if err != nil {
		return nil, err
	}
	
	// 简单的JSON到YAML转换（实际应该使用专门的库）
	yamlData := strings.ReplaceAll(string(jsonData), "  ", "  ")
	return []byte(yamlData), nil
}

// addStructSchemas 添加结构体schemas到OpenAPI规范
func (g *Generator) addStructSchemas(spec *OpenAPISpec, structs map[string]*StructInfo) {
	if structs == nil {
		return
	}

	g.logger.Info("添加结构体schemas", "structs_count", len(structs))

	for key, structInfo := range structs {
		if structInfo.Schema == nil {
			continue
		}

		// 使用结构体名称作为schema名称
		schemaName := structInfo.Name

		// 复制schema并添加标题
		schema := *structInfo.Schema
		schema.Title = structInfo.Name
		if schema.Description == "" {
			schema.Description = fmt.Sprintf("%s - %s", structInfo.Name, structInfo.Comment)
		}

		spec.Components.Schemas[schemaName] = &schema

		g.logger.Info("添加结构体schema", "name", schemaName, "key", key, "fields_count", len(structInfo.Fields))
	}
}
