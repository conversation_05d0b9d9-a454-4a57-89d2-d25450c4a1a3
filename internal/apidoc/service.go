package apidoc

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// Service API文档服务
type Service struct {
	scanner   *Scanner
	generator *Generator
	cache     *DocumentCache
	config    ServiceConfig
	logger    Logger
	mu        sync.RWMutex
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	ProjectRoot       string        // 项目根目录
	CacheEnabled      bool          // 是否启用缓存
	CacheTTL          time.Duration // 缓存过期时间
	AutoRefresh       bool          // 是否自动刷新
	RefreshInterval   time.Duration // 刷新间隔
	OutputDir         string        // 输出目录
	GeneratorConfig   GeneratorConfig // 生成器配置
}

// DocumentCache 文档缓存
type DocumentCache struct {
	spec      *OpenAPISpec
	endpoints []APIEndpoint
	timestamp time.Time
	ttl       time.Duration
	mu        sync.RWMutex
}

// NewService 创建新的API文档服务
func NewService(config ServiceConfig, logger Logger) *Service {
	scanner := NewScanner(logger)
	generator := NewGenerator(logger)
	cache := &DocumentCache{
		ttl: config.CacheTTL,
	}
	
	service := &Service{
		scanner:   scanner,
		generator: generator,
		cache:     cache,
		config:    config,
		logger:    logger,
	}
	
	// 启动自动刷新
	if config.AutoRefresh {
		go service.startAutoRefresh()
	}
	
	return service
}

// GenerateDocumentation 生成API文档
func (s *Service) GenerateDocumentation(ctx context.Context) (*OpenAPISpec, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.logger.Info("开始生成API文档")
	
	// 检查缓存
	if s.config.CacheEnabled && s.cache.isValid() {
		s.logger.Info("使用缓存的API文档")
		return s.cache.spec, nil
	}
	
	// 扫描API端点
	if err := s.scanner.ScanDirectory(s.config.ProjectRoot); err != nil {
		return nil, fmt.Errorf("扫描API端点失败: %w", err)
	}
	
	endpoints := s.scanner.GetEndpoints()
	structs := s.scanner.GetStructs()
	s.logger.Info("扫描完成", "endpoints_count", len(endpoints), "structs_count", len(structs))

	// 生成OpenAPI规范
	spec, err := s.generator.GenerateSpec(endpoints, s.config.GeneratorConfig, structs)
	if err != nil {
		return nil, fmt.Errorf("生成OpenAPI规范失败: %w", err)
	}
	
	// 更新缓存
	if s.config.CacheEnabled {
		s.cache.update(spec, endpoints)
	}
	
	// 保存到文件
	if err := s.saveDocumentation(spec); err != nil {
		s.logger.Error("保存文档失败", "error", err)
	}
	
	s.logger.Info("API文档生成完成")
	return spec, nil
}

// GetEndpoints 获取API端点列表
func (s *Service) GetEndpoints(ctx context.Context) ([]APIEndpoint, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	// 检查缓存
	if s.config.CacheEnabled && s.cache.isValid() {
		return s.cache.endpoints, nil
	}
	
	// 重新生成文档
	_, err := s.GenerateDocumentation(ctx)
	if err != nil {
		return nil, err
	}
	
	return s.cache.endpoints, nil
}

// GetOpenAPISpec 获取OpenAPI规范
func (s *Service) GetOpenAPISpec(ctx context.Context) (*OpenAPISpec, error) {
	return s.GenerateDocumentation(ctx)
}

// RefreshDocumentation 刷新文档
func (s *Service) RefreshDocumentation(ctx context.Context) error {
	s.logger.Info("手动刷新API文档")
	
	// 清除缓存
	s.cache.invalidate()
	
	// 重新生成
	_, err := s.GenerateDocumentation(ctx)
	return err
}

// GetDocumentationStats 获取文档统计信息
func (s *Service) GetDocumentationStats(ctx context.Context) (*DocumentationStats, error) {
	endpoints, err := s.GetEndpoints(ctx)
	if err != nil {
		return nil, err
	}
	
	stats := &DocumentationStats{
		TotalEndpoints: len(endpoints),
		MethodCounts:   make(map[string]int),
		TagCounts:      make(map[string]int),
		LastUpdated:    s.cache.timestamp,
	}
	
	// 统计方法和标签
	for _, endpoint := range endpoints {
		stats.MethodCounts[endpoint.Method]++
		for _, tag := range endpoint.Tags {
			stats.TagCounts[tag]++
		}
	}
	
	return stats, nil
}

// DocumentationStats 文档统计信息
type DocumentationStats struct {
	TotalEndpoints int               `json:"total_endpoints"`
	MethodCounts   map[string]int    `json:"method_counts"`
	TagCounts      map[string]int    `json:"tag_counts"`
	LastUpdated    time.Time         `json:"last_updated"`
}

// saveDocumentation 保存文档到文件
func (s *Service) saveDocumentation(spec *OpenAPISpec) error {
	if s.config.OutputDir == "" {
		return nil
	}
	
	// 确保输出目录存在
	if err := os.MkdirAll(s.config.OutputDir, 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %w", err)
	}
	
	// 保存JSON格式
	jsonData, err := spec.ToJSON()
	if err != nil {
		return fmt.Errorf("转换为JSON失败: %w", err)
	}
	
	jsonPath := filepath.Join(s.config.OutputDir, "openapi.json")
	if err := os.WriteFile(jsonPath, jsonData, 0644); err != nil {
		return fmt.Errorf("保存JSON文件失败: %w", err)
	}
	
	// 保存YAML格式
	yamlData, err := spec.ToYAML()
	if err != nil {
		return fmt.Errorf("转换为YAML失败: %w", err)
	}
	
	yamlPath := filepath.Join(s.config.OutputDir, "openapi.yaml")
	if err := os.WriteFile(yamlPath, yamlData, 0644); err != nil {
		return fmt.Errorf("保存YAML文件失败: %w", err)
	}
	
	// 保存端点列表
	endpointsData, err := json.MarshalIndent(s.cache.endpoints, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化端点列表失败: %w", err)
	}
	
	endpointsPath := filepath.Join(s.config.OutputDir, "endpoints.json")
	if err := os.WriteFile(endpointsPath, endpointsData, 0644); err != nil {
		return fmt.Errorf("保存端点列表失败: %w", err)
	}
	
	s.logger.Info("文档已保存", "output_dir", s.config.OutputDir)
	return nil
}

// startAutoRefresh 启动自动刷新
func (s *Service) startAutoRefresh() {
	ticker := time.NewTicker(s.config.RefreshInterval)
	defer ticker.Stop()
	
	s.logger.Info("启动自动刷新", "interval", s.config.RefreshInterval)
	
	for range ticker.C {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		if err := s.RefreshDocumentation(ctx); err != nil {
			s.logger.Error("自动刷新失败", "error", err)
		}
		cancel()
	}
}

// isValid 检查缓存是否有效
func (c *DocumentCache) isValid() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	if c.spec == nil {
		return false
	}
	
	if c.ttl > 0 && time.Since(c.timestamp) > c.ttl {
		return false
	}
	
	return true
}

// update 更新缓存
func (c *DocumentCache) update(spec *OpenAPISpec, endpoints []APIEndpoint) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.spec = spec
	c.endpoints = endpoints
	c.timestamp = time.Now()
}

// invalidate 使缓存失效
func (c *DocumentCache) invalidate() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.spec = nil
	c.endpoints = nil
	c.timestamp = time.Time{}
}

// DefaultServiceConfig 默认服务配置
func DefaultServiceConfig() ServiceConfig {
	return ServiceConfig{
		ProjectRoot:     ".",
		CacheEnabled:    true,
		CacheTTL:        5 * time.Minute,
		AutoRefresh:     false,
		RefreshInterval: 10 * time.Minute,
		OutputDir:       "docs/api",
		GeneratorConfig: GeneratorConfig{
			Title:             "AI文本游戏API",
			Description:       "AI文本游戏后端API文档",
			Version:           "1.0.0",
			ServerURL:         "http://localhost:8080",
			ServerDescription: "开发环境服务器",
			ContactName:       "开发团队",
			ContactEmail:      "<EMAIL>",
		},
	}
}

// ValidateConfig 验证配置
func ValidateConfig(config ServiceConfig) error {
	if config.ProjectRoot == "" {
		return fmt.Errorf("项目根目录不能为空")
	}
	
	if config.CacheTTL < 0 {
		return fmt.Errorf("缓存TTL不能为负数")
	}
	
	if config.RefreshInterval < time.Minute {
		return fmt.Errorf("刷新间隔不能少于1分钟")
	}
	
	if config.GeneratorConfig.Title == "" {
		return fmt.Errorf("API标题不能为空")
	}
	
	if config.GeneratorConfig.Version == "" {
		return fmt.Errorf("API版本不能为空")
	}
	
	return nil
}
