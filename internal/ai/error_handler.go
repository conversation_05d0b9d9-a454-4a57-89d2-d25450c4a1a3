package ai

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"ai-text-game-iam-npc/pkg/logger"
)

/**
 * 错误处理和重试机制
 * @description 为Windmill API调用提供智能的错误处理和重试策略
 */

// ErrorType 错误类型枚举
type ErrorType string

const (
	ErrorTypeNetwork      ErrorType = "network"       // 网络错误
	ErrorTypeAuth         ErrorType = "auth"          // 认证错误
	ErrorTypeRateLimit    ErrorType = "rate_limit"    // 频率限制
	ErrorTypeServer       ErrorType = "server"        // 服务器错误
	ErrorTypeTimeout      ErrorType = "timeout"       // 超时错误
	ErrorTypeValidation   ErrorType = "validation"    // 验证错误
	ErrorTypeUnknown      ErrorType = "unknown"       // 未知错误
)

// AIError AI服务错误
type AIError struct {
	Type       ErrorType `json:"type"`
	Message    string    `json:"message"`
	StatusCode int       `json:"status_code,omitempty"`
	Retryable  bool      `json:"retryable"`
	Cause      error     `json:"-"`
}

// Error 实现error接口
func (e *AIError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Type, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Unwrap 支持错误链
func (e *AIError) Unwrap() error {
	return e.Cause
}

// RetryStrategy 重试策略
type RetryStrategy struct {
	MaxRetries      int           `json:"max_retries"`       // 最大重试次数
	BaseDelay       time.Duration `json:"base_delay"`        // 基础延迟
	MaxDelay        time.Duration `json:"max_delay"`         // 最大延迟
	BackoffFactor   float64       `json:"backoff_factor"`    // 退避因子
	JitterEnabled   bool          `json:"jitter_enabled"`    // 是否启用抖动
	RetryableErrors []ErrorType   `json:"retryable_errors"`  // 可重试的错误类型
}

// DefaultRetryStrategy 默认重试策略
func DefaultRetryStrategy() *RetryStrategy {
	return &RetryStrategy{
		MaxRetries:    3,
		BaseDelay:     1 * time.Second,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: true,
		RetryableErrors: []ErrorType{
			ErrorTypeNetwork,
			ErrorTypeServer,
			ErrorTypeTimeout,
			ErrorTypeRateLimit,
		},
	}
}

// ErrorHandler 错误处理器
type ErrorHandler struct {
	strategy *RetryStrategy
	logger   logger.Logger
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(strategy *RetryStrategy, log logger.Logger) *ErrorHandler {
	if strategy == nil {
		strategy = DefaultRetryStrategy()
	}
	
	return &ErrorHandler{
		strategy: strategy,
		logger:   log,
	}
}

// ClassifyError 分类错误
func (eh *ErrorHandler) ClassifyError(err error, statusCode int) *AIError {
	if err == nil {
		return nil
	}
	
	errMsg := err.Error()
	errMsgLower := strings.ToLower(errMsg)
	
	// 根据状态码分类
	switch statusCode {
	case http.StatusUnauthorized, http.StatusForbidden:
		return &AIError{
			Type:       ErrorTypeAuth,
			Message:    "认证失败或权限不足",
			StatusCode: statusCode,
			Retryable:  false,
			Cause:      err,
		}
	case http.StatusTooManyRequests:
		return &AIError{
			Type:       ErrorTypeRateLimit,
			Message:    "请求频率过高，已被限制",
			StatusCode: statusCode,
			Retryable:  true,
			Cause:      err,
		}
	case http.StatusBadRequest:
		return &AIError{
			Type:       ErrorTypeValidation,
			Message:    "请求参数验证失败",
			StatusCode: statusCode,
			Retryable:  false,
			Cause:      err,
		}
	}
	
	// 根据状态码范围分类
	if statusCode >= 500 && statusCode < 600 {
		return &AIError{
			Type:       ErrorTypeServer,
			Message:    "服务器内部错误",
			StatusCode: statusCode,
			Retryable:  true,
			Cause:      err,
		}
	}
	
	// 根据错误信息分类
	if strings.Contains(errMsgLower, "timeout") || strings.Contains(errMsgLower, "deadline") {
		return &AIError{
			Type:      ErrorTypeTimeout,
			Message:   "请求超时",
			Retryable: true,
			Cause:     err,
		}
	}
	
	if strings.Contains(errMsgLower, "connection") || strings.Contains(errMsgLower, "network") {
		return &AIError{
			Type:      ErrorTypeNetwork,
			Message:   "网络连接错误",
			Retryable: true,
			Cause:     err,
		}
	}
	
	// 默认为未知错误
	return &AIError{
		Type:       ErrorTypeUnknown,
		Message:    "未知错误",
		StatusCode: statusCode,
		Retryable:  false,
		Cause:      err,
	}
}

// IsRetryable 检查错误是否可重试
func (eh *ErrorHandler) IsRetryable(aiErr *AIError) bool {
	if !aiErr.Retryable {
		return false
	}
	
	for _, retryableType := range eh.strategy.RetryableErrors {
		if aiErr.Type == retryableType {
			return true
		}
	}
	
	return false
}

// CalculateDelay 计算重试延迟
func (eh *ErrorHandler) CalculateDelay(attempt int) time.Duration {
	if attempt <= 0 {
		return eh.strategy.BaseDelay
	}
	
	// 指数退避
	delay := eh.strategy.BaseDelay
	for i := 0; i < attempt; i++ {
		delay = time.Duration(float64(delay) * eh.strategy.BackoffFactor)
	}
	
	// 限制最大延迟
	if delay > eh.strategy.MaxDelay {
		delay = eh.strategy.MaxDelay
	}
	
	// 添加抖动
	if eh.strategy.JitterEnabled {
		jitter := time.Duration(float64(delay) * 0.1) // 10%的抖动
		randomFactor := float64(time.Now().UnixNano()%1000) / 1000.0
		jitterAmount := time.Duration(float64(jitter) * (2*randomFactor - 1))
		delay += jitterAmount
	}
	
	return delay
}

// ExecuteWithRetry 执行带重试的操作
func (eh *ErrorHandler) ExecuteWithRetry(ctx context.Context, operation func() error) error {
	var lastErr *AIError
	
	for attempt := 0; attempt <= eh.strategy.MaxRetries; attempt++ {
		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
		
		// 执行操作
		err := operation()
		if err == nil {
			if attempt > 0 {
				eh.logger.Info("操作重试成功", "attempt", attempt)
			}
			return nil
		}
		
		// 分类错误
		var aiErr *AIError
		if e, ok := err.(*AIError); ok {
			aiErr = e
		} else {
			aiErr = eh.ClassifyError(err, 0)
		}
		
		lastErr = aiErr
		
		// 检查是否可重试
		if !eh.IsRetryable(aiErr) {
			eh.logger.Error("操作失败且不可重试", "error", aiErr, "attempt", attempt)
			return aiErr
		}
		
		// 如果是最后一次尝试，直接返回错误
		if attempt >= eh.strategy.MaxRetries {
			eh.logger.Error("操作重试次数已达上限", "error", aiErr, "max_retries", eh.strategy.MaxRetries)
			return aiErr
		}
		
		// 计算延迟并等待
		delay := eh.CalculateDelay(attempt)
		eh.logger.Warn("操作失败，准备重试", 
			"error", aiErr, 
			"attempt", attempt+1, 
			"max_retries", eh.strategy.MaxRetries,
			"delay", delay)
		
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// 继续下一次重试
		}
	}
	
	return lastErr
}

// WrapHTTPError 包装HTTP错误
func (eh *ErrorHandler) WrapHTTPError(err error, statusCode int, operation string) *AIError {
	aiErr := eh.ClassifyError(err, statusCode)
	aiErr.Message = fmt.Sprintf("%s失败: %s", operation, aiErr.Message)
	return aiErr
}

// LogError 记录错误日志
func (eh *ErrorHandler) LogError(err error, context map[string]interface{}) {
	if aiErr, ok := err.(*AIError); ok {
		logContext := map[string]interface{}{
			"error_type":   aiErr.Type,
			"retryable":    aiErr.Retryable,
			"status_code":  aiErr.StatusCode,
		}
		
		// 合并上下文
		for k, v := range context {
			logContext[k] = v
		}
		
		eh.logger.Error("AI服务错误", "error", aiErr.Message, "context", logContext)
	} else {
		eh.logger.Error("未分类错误", "error", err, "context", context)
	}
}

// GetRetryStatistics 获取重试统计信息
func (eh *ErrorHandler) GetRetryStatistics() map[string]interface{} {
	return map[string]interface{}{
		"max_retries":      eh.strategy.MaxRetries,
		"base_delay":       eh.strategy.BaseDelay.String(),
		"max_delay":        eh.strategy.MaxDelay.String(),
		"backoff_factor":   eh.strategy.BackoffFactor,
		"jitter_enabled":   eh.strategy.JitterEnabled,
		"retryable_errors": eh.strategy.RetryableErrors,
	}
}
