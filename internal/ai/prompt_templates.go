package ai

import (
	"fmt"
	"strings"
	"text/template"
)

/**
 * 提示词模板管理器
 * @description 为不同游戏场景设计相应的提示词模板和系统角色设定
 */

// PromptTemplate 提示词模板
type PromptTemplate struct {
	SystemInstruction string                 // 系统指令
	PromptTemplate    string                 // 提示词模板
	DefaultContext    map[string]interface{} // 默认上下文
	Examples          []string               // 示例
}

// PromptTemplateManager 提示词模板管理器
type PromptTemplateManager struct {
	templates map[string]*PromptTemplate
}

// NewPromptTemplateManager 创建提示词模板管理器
func NewPromptTemplateManager() *PromptTemplateManager {
	manager := &PromptTemplateManager{
		templates: make(map[string]*PromptTemplate),
	}
	
	// 注册所有模板
	manager.registerAllTemplates()
	
	return manager
}

// GetTemplate 获取指定类型的模板
func (ptm *PromptTemplateManager) GetTemplate(contentType string) (*PromptTemplate, error) {
	template, exists := ptm.templates[contentType]
	if !exists {
		return nil, fmt.Errorf("未找到内容类型 '%s' 的提示词模板", contentType)
	}
	return template, nil
}

// BuildPrompt 构建完整的提示词
func (ptm *PromptTemplateManager) BuildPrompt(contentType string, userPrompt string, context map[string]interface{}) (string, string, error) {
	template, err := ptm.GetTemplate(contentType)
	if err != nil {
		return "", "", err
	}
	
	// 合并上下文
	mergedContext := make(map[string]interface{})
	for k, v := range template.DefaultContext {
		mergedContext[k] = v
	}
	for k, v := range context {
		mergedContext[k] = v
	}
	mergedContext["user_prompt"] = userPrompt
	
	// 构建提示词
	prompt, err := ptm.renderTemplate(template.PromptTemplate, mergedContext)
	if err != nil {
		return "", "", fmt.Errorf("构建提示词失败: %w", err)
	}
	
	return prompt, template.SystemInstruction, nil
}

// renderTemplate 渲染模板
func (ptm *PromptTemplateManager) renderTemplate(templateStr string, context map[string]interface{}) (string, error) {
	tmpl, err := template.New("prompt").Parse(templateStr)
	if err != nil {
		return "", err
	}
	
	var buf strings.Builder
	if err := tmpl.Execute(&buf, context); err != nil {
		return "", err
	}
	
	return buf.String(), nil
}

// registerAllTemplates 注册所有模板
func (ptm *PromptTemplateManager) registerAllTemplates() {
	// 场景生成模板
	ptm.templates["scene"] = &PromptTemplate{
		SystemInstruction: `你是一个专业的游戏世界设计师，擅长创造富有想象力和沉浸感的游戏场景。你的任务是根据用户的要求生成详细的游戏场景描述。

请遵循以下原则：
1. 场景描述要生动具体，能够让玩家产生身临其境的感觉
2. 包含丰富的感官细节（视觉、听觉、嗅觉等）
3. 考虑场景的功能性和游戏性
4. 确保场景与整体世界观保持一致
5. 提供有意义的互动元素和探索机会

请严格按照提供的JSON Schema格式返回结果。`,
		PromptTemplate: `请为游戏世界生成一个场景。

用户需求：{{.user_prompt}}

{{if .world_theme}}世界主题：{{.world_theme}}{{end}}
{{if .target_atmosphere}}期望氛围：{{.target_atmosphere}}{{end}}
{{if .connected_scenes}}相邻场景：{{range .connected_scenes}}{{.}}, {{end}}{{end}}
{{if .special_requirements}}特殊要求：{{.special_requirements}}{{end}}

请生成一个详细的游戏场景，包含以下要素：
- 场景名称（简洁且富有特色）
- 详细的环境描述（至少100字）
- 场景氛围和类型
- 关键特征和可互动元素
- 玩家可能的行动选项
- 与其他场景的连接
- 隐藏元素和探索要素
- 危险等级和环境条件

确保场景具有游戏性和探索价值，能够为玩家提供有趣的体验。`,
		DefaultContext: map[string]interface{}{
			"world_theme": "奇幻",
			"target_atmosphere": "神秘",
		},
	}
	
	// 角色生成模板
	ptm.templates["character"] = &PromptTemplate{
		SystemInstruction: `你是一个专业的角色设计师，擅长创造立体、有趣且符合游戏世界观的角色。你的任务是根据用户的要求生成详细的游戏角色。

请遵循以下原则：
1. 角色要有鲜明的个性和独特的特征
2. 背景故事要合理且与世界观相符
3. 角色的动机和目标要明确
4. 对话风格要符合角色性格
5. 属性设定要平衡且有游戏性
6. 外观描述要具体且富有特色

请严格按照提供的JSON Schema格式返回结果。`,
		PromptTemplate: `请为游戏世界生成一个角色。

用户需求：{{.user_prompt}}

{{if .world_theme}}世界主题：{{.world_theme}}{{end}}
{{if .character_role}}角色定位：{{.character_role}}{{end}}
{{if .target_personality}}期望性格：{{.target_personality}}{{end}}
{{if .location_context}}所在地点：{{.location_context}}{{end}}
{{if .special_requirements}}特殊要求：{{.special_requirements}}{{end}}

请生成一个详细的游戏角色，包含以下要素：
- 角色名称（符合世界观设定）
- 基本描述和第一印象
- 详细的外观特征
- 性格特征和行为模式
- 完整的背景故事
- 技能和能力
- 对话风格和语言习惯
- 内在动机和恐惧
- 人际关系和社会地位
- 数值属性和阵营倾向

确保角色有深度和复杂性，能够为游戏增添丰富的互动体验。`,
		DefaultContext: map[string]interface{}{
			"world_theme": "奇幻",
			"character_role": "NPC",
		},
	}
	
	// 事件生成模板
	ptm.templates["event"] = &PromptTemplate{
		SystemInstruction: `你是一个专业的游戏事件设计师，擅长创造引人入胜且具有游戏性的事件。你的任务是根据用户的要求生成详细的游戏事件。

请遵循以下原则：
1. 事件要有明确的触发条件和结果
2. 提供有意义的选择和后果
3. 考虑事件对游戏进程的影响
4. 确保事件与世界观和当前情境相符
5. 平衡挑战性和可完成性
6. 提供多样化的解决方案

请严格按照提供的JSON Schema格式返回结果。`,
		PromptTemplate: `请为游戏世界生成一个事件。

用户需求：{{.user_prompt}}

{{if .world_theme}}世界主题：{{.world_theme}}{{end}}
{{if .event_category}}事件类别：{{.event_category}}{{end}}
{{if .target_difficulty}}期望难度：{{.target_difficulty}}{{end}}
{{if .current_location}}当前地点：{{.current_location}}{{end}}
{{if .involved_characters}}涉及角色：{{range .involved_characters}}{{.}}, {{end}}{{end}}
{{if .special_requirements}}特殊要求：{{.special_requirements}}{{end}}

请生成一个详细的游戏事件，包含以下要素：
- 事件名称（吸引人且概括性强）
- 详细的事件描述和背景
- 事件类型和优先级
- 触发条件和参与者
- 持续时间和地点要求
- 事件效果和后果
- 玩家选择选项
- 奖励和惩罚机制
- 难度等级和重复性

确保事件具有戏剧性和互动性，能够推动游戏剧情发展。`,
		DefaultContext: map[string]interface{}{
			"world_theme": "奇幻",
			"event_category": "随机遭遇",
			"target_difficulty": "普通",
		},
	}
	
	// 对话生成模板
	ptm.templates["dialogue"] = &PromptTemplate{
		SystemInstruction: `你是一个专业的对话编写师，擅长创造自然、生动且符合角色性格的对话。你的任务是根据用户的要求生成高质量的游戏对话。

请遵循以下原则：
1. 对话要符合角色的性格和背景
2. 语言风格要与游戏世界观一致
3. 考虑当前情境和角色关系
4. 提供有意义的信息或推动剧情
5. 包含适当的情感表达
6. 为后续互动留下空间

请严格按照提供的JSON Schema格式返回结果。`,
		PromptTemplate: `请为游戏角色生成对话内容。

用户需求：{{.user_prompt}}

{{if .speaker_name}}说话者：{{.speaker_name}}{{end}}
{{if .speaker_personality}}角色性格：{{.speaker_personality}}{{end}}
{{if .target_character}}对话对象：{{.target_character}}{{end}}
{{if .current_situation}}当前情境：{{.current_situation}}{{end}}
{{if .dialogue_purpose}}对话目的：{{.dialogue_purpose}}{{end}}
{{if .emotional_state}}情感状态：{{.emotional_state}}{{end}}
{{if .special_requirements}}特殊要求：{{.special_requirements}}{{end}}

请生成符合角色特征的对话内容，包含以下要素：
- 对话内容（自然且符合角色性格）
- 情感状态和语调
- 对话意图和目的
- 对当前情境的认知
- 后续对话建议
- 肢体语言描述
- 声音特征

确保对话真实可信，能够增强角色的立体感和游戏的沉浸感。`,
		DefaultContext: map[string]interface{}{
			"emotional_state": "中性",
			"dialogue_purpose": "友好交流",
		},
	}
	
	// 物品生成模板
	ptm.templates["item"] = &PromptTemplate{
		SystemInstruction: `你是一个专业的游戏物品设计师，擅长创造有趣且平衡的游戏物品。你的任务是根据用户的要求生成详细的游戏物品。

请遵循以下原则：
1. 物品要有明确的用途和价值
2. 属性设定要平衡且合理
3. 外观描述要具体且吸引人
4. 考虑物品的稀有度和获取难度
5. 确保物品与世界观相符
6. 提供有趣的背景故事

请严格按照提供的JSON Schema格式返回结果。`,
		PromptTemplate: `请为游戏世界生成一个物品。

用户需求：{{.user_prompt}}

{{if .world_theme}}世界主题：{{.world_theme}}{{end}}
{{if .item_category}}物品类别：{{.item_category}}{{end}}
{{if .target_rarity}}期望稀有度：{{.target_rarity}}{{end}}
{{if .intended_use}}预期用途：{{.intended_use}}{{end}}
{{if .special_requirements}}特殊要求：{{.special_requirements}}{{end}}

请生成一个详细的游戏物品，包含以下要素：
- 物品名称（富有特色且易记）
- 详细的外观描述
- 物品类型和子类型
- 完整的属性数据
- 使用效果和要求
- 制作材料和方法
- 背景故事或传说
- 尺寸和重量信息

确保物品设计合理且有游戏价值，能够丰富玩家的游戏体验。`,
		DefaultContext: map[string]interface{}{
			"world_theme": "奇幻",
			"target_rarity": "普通",
		},
	}

	// 世界描述模板
	ptm.templates["world_description"] = &PromptTemplate{
		SystemInstruction: `你是一个专业的世界构建师，擅长创造完整、一致且引人入胜的游戏世界。你的任务是根据用户的要求生成详细的世界描述。

请遵循以下原则：
1. 世界设定要内在一致且逻辑合理
2. 文化和社会结构要有深度
3. 地理和历史要相互呼应
4. 考虑世界的独特性和吸引力
5. 为冒险和故事提供丰富的背景
6. 平衡熟悉感和新奇感

请严格按照提供的JSON Schema格式返回结果。`,
		PromptTemplate: `请为游戏创建一个世界设定。

用户需求：{{.user_prompt}}

{{if .world_scale}}世界规模：{{.world_scale}}{{end}}
{{if .primary_theme}}主要主题：{{.primary_theme}}{{end}}
{{if .technology_level}}科技水平：{{.technology_level}}{{end}}
{{if .magic_presence}}魔法存在：{{.magic_presence}}{{end}}
{{if .conflict_focus}}冲突焦点：{{.conflict_focus}}{{end}}
{{if .special_requirements}}特殊要求：{{.special_requirements}}{{end}}

请生成一个完整的世界设定，包含以下要素：
- 世界名称（独特且富有意义）
- 总体描述和世界观
- 主要地理特征和气候
- 重要地点和城市
- 主要文化和种族
- 历史背景和重要事件
- 当前的冲突和问题
- 著名人物和组织
- 世界规则和体系
- 冒险机会和危险

确保世界设定丰富且有深度，能够支撑长期的游戏体验。`,
		DefaultContext: map[string]interface{}{
			"primary_theme": "奇幻",
			"technology_level": "中世纪",
			"magic_presence": "存在",
		},
	}

	// 任务生成模板
	ptm.templates["quest"] = &PromptTemplate{
		SystemInstruction: `你是一个专业的任务设计师，擅长创造有趣、有挑战性且有意义的游戏任务。你的任务是根据用户的要求生成详细的游戏任务。

请遵循以下原则：
1. 任务目标要明确且可达成
2. 提供适当的挑战和奖励
3. 考虑任务的故事性和意义
4. 设计合理的任务步骤
5. 平衡难度和趣味性
6. 与世界观和角色发展相符

请严格按照提供的JSON Schema格式返回结果。`,
		PromptTemplate: `请为游戏生成一个任务。

用户需求：{{.user_prompt}}

{{if .quest_giver}}任务发布者：{{.quest_giver}}{{end}}
{{if .target_level}}目标等级：{{.target_level}}{{end}}
{{if .quest_theme}}任务主题：{{.quest_theme}}{{end}}
{{if .time_constraint}}时间限制：{{.time_constraint}}{{end}}
{{if .reward_type}}奖励类型：{{.reward_type}}{{end}}
{{if .location_focus}}主要地点：{{.location_focus}}{{end}}
{{if .special_requirements}}特殊要求：{{.special_requirements}}{{end}}

请生成一个详细的游戏任务，包含以下要素：
- 任务名称（吸引人且概括性强）
- 详细的任务描述和背景
- 任务类型和难度等级
- 预计完成时间和前置条件
- 详细的任务步骤
- 奖励和失败后果
- 时间限制和重复性
- 任务发布者和执行地点

确保任务有趣且有挑战性，能够推动角色成长和故事发展。`,
		DefaultContext: map[string]interface{}{
			"target_level": "普通",
			"quest_theme": "冒险探索",
		},
	}

	// 环境效果模板
	ptm.templates["environment_effect"] = &PromptTemplate{
		SystemInstruction: `你是一个专业的环境设计师，擅长创造富有氛围且影响游戏体验的环境效果。你的任务是根据用户的要求生成详细的环境效果。

请遵循以下原则：
1. 效果要有明确的视觉和游戏影响
2. 考虑效果的持续时间和强度
3. 确保效果与环境和故事相符
4. 平衡美观性和功能性
5. 提供有趣的互动机会
6. 考虑对玩家策略的影响

请严格按照提供的JSON Schema格式返回结果。`,
		PromptTemplate: `请为游戏环境生成一个效果。

用户需求：{{.user_prompt}}

{{if .environment_type}}环境类型：{{.environment_type}}{{end}}
{{if .effect_intensity}}效果强度：{{.effect_intensity}}{{end}}
{{if .duration_type}}持续类型：{{.duration_type}}{{end}}
{{if .gameplay_impact}}游戏影响：{{.gameplay_impact}}{{end}}
{{if .visual_style}}视觉风格：{{.visual_style}}{{end}}
{{if .special_requirements}}特殊要求：{{.special_requirements}}{{end}}

请生成一个详细的环境效果，包含以下要素：
- 效果名称（描述性且易理解）
- 详细的效果描述
- 效果类型和强度等级
- 持续时间和影响范围
- 视觉和音效表现
- 对游戏机制的影响
- 属性修正和状态变化
- 触发和结束条件

确保环境效果能够增强游戏氛围，提供有意义的战术考量。`,
		DefaultContext: map[string]interface{}{
			"effect_intensity": "中等",
			"duration_type": "临时",
		},
	}
}
