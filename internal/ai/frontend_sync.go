package ai

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"ai-text-game-iam-npc/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

/**
 * 前端状态同步器
 * @description 实现与前端的实时状态同步和WebSocket通信
 */

// FrontendMessage 前端消息
type FrontendMessage struct {
	ID        string                 `json:"id"`         // 消息ID
	Type      string                 `json:"type"`       // 消息类型
	Event     string                 `json:"event"`      // 事件名称
	Data      map[string]interface{} `json:"data"`       // 消息数据
	Timestamp time.Time              `json:"timestamp"`  // 时间戳
	UserID    string                 `json:"user_id"`    // 用户ID
	WorldID   string                 `json:"world_id"`   // 世界ID
}

// WebSocketConnection WebSocket连接
type WebSocketConnection struct {
	ID       string                 // 连接ID
	UserID   string                 // 用户ID
	WorldID  string                 // 世界ID
	Channel  chan *FrontendMessage  // 消息通道
	Context  map[string]interface{} // 连接上下文
	LastPing time.Time              // 最后心跳时间
}

// FrontendSyncManager 前端同步管理器
type FrontendSyncManager struct {
	connections map[string]*WebSocketConnection
	userConns   map[string][]*WebSocketConnection // 用户连接映射
	worldConns  map[string][]*WebSocketConnection // 世界连接映射
	mutex       sync.RWMutex
	logger      logger.Logger
	
	// 配置
	pingInterval    time.Duration
	connectionTTL   time.Duration
	maxConnections  int
	messageBuffer   int
}

// NewFrontendSyncManager 创建前端同步管理器
func NewFrontendSyncManager(log logger.Logger) *FrontendSyncManager {
	manager := &FrontendSyncManager{
		connections:    make(map[string]*WebSocketConnection),
		userConns:     make(map[string][]*WebSocketConnection),
		worldConns:    make(map[string][]*WebSocketConnection),
		logger:        log,
		pingInterval:  30 * time.Second,
		connectionTTL: 5 * time.Minute,
		maxConnections: 1000,
		messageBuffer: 100,
	}
	
	// 启动清理协程
	go manager.cleanupLoop()
	
	return manager
}

// AddConnection 添加WebSocket连接
func (fsm *FrontendSyncManager) AddConnection(userID, worldID string) *WebSocketConnection {
	fsm.mutex.Lock()
	defer fsm.mutex.Unlock()
	
	// 检查连接数限制
	if len(fsm.connections) >= fsm.maxConnections {
		fsm.logger.Warn("连接数已达上限", "max_connections", fsm.maxConnections)
		return nil
	}
	
	conn := &WebSocketConnection{
		ID:       uuid.New().String(),
		UserID:   userID,
		WorldID:  worldID,
		Channel:  make(chan *FrontendMessage, fsm.messageBuffer),
		Context:  make(map[string]interface{}),
		LastPing: time.Now(),
	}
	
	// 存储连接
	fsm.connections[conn.ID] = conn
	
	// 添加到用户连接映射
	fsm.userConns[userID] = append(fsm.userConns[userID], conn)
	
	// 添加到世界连接映射
	if worldID != "" {
		fsm.worldConns[worldID] = append(fsm.worldConns[worldID], conn)
	}
	
	fsm.logger.Info("新的WebSocket连接", "conn_id", conn.ID, "user_id", userID, "world_id", worldID)
	return conn
}

// RemoveConnection 移除WebSocket连接
func (fsm *FrontendSyncManager) RemoveConnection(connID string) {
	fsm.mutex.Lock()
	defer fsm.mutex.Unlock()
	
	conn, exists := fsm.connections[connID]
	if !exists {
		return
	}
	
	// 从连接映射中移除
	delete(fsm.connections, connID)
	
	// 从用户连接映射中移除
	if userConns, exists := fsm.userConns[conn.UserID]; exists {
		for i, c := range userConns {
			if c.ID == connID {
				fsm.userConns[conn.UserID] = append(userConns[:i], userConns[i+1:]...)
				break
			}
		}
		// 如果用户没有其他连接，删除映射
		if len(fsm.userConns[conn.UserID]) == 0 {
			delete(fsm.userConns, conn.UserID)
		}
	}
	
	// 从世界连接映射中移除
	if conn.WorldID != "" {
		if worldConns, exists := fsm.worldConns[conn.WorldID]; exists {
			for i, c := range worldConns {
				if c.ID == connID {
					fsm.worldConns[conn.WorldID] = append(worldConns[:i], worldConns[i+1:]...)
					break
				}
			}
			// 如果世界没有其他连接，删除映射
			if len(fsm.worldConns[conn.WorldID]) == 0 {
				delete(fsm.worldConns, conn.WorldID)
			}
		}
	}
	
	// 关闭通道
	close(conn.Channel)
	
	fsm.logger.Info("WebSocket连接已移除", "conn_id", connID, "user_id", conn.UserID)
}

// SendToUser 发送消息给指定用户
func (fsm *FrontendSyncManager) SendToUser(userID string, message *FrontendMessage) {
	fsm.mutex.RLock()
	defer fsm.mutex.RUnlock()
	
	connections, exists := fsm.userConns[userID]
	if !exists {
		fsm.logger.Debug("用户没有活跃连接", "user_id", userID)
		return
	}
	
	message.UserID = userID
	message.Timestamp = time.Now()
	
	for _, conn := range connections {
		select {
		case conn.Channel <- message:
			fsm.logger.Debug("消息已发送给用户", "user_id", userID, "conn_id", conn.ID, "message_type", message.Type)
		default:
			fsm.logger.Warn("用户连接通道已满", "user_id", userID, "conn_id", conn.ID)
		}
	}
}

// SendToWorld 发送消息给世界中的所有用户
func (fsm *FrontendSyncManager) SendToWorld(worldID string, message *FrontendMessage) {
	fsm.mutex.RLock()
	defer fsm.mutex.RUnlock()
	
	connections, exists := fsm.worldConns[worldID]
	if !exists {
		fsm.logger.Debug("世界没有活跃连接", "world_id", worldID)
		return
	}
	
	message.WorldID = worldID
	message.Timestamp = time.Now()
	
	for _, conn := range connections {
		select {
		case conn.Channel <- message:
			fsm.logger.Debug("消息已发送给世界", "world_id", worldID, "conn_id", conn.ID, "message_type", message.Type)
		default:
			fsm.logger.Warn("世界连接通道已满", "world_id", worldID, "conn_id", conn.ID)
		}
	}
}

// BroadcastToAll 广播消息给所有连接
func (fsm *FrontendSyncManager) BroadcastToAll(message *FrontendMessage) {
	fsm.mutex.RLock()
	defer fsm.mutex.RUnlock()
	
	message.Timestamp = time.Now()
	
	for _, conn := range fsm.connections {
		select {
		case conn.Channel <- message:
			fsm.logger.Debug("广播消息已发送", "conn_id", conn.ID, "message_type", message.Type)
		default:
			fsm.logger.Warn("连接通道已满，跳过广播", "conn_id", conn.ID)
		}
	}
}

// UpdateConnectionPing 更新连接心跳
func (fsm *FrontendSyncManager) UpdateConnectionPing(connID string) {
	fsm.mutex.Lock()
	defer fsm.mutex.Unlock()
	
	if conn, exists := fsm.connections[connID]; exists {
		conn.LastPing = time.Now()
	}
}

// GetConnectionStats 获取连接统计信息
func (fsm *FrontendSyncManager) GetConnectionStats() map[string]interface{} {
	fsm.mutex.RLock()
	defer fsm.mutex.RUnlock()
	
	return map[string]interface{}{
		"total_connections": len(fsm.connections),
		"users_online":      len(fsm.userConns),
		"active_worlds":     len(fsm.worldConns),
		"max_connections":   fsm.maxConnections,
		"ping_interval":     fsm.pingInterval.String(),
		"connection_ttl":    fsm.connectionTTL.String(),
	}
}

// cleanupLoop 清理过期连接
func (fsm *FrontendSyncManager) cleanupLoop() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		fsm.cleanupExpiredConnections()
	}
}

// cleanupExpiredConnections 清理过期连接
func (fsm *FrontendSyncManager) cleanupExpiredConnections() {
	fsm.mutex.Lock()
	defer fsm.mutex.Unlock()
	
	cutoff := time.Now().Add(-fsm.connectionTTL)
	expiredConns := make([]string, 0)
	
	for connID, conn := range fsm.connections {
		if conn.LastPing.Before(cutoff) {
			expiredConns = append(expiredConns, connID)
		}
	}
	
	for _, connID := range expiredConns {
		fsm.logger.Info("清理过期连接", "conn_id", connID)
		fsm.RemoveConnection(connID)
	}
	
	if len(expiredConns) > 0 {
		fsm.logger.Info("连接清理完成", "cleaned_count", len(expiredConns))
	}
}

// 消息构建辅助方法

// CreateTaskStatusMessage 创建任务状态消息
func (fsm *FrontendSyncManager) CreateTaskStatusMessage(taskID string, status TaskStatus, progress float64, data map[string]interface{}) *FrontendMessage {
	return &FrontendMessage{
		ID:   uuid.New().String(),
		Type: "task_status",
		Event: "task_update",
		Data: map[string]interface{}{
			"task_id":  taskID,
			"status":   string(status),
			"progress": progress,
			"data":     data,
		},
	}
}

// CreateContentGeneratedMessage 创建内容生成消息
func (fsm *FrontendSyncManager) CreateContentGeneratedMessage(contentType string, contentID string, data map[string]interface{}) *FrontendMessage {
	return &FrontendMessage{
		ID:   uuid.New().String(),
		Type: "content_generated",
		Event: fmt.Sprintf("%s_created", contentType),
		Data: map[string]interface{}{
			"content_type": contentType,
			"content_id":   contentID,
			"data":         data,
		},
	}
}

// CreateErrorMessage 创建错误消息
func (fsm *FrontendSyncManager) CreateErrorMessage(errorType string, message string, details map[string]interface{}) *FrontendMessage {
	return &FrontendMessage{
		ID:   uuid.New().String(),
		Type: "error",
		Event: "error_occurred",
		Data: map[string]interface{}{
			"error_type": errorType,
			"message":    message,
			"details":    details,
		},
	}
}

// CreateSystemMessage 创建系统消息
func (fsm *FrontendSyncManager) CreateSystemMessage(messageType string, content string, data map[string]interface{}) *FrontendMessage {
	return &FrontendMessage{
		ID:   uuid.New().String(),
		Type: "system",
		Event: messageType,
		Data: map[string]interface{}{
			"content": content,
			"data":    data,
		},
	}
}

// WebSocket处理器

// HandleWebSocket 处理WebSocket连接
func (fsm *FrontendSyncManager) HandleWebSocket(c *gin.Context) {
	// 这里应该实现WebSocket升级逻辑
	// 由于这是一个示例，我们提供基本的结构
	
	userID := c.Query("user_id")
	worldID := c.Query("world_id")
	
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少用户ID"})
		return
	}
	
	// 创建连接
	conn := fsm.AddConnection(userID, worldID)
	if conn == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "无法创建连接"})
		return
	}
	
	// 在实际实现中，这里应该处理WebSocket升级和消息循环
	// 这里只是返回连接信息作为示例
	c.JSON(http.StatusOK, gin.H{
		"connection_id": conn.ID,
		"user_id":       userID,
		"world_id":      worldID,
		"status":        "connected",
	})
}

// HandleWebSocketMessage 处理WebSocket消息
func (fsm *FrontendSyncManager) HandleWebSocketMessage(connID string, messageData []byte) {
	var message FrontendMessage
	if err := json.Unmarshal(messageData, &message); err != nil {
		fsm.logger.Error("解析WebSocket消息失败", "conn_id", connID, "error", err)
		return
	}
	
	// 更新心跳
	fsm.UpdateConnectionPing(connID)
	
	// 处理不同类型的消息
	switch message.Type {
	case "ping":
		fsm.handlePingMessage(connID, &message)
	case "subscribe":
		fsm.handleSubscribeMessage(connID, &message)
	case "unsubscribe":
		fsm.handleUnsubscribeMessage(connID, &message)
	default:
		fsm.logger.Warn("未知的WebSocket消息类型", "conn_id", connID, "type", message.Type)
	}
}

// handlePingMessage 处理心跳消息
func (fsm *FrontendSyncManager) handlePingMessage(connID string, message *FrontendMessage) {
	// 发送pong响应
	pongMessage := &FrontendMessage{
		ID:        uuid.New().String(),
		Type:      "pong",
		Event:     "heartbeat",
		Timestamp: time.Now(),
		Data:      map[string]interface{}{"server_time": time.Now().Unix()},
	}
	
	fsm.mutex.RLock()
	if conn, exists := fsm.connections[connID]; exists {
		select {
		case conn.Channel <- pongMessage:
			// 成功发送
		default:
			fsm.logger.Warn("无法发送pong消息，通道已满", "conn_id", connID)
		}
	}
	fsm.mutex.RUnlock()
}

// handleSubscribeMessage 处理订阅消息
func (fsm *FrontendSyncManager) handleSubscribeMessage(connID string, message *FrontendMessage) {
	// 实现订阅逻辑
	fsm.logger.Info("处理订阅请求", "conn_id", connID, "event", message.Event)
}

// handleUnsubscribeMessage 处理取消订阅消息
func (fsm *FrontendSyncManager) handleUnsubscribeMessage(connID string, message *FrontendMessage) {
	// 实现取消订阅逻辑
	fsm.logger.Info("处理取消订阅请求", "conn_id", connID, "event", message.Event)
}
