package ai

import (
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
)

/**
 * JSON Schema验证器
 * @description 用于验证AI生成的数据是否符合预定义的Schema
 */

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`   // 字段路径
	Message string `json:"message"` // 错误信息
	Value   interface{} `json:"value"`   // 实际值
}

// Error 实现error接口
func (ve *ValidationError) Error() string {
	return fmt.Sprintf("字段 '%s' 验证失败: %s (实际值: %v)", ve.Field, ve.Message, ve.Value)
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid  bool               `json:"valid"`  // 是否通过验证
	Errors []*ValidationError `json:"errors"` // 验证错误列表
}

// SchemaValidator JSON Schema验证器
type SchemaValidator struct {
	schema *JSONSchema
}

// NewSchemaValidator 创建Schema验证器
func NewSchemaValidator(schema *JSONSchema) *SchemaValidator {
	return &SchemaValidator{
		schema: schema,
	}
}

// Validate 验证数据是否符合Schema
func (sv *SchemaValidator) Validate(data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:  true,
		Errors: make([]*ValidationError, 0),
	}
	
	sv.validateValue(data, sv.schema, "", result)
	
	result.Valid = len(result.Errors) == 0
	return result
}

// validateValue 验证单个值
func (sv *SchemaValidator) validateValue(value interface{}, schema *JSONSchema, fieldPath string, result *ValidationResult) {
	if schema == nil {
		return
	}
	
	// 检查类型
	if !sv.validateType(value, schema.Type, fieldPath, result) {
		return // 类型不匹配，跳过其他验证
	}
	
	// 根据类型进行具体验证
	switch schema.Type {
	case "object":
		sv.validateObject(value, schema, fieldPath, result)
	case "array":
		sv.validateArray(value, schema, fieldPath, result)
	case "string":
		sv.validateString(value, schema, fieldPath, result)
	case "number", "integer":
		sv.validateNumber(value, schema, fieldPath, result)
	}
}

// validateType 验证数据类型
func (sv *SchemaValidator) validateType(value interface{}, expectedType string, fieldPath string, result *ValidationResult) bool {
	if expectedType == "" {
		return true // 没有指定类型，跳过验证
	}
	
	actualType := sv.getValueType(value)
	
	// 特殊处理：integer可以接受number
	if expectedType == "integer" && actualType == "number" {
		if num, ok := value.(float64); ok && num == float64(int64(num)) {
			return true // 是整数
		}
	}
	
	if actualType != expectedType {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: fmt.Sprintf("期望类型 %s，实际类型 %s", expectedType, actualType),
			Value:   value,
		})
		return false
	}
	
	return true
}

// validateObject 验证对象
func (sv *SchemaValidator) validateObject(value interface{}, schema *JSONSchema, fieldPath string, result *ValidationResult) {
	objMap, ok := value.(map[string]interface{})
	if !ok {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: "无法转换为对象类型",
			Value:   value,
		})
		return
	}
	
	// 检查必需字段
	for _, requiredField := range schema.Required {
		if _, exists := objMap[requiredField]; !exists {
			result.Errors = append(result.Errors, &ValidationError{
				Field:   sv.joinPath(fieldPath, requiredField),
				Message: "缺少必需字段",
				Value:   nil,
			})
		}
	}
	
	// 验证每个属性
	if schema.Properties != nil {
		for fieldName, fieldSchema := range schema.Properties {
			if fieldValue, exists := objMap[fieldName]; exists {
				sv.validateValue(fieldValue, fieldSchema, sv.joinPath(fieldPath, fieldName), result)
			}
		}
	}
}

// validateArray 验证数组
func (sv *SchemaValidator) validateArray(value interface{}, schema *JSONSchema, fieldPath string, result *ValidationResult) {
	arr, ok := value.([]interface{})
	if !ok {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: "无法转换为数组类型",
			Value:   value,
		})
		return
	}
	
	// 检查数组长度
	if schema.MinItems != nil && len(arr) < *schema.MinItems {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: fmt.Sprintf("数组长度不能少于 %d", *schema.MinItems),
			Value:   len(arr),
		})
	}
	
	if schema.MaxItems != nil && len(arr) > *schema.MaxItems {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: fmt.Sprintf("数组长度不能超过 %d", *schema.MaxItems),
			Value:   len(arr),
		})
	}
	
	// 验证数组项
	if schema.Items != nil {
		for i, item := range arr {
			itemPath := fmt.Sprintf("%s[%d]", fieldPath, i)
			sv.validateValue(item, schema.Items, itemPath, result)
		}
	}
}

// validateString 验证字符串
func (sv *SchemaValidator) validateString(value interface{}, schema *JSONSchema, fieldPath string, result *ValidationResult) {
	str, ok := value.(string)
	if !ok {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: "无法转换为字符串类型",
			Value:   value,
		})
		return
	}
	
	// 检查长度
	if schema.MinLength != nil && len(str) < *schema.MinLength {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: fmt.Sprintf("字符串长度不能少于 %d", *schema.MinLength),
			Value:   len(str),
		})
	}
	
	if schema.MaxLength != nil && len(str) > *schema.MaxLength {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: fmt.Sprintf("字符串长度不能超过 %d", *schema.MaxLength),
			Value:   len(str),
		})
	}
	
	// 检查枚举值
	if len(schema.Enum) > 0 {
		found := false
		for _, enumValue := range schema.Enum {
			if str == enumValue {
				found = true
				break
			}
		}
		if !found {
			result.Errors = append(result.Errors, &ValidationError{
				Field:   fieldPath,
				Message: fmt.Sprintf("值必须是以下之一: %s", strings.Join(schema.Enum, ", ")),
				Value:   str,
			})
		}
	}
	
	// 检查模式匹配
	if schema.Pattern != "" {
		if matched, err := regexp.MatchString(schema.Pattern, str); err != nil {
			result.Errors = append(result.Errors, &ValidationError{
				Field:   fieldPath,
				Message: fmt.Sprintf("正则表达式验证失败: %v", err),
				Value:   str,
			})
		} else if !matched {
			result.Errors = append(result.Errors, &ValidationError{
				Field:   fieldPath,
				Message: fmt.Sprintf("字符串不匹配模式: %s", schema.Pattern),
				Value:   str,
			})
		}
	}
}

// validateNumber 验证数字
func (sv *SchemaValidator) validateNumber(value interface{}, schema *JSONSchema, fieldPath string, result *ValidationResult) {
	var num float64
	var ok bool
	
	switch v := value.(type) {
	case float64:
		num = v
		ok = true
	case int:
		num = float64(v)
		ok = true
	case int64:
		num = float64(v)
		ok = true
	case string:
		if parsed, err := strconv.ParseFloat(v, 64); err == nil {
			num = parsed
			ok = true
		}
	}
	
	if !ok {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: "无法转换为数字类型",
			Value:   value,
		})
		return
	}
	
	// 检查范围
	if schema.Minimum != nil && num < *schema.Minimum {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: fmt.Sprintf("数值不能小于 %g", *schema.Minimum),
			Value:   num,
		})
	}
	
	if schema.Maximum != nil && num > *schema.Maximum {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: fmt.Sprintf("数值不能大于 %g", *schema.Maximum),
			Value:   num,
		})
	}
	
	// 对于integer类型，检查是否为整数
	if schema.Type == "integer" && num != float64(int64(num)) {
		result.Errors = append(result.Errors, &ValidationError{
			Field:   fieldPath,
			Message: "必须是整数",
			Value:   num,
		})
	}
}

// getValueType 获取值的类型
func (sv *SchemaValidator) getValueType(value interface{}) string {
	if value == nil {
		return "null"
	}
	
	switch value.(type) {
	case bool:
		return "boolean"
	case string:
		return "string"
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		return "integer"
	case float32, float64:
		return "number"
	case []interface{}:
		return "array"
	case map[string]interface{}:
		return "object"
	default:
		// 使用反射检查
		rv := reflect.ValueOf(value)
		switch rv.Kind() {
		case reflect.Slice, reflect.Array:
			return "array"
		case reflect.Map, reflect.Struct:
			return "object"
		default:
			return "unknown"
		}
	}
}

// joinPath 连接字段路径
func (sv *SchemaValidator) joinPath(parent, child string) string {
	if parent == "" {
		return child
	}
	return parent + "." + child
}
