package ai

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/validation"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// WindmillClient Windmill结构化文本接口客户端
type WindmillClient struct {
	baseURL      string
	workspace    string
	token        string
	client       *http.Client
	logger       logger.Logger
	maxRetries   int
	retryDelay   time.Duration
	errorHandler *ErrorHandler
}

// NewWindmillClient 创建Windmill客户端
func NewWindmillClient(cfg *config.Config, log logger.Logger) *WindmillClient {
	// 创建重试策略
	retryStrategy := &RetryStrategy{
		MaxRetries:    cfg.AI.MaxRetries,
		BaseDelay:     cfg.AI.RetryDelay,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: true,
		RetryableErrors: []ErrorType{
			ErrorTypeNetwork,
			ErrorTypeServer,
			ErrorTypeTimeout,
			ErrorTypeRateLimit,
		},
	}

	return &WindmillClient{
		baseURL:      cfg.AI.BaseURL,
		workspace:    cfg.AI.Windmill.Workspace,
		token:        cfg.AI.Token,
		client:       &http.Client{Timeout: cfg.AI.Timeout},
		logger:       log,
		maxRetries:   cfg.AI.MaxRetries,
		retryDelay:   cfg.AI.RetryDelay,
		errorHandler: NewErrorHandler(retryStrategy, log),
	}
}

// WindmillRequest Windmill API请求结构
type WindmillRequest struct {
	Model             string                 `json:"model"`                       // 模型名称，必填
	Prompt            string                 `json:"prompt"`                      // 主提示词，必填
	SystemInstruction string                 `json:"system_instruction,omitempty"` // 系统指令，可选
	ResponseSchema    map[string]interface{} `json:"responseSchema"`              // 响应结构定义，必填
}

// WindmillJobResponse Windmill任务提交响应
type WindmillJobResponse struct {
	JobID string `json:"job_id"` // 任务ID
}

// WindmillResultResponse Windmill结果查询响应
type WindmillResultResponse struct {
	Completed bool                   `json:"completed"` // 是否完成
	Result    map[string]interface{} `json:"result"`    // 结果数据
	Error     string                 `json:"error"`     // 错误信息
}

// GenerateStructuredContent 调用Windmill结构化文本生成接口
func (wc *WindmillClient) GenerateStructuredContent(ctx context.Context, req *WindmillRequest) (map[string]interface{}, error) {
	wc.logger.Info("开始调用Windmill结构化文本接口",
		"model", req.Model,
		"prompt_length", len(req.Prompt),
		"has_system_instruction", req.SystemInstruction != "")

	// 第一步：提交任务
	jobID, err := wc.submitJob(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("提交Windmill任务失败: %w", err)
	}

	wc.logger.Info("Windmill任务提交成功", "job_id", jobID)

	// 第二步：轮询获取结果
	result, err := wc.pollResult(ctx, jobID)
	if err != nil {
		return nil, fmt.Errorf("获取Windmill任务结果失败: %w", err)
	}

	wc.logger.Info("Windmill任务完成", "job_id", jobID)
	return result, nil
}

// submitJob 提交任务到Windmill
func (wc *WindmillClient) submitJob(ctx context.Context, req *WindmillRequest) (string, error) {
	// 构建请求URL，使用配置的工作空间
	submitURL := fmt.Sprintf("%s/api/w/%s/jobs/run/p/f/gemini/js_structured_output", wc.baseURL, wc.workspace)

	// 序列化请求体
	requestBody, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("序列化请求失败: %w", err)
	}

	wc.logger.Debug("Windmill任务提交请求", "url", submitURL, "body", string(requestBody))

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", submitURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	if wc.token != "" {
		httpReq.Header.Set("Authorization", "Bearer "+wc.token)
	}

	// 发送请求，带重试机制
	var resp *http.Response
	var lastErr error

	for attempt := 0; attempt <= wc.maxRetries; attempt++ {
		if attempt > 0 {
			wc.logger.Info("重试Windmill任务提交", "attempt", attempt, "max_retries", wc.maxRetries)
			time.Sleep(wc.retryDelay * time.Duration(attempt))
		}

		resp, lastErr = wc.client.Do(httpReq)
		if lastErr == nil && resp.StatusCode < 500 {
			break
		}

		if resp != nil {
			resp.Body.Close()
		}
	}

	if lastErr != nil {
		return "", fmt.Errorf("发送请求失败: %w", lastErr)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	wc.logger.Debug("Windmill任务提交响应", "status", resp.StatusCode, "body", string(responseBody))

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("任务提交失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应获取任务ID
	// 根据需求文档，直接返回的是UUID字符串
	jobID := strings.Trim(string(responseBody), "\"")
	if jobID == "" {
		return "", fmt.Errorf("任务ID为空")
	}

	return jobID, nil
}

// pollResult 轮询获取任务结果
func (wc *WindmillClient) pollResult(ctx context.Context, jobID string) (map[string]interface{}, error) {
	// 构建查询URL，使用配置的工作空间
	resultURL := fmt.Sprintf("%s/api/w/%s/jobs_u/completed/get_result_maybe/%s", wc.baseURL, wc.workspace, jobID)

	wc.logger.Debug("开始轮询Windmill任务结果", "job_id", jobID, "url", resultURL)

	// 设置轮询参数
	pollInterval := 2 * time.Second  // 轮询间隔，增加到2秒减少服务器压力
	maxPollTime := 2 * time.Minute   // 最大轮询时间，减少到2分钟避免长时间挂起
	startTime := time.Now()

	wc.logger.Info("开始轮询任务结果",
		"job_id", jobID,
		"poll_interval", pollInterval,
		"max_poll_time", maxPollTime)

	for {
		// 检查是否超时
		elapsed := time.Since(startTime)
		if elapsed > maxPollTime {
			wc.logger.Error("轮询超时",
				"job_id", jobID,
				"elapsed", elapsed,
				"max_poll_time", maxPollTime)
			return nil, fmt.Errorf("轮询超时，任务ID: %s，已耗时: %v", jobID, elapsed)
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			wc.logger.Warn("轮询被取消", "job_id", jobID, "reason", ctx.Err())
			return nil, ctx.Err()
		default:
		}

		// 创建查询请求
		httpReq, err := http.NewRequestWithContext(ctx, "GET", resultURL, nil)
		if err != nil {
			return nil, fmt.Errorf("创建查询请求失败: %w", err)
		}

		// 设置请求头
		if wc.token != "" {
			httpReq.Header.Set("Authorization", "Bearer "+wc.token)
		}

		// 发送查询请求
		resp, err := wc.client.Do(httpReq)
		if err != nil {
			wc.logger.Warn("查询任务结果失败，将重试", "error", err, "job_id", jobID)
			time.Sleep(pollInterval)
			continue
		}

		// 读取响应
		responseBody, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			wc.logger.Warn("读取查询响应失败，将重试", "error", err, "job_id", jobID)
			time.Sleep(pollInterval)
			continue
		}

		wc.logger.Debug("Windmill任务查询响应", "status", resp.StatusCode, "body", string(responseBody))

		if resp.StatusCode != http.StatusOK {
			wc.logger.Warn("查询请求失败，将重试", "status", resp.StatusCode, "response", string(responseBody))
			time.Sleep(pollInterval)
			continue
		}

		// 解析响应
		var resultResp WindmillResultResponse
		if err := json.Unmarshal(responseBody, &resultResp); err != nil {
			wc.logger.Warn("解析查询响应失败，将重试", "error", err, "response", string(responseBody))
			time.Sleep(pollInterval)
			continue
		}

		// 检查是否完成
		if !resultResp.Completed {
			wc.logger.Debug("任务尚未完成，继续轮询", "job_id", jobID)
			time.Sleep(pollInterval)
			continue
		}

		// 检查是否有错误
		if resultResp.Error != "" {
			return nil, fmt.Errorf("Windmill任务执行失败: %s", resultResp.Error)
		}

		// 返回结果
		wc.logger.Info("Windmill任务执行成功", "job_id", jobID)
		return resultResp.Result, nil
	}
}

// Service AI服务
type Service struct {
	config          *config.Config
	db              *gorm.DB
	client          *http.Client
	windmillClient  *WindmillClient
	schemaRegistry  *GameSchemaRegistry
	promptManager   *PromptTemplateManager
	configManager   *AIConfigManager
	transformer     *DataTransformer
	asyncManager    *AsyncTaskManager
	dataFlow        *DataFlowProcessor
	frontendSync    *FrontendSyncManager
	jsonValidator   *validation.JSONValidator
	logger          logger.Logger
}

// NewService 创建AI服务
func NewService(cfg *config.Config, db *gorm.DB, log logger.Logger) *Service {
	// 创建基础组件
	transformer := NewDataTransformer(log)
	frontendSync := NewFrontendSyncManager(log)

	service := &Service{
		config: cfg,
		db:     db,
		client: &http.Client{
			Timeout: cfg.AI.Timeout,
		},
		windmillClient: NewWindmillClient(cfg, log),
		schemaRegistry: NewGameSchemaRegistry(),
		promptManager:  NewPromptTemplateManager(),
		configManager:  NewAIConfigManager(cfg),
		transformer:    transformer,
		frontendSync:   frontendSync,
		jsonValidator:  validation.NewJSONValidator(log),
		logger:         log,
	}

	// 创建数据流处理器
	service.dataFlow = NewDataFlowProcessor(db, transformer, log)

	// 创建异步任务管理器
	service.asyncManager = NewAsyncTaskManager(db, service, 5, 100, log)

	// 启动数据流处理器
	service.dataFlow.Start()

	return service
}

// GenerateRequest AI生成请求
// @Description AI内容生成请求结构，用于指定生成内容的类型、提示词和相关参数
type GenerateRequest struct {
	Type           string                 `json:"type" binding:"required" example:"scene" enums:"scene,character,event,dialogue,world,item"`            // 生成类型，必填，指定要生成的内容类型：scene(场景)、character(角色)、event(事件)、dialogue(对话)、world(世界)、item(物品)等
	Prompt         string                 `json:"prompt" binding:"required" example:"生成一个神秘的森林场景，充满魔法气息"`          // 提示词，必填，描述要生成内容的具体要求和特征，越详细越能生成符合期望的内容
	Context        map[string]interface{} `json:"context,omitempty" example:"{\"world_theme\":\"奇幻\",\"current_location\":\"精灵王国\"}"`         // 上下文信息，可选，提供生成内容所需的背景信息和相关数据，如世界设定、相关角色等
	Schema         map[string]interface{} `json:"schema,omitempty" example:"{\"name\":\"string\",\"description\":\"string\",\"type\":\"string\"}"`          // 期望的响应结构，可选，定义生成内容的数据结构格式，用于约束AI输出的结构
	MaxTokens      int                    `json:"max_tokens,omitempty" default:"500" minimum:"1" maximum:"2000" example:"500"`      // 最大token数，可选，限制生成内容的长度，数值越大生成的内容越详细，默认500
	Temperature    float64                `json:"temperature,omitempty" default:"0.7" minimum:"0.0" maximum:"1.0" example:"0.7"`     // 温度参数，可选，控制生成内容的创造性，范围0.0-1.0，0.0最保守，1.0最有创意，默认0.7
	WorldID        *string                `json:"world_id,omitempty" format:"uuid" example:"123e4567-e89b-12d3-a456-************"`        // 世界ID，可选，指定内容所属的游戏世界，用于保持世界观一致性
	UserID         *string                `json:"user_id,omitempty" format:"uuid"`         // 用户ID，可选，标识请求用户，用于个性化生成和使用统计，通常由系统自动填充
}

// GenerateResponse AI生成响应
// @Description AI内容生成响应结构，包含生成的内容和相关统计信息
type GenerateResponse struct {
	Content        string                 `json:"content" example:"这是一个充满神秘气息的古老森林，高大的橡树遮天蔽日..."`         // 生成的文本内容，包含AI生成的主要文本描述
	StructuredData map[string]interface{} `json:"structured_data" example:"{\"name\":\"神秘森林\",\"type\":\"outdoor\",\"atmosphere\":\"mysterious\"}"`  // 结构化数据，包含按照指定schema格式化的生成结果
	TokenUsage     int                    `json:"token_usage" example:"156"`     // 使用的token数量，用于计费和使用统计
	ResponseTime   int                    `json:"response_time" example:"1250"`   // 响应时间，单位毫秒，表示AI生成内容所用的时间
}

// ChatRequest 对话请求结构
type ChatRequest struct {
	Messages       []models.MessageRequest `json:"messages" binding:"required,min=1"`        // 消息列表
	ConversationID *string                 `json:"conversation_id,omitempty"`               // 对话ID（可选）
	Model          string                  `json:"model" binding:"required"`                // AI模型名称
	Stream         bool                    `json:"stream"`                                  // 是否使用流式响应
	MaxTokens      *int                    `json:"max_tokens,omitempty"`                    // 最大token数量
	Temperature    *float64                `json:"temperature,omitempty"`                   // 温度参数
	WorldID        *string                 `json:"world_id,omitempty"`                      // 关联的游戏世界ID
	UserID         string                  `json:"user_id" binding:"required"`              // 用户ID
}

// ChatResponse 对话响应结构
type ChatResponse struct {
	ConversationID string                 `json:"conversation_id"`            // 对话ID
	MessageID      string                 `json:"message_id"`                 // 新生成的消息ID
	Content        string                 `json:"content"`                    // 响应内容
	Model          string                 `json:"model"`                      // 使用的模型
	TokenUsage     int                    `json:"token_usage"`                // token使用量
	FinishReason   string                 `json:"finish_reason"`              // 完成原因
	StructuredData map[string]interface{} `json:"structured_data,omitempty"`  // 结构化数据
	ResponseTime   int                    `json:"response_time"`              // 响应时间
}

// StreamWriter 流式响应写入器接口
type StreamWriter interface {
	WriteChunk(chunk *models.StreamChunk) error
	Close() error
}

// GenerateContent 生成内容
func (s *Service) GenerateContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	startTime := time.Now()
	
	// 记录AI交互日志
	interaction := &models.AIInteraction{
		WorldID:         req.WorldID,
		UserID:          req.UserID,
		InteractionType: req.Type,
		Prompt:          req.Prompt,
		ResponseSchema:  models.JSON(req.Schema),
		Status:          "pending",
	}
	
	if err := s.db.Create(interaction).Error; err != nil {
		s.logger.Error("创建AI交互记录失败", "error", err)
	}
	
	var response *GenerateResponse
	var err error
	
	// 根据配置决定使用真实API还是Mock
	if s.config.AI.MockEnabled {
		response, err = s.generateMockContent(req)
	} else {
		response, err = s.generateRealContent(ctx, req)
	}
	
	responseTime := int(time.Since(startTime).Milliseconds())
	
	// 更新交互记录
	if err != nil {
		interaction.SetFailed(s.db, err.Error())
	} else {
		responseJSON, _ := json.Marshal(response.StructuredData)
		interaction.SetCompleted(s.db, string(responseJSON), response.TokenUsage, responseTime)
	}
	
	if response != nil {
		response.ResponseTime = responseTime
	}
	
	return response, err
}

// generateRealContent 调用真实的Windmill结构化文本API生成内容
func (s *Service) generateRealContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	s.logger.Info("开始调用Windmill结构化文本API",
		"type", req.Type,
		"world_id", req.WorldID,
		"prompt_length", len(req.Prompt))

	// 使用提示词模板构建请求
	prompt, systemInstruction, err := s.promptManager.BuildPrompt(req.Type, req.Prompt, req.Context)
	if err != nil {
		s.logger.Warn("使用提示词模板失败，回退到默认方式", "error", err)
		prompt = req.Prompt
		systemInstruction = s.buildSystemPrompt(req.Type)
	}

	// 构建Windmill请求
	windmillReq := &WindmillRequest{
		Model:             s.getModelForType(req.Type),
		Prompt:            prompt,
		SystemInstruction: systemInstruction,
		ResponseSchema:    s.buildResponseSchema(req.Type, req.Schema),
	}

	// 调用Windmill结构化文本接口
	result, err := s.windmillClient.GenerateStructuredContent(ctx, windmillReq)
	if err != nil {
		s.logger.Error("Windmill结构化文本API调用失败", "error", err)
		return nil, fmt.Errorf("调用Windmill API失败: %w", err)
	}

	// 构建响应
	response := &GenerateResponse{
		StructuredData: result,
		TokenUsage:     s.estimateTokenUsage(req.Prompt, result), // 估算token使用量
	}

	// 提取内容文本
	if content, ok := s.extractContentFromResult(result); ok {
		response.Content = content
	} else {
		// 如果无法提取内容，使用结构化数据的JSON字符串
		if contentBytes, err := json.Marshal(result); err == nil {
			response.Content = string(contentBytes)
		}
	}

	s.logger.Info("Windmill结构化文本API调用成功",
		"type", req.Type,
		"token_usage", response.TokenUsage,
		"content_length", len(response.Content))

	return response, nil
}

// getModelForType 根据内容类型获取合适的模型
func (s *Service) getModelForType(contentType string) string {
	// 使用配置管理器获取最优模型
	if model, err := s.configManager.GetOptimalModelForContentType(contentType); err == nil {
		return model
	}

	// 如果配置管理器失败，回退到默认模型
	s.logger.Warn("获取最优模型失败，使用默认模型", "content_type", contentType)
	return s.config.AI.Windmill.DefaultModel
}

// buildResponseSchema 构建响应结构定义
func (s *Service) buildResponseSchema(contentType string, customSchema map[string]interface{}) map[string]interface{} {
	// 如果提供了自定义Schema，优先使用
	if customSchema != nil && len(customSchema) > 0 {
		s.logger.Debug("使用自定义Schema", "content_type", contentType)
		return customSchema
	}

	// 尝试从Schema注册表获取预定义的Schema
	if schema, err := s.schemaRegistry.GetSchemaAsMap(contentType); err == nil {
		s.logger.Debug("使用注册表Schema", "content_type", contentType)
		return schema
	}

	// 如果注册表中没有找到，使用旧的默认Schema作为后备
	s.logger.Warn("未找到预定义Schema，使用默认Schema", "content_type", contentType)

	// 根据内容类型构建默认的响应结构
	switch contentType {
	case "scene":
		return map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"name": map[string]interface{}{
					"type":        "string",
					"description": "场景名称",
				},
				"description": map[string]interface{}{
					"type":        "string",
					"description": "详细的场景描述，包含环境、氛围、关键特征等",
				},
				"atmosphere": map[string]interface{}{
					"type":        "string",
					"description": "场景氛围（如：神秘、温馨、紧张等）",
				},
				"key_features": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "场景的关键特征列表",
				},
				"possible_actions": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "在此场景中可能的行动列表",
				},
				"connections": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"direction": map[string]interface{}{
								"type":        "string",
								"description": "方向（如：北、南、东、西）",
							},
							"description": map[string]interface{}{
								"type":        "string",
								"description": "连接描述",
							},
							"scene_name": map[string]interface{}{
								"type":        "string",
								"description": "连接的场景名称",
							},
						},
						"required": []string{"direction", "description", "scene_name"},
					},
					"description": "场景连接信息",
				},
			},
			"required": []string{"name", "description", "atmosphere", "key_features", "possible_actions", "connections"},
		}

	case "character":
		return map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"name": map[string]interface{}{
					"type":        "string",
					"description": "角色名称",
				},
				"description": map[string]interface{}{
					"type":        "string",
					"description": "角色的外观和基本描述",
				},
				"personality": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "性格特征列表",
				},
				"background": map[string]interface{}{
					"type":        "string",
					"description": "角色背景故事",
				},
				"skills": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "技能列表",
				},
				"dialogue_style": map[string]interface{}{
					"type":        "string",
					"description": "对话风格描述",
				},
				"motivations": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "动机列表",
				},
			},
			"required": []string{"name", "description", "personality", "background", "skills", "dialogue_style", "motivations"},
		}

	case "event":
		return map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"name": map[string]interface{}{
					"type":        "string",
					"description": "事件名称",
				},
				"description": map[string]interface{}{
					"type":        "string",
					"description": "事件详细描述",
				},
				"type": map[string]interface{}{
					"type":        "string",
					"description": "事件类型（如：random、plot、character、environmental）",
				},
				"priority": map[string]interface{}{
					"type":        "integer",
					"description": "事件优先级（1-10的数字）",
					"minimum":     1,
					"maximum":     10,
				},
				"duration": map[string]interface{}{
					"type":        "integer",
					"description": "事件持续时间（分钟）",
					"minimum":     1,
				},
				"effects": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"description": map[string]interface{}{
							"type":        "string",
							"description": "事件效果描述",
						},
						"consequences": map[string]interface{}{
							"type": "array",
							"items": map[string]interface{}{
								"type": "string",
							},
							"description": "事件后果列表",
						},
					},
					"required":    []string{"description", "consequences"},
					"description": "事件效果",
				},
			},
			"required": []string{"name", "description", "type", "priority", "duration", "effects"},
		}

	case "world":
		// 世界生成的Schema，基于我们创建的JSON Schema
		return map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"worldDescription": map[string]interface{}{
					"type":        "string",
					"description": "对游戏世界的详细描述，包括整体氛围、主要特征和背景设定",
					"minLength":   50,
					"maxLength":   2000,
				},
				"worldRules": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"name": map[string]interface{}{
								"type":        "string",
								"description": "规则名称",
								"maxLength":   100,
							},
							"description": map[string]interface{}{
								"type":        "string",
								"description": "规则详细说明",
								"maxLength":   500,
							},
							"category": map[string]interface{}{
								"type":        "string",
								"description": "规则类别",
								"enum":        []string{"magic", "combat", "social", "economic", "environmental", "character", "other"},
							},
							"severity": map[string]interface{}{
								"type":        "string",
								"description": "违规严重性",
								"enum":        []string{"low", "medium", "high", "critical"},
							},
							"enforcement": map[string]interface{}{
								"type":        "string",
								"description": "执行方式",
								"enum":        []string{"automatic", "manual", "community", "none"},
							},
						},
						"required": []string{"name", "description"},
					},
					"description": "世界运行的基本规则和限制",
					"maxItems":    20,
				},
				"environment": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"climate": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"type": map[string]interface{}{
									"type": "string",
									"enum": []string{"tropical", "temperate", "arctic", "desert", "mediterranean", "continental", "mixed"},
								},
								"seasons": map[string]interface{}{
									"type": "array",
									"items": map[string]interface{}{
										"type": "string",
										"enum": []string{"spring", "summer", "autumn", "winter"},
									},
								},
								"weatherPatterns": map[string]interface{}{
									"type": "array",
									"items": map[string]interface{}{
										"type": "string",
										"enum": []string{"sunny", "cloudy", "rainy", "stormy", "snowy", "foggy", "windy", "magical"},
									},
								},
							},
						},
						"terrain": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"primaryTerrain": map[string]interface{}{
									"type": "string",
									"enum": []string{"plains", "mountains", "forests", "deserts", "islands", "underground", "floating", "mixed"},
								},
								"landmarks": map[string]interface{}{
									"type": "array",
									"items": map[string]interface{}{
										"type": "object",
										"properties": map[string]interface{}{
											"name": map[string]interface{}{
												"type":      "string",
												"maxLength": 100,
											},
											"type": map[string]interface{}{
												"type": "string",
												"enum": []string{"city", "castle", "temple", "tower", "ruins", "natural", "magical", "other"},
											},
											"description": map[string]interface{}{
												"type":      "string",
												"maxLength": 300,
											},
										},
										"required": []string{"name", "type"},
									},
									"maxItems": 10,
								},
							},
						},
					},
				},
			},
			"required": []string{"worldDescription"},
		}

	default:
		// 通用结构
		return map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"content": map[string]interface{}{
					"type":        "string",
					"description": "生成的内容",
				},
				"description": map[string]interface{}{
					"type":        "string",
					"description": "内容描述",
				},
			},
			"required": []string{"content", "description"},
		}
	}
}

// extractContentFromResult 从结构化结果中提取内容文本
func (s *Service) extractContentFromResult(result map[string]interface{}) (string, bool) {
	// 尝试提取描述字段作为主要内容
	if description, ok := result["description"].(string); ok && description != "" {
		return description, true
	}

	// 尝试提取名称字段
	if name, ok := result["name"].(string); ok && name != "" {
		return name, true
	}

	// 尝试提取内容字段
	if content, ok := result["content"].(string); ok && content != "" {
		return content, true
	}

	return "", false
}

// estimateTokenUsage 估算token使用量
func (s *Service) estimateTokenUsage(prompt string, result map[string]interface{}) int {
	// 简单的token估算：大约每4个字符为1个token
	promptTokens := len(prompt) / 4

	// 估算结果的token数
	resultBytes, _ := json.Marshal(result)
	resultTokens := len(resultBytes) / 4

	// 返回总估算token数，加上一些系统开销
	return promptTokens + resultTokens + 50
}

// 注意：旧的buildWindmillRequest方法已被删除，现在使用新的Windmill结构化文本接口

// buildSystemPrompt 根据内容类型构建系统提示
func (s *Service) buildSystemPrompt(contentType string) string {
	basePrompt := "你是一个专业的AI文本游戏内容生成助手。请根据用户的要求生成高质量的游戏内容，并以JSON格式返回结果。"

	switch contentType {
	case "scene":
		return basePrompt + `

对于场景生成，请返回包含以下字段的JSON：
{
  "name": "场景名称",
  "description": "详细的场景描述，包含环境、氛围、关键特征等",
  "atmosphere": "场景氛围（如：神秘、温馨、紧张等）",
  "key_features": ["关键特征1", "关键特征2"],
  "possible_actions": ["可能的行动1", "可能的行动2"],
  "connections": [
    {
      "direction": "方向（如：北、南、东、西）",
      "description": "连接描述",
      "scene_name": "连接的场景名称"
    }
  ]
}`

	case "character":
		return basePrompt + `

对于角色生成，请返回包含以下字段的JSON：
{
  "name": "角色名称",
  "description": "角色的外观和基本描述",
  "personality": ["性格特征1", "性格特征2"],
  "background": "角色背景故事",
  "skills": ["技能1", "技能2"],
  "dialogue_style": "对话风格描述",
  "motivations": ["动机1", "动机2"]
}`

	case "event":
		return basePrompt + `

对于事件生成，请返回包含以下字段的JSON：
{
  "name": "事件名称",
  "description": "事件详细描述",
  "type": "事件类型（如：random、plot、character、environmental）",
  "priority": 事件优先级（1-10的数字）,
  "duration": 事件持续时间（分钟）,
  "effects": {
    "description": "事件效果描述",
    "consequences": ["后果1", "后果2"]
  }
}`

	case "world":
		return basePrompt + `

对于世界生成，请返回包含以下字段的JSON：
{
  "worldDescription": "对游戏世界的详细描述，包括整体氛围、主要特征和背景设定（50-2000字符）",
  "worldRules": [
    {
      "name": "规则名称",
      "description": "规则详细说明",
      "category": "规则类别（magic、combat、social、economic、environmental、character、other）",
      "severity": "违规严重性（low、medium、high、critical）",
      "enforcement": "执行方式（automatic、manual、community、none）"
    }
  ],
  "environment": {
    "climate": {
      "type": "气候类型（tropical、temperate、arctic、desert、mediterranean、continental、mixed）",
      "seasons": ["季节列表"],
      "weatherPatterns": ["天气模式列表"]
    },
    "terrain": {
      "primaryTerrain": "主要地形类型",
      "landmarks": [
        {
          "name": "地标名称",
          "type": "地标类型",
          "description": "地标描述"
        }
      ]
    }
  },
  "culture": {
    "societies": [
      {
        "name": "群体名称",
        "type": "群体类型",
        "description": "群体描述",
        "traits": ["群体特征列表"]
      }
    ],
    "languages": [
      {
        "name": "语言名称",
        "speakers": ["使用者列表"],
        "script": "文字系统"
      }
    ]
  },
  "history": {
    "eras": [
      {
        "name": "时代名称",
        "description": "时代描述",
        "duration": "持续时间",
        "keyEvents": ["关键事件列表"]
      }
    ],
    "legends": [
      {
        "title": "传说标题",
        "summary": "传说概要",
        "characters": ["传说人物列表"],
        "truthLevel": "真实程度"
      }
    ]
  },
  "geography": {
    "worldType": "世界类型",
    "size": "世界规模",
    "regions": [
      {
        "name": "区域名称",
        "type": "区域类型",
        "description": "区域描述",
        "climate": "区域气候",
        "population": "人口密度",
        "governance": "治理方式"
      }
    ]
  }
}

请确保生成的世界配置：
1. 具有内在一致性和逻辑性
2. 富有创意和想象力
3. 适合文本角色扮演游戏
4. 包含足够的细节以支持游戏玩法
5. 各个部分相互呼应，形成完整的世界观`

	default:
		return basePrompt + "\n\n请根据用户要求生成相应的游戏内容，并以结构化的JSON格式返回。"
	}
}

// formatContext 格式化上下文信息
func (s *Service) formatContext(context map[string]interface{}) string {
	if context == nil || len(context) == 0 {
		return ""
	}

	var parts []string
	for key, value := range context {
		if valueStr, ok := value.(string); ok && valueStr != "" {
			parts = append(parts, fmt.Sprintf("%s: %s", key, valueStr))
		}
	}

	return strings.Join(parts, "; ")
}

// buildSchemaPrompt 构建Schema提示
func (s *Service) buildSchemaPrompt(schema map[string]interface{}) string {
	if schema == nil || len(schema) == 0 {
		return ""
	}

	prompt := "请确保返回的JSON包含以下字段："
	for field, fieldType := range schema {
		prompt += fmt.Sprintf("\n- %s: %v", field, fieldType)
	}

	return prompt
}

// 注意：旧的parseWindmillResponse方法已被删除，现在直接使用Windmill结构化文本接口返回的结构化数据

// createFallbackStructuredData 创建备用的结构化数据
func (s *Service) createFallbackStructuredData(content, contentType string) map[string]interface{} {
	switch contentType {
	case "scene":
		return map[string]interface{}{
			"name":            "生成的场景",
			"description":     content,
			"atmosphere":      "未知",
			"key_features":    []string{},
			"possible_actions": []string{},
			"connections":     []interface{}{},
		}
	case "character":
		return map[string]interface{}{
			"name":           "生成的角色",
			"description":    content,
			"personality":    []string{},
			"background":     content,
			"skills":         []string{},
			"dialogue_style": "未知",
			"motivations":    []string{},
		}
	case "event":
		return map[string]interface{}{
			"name":        "生成的事件",
			"description": content,
			"type":        "random",
			"priority":    5,
			"duration":    30,
			"effects": map[string]interface{}{
				"description":   content,
				"consequences": []string{},
			},
		}
	default:
		return map[string]interface{}{
			"content":     content,
			"description": content,
		}
	}
}

// generateMockContent 生成Mock内容
func (s *Service) generateMockContent(req *GenerateRequest) (*GenerateResponse, error) {
	s.logger.Info("使用Mock模式生成AI内容", "type", req.Type)
	
	switch req.Type {
	case "scene":
		return s.generateMockScene(req)
	case "character":
		return s.generateMockCharacter(req)
	case "event":
		return s.generateMockEvent(req)
	case "dialogue":
		return s.generateMockDialogue(req)
	case "description":
		return s.generateMockDescription(req)
	default:
		return s.generateMockGeneral(req)
	}
}

// generateMockScene 生成Mock场景
func (s *Service) generateMockScene(req *GenerateRequest) (*GenerateResponse, error) {
	mockScenes := []map[string]interface{}{
		{
			"name":        "神秘森林",
			"description": "一片古老而神秘的森林，高大的树木遮天蔽日，林间弥漫着淡淡的雾气。偶尔能听到远处传来的鸟鸣声和树叶沙沙作响的声音。",
			"type":        "forest",
			"atmosphere":  "mysterious",
			"connections": map[string]string{
				"north": "山洞入口",
				"south": "村庄广场",
				"east":  "河边小径",
			},
			"entities": []string{"古老的橡树", "发光的蘑菇", "小溪"},
		},
		{
			"name":        "废弃城堡",
			"description": "一座年代久远的城堡，石墙斑驳，藤蔓缠绕。城堡内部阴暗潮湿，回音在空旷的大厅中回荡。",
			"type":        "castle",
			"atmosphere":  "eerie",
			"connections": map[string]string{
				"west":  "城堡花园",
				"north": "塔楼",
				"down":  "地下室",
			},
			"entities": []string{"破损的盔甲", "古老的画像", "蜘蛛网"},
		},
	}
	
	// 随机选择一个场景
	selectedScene := mockScenes[time.Now().Unix()%int64(len(mockScenes))]
	
	return &GenerateResponse{
		Content:        selectedScene["description"].(string),
		StructuredData: selectedScene,
		TokenUsage:     150,
	}, nil
}

// generateMockCharacter 生成Mock角色
func (s *Service) generateMockCharacter(req *GenerateRequest) (*GenerateResponse, error) {
	character := map[string]interface{}{
		"name":        "艾莉娅",
		"description": "一位年轻的精灵法师，有着银色的长发和翠绿的眼睛。",
		"type":        "npc",
		"personality": []string{"智慧", "善良", "好奇"},
		"skills":      []string{"魔法", "草药学"},
		"background":  "来自精灵王国的年轻法师。",
	}

	return &GenerateResponse{
		Content:        character["description"].(string),
		StructuredData: character,
		TokenUsage:     120,
	}, nil
}


// generateMockEvent 生成Mock事件
func (s *Service) generateMockEvent(req *GenerateRequest) (*GenerateResponse, error) {
	mockEvents := []map[string]interface{}{
		{
			"name":        "神秘商人的到来",
			"description": "一位穿着华丽长袍的神秘商人来到了村庄，他的马车上装满了奇异的物品和魔法道具。",
			"type":        "encounter",
			"priority":    5,
			"duration":    30,
			"effects": map[string]interface{}{
				"new_items":     []string{"魔法药水", "古老地图", "神秘护符"},
				"new_quests":    []string{"寻找失落的宝藏"},
				"reputation":    10,
			},
		},
		{
			"name":        "暴风雨来袭",
			"description": "天空突然乌云密布，雷声轰鸣，一场猛烈的暴风雨即将来临。所有户外活动都必须暂停。",
			"type":        "weather",
			"priority":    3,
			"duration":    60,
			"effects": map[string]interface{}{
				"weather_change": "storm",
				"visibility":     "low",
				"movement_speed": 0.5,
			},
		},
	}
	
	selectedEvent := mockEvents[time.Now().Unix()%int64(len(mockEvents))]
	
	return &GenerateResponse{
		Content:        selectedEvent["description"].(string),
		StructuredData: selectedEvent,
		TokenUsage:     100,
	}, nil
}

// generateMockDialogue 生成Mock对话
func (s *Service) generateMockDialogue(req *GenerateRequest) (*GenerateResponse, error) {
	mockDialogues := []string{
		"欢迎来到我们的村庄，陌生人。你看起来像是从很远的地方来的。",
		"这里最近发生了一些奇怪的事情，也许你能帮助我们解决这个问题。",
		"小心那片森林，据说里面住着危险的生物。",
		"你有什么需要的吗？我这里有各种各样的物品。",
		"传说中的宝藏就在那座古老的城堡里，但是没有人敢去寻找。",
	}
	
	selectedDialogue := mockDialogues[time.Now().Unix()%int64(len(mockDialogues))]
	
	return &GenerateResponse{
		Content: selectedDialogue,
		StructuredData: map[string]interface{}{
			"dialogue": selectedDialogue,
			"emotion":  "neutral",
			"intent":   "information",
		},
		TokenUsage: 50,
	}, nil
}

// generateMockDescription 生成Mock描述
func (s *Service) generateMockDescription(req *GenerateRequest) (*GenerateResponse, error) {
	descriptions := []string{
		"这是一个充满魔法和奇迹的世界，古老的传说在这里成为现实。",
		"微风轻抚过草地，带来了远方花朵的香气。",
		"夕阳西下，金色的光芒洒在大地上，一切都显得那么宁静美好。",
		"古老的石碑上刻着神秘的符文，似乎在诉说着久远的故事。",
		"篝火在夜晚中跳跃着，温暖的光芒驱散了黑暗和寒冷。",
	}
	
	selectedDescription := descriptions[time.Now().Unix()%int64(len(descriptions))]
	
	return &GenerateResponse{
		Content: selectedDescription,
		StructuredData: map[string]interface{}{
			"description": selectedDescription,
			"mood":        "peaceful",
			"style":       "descriptive",
		},
		TokenUsage: 80,
	}, nil
}

// generateMockGeneral 生成通用Mock内容
func (s *Service) generateMockGeneral(req *GenerateRequest) (*GenerateResponse, error) {
	return &GenerateResponse{
		Content: fmt.Sprintf("这是一个关于%s的Mock响应。在实际环境中，这里会调用真实的AI API来生成内容。", req.Type),
		StructuredData: map[string]interface{}{
			"type":    req.Type,
			"mock":    true,
			"prompt":  req.Prompt,
			"context": req.Context,
		},
		TokenUsage: 75,
	}, nil
}

// GetInteractionHistory 获取AI交互历史
func (s *Service) GetInteractionHistory(worldID *uuid.UUID, userID *uuid.UUID, limit int) ([]models.AIInteraction, error) {
	var interactions []models.AIInteraction
	query := s.db.Order("created_at DESC")
	
	if worldID != nil {
		query = query.Where("world_id = ?", *worldID)
	}
	
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	err := query.Find(&interactions).Error
	return interactions, err
}

// GetTokenUsageStats 获取token使用统计
func (s *Service) GetTokenUsageStats(worldID *uuid.UUID, userID *uuid.UUID, days int) (map[string]interface{}, error) {
	var stats struct {
		TotalInteractions int64 `json:"total_interactions"`
		TotalTokens       int   `json:"total_tokens"`
		AvgTokensPerReq   int   `json:"avg_tokens_per_request"`
	}

	query := s.db.Model(&models.AIInteraction{}).Where("status = ?", "completed")

	if worldID != nil {
		query = query.Where("world_id = ?", *worldID)
	}

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	if days > 0 {
		query = query.Where("created_at >= ?", time.Now().AddDate(0, 0, -days))
	}

	// 获取总交互数
	query.Count(&stats.TotalInteractions)
	
	// 获取总token数
	var totalTokens sql.NullInt64
	query.Select("SUM(token_usage)").Scan(&totalTokens)
	stats.TotalTokens = int(totalTokens.Int64)
	
	// 计算平均值
	if stats.TotalInteractions > 0 {
		stats.AvgTokensPerReq = stats.TotalTokens / int(stats.TotalInteractions)
	}
	
	return map[string]interface{}{
		"total_interactions":     stats.TotalInteractions,
		"total_tokens":          stats.TotalTokens,
		"avg_tokens_per_request": stats.AvgTokensPerReq,
	}, nil
}

// GetAvailableContentTypes 获取所有可用的内容类型
func (s *Service) GetAvailableContentTypes() []string {
	return s.schemaRegistry.ListAvailableTypes()
}

// GetSchemaForContentType 获取指定内容类型的Schema定义
func (s *Service) GetSchemaForContentType(contentType string) (map[string]interface{}, error) {
	return s.schemaRegistry.GetSchemaAsMap(contentType)
}

// ValidateContentType 验证内容类型是否支持
func (s *Service) ValidateContentType(contentType string) bool {
	_, err := s.schemaRegistry.GetSchema(contentType)
	return err == nil
}

// GenerateContentWithValidation 生成内容并进行Schema验证
func (s *Service) GenerateContentWithValidation(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	// 验证内容类型
	if !s.ValidateContentType(req.Type) {
		return nil, fmt.Errorf("不支持的内容类型: %s", req.Type)
	}

	// 调用原有的生成方法
	response, err := s.GenerateContent(ctx, req)
	if err != nil {
		return nil, err
	}

	// 可以在这里添加额外的验证逻辑
	s.logger.Info("内容生成完成并通过验证",
		"content_type", req.Type,
		"response_size", len(response.Content))

	return response, nil
}

// GenerateAndSaveScene 生成场景并保存到数据库
func (s *Service) GenerateAndSaveScene(ctx context.Context, req *GenerateRequest) (*models.Scene, error) {
	// 验证请求
	if req.WorldID == nil {
		return nil, fmt.Errorf("世界ID不能为空")
	}

	// 设置内容类型为场景
	req.Type = "scene"

	// 生成内容
	response, err := s.GenerateContentWithValidation(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("生成场景内容失败: %w", err)
	}

	// 验证生成的数据
	schema, err := s.schemaRegistry.GetSchema("scene")
	if err != nil {
		return nil, fmt.Errorf("获取场景Schema失败: %w", err)
	}

	validator := NewSchemaValidator(schema)
	validationResult := validator.Validate(response.StructuredData)
	if !validationResult.Valid {
		s.logger.Warn("生成的场景数据验证失败", "errors", validationResult.Errors)
		// 可以选择继续处理或返回错误
	}

	// 转换为数据模型
	scene, err := s.transformer.TransformToScene(response.StructuredData, *req.WorldID)
	if err != nil {
		return nil, fmt.Errorf("转换场景数据失败: %w", err)
	}

	// 保存到数据库
	if err := s.db.Create(scene).Error; err != nil {
		return nil, fmt.Errorf("保存场景到数据库失败: %w", err)
	}

	s.logger.Info("场景生成并保存成功",
		"scene_id", scene.ID,
		"scene_name", scene.Name,
		"world_id", *req.WorldID)

	return scene, nil
}

// GenerateAndSaveCharacter 生成角色并保存到数据库
func (s *Service) GenerateAndSaveCharacter(ctx context.Context, req *GenerateRequest) (*models.Character, error) {
	// 验证请求
	if req.WorldID == nil {
		return nil, fmt.Errorf("世界ID不能为空")
	}

	// 设置内容类型为角色
	req.Type = "character"

	// 生成内容
	response, err := s.GenerateContentWithValidation(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("生成角色内容失败: %w", err)
	}

	// 验证生成的数据
	schema, err := s.schemaRegistry.GetSchema("character")
	if err != nil {
		return nil, fmt.Errorf("获取角色Schema失败: %w", err)
	}

	validator := NewSchemaValidator(schema)
	validationResult := validator.Validate(response.StructuredData)
	if !validationResult.Valid {
		s.logger.Warn("生成的角色数据验证失败", "errors", validationResult.Errors)
	}

	// 转换为数据模型
	character, err := s.transformer.TransformToCharacter(response.StructuredData, *req.WorldID, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("转换角色数据失败: %w", err)
	}

	// 保存到数据库
	if err := s.db.Create(character).Error; err != nil {
		return nil, fmt.Errorf("保存角色到数据库失败: %w", err)
	}

	s.logger.Info("角色生成并保存成功",
		"character_id", character.ID,
		"character_name", character.Name,
		"world_id", *req.WorldID)

	return character, nil
}

// GenerateAndSaveEvent 生成事件并保存到数据库
func (s *Service) GenerateAndSaveEvent(ctx context.Context, req *GenerateRequest) (*models.Event, error) {
	// 验证请求
	if req.WorldID == nil {
		return nil, fmt.Errorf("世界ID不能为空")
	}

	// 设置内容类型为事件
	req.Type = "event"

	// 生成内容
	response, err := s.GenerateContentWithValidation(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("生成事件内容失败: %w", err)
	}

	// 验证生成的数据
	schema, err := s.schemaRegistry.GetSchema("event")
	if err != nil {
		return nil, fmt.Errorf("获取事件Schema失败: %w", err)
	}

	validator := NewSchemaValidator(schema)
	validationResult := validator.Validate(response.StructuredData)
	if !validationResult.Valid {
		s.logger.Warn("生成的事件数据验证失败", "errors", validationResult.Errors)
	}

	// 转换为数据模型
	event, err := s.transformer.TransformToEvent(response.StructuredData, *req.WorldID, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("转换事件数据失败: %w", err)
	}

	// 保存到数据库
	if err := s.db.Create(event).Error; err != nil {
		return nil, fmt.Errorf("保存事件到数据库失败: %w", err)
	}

	s.logger.Info("事件生成并保存成功",
		"event_id", event.ID,
		"event_name", *event.Name,
		"world_id", *req.WorldID)

	return event, nil
}

// GetAIConfiguration 获取AI配置信息
func (s *Service) GetAIConfiguration() map[string]interface{} {
	return s.configManager.GetConfigSummary()
}

// UpdateModelConfiguration 更新模型配置
func (s *Service) UpdateModelConfiguration(modelName string, config *ModelConfig) error {
	return s.configManager.UpdateModelConfig(modelName, config)
}

// UpdateContentTypeConfiguration 更新内容类型配置
func (s *Service) UpdateContentTypeConfiguration(contentType string, config *ContentTypeConfig) error {
	return s.configManager.UpdateContentTypeConfig(contentType, config)
}

// GetModelConfiguration 获取模型配置
func (s *Service) GetModelConfiguration(modelName string) (*ModelConfig, error) {
	return s.configManager.GetModelConfig(modelName)
}

// GetContentTypeConfiguration 获取内容类型配置
func (s *Service) GetContentTypeConfiguration(contentType string) (*ContentTypeConfig, error) {
	return s.configManager.GetContentTypeConfig(contentType)
}

// GetSupportedModels 获取支持的模型列表
func (s *Service) GetSupportedModels() []string {
	return s.configManager.GetAllModelNames()
}

// GetSupportedContentTypes 获取支持的内容类型列表
func (s *Service) GetSupportedContentTypes() []string {
	return s.configManager.GetAllContentTypes()
}

// SetGlobalAISetting 设置全局AI设置
func (s *Service) SetGlobalAISetting(key string, value interface{}) {
	s.configManager.SetGlobalSetting(key, value)
	s.logger.Info("全局AI设置已更新", "key", key, "value", value)
}

// GetGlobalAISetting 获取全局AI设置
func (s *Service) GetGlobalAISetting(key string) (interface{}, bool) {
	return s.configManager.GetGlobalSetting(key)
}

// 异步生成方法

// GenerateContentAsync 异步生成内容
func (s *Service) GenerateContentAsync(req *GenerateRequest) (*AsyncTask, error) {
	// 提交异步任务
	task, err := s.asyncManager.SubmitTask(req)
	if err != nil {
		return nil, fmt.Errorf("提交异步任务失败: %w", err)
	}

	// 添加任务完成回调
	s.asyncManager.AddTaskCallback(task.ID, func(completedTask *AsyncTask) {
		s.handleAsyncTaskCompletion(completedTask)
	})

	s.logger.Info("异步内容生成任务已提交", "task_id", task.ID, "type", req.Type)
	return task, nil
}

// handleAsyncTaskCompletion 处理异步任务完成
func (s *Service) handleAsyncTaskCompletion(task *AsyncTask) {
	if task.Status == TaskStatusCompleted && task.Response != nil {
		// 发布数据流事件
		event := &DataFlowEvent{
			ID:        uuid.New().String(),
			Type:      "ai_content_generated",
			Source:    "async_task_manager",
			Target:    "data_flow_processor",
			Timestamp: time.Now(),
			Data: map[string]interface{}{
				"task_id":         task.ID,
				"content_type":    task.Type,
				"structured_data": task.Response.StructuredData,
				"world_id":        task.Request.WorldID,
				"user_id":         task.Request.UserID,
			},
		}

		s.dataFlow.PublishEvent(event)

		// 通知前端
		if task.Request.UserID != nil {
			message := s.frontendSync.CreateTaskStatusMessage(
				task.ID,
				task.Status,
				task.Progress,
				map[string]interface{}{
					"content_type": task.Type,
					"response":     task.Response,
				},
			)
			if task.Request.UserID != nil {
				s.frontendSync.SendToUser(*task.Request.UserID, message)
			}
		}

		s.logger.Info("异步任务完成处理", "task_id", task.ID, "type", task.Type)
	} else if task.Status == TaskStatusFailed {
		// 处理任务失败
		if task.Request.UserID != nil {
			message := s.frontendSync.CreateErrorMessage(
				"task_failed",
				"AI内容生成失败",
				map[string]interface{}{
					"task_id": task.ID,
					"error":   task.Error,
				},
			)
			if task.Request.UserID != nil {
				s.frontendSync.SendToUser(*task.Request.UserID, message)
			}
		}

		s.logger.Error("异步任务失败", "task_id", task.ID, "error", task.Error)
	}
}

// GetAsyncTask 获取异步任务状态
func (s *Service) GetAsyncTask(taskID string) (*AsyncTask, error) {
	return s.asyncManager.GetTask(taskID)
}

// CancelAsyncTask 取消异步任务
func (s *Service) CancelAsyncTask(taskID string) error {
	return s.asyncManager.CancelTask(taskID)
}

// GetAsyncTaskList 获取异步任务列表
func (s *Service) GetAsyncTaskList(limit, offset int) ([]*AsyncTask, int, error) {
	return s.asyncManager.GetTaskList(limit, offset)
}

// GetAsyncTaskStatistics 获取异步任务统计
func (s *Service) GetAsyncTaskStatistics() map[string]interface{} {
	return s.asyncManager.GetStatistics()
}

// 前端同步方法

// GetFrontendSyncManager 获取前端同步管理器
func (s *Service) GetFrontendSyncManager() *FrontendSyncManager {
	return s.frontendSync
}

// SendToUser 发送消息给用户
func (s *Service) SendToUser(userID string, message *FrontendMessage) {
	s.frontendSync.SendToUser(userID, message)
}

// SendToWorld 发送消息给世界
func (s *Service) SendToWorld(worldID string, message *FrontendMessage) {
	s.frontendSync.SendToWorld(worldID, message)
}

// 对话生成方法

// GenerateChat 生成对话响应
func (s *Service) GenerateChat(ctx context.Context, req *ChatRequest) (*ChatResponse, error) {
	startTime := time.Now()
	s.logger.Info("开始生成对话响应",
		"user_id", req.UserID,
		"conversation_id", req.ConversationID,
		"model", req.Model,
		"message_count", len(req.Messages))

	// 构建对话提示词
	prompt, err := s.buildChatPrompt(req.Messages)
	if err != nil {
		return nil, fmt.Errorf("构建对话提示词失败: %w", err)
	}

	// 构建AI生成请求
	generateReq := &GenerateRequest{
		Type:        "dialogue",
		Prompt:      prompt,
		Context:     s.buildChatContext(req),
		MaxTokens:   s.getChatMaxTokens(req.MaxTokens),
		Temperature: s.getChatTemperature(req.Temperature),
		WorldID:     req.WorldID,
		UserID:      &req.UserID,
		Schema:      s.buildChatResponseSchema(),
	}

	// 调用AI生成服务
	var response *GenerateResponse
	if s.config.AI.MockEnabled {
		response, err = s.generateMockChat(generateReq)
	} else {
		response, err = s.generateRealContent(ctx, generateReq)
	}

	if err != nil {
		s.logger.Error("AI对话生成失败", "error", err)
		return nil, fmt.Errorf("AI对话生成失败: %w", err)
	}

	// 验证JSON结构
	validationResult := s.jsonValidator.ValidateChatResponse(response.StructuredData)
	if !validationResult.Valid {
		s.logger.Warn("AI响应JSON结构验证失败",
			"errors", validationResult.Errors,
			"score", validationResult.Score)

		// 尝试修复结构
		if len(validationResult.Missing) > 0 {
			schema := s.buildChatResponseSchema()
			response.StructuredData = s.jsonValidator.FixJSONStructure(response.StructuredData, schema)
			s.logger.Info("已尝试修复JSON结构")
		}
	} else {
		s.logger.Debug("AI响应JSON结构验证通过", "score", validationResult.Score)
	}

	// 构建对话响应
	chatResponse := &ChatResponse{
		Content:        response.Content,
		Model:          req.Model,
		TokenUsage:     response.TokenUsage,
		FinishReason:   "stop",
		StructuredData: response.StructuredData,
		ResponseTime:   int(time.Since(startTime).Milliseconds()),
	}

	s.logger.Info("对话生成成功",
		"token_usage", chatResponse.TokenUsage,
		"response_time", chatResponse.ResponseTime,
		"json_valid", validationResult.Valid,
		"json_score", validationResult.Score)

	return chatResponse, nil
}

// GenerateChatStream 生成流式对话响应
func (s *Service) GenerateChatStream(ctx context.Context, req *ChatRequest, writer StreamWriter) error {
	s.logger.Info("开始生成流式对话响应",
		"user_id", req.UserID,
		"conversation_id", req.ConversationID,
		"model", req.Model)

	// 构建对话提示词
	prompt, err := s.buildChatPrompt(req.Messages)
	if err != nil {
		return fmt.Errorf("构建对话提示词失败: %w", err)
	}

	// 如果启用Mock模式，使用模拟流式响应
	if s.config.AI.MockEnabled {
		return s.generateMockChatStream(ctx, req, prompt, writer)
	}

	// 调用真实的流式API（这里需要扩展Windmill客户端支持流式）
	return s.generateRealChatStream(ctx, req, prompt, writer)
}

// buildChatPrompt 构建对话提示词
func (s *Service) buildChatPrompt(messages []models.MessageRequest) (string, error) {
	if len(messages) == 0 {
		return "", fmt.Errorf("消息列表不能为空")
	}

	var promptBuilder strings.Builder
	promptBuilder.WriteString("你是一个智能AI助手，请根据以下对话历史生成合适的回复。\n\n")
	promptBuilder.WriteString("对话历史：\n")

	for i, msg := range messages {
		switch msg.Role {
		case "user":
			promptBuilder.WriteString(fmt.Sprintf("用户: %s\n", msg.Content))
		case "assistant":
			promptBuilder.WriteString(fmt.Sprintf("助手: %s\n", msg.Content))
		case "system":
			promptBuilder.WriteString(fmt.Sprintf("系统: %s\n", msg.Content))
		}

		// 限制历史消息数量，避免提示词过长
		if i >= 10 {
			break
		}
	}

	promptBuilder.WriteString("\n请生成一个合适的回复，要求：\n")
	promptBuilder.WriteString("1. 回复应该自然、有帮助且符合上下文\n")
	promptBuilder.WriteString("2. 保持友好和专业的语调\n")
	promptBuilder.WriteString("3. 如果涉及游戏内容，请保持角色扮演的一致性\n")
	promptBuilder.WriteString("4. 必须返回符合指定JSON结构的响应\n\n")

	return promptBuilder.String(), nil
}

// buildChatContext 构建对话上下文
func (s *Service) buildChatContext(req *ChatRequest) map[string]interface{} {
	context := make(map[string]interface{})

	context["model"] = req.Model
	context["message_count"] = len(req.Messages)
	context["stream"] = req.Stream

	if req.WorldID != nil {
		context["world_id"] = *req.WorldID
	}

	if req.ConversationID != nil {
		context["conversation_id"] = *req.ConversationID
	}

	return context
}

// buildChatResponseSchema 构建对话响应的JSON结构
func (s *Service) buildChatResponseSchema() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"content": map[string]interface{}{
				"type":        "string",
				"description": "AI生成的回复内容",
				"minLength":   1,
				"maxLength":   2000,
			},
			"emotion": map[string]interface{}{
				"type":        "string",
				"description": "回复的情感色彩",
				"enum":        []string{"neutral", "happy", "sad", "excited", "confused", "helpful", "friendly"},
			},
			"intent": map[string]interface{}{
				"type":        "string",
				"description": "回复的意图",
				"enum":        []string{"answer", "question", "suggestion", "clarification", "greeting", "farewell"},
			},
			"confidence": map[string]interface{}{
				"type":        "number",
				"description": "回复的置信度",
				"minimum":     0.0,
				"maximum":     1.0,
			},
		},
		"required": []string{"content", "emotion", "intent", "confidence"},
	}
}

// getChatMaxTokens 获取对话的最大token数
func (s *Service) getChatMaxTokens(maxTokens *int) int {
	if maxTokens != nil && *maxTokens > 0 {
		return *maxTokens
	}
	return 800 // 默认值
}

// getChatTemperature 获取对话的温度参数
func (s *Service) getChatTemperature(temperature *float64) float64 {
	if temperature != nil && *temperature >= 0.0 && *temperature <= 1.0 {
		return *temperature
	}
	return 0.7 // 默认值
}

// generateMockChat 生成Mock对话响应
func (s *Service) generateMockChat(req *GenerateRequest) (*GenerateResponse, error) {
	s.logger.Info("使用Mock模式生成对话响应")

	// 模拟的回复内容
	mockReplies := []string{
		"你好！我是AI助手，很高兴为您服务。有什么我可以帮助您的吗？",
		"这是一个很有趣的问题。让我来为您详细解答一下。",
		"根据您的描述，我建议您可以尝试以下几种方法。",
		"感谢您的提问！这确实是一个值得深入探讨的话题。",
		"我理解您的困惑，让我用更简单的方式来解释这个概念。",
		"这个问题很常见，许多用户都会遇到类似的情况。",
		"基于我的知识，我认为最佳的解决方案是...",
		"您提到的这个场景让我想到了一个有趣的故事。",
	}

	// 随机选择一个回复
	selectedReply := mockReplies[time.Now().Unix()%int64(len(mockReplies))]

	// 构建结构化数据
	structuredData := map[string]interface{}{
		"content":    selectedReply,
		"emotion":    "friendly",
		"intent":     "answer",
		"confidence": 0.85,
	}

	return &GenerateResponse{
		Content:        selectedReply,
		StructuredData: structuredData,
		TokenUsage:     len(selectedReply) / 4, // 粗略估算token数量
	}, nil
}

// generateMockChatStream 生成Mock流式对话响应
func (s *Service) generateMockChatStream(ctx context.Context, req *ChatRequest, prompt string, writer StreamWriter) error {
	s.logger.Info("使用Mock模式生成流式对话响应")

	// 模拟的回复内容
	fullResponse := "你好！我是AI助手，很高兴为您服务。我会逐步为您提供详细的回答，请耐心等待。这是一个模拟的流式响应，用于演示SSE功能的工作原理。"

	// 生成消息ID
	messageID := uuid.New().String()
	requestID := uuid.New().String()
	conversationID := ""
	if req.ConversationID != nil {
		conversationID = *req.ConversationID
	} else {
		conversationID = uuid.New().String()
	}

	// 分块发送响应
	words := strings.Fields(fullResponse)
	for _, word := range words {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 构建流式数据块
		chunk := &models.StreamChunk{
			ID:             messageID,
			Object:         "chat.completion.chunk",
			Created:        time.Now().Unix(),
			Model:          req.Model,
			ContentType:    "text",
			Role:           "assistant",
			Type:           "answer",
			ConversationID: conversationID,
			MessageID:      messageID,
			RequestID:      requestID,
			Choices: []models.StreamChoice{
				{
					Index: 0,
					Delta: models.StreamDelta{
						Content: word + " ",
					},
					FinishReason: nil,
				},
			},
		}

		// 发送数据块
		if err := writer.WriteChunk(chunk); err != nil {
			return fmt.Errorf("写入流式数据失败: %w", err)
		}

		// 模拟网络延迟
		time.Sleep(100 * time.Millisecond)
	}

	// 构建结构化数据
	structuredData := map[string]interface{}{
		"content":    fullResponse,
		"emotion":    "friendly",
		"intent":     "answer",
		"confidence": 0.85,
	}

	// 验证JSON结构
	validationResult := s.jsonValidator.ValidateChatResponse(structuredData)
	if !validationResult.Valid {
		s.logger.Warn("流式响应JSON结构验证失败",
			"errors", validationResult.Errors,
			"score", validationResult.Score)

		// 尝试修复结构
		schema := s.buildChatResponseSchema()
		structuredData = s.jsonValidator.FixJSONStructure(structuredData, schema)
		s.logger.Info("已修复流式响应JSON结构")
	} else {
		s.logger.Debug("流式响应JSON结构验证通过", "score", validationResult.Score)
	}

	// 发送结束标记
	finishChunk := &models.StreamChunk{
		ID:             messageID,
		Object:         "chat.completion.chunk",
		Created:        time.Now().Unix(),
		Model:          req.Model,
		ConversationID: conversationID,
		MessageID:      messageID,
		RequestID:      requestID,
		Choices: []models.StreamChoice{
			{
				Index:        0,
				Delta:        models.StreamDelta{},
				FinishReason: stringPtr("stop"),
			},
		},
		StructuredData: structuredData,
	}

	if err := writer.WriteChunk(finishChunk); err != nil {
		return fmt.Errorf("写入结束标记失败: %w", err)
	}

	return nil
}

// generateRealChatStream 生成真实的流式对话响应
func (s *Service) generateRealChatStream(ctx context.Context, req *ChatRequest, prompt string, writer StreamWriter) error {
	s.logger.Info("调用真实API生成流式对话响应")

	// TODO: 实现真实的Windmill流式API调用
	// 目前先使用Mock模式
	s.logger.Warn("真实流式API尚未实现，回退到Mock模式")
	return s.generateMockChatStream(ctx, req, prompt, writer)
}

// stringPtr 返回字符串指针
func stringPtr(s string) *string {
	return &s
}

// BroadcastMessage 广播消息
func (s *Service) BroadcastMessage(message *FrontendMessage) {
	s.frontendSync.BroadcastToAll(message)
}

// GetConnectionStatistics 获取连接统计
func (s *Service) GetConnectionStatistics() map[string]interface{} {
	return s.frontendSync.GetConnectionStats()
}

// 数据流方法

// GetDataFlowProcessor 获取数据流处理器
func (s *Service) GetDataFlowProcessor() *DataFlowProcessor {
	return s.dataFlow
}

// SubscribeToDataFlowEvents 订阅数据流事件
func (s *Service) SubscribeToDataFlowEvents(eventType string) <-chan *DataFlowEvent {
	return s.dataFlow.Subscribe(eventType)
}

// PublishDataFlowEvent 发布数据流事件
func (s *Service) PublishDataFlowEvent(event *DataFlowEvent) {
	s.dataFlow.PublishEvent(event)
}


