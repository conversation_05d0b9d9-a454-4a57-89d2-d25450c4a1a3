package ai

import (
	"fmt"
)

/**
 * 游戏场景专用JSON Schema定义
 * @description 为不同游戏场景定义具体的JSON Schema结构
 */

// GameSchemaRegistry 游戏Schema注册表
type GameSchemaRegistry struct {
	schemas map[string]*JSONSchema
}

// NewGameSchemaRegistry 创建游戏Schema注册表
func NewGameSchemaRegistry() *GameSchemaRegistry {
	registry := &GameSchemaRegistry{
		schemas: make(map[string]*JSONSchema),
	}
	
	// 注册所有游戏场景的Schema
	registry.registerAllSchemas()
	
	return registry
}

// GetSchema 获取指定类型的Schema
func (gsr *GameSchemaRegistry) GetSchema(contentType string) (*JSONSchema, error) {
	schema, exists := gsr.schemas[contentType]
	if !exists {
		return nil, fmt.Errorf("未找到内容类型 '%s' 的Schema定义", contentType)
	}
	return schema, nil
}

// GetSchemaAsMap 获取指定类型的Schema并转换为map格式
func (gsr *GameSchemaRegistry) GetSchemaAsMap(contentType string) (map[string]interface{}, error) {
	schema, err := gsr.GetSchema(contentType)
	if err != nil {
		return nil, err
	}
	return schema.ToMap(), nil
}

// ListAvailableTypes 列出所有可用的内容类型
func (gsr *GameSchemaRegistry) ListAvailableTypes() []string {
	types := make([]string, 0, len(gsr.schemas))
	for contentType := range gsr.schemas {
		types = append(types, contentType)
	}
	return types
}

// registerAllSchemas 注册所有游戏场景的Schema
func (gsr *GameSchemaRegistry) registerAllSchemas() {
	// 六个核心场景的Schema

	// 1. 世界创建场景 (World Creation)
	gsr.schemas["world_creation"] = gsr.createWorldCreationSchema()

	// 2. 世界进入场景 (World Entry)
	gsr.schemas["world_entry"] = gsr.createWorldEntrySchema()

	// 3. 世界探索场景 (World Exploration)
	gsr.schemas["world_exploration"] = gsr.createWorldExplorationSchema()

	// 4. 世界进程更新场景 (World Heartbeat)
	gsr.schemas["world_heartbeat"] = gsr.createWorldHeartbeatSchema()

	// 5. 书信对话场景 (Letter Communication)
	gsr.schemas["letter_communication"] = gsr.createLetterCommunicationSchema()

	// 6. 事件交互场景 (Event Interaction)
	gsr.schemas["event_interaction"] = gsr.createEventInteractionSchema()

	// 保留原有的通用Schema（向后兼容）
	gsr.schemas["scene"] = gsr.createSceneSchema()
	gsr.schemas["character"] = gsr.createCharacterSchema()
	gsr.schemas["event"] = gsr.createEventSchema()
	gsr.schemas["dialogue"] = gsr.createDialogueSchema()
	gsr.schemas["item"] = gsr.createItemSchema()
	gsr.schemas["world_description"] = gsr.createWorldDescriptionSchema()
	gsr.schemas["quest"] = gsr.createQuestSchema()
	gsr.schemas["environment_effect"] = gsr.createEnvironmentEffectSchema()
}

// ========== 六个核心场景的Schema定义 ==========

// createWorldCreationSchema 创建世界创建场景Schema
// 输入：世界名称（必需）、世界设定（可选）
// 输出：多个详细世界描述选项的JSON Schema
func (gsr *GameSchemaRegistry) createWorldCreationSchema() *JSONSchema {
	// 世界描述选项Schema
	worldOptionSchema := NewSchemaBuilder().Object().
		Description("世界描述选项").
		Property("world_name", NewSchemaBuilder().String().
			Description("世界名称，应该独特且富有想象力").
			MinLength(2).MaxLength(50).Build()).
		Property("environment_description", NewSchemaBuilder().String().
			Description("环境描述，包含地理、气候、自然环境等详细信息").
			MinLength(100).MaxLength(800).Build()).
		Property("cultural_background", NewSchemaBuilder().String().
			Description("文化背景，包含社会结构、习俗、信仰、艺术等文化要素").
			MinLength(100).MaxLength(600).Build()).
		Property("historical_background", NewSchemaBuilder().String().
			Description("历史背景，包含重要历史事件、传说、古代文明等").
			MinLength(100).MaxLength(600).Build()).
		Property("geographical_info", NewSchemaBuilder().Object().
			Description("地理位置信息").
			Property("major_regions", StringArraySchema("主要地理区域列表，如'北方山脉'、'中央平原'等")).
			Property("important_landmarks", StringArraySchema("重要地标列表，如主要城市、关键地点等")).
			Property("climate_zones", StringArraySchema("气候区域列表")).
			Property("natural_resources", StringArraySchema("自然资源列表")).
			Property("terrain_features", StringArraySchema("地形特征列表")).
			Required("major_regions", "important_landmarks", "climate_zones").
			Build()).
		Property("world_rules", NewSchemaBuilder().Object().
			Description("世界规则（可选）").
			Property("game_rules", StringArraySchema("游戏规则列表，定义世界运行的基本法则")).
			Property("restrictions", StringArraySchema("限制条件列表，定义世界中的约束和禁忌")).
			Property("magic_system", StringSchema("魔法体系描述（如果适用）")).
			Property("technology_level", EnumSchema("科技水平", "原始", "古代", "中世纪", "文艺复兴", "工业", "现代", "未来")).
			Property("social_structure", StringSchema("社会结构描述")).
			Property("economic_system", StringSchema("经济体系描述")).
			Build()).
		Property("theme", EnumSchema("世界主题",
			"奇幻", "科幻", "现代", "历史", "蒸汽朋克", "赛博朋克", "后末日", "超自然", "武侠", "仙侠", "现实主义")).
		Property("difficulty_level", EnumSchema("难度等级", "休闲", "普通", "困难", "专家", "大师")).
		Property("estimated_scale", EnumSchema("世界规模", "小型", "中型", "大型", "巨大")).
		Required("world_name", "environment_description", "cultural_background",
			"historical_background", "geographical_info", "theme", "difficulty_level", "estimated_scale").
		Build()

	return NewSchemaBuilder().Object().
		Description("世界创建场景输出结果").
		Property("world_options", ArraySchema("世界描述选项列表，提供3-5个不同的世界选项供玩家选择", worldOptionSchema)).
		Property("creation_timestamp", StringSchema("创建时间戳")).
		Property("creator_notes", StringSchema("创建者备注信息（可选）")).
		Required("world_options", "creation_timestamp").
		Build()
}

// createWorldEntrySchema 创建世界进入场景Schema
// 输入：世界名称、世界设定（可选）、世界描述、世界规则（可选）
// 输出：初始场景及其连接场景的JSON Schema
func (gsr *GameSchemaRegistry) createWorldEntrySchema() *JSONSchema {
	// 场景信息Schema
	sceneInfoSchema := NewSchemaBuilder().Object().
		Description("场景信息").
		Property("scene_id", StringSchema("场景唯一标识符")).
		Property("scene_name", NewSchemaBuilder().String().
			Description("场景名称").
			MinLength(2).MaxLength(100).Build()).
		Property("scene_description", NewSchemaBuilder().String().
			Description("场景详细描述，包含环境、氛围、关键特征等").
			MinLength(100).MaxLength(1000).Build()).
		Property("scene_type", EnumSchema("场景类型",
			"室内", "室外", "地下", "城镇", "荒野", "森林", "山脉", "海洋", "天空", "特殊")).
		Property("atmosphere", EnumSchema("场景氛围",
			"神秘", "温馨", "紧张", "恐怖", "宁静", "热闹", "庄严", "荒凉", "梦幻", "危险")).
		Property("connections", ArraySchema("连接场景列表", NewSchemaBuilder().Object().
			Description("连接场景信息").
			Property("direction", StringSchema("方向（如：北、南、东、西、上、下）")).
			Property("target_scene_id", StringSchema("目标场景ID")).
			Property("target_scene_name", StringSchema("目标场景名称")).
			Property("connection_description", StringSchema("连接描述")).
			Property("is_accessible", NewSchemaBuilder().Boolean().Description("是否可通行").Build()).
			Property("access_requirements", StringArraySchema("通行要求列表（可选）")).
			Required("direction", "target_scene_id", "target_scene_name", "connection_description", "is_accessible").
			Build())).
		Property("events", ArraySchema("事件列表", NewSchemaBuilder().Object().
			Description("场景事件").
			Property("event_id", StringSchema("事件唯一标识符")).
			Property("event_name", StringSchema("事件名称")).
			Property("event_description", StringSchema("事件描述")).
			Property("event_type", EnumSchema("事件类型", "随机遭遇", "剧情事件", "角色互动", "环境变化", "探索", "谜题", "交易")).
			Property("trigger_conditions", StringArraySchema("触发条件列表")).
			Property("is_active", NewSchemaBuilder().Boolean().Description("是否激活").Build()).
			Property("priority", IntegerSchema("优先级（1-10）", floatPtr(1), floatPtr(10))).
			Required("event_id", "event_name", "event_description", "event_type", "is_active", "priority").
			Build())).
		Property("items", ArraySchema("物品列表", NewSchemaBuilder().Object().
			Description("场景物品").
			Property("item_id", StringSchema("物品唯一标识符")).
			Property("item_name", StringSchema("物品名称")).
			Property("item_description", StringSchema("物品描述")).
			Property("item_type", EnumSchema("物品类型", "武器", "防具", "工具", "消耗品", "材料", "宝石", "书籍", "钥匙", "装饰品", "特殊物品")).
			Property("is_visible", NewSchemaBuilder().Boolean().Description("是否可见").Build()).
			Property("is_obtainable", NewSchemaBuilder().Boolean().Description("是否可获取").Build()).
			Property("obtain_requirements", StringArraySchema("获取要求列表（可选）")).
			Required("item_id", "item_name", "item_description", "item_type", "is_visible", "is_obtainable").
			Build())).
		Property("characters", ArraySchema("角色列表", NewSchemaBuilder().Object().
			Description("场景角色").
			Property("character_id", StringSchema("角色唯一标识符")).
			Property("character_name", StringSchema("角色名称")).
			Property("character_description", StringSchema("角色描述")).
			Property("character_type", EnumSchema("角色类型", "玩家", "NPC", "敌人", "商人", "导师", "守卫", "平民")).
			Property("is_interactive", NewSchemaBuilder().Boolean().Description("是否可交互").Build()).
			Property("dialogue_available", NewSchemaBuilder().Boolean().Description("是否可对话").Build()).
			Property("relationship_status", EnumSchema("关系状态", "友好", "中立", "敌对", "未知")).
			Required("character_id", "character_name", "character_description", "character_type", "is_interactive").
			Build())).
		Required("scene_id", "scene_name", "scene_description", "scene_type", "atmosphere", "connections", "events", "items", "characters").
		Build()

	return NewSchemaBuilder().Object().
		Description("世界进入场景输出结果").
		Property("initial_scene", sceneInfoSchema).
		Property("connected_scenes", ArraySchema("连接场景列表，包含初始场景周围的3-5个场景", sceneInfoSchema)).
		Property("game_time", IntegerSchema("当前游戏时间（分钟）", floatPtr(0), nil)).
		Property("world_status", NewSchemaBuilder().Object().
			Description("世界状态信息").
			Property("current_season", EnumSchema("当前季节", "春", "夏", "秋", "冬")).
			Property("weather", StringSchema("当前天气描述")).
			Property("time_of_day", EnumSchema("时间段", "黎明", "上午", "中午", "下午", "黄昏", "夜晚", "深夜")).
			Required("current_season", "weather", "time_of_day").
			Build()).
		Property("player_spawn_info", NewSchemaBuilder().Object().
			Description("玩家出生信息").
			Property("spawn_scene_id", StringSchema("出生场景ID")).
			Property("spawn_description", StringSchema("出生情况描述")).
			Property("initial_equipment", StringArraySchema("初始装备列表")).
			Property("initial_traits", StringArraySchema("初始特质列表")).
			Required("spawn_scene_id", "spawn_description").
			Build()).
		Required("initial_scene", "connected_scenes", "game_time", "world_status", "player_spawn_info").
		Build()
}

// createWorldExplorationSchema 创建世界探索场景Schema
// 输入：世界基础信息（名称、设定、描述、规则）+ 当前场景周围信息
// 输出：可连接场景列表的JSON Schema
func (gsr *GameSchemaRegistry) createWorldExplorationSchema() *JSONSchema {
	// 可连接场景Schema（复用场景信息结构，但针对探索优化）
	explorableSceneSchema := NewSchemaBuilder().Object().
		Description("可探索场景信息").
		Property("scene_id", StringSchema("场景唯一标识符")).
		Property("scene_name", NewSchemaBuilder().String().
			Description("场景名称").
			MinLength(2).MaxLength(100).Build()).
		Property("scene_description", NewSchemaBuilder().String().
			Description("场景详细描述，基于当前游戏时间动态生成").
			MinLength(100).MaxLength(1000).Build()).
		Property("scene_type", EnumSchema("场景类型",
			"室内", "室外", "地下", "城镇", "荒野", "森林", "山脉", "海洋", "天空", "特殊")).
		Property("atmosphere", EnumSchema("场景氛围",
			"神秘", "温馨", "紧张", "恐怖", "宁静", "热闹", "庄严", "荒凉", "梦幻", "危险")).
		Property("accessibility", NewSchemaBuilder().Object().
			Description("可达性信息").
			Property("is_accessible", NewSchemaBuilder().Boolean().Description("当前是否可进入").Build()).
			Property("access_requirements", StringArraySchema("进入要求列表")).
			Property("travel_time", IntegerSchema("到达所需时间（分钟）", floatPtr(1), floatPtr(1440))).
			Property("travel_difficulty", EnumSchema("旅行难度", "简单", "普通", "困难", "危险", "极危险")).
			Property("travel_description", StringSchema("旅行过程描述")).
			Required("is_accessible", "travel_time", "travel_difficulty", "travel_description").
			Build()).
		Property("dynamic_events", ArraySchema("基于游戏时间的动态事件列表", NewSchemaBuilder().Object().
			Description("动态事件").
			Property("event_id", StringSchema("事件唯一标识符")).
			Property("event_name", StringSchema("事件名称")).
			Property("event_description", StringSchema("事件描述")).
			Property("event_type", EnumSchema("事件类型", "随机遭遇", "剧情事件", "角色互动", "环境变化", "探索", "谜题", "交易", "时间敏感事件")).
			Property("time_sensitivity", NewSchemaBuilder().Object().
				Description("时间敏感性").
				Property("is_time_sensitive", NewSchemaBuilder().Boolean().Description("是否时间敏感").Build()).
				Property("available_until", IntegerSchema("可用截止时间（游戏时间分钟）", floatPtr(0), nil)).
				Property("optimal_time_range", NewSchemaBuilder().Object().
					Description("最佳时间范围").
					Property("start_time", IntegerSchema("开始时间（游戏时间分钟）", floatPtr(0), nil)).
					Property("end_time", IntegerSchema("结束时间（游戏时间分钟）", floatPtr(0), nil)).
					Build()).
				Required("is_time_sensitive").
				Build()).
			Property("trigger_conditions", StringArraySchema("触发条件列表")).
			Property("priority", IntegerSchema("优先级（1-10）", floatPtr(1), floatPtr(10))).
			Required("event_id", "event_name", "event_description", "event_type", "time_sensitivity", "priority").
			Build())).
		Property("available_items", ArraySchema("可获得物品列表", NewSchemaBuilder().Object().
			Description("可获得物品").
			Property("item_id", StringSchema("物品唯一标识符")).
			Property("item_name", StringSchema("物品名称")).
			Property("item_description", StringSchema("物品描述")).
			Property("item_type", EnumSchema("物品类型", "武器", "防具", "工具", "消耗品", "材料", "宝石", "书籍", "钥匙", "装饰品", "特殊物品")).
			Property("availability", NewSchemaBuilder().Object().
				Description("可用性信息").
				Property("is_currently_available", NewSchemaBuilder().Boolean().Description("当前是否可获得").Build()).
				Property("spawn_conditions", StringArraySchema("出现条件列表")).
				Property("time_dependent", NewSchemaBuilder().Boolean().Description("是否依赖时间").Build()).
				Required("is_currently_available", "time_dependent").
				Build()).
			Required("item_id", "item_name", "item_description", "item_type", "availability").
			Build())).
		Property("present_characters", ArraySchema("当前角色列表", NewSchemaBuilder().Object().
			Description("场景中的角色").
			Property("character_id", StringSchema("角色唯一标识符")).
			Property("character_name", StringSchema("角色名称")).
			Property("character_description", StringSchema("角色描述")).
			Property("character_type", EnumSchema("角色类型", "玩家", "NPC", "敌人", "商人", "导师", "守卫", "平民")).
			Property("current_activity", StringSchema("当前活动描述")).
			Property("interaction_availability", NewSchemaBuilder().Object().
				Description("交互可用性").
				Property("is_interactive", NewSchemaBuilder().Boolean().Description("是否可交互").Build()).
				Property("interaction_types", StringArraySchema("可用交互类型列表")).
				Property("time_restrictions", StringArraySchema("时间限制列表")).
				Required("is_interactive").
				Build()).
			Required("character_id", "character_name", "character_description", "character_type", "current_activity", "interaction_availability").
			Build())).
		Property("exploration_rewards", NewSchemaBuilder().Object().
			Description("探索奖励").
			Property("discovery_bonus", StringArraySchema("发现奖励列表")).
			Property("hidden_secrets", StringArraySchema("隐藏秘密列表")).
			Property("lore_information", StringArraySchema("背景知识列表")).
			Build()).
		Required("scene_id", "scene_name", "scene_description", "scene_type", "atmosphere",
			"accessibility", "dynamic_events", "available_items", "present_characters").
		Build()

	return NewSchemaBuilder().Object().
		Description("世界探索场景输出结果").
		Property("explorable_scenes", ArraySchema("可探索场景列表，基于当前位置和游戏时间生成", explorableSceneSchema)).
		Property("current_game_time", IntegerSchema("当前游戏时间（分钟）", floatPtr(0), nil)).
		Property("time_based_recommendations", ArraySchema("基于时间的推荐行动列表", NewSchemaBuilder().Object().
			Description("时间推荐").
			Property("action_type", EnumSchema("行动类型", "探索", "等待", "快速移动", "休息", "调查")).
			Property("recommended_target", StringSchema("推荐目标")).
			Property("reason", StringSchema("推荐理由")).
			Property("time_window", NewSchemaBuilder().Object().
				Description("时间窗口").
				Property("optimal_start", IntegerSchema("最佳开始时间", floatPtr(0), nil)).
				Property("optimal_end", IntegerSchema("最佳结束时间", floatPtr(0), nil)).
				Build()).
			Required("action_type", "recommended_target", "reason").
			Build())).
		Property("world_state_summary", NewSchemaBuilder().Object().
			Description("世界状态摘要").
			Property("active_global_events", StringArraySchema("全局活跃事件列表")).
			Property("weather_forecast", StringArraySchema("天气预报列表")).
			Property("seasonal_effects", StringArraySchema("季节影响列表")).
			Build()).
		Required("explorable_scenes", "current_game_time", "time_based_recommendations", "world_state_summary").
		Build()
}

// createWorldHeartbeatSchema 创建世界进程更新场景Schema
// 输入：世界基础信息 + 当前游戏时间 + 现有场景状态（事件、物品、角色）
// 输出：场景变化列表的JSON Schema（数组格式）
func (gsr *GameSchemaRegistry) createWorldHeartbeatSchema() *JSONSchema {
	// 场景变化项Schema
	sceneChangeSchema := NewSchemaBuilder().Object().
		Description("场景变化项").
		Property("scene_id", StringSchema("场景唯一标识符")).
		Property("scene_name", StringSchema("场景名称")).
		Property("change_type", EnumSchema("变化类型", "事件更新", "物品变化", "角色变化", "环境变化", "全面更新")).
		Property("change_description", StringSchema("变化描述，说明发生了什么变化")).
		Property("updated_events", ArraySchema("更新后的事件列表", NewSchemaBuilder().Object().
			Description("事件信息").
			Property("event_id", StringSchema("事件唯一标识符")).
			Property("event_name", StringSchema("事件名称")).
			Property("event_description", StringSchema("事件描述")).
			Property("event_type", EnumSchema("事件类型", "随机遭遇", "剧情事件", "角色互动", "环境变化", "探索", "谜题", "交易", "时间敏感事件", "NPC主动事件")).
			Property("event_status", EnumSchema("事件状态", "新增", "激活", "进行中", "暂停", "完成", "失效", "移除")).
			Property("trigger_conditions", StringArraySchema("触发条件列表")).
			Property("duration_remaining", IntegerSchema("剩余持续时间（分钟，-1表示无限制）", floatPtr(-1), nil)).
			Property("participants", StringArraySchema("参与者ID列表")).
			Property("priority", IntegerSchema("优先级（1-10）", floatPtr(1), floatPtr(10))).
			Property("auto_resolve_time", IntegerSchema("自动解决时间（游戏时间分钟，0表示不自动解决）", floatPtr(0), nil)).
			Required("event_id", "event_name", "event_description", "event_type", "event_status", "priority").
			Build())).
		Property("updated_items", ArraySchema("更新后的物品列表", NewSchemaBuilder().Object().
			Description("物品信息").
			Property("item_id", StringSchema("物品唯一标识符")).
			Property("item_name", StringSchema("物品名称")).
			Property("item_description", StringSchema("物品描述")).
			Property("item_type", EnumSchema("物品类型", "武器", "防具", "工具", "消耗品", "材料", "宝石", "书籍", "钥匙", "装饰品", "特殊物品")).
			Property("item_status", EnumSchema("物品状态", "新增", "存在", "移动", "消耗", "损坏", "修复", "升级", "移除")).
			Property("location_info", NewSchemaBuilder().Object().
				Description("位置信息").
				Property("location_type", EnumSchema("位置类型", "场景", "角色", "容器", "隐藏")).
				Property("location_id", StringSchema("位置ID")).
				Property("is_visible", NewSchemaBuilder().Boolean().Description("是否可见").Build()).
				Property("access_requirements", StringArraySchema("获取要求列表")).
				Required("location_type", "is_visible").
				Build()).
			Property("condition_changes", StringArraySchema("状态变化列表")).
			Required("item_id", "item_name", "item_description", "item_type", "item_status", "location_info").
			Build())).
		Property("updated_characters", ArraySchema("更新后的角色列表", NewSchemaBuilder().Object().
			Description("角色信息").
			Property("character_id", StringSchema("角色唯一标识符")).
			Property("character_name", StringSchema("角色名称")).
			Property("character_description", StringSchema("角色描述")).
			Property("character_type", EnumSchema("角色类型", "玩家", "NPC", "敌人", "商人", "导师", "守卫", "平民")).
			Property("character_status", EnumSchema("角色状态", "新增", "存在", "移动", "活动变化", "状态变化", "离开", "移除")).
			Property("current_activity", StringSchema("当前活动描述")).
			Property("location_info", NewSchemaBuilder().Object().
				Description("位置信息").
				Property("current_scene_id", StringSchema("当前场景ID")).
				Property("movement_plan", StringArraySchema("移动计划列表")).
				Property("arrival_time", IntegerSchema("预计到达时间（游戏时间分钟）", floatPtr(0), nil)).
				Build()).
			Property("mood_changes", StringArraySchema("情绪变化列表")).
			Property("relationship_changes", ArraySchema("关系变化列表", NewSchemaBuilder().Object().
				Description("关系变化").
				Property("target_character_id", StringSchema("目标角色ID")).
				Property("relationship_type", EnumSchema("关系类型", "友好", "中立", "敌对", "恋爱", "仇恨", "尊敬", "恐惧")).
				Property("change_reason", StringSchema("变化原因")).
				Required("target_character_id", "relationship_type", "change_reason").
				Build())).
			Property("new_goals", StringArraySchema("新目标列表")).
			Property("completed_goals", StringArraySchema("完成目标列表")).
			Required("character_id", "character_name", "character_description", "character_type", "character_status", "current_activity").
			Build())).
		Property("environmental_changes", NewSchemaBuilder().Object().
			Description("环境变化").
			Property("weather_changes", StringArraySchema("天气变化列表")).
			Property("lighting_changes", StringArraySchema("光照变化列表")).
			Property("atmospheric_changes", StringArraySchema("氛围变化列表")).
			Property("structural_changes", StringArraySchema("结构变化列表")).
			Property("seasonal_effects", StringArraySchema("季节影响列表")).
			Build()).
		Property("narrative_summary", StringSchema("变化的叙事总结，描述这些变化如何影响场景")).
		Property("change_timestamp", IntegerSchema("变化发生的游戏时间", floatPtr(0), nil)).
		Property("change_priority", IntegerSchema("变化优先级（1-10）", floatPtr(1), floatPtr(10))).
		Property("affects_connected_scenes", NewSchemaBuilder().Boolean().Description("是否影响连接的场景").Build()).
		Property("connected_scene_effects", StringArraySchema("对连接场景的影响列表")).
		Required("scene_id", "scene_name", "change_type", "change_description", "updated_events",
			"updated_items", "updated_characters", "narrative_summary", "change_timestamp", "change_priority").
		Build()

	return NewSchemaBuilder().Object().
		Description("世界进程更新场景输出结果").
		Property("scene_changes", ArraySchema("场景变化列表，包含所有发生变化的场景", sceneChangeSchema)).
		Property("global_changes", NewSchemaBuilder().Object().
			Description("全局变化").
			Property("time_progression", IntegerSchema("时间推进（分钟）", floatPtr(0), nil)).
			Property("world_events", StringArraySchema("世界级事件列表")).
			Property("seasonal_transitions", StringArraySchema("季节转换列表")).
			Property("global_effects", StringArraySchema("全局影响列表")).
			Build()).
		Property("update_summary", StringSchema("更新总结，概述本次心跳更新的主要变化")).
		Property("next_heartbeat_time", IntegerSchema("下次心跳时间（游戏时间分钟）", floatPtr(0), nil)).
		Property("affected_players", StringArraySchema("受影响的玩家ID列表")).
		Property("notification_messages", ArraySchema("通知消息列表", NewSchemaBuilder().Object().
			Description("通知消息").
			Property("target_player_id", StringSchema("目标玩家ID")).
			Property("message_type", EnumSchema("消息类型", "场景变化", "事件提醒", "角色动态", "环境变化", "系统通知")).
			Property("message_content", StringSchema("消息内容")).
			Property("priority", EnumSchema("优先级", "低", "普通", "高", "紧急")).
			Required("target_player_id", "message_type", "message_content", "priority").
			Build())).
		Required("scene_changes", "global_changes", "update_summary", "next_heartbeat_time", "affected_players").
		Build()
}

// createLetterCommunicationSchema 创建书信对话场景Schema
// 输入：角色特性、相关记忆、书信历史
// 输出：对话回复列表的JSON Schema（数组格式）
func (gsr *GameSchemaRegistry) createLetterCommunicationSchema() *JSONSchema {
	// 对话回复项Schema
	letterReplySchema := NewSchemaBuilder().Object().
		Description("书信回复项").
		Property("reply_id", StringSchema("回复唯一标识符")).
		Property("letter_content", NewSchemaBuilder().String().
			Description("回信内容，应该符合角色性格和当前情境").
			MinLength(50).MaxLength(2000).Build()).
		Property("sender_character", NewSchemaBuilder().Object().
			Description("发信角色信息").
			Property("character_id", StringSchema("角色唯一标识符")).
			Property("character_name", StringSchema("角色名称")).
			Property("character_type", EnumSchema("角色类型", "玩家", "NPC", "敌人", "商人", "导师", "守卫", "平民")).
			Property("current_mood", EnumSchema("当前情绪",
				"高兴", "愤怒", "悲伤", "恐惧", "惊讶", "厌恶", "中性", "兴奋", "困惑", "失望", "焦虑", "满足")).
			Property("relationship_context", NewSchemaBuilder().Object().
				Description("关系背景").
				Property("relationship_type", EnumSchema("关系类型", "朋友", "敌人", "恋人", "家人", "师生", "主仆", "同事", "陌生人")).
				Property("relationship_history", StringArraySchema("关系历史要点列表")).
				Property("current_relationship_status", EnumSchema("当前关系状态", "友好", "紧张", "冷淡", "热情", "复杂", "未知")).
				Required("relationship_type", "current_relationship_status").
				Build()).
			Property("personality_traits", StringArraySchema("性格特征列表，影响书信风格")).
			Property("communication_style", NewSchemaBuilder().Object().
				Description("沟通风格").
				Property("formality_level", EnumSchema("正式程度", "非常正式", "正式", "半正式", "随意", "非常随意")).
				Property("emotional_expression", EnumSchema("情感表达", "内敛", "适中", "丰富", "夸张")).
				Property("writing_style", EnumSchema("写作风格", "简洁", "详细", "诗意", "直白", "幽默", "严肃")).
				Property("language_complexity", EnumSchema("语言复杂度", "简单", "普通", "复杂", "文雅")).
				Required("formality_level", "emotional_expression", "writing_style", "language_complexity").
				Build()).
			Required("character_id", "character_name", "character_type", "current_mood", "relationship_context", "personality_traits", "communication_style").
			Build()).
		Property("letter_metadata", NewSchemaBuilder().Object().
			Description("书信元数据").
			Property("letter_type", EnumSchema("书信类型", "私人信件", "正式信函", "商业信件", "情书", "威胁信", "邀请函", "报告", "日记", "便条")).
			Property("urgency_level", EnumSchema("紧急程度", "不急", "普通", "重要", "紧急", "极紧急")).
			Property("confidentiality", EnumSchema("保密级别", "公开", "私人", "机密", "绝密")).
			Property("expected_response_time", IntegerSchema("期望回复时间（小时）", floatPtr(1), floatPtr(168))).
			Property("delivery_method", EnumSchema("送达方式", "信使", "飞鸽", "魔法传送", "亲自送达", "公告板", "其他")).
			Required("letter_type", "urgency_level", "confidentiality", "expected_response_time", "delivery_method").
			Build()).
		Property("content_analysis", NewSchemaBuilder().Object().
			Description("内容分析").
			Property("main_topics", StringArraySchema("主要话题列表")).
			Property("emotional_tone", EnumSchema("情感基调", "友善", "敌意", "中性", "热情", "冷淡", "悲伤", "愤怒", "喜悦", "担忧", "期待")).
			Property("intent_categories", StringArraySchema("意图分类列表，如'请求帮助'、'分享信息'、'表达情感'等")).
			Property("requires_action", NewSchemaBuilder().Boolean().Description("是否需要收信人采取行动").Build()).
			Property("action_requests", StringArraySchema("具体行动请求列表")).
			Property("information_shared", StringArraySchema("分享的信息列表")).
			Property("questions_asked", StringArraySchema("提出的问题列表")).
			Required("main_topics", "emotional_tone", "intent_categories", "requires_action").
			Build()).
		Property("memory_references", ArraySchema("相关记忆引用列表", NewSchemaBuilder().Object().
			Description("记忆引用").
			Property("memory_id", StringSchema("记忆唯一标识符")).
			Property("memory_type", EnumSchema("记忆类型", "事件记忆", "人物记忆", "地点记忆", "物品记忆", "知识记忆")).
			Property("memory_content", StringSchema("记忆内容摘要")).
			Property("relevance_score", NewSchemaBuilder().Number().
				Description("相关性评分（0-1）").Minimum(0).Maximum(1).Build()).
			Property("emotional_impact", NewSchemaBuilder().Number().
				Description("情感影响（-1到1）").Minimum(-1).Maximum(1).Build()).
			Required("memory_id", "memory_type", "memory_content", "relevance_score").
			Build())).
		Property("response_options", ArraySchema("回复选项列表", NewSchemaBuilder().Object().
			Description("回复选项").
			Property("option_id", StringSchema("选项唯一标识符")).
			Property("option_type", EnumSchema("选项类型", "直接回复", "延迟回复", "拒绝回复", "转发他人", "面谈请求")).
			Property("option_description", StringSchema("选项描述")).
			Property("estimated_consequences", StringArraySchema("预期后果列表")).
			Property("relationship_impact", EnumSchema("关系影响", "正面", "负面", "中性", "复杂")).
			Required("option_id", "option_type", "option_description", "relationship_impact").
			Build())).
		Property("contextual_factors", NewSchemaBuilder().Object().
			Description("上下文因素").
			Property("current_world_events", StringArraySchema("当前世界事件列表")).
			Property("character_current_situation", StringSchema("角色当前处境描述")).
			Property("time_constraints", StringArraySchema("时间限制列表")).
			Property("external_pressures", StringArraySchema("外部压力列表")).
			Property("available_resources", StringArraySchema("可用资源列表")).
			Build()).
		Property("delivery_info", NewSchemaBuilder().Object().
			Description("送达信息").
			Property("estimated_delivery_time", IntegerSchema("预计送达时间（小时）", floatPtr(1), floatPtr(72))).
			Property("delivery_risks", StringArraySchema("送达风险列表")).
			Property("delivery_cost", StringSchema("送达成本描述")).
			Property("alternative_delivery_methods", StringArraySchema("备选送达方式列表")).
			Build()).
		Required("reply_id", "letter_content", "sender_character", "letter_metadata", "content_analysis", "memory_references", "response_options").
		Build()

	return NewSchemaBuilder().Object().
		Description("书信对话场景输出结果").
		Property("letter_replies", ArraySchema("书信回复列表，包含所有相关角色的回信", letterReplySchema)).
		Property("communication_summary", NewSchemaBuilder().Object().
			Description("沟通总结").
			Property("total_participants", IntegerSchema("参与者总数", floatPtr(1), floatPtr(20))).
			Property("communication_network", StringArraySchema("沟通网络描述列表")).
			Property("key_information_flow", StringArraySchema("关键信息流动列表")).
			Property("relationship_changes", StringArraySchema("关系变化列表")).
			Build()).
		Property("follow_up_actions", ArraySchema("后续行动列表", NewSchemaBuilder().Object().
			Description("后续行动").
			Property("action_type", EnumSchema("行动类型", "发送新信件", "安排会面", "调查信息", "准备物品", "通知他人", "等待回复")).
			Property("action_description", StringSchema("行动描述")).
			Property("responsible_character_id", StringSchema("负责角色ID")).
			Property("estimated_completion_time", IntegerSchema("预计完成时间（小时）", floatPtr(1), floatPtr(168))).
			Property("priority", EnumSchema("优先级", "低", "普通", "高", "紧急")).
			Required("action_type", "action_description", "responsible_character_id", "priority").
			Build())).
		Property("world_impact", NewSchemaBuilder().Object().
			Description("世界影响").
			Property("information_spread", StringArraySchema("信息传播影响列表")).
			Property("political_implications", StringArraySchema("政治影响列表")).
			Property("economic_effects", StringArraySchema("经济影响列表")).
			Property("social_consequences", StringArraySchema("社会后果列表")).
			Build()).
		Required("letter_replies", "communication_summary", "follow_up_actions").
		Build()
}

// createEventInteractionSchema 创建事件交互场景Schema
// 输入：事件描述、物品描述、角色特性、角色阅历、角色行动
// 输出：交互结果列表的JSON Schema（数组格式）
func (gsr *GameSchemaRegistry) createEventInteractionSchema() *JSONSchema {
	// 交互结果项Schema
	interactionResultSchema := NewSchemaBuilder().Object().
		Description("事件交互结果项").
		Property("result_id", StringSchema("结果唯一标识符")).
		Property("event_result", NewSchemaBuilder().Object().
			Description("事件结果").
			Property("outcome_type", EnumSchema("结果类型", "成功", "部分成功", "失败", "意外结果", "延迟结果", "连锁反应")).
			Property("outcome_description", NewSchemaBuilder().String().
				Description("结果详细描述，包含发生的具体情况").
				MinLength(100).MaxLength(1500).Build()).
			Property("success_degree", NewSchemaBuilder().Number().
				Description("成功程度（0-1，0为完全失败，1为完全成功）").
				Minimum(0).Maximum(1).Build()).
			Property("unexpected_elements", StringArraySchema("意外元素列表，描述超出预期的情况")).
			Property("narrative_impact", EnumSchema("叙事影响", "微小", "轻微", "中等", "重大", "关键", "史诗级")).
			Property("resolution_status", EnumSchema("解决状态", "完全解决", "部分解决", "未解决", "产生新问题", "需要后续行动")).
			Required("outcome_type", "outcome_description", "success_degree", "narrative_impact", "resolution_status").
			Build()).
		Property("event_id", StringSchema("事件唯一标识符")).
		Property("event_name", StringSchema("事件名称")).
		Property("event_final_state", EnumSchema("事件最终状态", "完成", "进行中", "暂停", "失败", "取消", "转化为新事件")).
		Property("participating_character", NewSchemaBuilder().Object().
			Description("参与角色信息").
			Property("character_id", StringSchema("角色唯一标识符")).
			Property("character_name", StringSchema("角色名称")).
			Property("character_type", EnumSchema("角色类型", "玩家", "NPC", "敌人", "商人", "导师", "守卫", "平民")).
			Property("role_in_event", EnumSchema("在事件中的角色", "主导者", "参与者", "观察者", "受害者", "受益者", "调解者")).
			Property("performance_evaluation", NewSchemaBuilder().Object().
				Description("表现评估").
				Property("skill_application", StringArraySchema("应用技能列表")).
				Property("experience_gained", StringArraySchema("获得经验列表")).
				Property("mistakes_made", StringArraySchema("犯错列表")).
				Property("creative_solutions", StringArraySchema("创新解决方案列表")).
				Property("learning_opportunities", StringArraySchema("学习机会列表")).
				Build()).
			Required("character_id", "character_name", "character_type", "role_in_event", "performance_evaluation").
			Build()).
		Property("character_action_details", NewSchemaBuilder().Object().
			Description("角色行动详情").
			Property("intended_action", StringSchema("预期行动描述")).
			Property("actual_execution", StringSchema("实际执行描述")).
			Property("action_effectiveness", NewSchemaBuilder().Number().
				Description("行动有效性（0-1）").Minimum(0).Maximum(1).Build()).
			Property("resource_usage", ArraySchema("资源使用列表", NewSchemaBuilder().Object().
				Description("资源使用").
				Property("resource_type", EnumSchema("资源类型", "物品", "技能", "时间", "体力", "魔力", "金钱", "人脉", "信息")).
				Property("resource_name", StringSchema("资源名称")).
				Property("usage_amount", StringSchema("使用量描述")).
				Property("usage_result", EnumSchema("使用结果", "有效", "部分有效", "无效", "浪费", "意外效果")).
				Required("resource_type", "resource_name", "usage_amount", "usage_result").
				Build())).
			Property("decision_points", ArraySchema("决策点列表", NewSchemaBuilder().Object().
				Description("决策点").
				Property("decision_description", StringSchema("决策描述")).
				Property("available_options", StringArraySchema("可选选项列表")).
				Property("chosen_option", StringSchema("选择的选项")).
				Property("decision_reasoning", StringSchema("决策理由")).
				Property("decision_consequences", StringArraySchema("决策后果列表")).
				Required("decision_description", "chosen_option", "decision_reasoning").
				Build())).
			Property("collaboration_aspects", ArraySchema("协作方面列表", NewSchemaBuilder().Object().
				Description("协作方面").
				Property("collaborator_id", StringSchema("协作者ID")).
				Property("collaboration_type", EnumSchema("协作类型", "配合", "竞争", "冲突", "支援", "指导", "被指导")).
				Property("collaboration_effectiveness", EnumSchema("协作效果", "优秀", "良好", "一般", "较差", "失败")).
				Property("collaboration_description", StringSchema("协作描述")).
				Required("collaborator_id", "collaboration_type", "collaboration_effectiveness", "collaboration_description").
				Build())).
			Required("intended_action", "actual_execution", "action_effectiveness", "resource_usage", "decision_points").
			Build()).
		Property("consequences", NewSchemaBuilder().Object().
			Description("后果影响").
			Property("immediate_effects", ArraySchema("即时影响列表", NewSchemaBuilder().Object().
				Description("即时影响").
				Property("effect_type", EnumSchema("影响类型", "角色状态", "物品变化", "环境变化", "关系变化", "信息获得", "技能提升")).
				Property("effect_description", StringSchema("影响描述")).
				Property("effect_magnitude", EnumSchema("影响程度", "微小", "轻微", "中等", "重大", "巨大")).
				Property("affected_targets", StringArraySchema("受影响目标列表")).
				Required("effect_type", "effect_description", "effect_magnitude").
				Build())).
			Property("long_term_consequences", StringArraySchema("长期后果列表")).
			Property("ripple_effects", StringArraySchema("连锁反应列表")).
			Property("world_state_changes", StringArraySchema("世界状态变化列表")).
			Property("reputation_changes", ArraySchema("声誉变化列表", NewSchemaBuilder().Object().
				Description("声誉变化").
				Property("reputation_type", EnumSchema("声誉类型", "个人声誉", "组织声誉", "地区声誉", "专业声誉")).
				Property("change_amount", EnumSchema("变化幅度", "大幅下降", "下降", "轻微下降", "无变化", "轻微上升", "上升", "大幅上升")).
				Property("affected_groups", StringArraySchema("受影响群体列表")).
				Property("change_reason", StringSchema("变化原因")).
				Required("reputation_type", "change_amount", "change_reason").
				Build())).
			Required("immediate_effects").
			Build()).
		Property("experience_and_learning", NewSchemaBuilder().Object().
			Description("经验与学习").
			Property("experience_categories", ArraySchema("经验分类列表", NewSchemaBuilder().Object().
				Description("经验分类").
				Property("category", EnumSchema("经验类别", "战斗阅历", "社交阅历", "探索阅历", "制作阅历", "事件阅历", "领导阅历", "学术阅历")).
				Property("experience_gained", StringSchema("获得经验描述")).
				Property("proficiency_improvement", EnumSchema("熟练度提升", "无提升", "轻微提升", "明显提升", "重大提升", "突破性提升")).
				Property("new_insights", StringArraySchema("新见解列表")).
				Required("category", "experience_gained", "proficiency_improvement").
				Build())).
			Property("skill_developments", StringArraySchema("技能发展列表")).
			Property("knowledge_acquired", StringArraySchema("获得知识列表")).
			Property("wisdom_gained", StringArraySchema("获得智慧列表")).
			Property("character_growth", StringArraySchema("角色成长列表")).
			Required("experience_categories").
			Build()).
		Property("future_implications", NewSchemaBuilder().Object().
			Description("未来影响").
			Property("new_opportunities", StringArraySchema("新机会列表")).
			Property("potential_challenges", StringArraySchema("潜在挑战列表")).
			Property("relationship_developments", StringArraySchema("关系发展列表")).
			Property("story_hooks", StringArraySchema("故事钩子列表")).
			Property("world_evolution_triggers", StringArraySchema("世界演化触发器列表")).
			Build()).
		Required("result_id", "event_result", "event_id", "event_name", "event_final_state",
			"participating_character", "character_action_details", "consequences", "experience_and_learning").
		Build()

	return NewSchemaBuilder().Object().
		Description("事件交互场景输出结果").
		Property("interaction_results", ArraySchema("交互结果列表，包含所有参与角色的交互结果", interactionResultSchema)).
		Property("event_summary", NewSchemaBuilder().Object().
			Description("事件总结").
			Property("event_conclusion", StringSchema("事件结论描述")).
			Property("overall_success_rate", NewSchemaBuilder().Number().
				Description("整体成功率（0-1）").Minimum(0).Maximum(1).Build()).
			Property("participant_count", IntegerSchema("参与者数量", floatPtr(1), floatPtr(50))).
			Property("duration_actual", IntegerSchema("实际持续时间（分钟）", floatPtr(1), floatPtr(1440))).
			Property("complexity_level", EnumSchema("复杂度等级", "简单", "普通", "复杂", "极复杂")).
			Required("event_conclusion", "overall_success_rate", "participant_count", "duration_actual", "complexity_level").
			Build()).
		Property("world_impact_assessment", NewSchemaBuilder().Object().
			Description("世界影响评估").
			Property("local_impact", StringArraySchema("本地影响列表")).
			Property("regional_impact", StringArraySchema("区域影响列表")).
			Property("global_impact", StringArraySchema("全球影响列表")).
			Property("historical_significance", EnumSchema("历史意义", "无意义", "轻微", "一般", "重要", "重大", "历史性")).
			Property("cultural_impact", StringArraySchema("文化影响列表")).
			Property("economic_impact", StringArraySchema("经济影响列表")).
			Property("political_impact", StringArraySchema("政治影响列表")).
			Build()).
		Property("follow_up_events", ArraySchema("后续事件列表", NewSchemaBuilder().Object().
			Description("后续事件").
			Property("event_type", EnumSchema("事件类型", "直接后果", "间接影响", "连锁反应", "报复行动", "庆祝活动", "调查事件")).
			Property("event_description", StringSchema("事件描述")).
			Property("estimated_trigger_time", IntegerSchema("预计触发时间（小时）", floatPtr(1), floatPtr(168))).
			Property("probability", NewSchemaBuilder().Number().
				Description("发生概率（0-1）").Minimum(0).Maximum(1).Build()).
			Property("involved_parties", StringArraySchema("涉及方列表")).
			Required("event_type", "event_description", "estimated_trigger_time", "probability").
			Build())).
		Required("interaction_results", "event_summary", "world_impact_assessment", "follow_up_events").
		Build()
}

// ========== 通用Schema定义（向后兼容） ==========

// createSceneSchema 创建场景生成Schema
func (gsr *GameSchemaRegistry) createSceneSchema() *JSONSchema {
	// 连接信息Schema
	connectionSchema := NewSchemaBuilder().Object().
		Description("场景连接信息").
		Property("direction", StringSchema("方向（如：北、南、东、西、上、下）")).
		Property("description", StringSchema("连接的详细描述")).
		Property("scene_name", StringSchema("连接到的场景名称")).
		Property("is_locked", NewSchemaBuilder().Boolean().Description("是否被锁定").Build()).
		Property("unlock_condition", StringSchema("解锁条件描述（可选）")).
		Required("direction", "description", "scene_name").
		Build()
	
	return NewSchemaBuilder().Object().
		Description("游戏场景生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("场景名称，应该简洁且富有想象力").
			MinLength(2).MaxLength(50).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("详细的场景描述，包含环境、氛围、关键特征等，应该生动形象").
			MinLength(50).MaxLength(500).Build()).
		Property("atmosphere", EnumSchema("场景氛围类型", 
			"神秘", "温馨", "紧张", "恐怖", "宁静", "热闹", "庄严", "荒凉", "梦幻", "危险")).
		Property("scene_type", EnumSchema("场景类型",
			"室内", "室外", "地下", "城镇", "荒野", "森林", "山脉", "海洋", "天空", "特殊")).
		Property("key_features", StringArraySchema("场景的关键特征列表，每个特征应该具体且有用")).
		Property("possible_actions", StringArraySchema("在此场景中玩家可能进行的行动列表")).
		Property("connections", ArraySchema("场景连接信息列表", connectionSchema)).
		Property("hidden_elements", StringArraySchema("隐藏元素列表，需要特定条件才能发现")).
		Property("danger_level", IntegerSchema("危险等级（1-10）", floatPtr(1), floatPtr(10))).
		Property("lighting", EnumSchema("光照条件", "明亮", "昏暗", "黑暗", "闪烁", "彩色", "自然")).
		Property("sounds", StringArraySchema("环境音效描述列表")).
		Property("smells", StringArraySchema("环境气味描述列表")).
		Required("name", "description", "atmosphere", "scene_type", "key_features", 
			"possible_actions", "connections", "danger_level", "lighting").
		Build()
}

// createCharacterSchema 创建角色生成Schema
func (gsr *GameSchemaRegistry) createCharacterSchema() *JSONSchema {
	// 属性Schema
	attributesSchema := NewSchemaBuilder().Object().
		Description("角色属性").
		Property("strength", IntegerSchema("力量值（1-100）", floatPtr(1), floatPtr(100))).
		Property("intelligence", IntegerSchema("智力值（1-100）", floatPtr(1), floatPtr(100))).
		Property("agility", IntegerSchema("敏捷值（1-100）", floatPtr(1), floatPtr(100))).
		Property("charisma", IntegerSchema("魅力值（1-100）", floatPtr(1), floatPtr(100))).
		Property("health", IntegerSchema("生命值（1-1000）", floatPtr(1), floatPtr(1000))).
		Property("mana", IntegerSchema("魔法值（0-1000）", floatPtr(0), floatPtr(1000))).
		Required("strength", "intelligence", "agility", "charisma", "health").
		Build()
	
	// 外观Schema
	appearanceSchema := NewSchemaBuilder().Object().
		Description("角色外观描述").
		Property("height", StringSchema("身高描述")).
		Property("build", EnumSchema("体型", "瘦弱", "苗条", "标准", "健壮", "肥胖", "魁梧")).
		Property("hair_color", StringSchema("头发颜色")).
		Property("eye_color", StringSchema("眼睛颜色")).
		Property("skin_tone", StringSchema("肤色")).
		Property("distinctive_features", StringArraySchema("显著特征列表")).
		Property("clothing_style", StringSchema("服装风格描述")).
		Required("height", "build", "hair_color", "eye_color", "skin_tone").
		Build()
	
	return NewSchemaBuilder().Object().
		Description("游戏角色生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("角色名称，应该符合游戏世界观").
			MinLength(2).MaxLength(30).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("角色的基本描述，包含外观和给人的第一印象").
			MinLength(50).MaxLength(300).Build()).
		Property("character_type", EnumSchema("角色类型", "玩家", "NPC", "敌人", "商人", "导师", "守卫", "平民")).
		Property("personality", StringArraySchema("性格特征列表，每个特征应该具体且影响行为")).
		Property("background", NewSchemaBuilder().String().
			Description("角色背景故事，解释其来历和经历").
			MinLength(100).MaxLength(500).Build()).
		Property("skills", StringArraySchema("技能列表，包含战斗和非战斗技能")).
		Property("dialogue_style", StringSchema("对话风格描述，包含语言习惯和表达方式")).
		Property("motivations", StringArraySchema("动机列表，驱动角色行为的内在原因")).
		Property("fears", StringArraySchema("恐惧列表，角色害怕或回避的事物")).
		Property("goals", StringArraySchema("目标列表，角色想要达成的目的")).
		Property("relationships", StringArraySchema("重要关系描述列表")).
		Property("attributes", attributesSchema).
		Property("appearance", appearanceSchema).
		Property("age_range", EnumSchema("年龄段", "儿童", "青少年", "青年", "中年", "老年", "古老")).
		Property("occupation", StringSchema("职业或身份")).
		Property("alignment", EnumSchema("阵营倾向", "善良", "中立", "邪恶", "守序", "混乱")).
		Required("name", "description", "character_type", "personality", "background", 
			"skills", "dialogue_style", "motivations", "attributes", "appearance", 
			"age_range", "occupation", "alignment").
		Build()
}

// createEventSchema 创建事件生成Schema
func (gsr *GameSchemaRegistry) createEventSchema() *JSONSchema {
	// 事件效果Schema
	effectsSchema := NewSchemaBuilder().Object().
		Description("事件效果").
		Property("description", StringSchema("事件效果的详细描述")).
		Property("consequences", StringArraySchema("事件后果列表")).
		Property("rewards", StringArraySchema("可能的奖励列表")).
		Property("penalties", StringArraySchema("可能的惩罚列表")).
		Property("stat_changes", NewSchemaBuilder().Object().
			Description("属性变化").
			Property("health", IntegerSchema("生命值变化", floatPtr(-100), floatPtr(100))).
			Property("mana", IntegerSchema("魔法值变化", floatPtr(-100), floatPtr(100))).
			Property("experience", IntegerSchema("经验值变化", floatPtr(-50), floatPtr(200))).
			Build()).
		Required("description", "consequences").
		Build()
	
	return NewSchemaBuilder().Object().
		Description("游戏事件生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("事件名称，应该简洁且吸引人").
			MinLength(5).MaxLength(50).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("事件的详细描述，包含发生的情况和背景").
			MinLength(100).MaxLength(800).Build()).
		Property("event_type", EnumSchema("事件类型", 
			"随机遭遇", "剧情事件", "角色互动", "环境变化", "战斗", "探索", "谜题", "交易", "任务")).
		Property("priority", IntegerSchema("事件优先级（1-10，数字越大优先级越高）", floatPtr(1), floatPtr(10))).
		Property("duration", IntegerSchema("事件持续时间（分钟）", floatPtr(1), floatPtr(1440))).
		Property("trigger_conditions", StringArraySchema("触发条件列表")).
		Property("participants", StringArraySchema("参与者列表（角色名称或类型）")).
		Property("location_requirements", StringArraySchema("地点要求列表")).
		Property("effects", effectsSchema).
		Property("choices", ArraySchema("玩家选择列表", NewSchemaBuilder().Object().
			Description("玩家选择选项").
			Property("text", StringSchema("选择文本")).
			Property("consequence", StringSchema("选择后果")).
			Required("text", "consequence").
			Build())).
		Property("difficulty", EnumSchema("难度等级", "简单", "普通", "困难", "极难")).
		Property("repeatable", NewSchemaBuilder().Boolean().Description("是否可重复触发").Build()).
		Required("name", "description", "event_type", "priority", "duration", 
			"trigger_conditions", "effects", "difficulty", "repeatable").
		Build()
}

// createDialogueSchema 创建对话生成Schema
func (gsr *GameSchemaRegistry) createDialogueSchema() *JSONSchema {
	return NewSchemaBuilder().Object().
		Description("游戏对话生成结果").
		Property("content", NewSchemaBuilder().String().
			Description("对话内容，应该符合角色性格和当前情境").
			MinLength(10).MaxLength(500).Build()).
		Property("emotion", EnumSchema("情感状态",
			"高兴", "愤怒", "悲伤", "恐惧", "惊讶", "厌恶", "中性", "兴奋", "困惑", "失望")).
		Property("tone", EnumSchema("语调",
			"友好", "敌对", "正式", "随意", "神秘", "威胁", "恳求", "自信", "怯懦", "傲慢")).
		Property("intent", EnumSchema("对话意图",
			"信息传递", "请求帮助", "威胁警告", "友好交流", "商业交易", "任务发布", "情感表达", "指导教学")).
		Property("context_awareness", StringSchema("对当前情境的认知和反应")).
		Property("follow_up_suggestions", StringArraySchema("后续对话建议列表")).
		Property("body_language", StringSchema("肢体语言描述")).
		Property("voice_characteristics", StringSchema("声音特征描述")).
		Required("content", "emotion", "tone", "intent", "context_awareness").
		Build()
}

// createItemSchema 创建物品生成Schema
func (gsr *GameSchemaRegistry) createItemSchema() *JSONSchema {
	// 物品属性Schema
	itemPropertiesSchema := NewSchemaBuilder().Object().
		Description("物品属性").
		Property("durability", IntegerSchema("耐久度（1-100）", floatPtr(1), floatPtr(100))).
		Property("weight", NewSchemaBuilder().Number().
			Description("重量（千克）").Minimum(0.01).Maximum(1000).Build()).
		Property("value", IntegerSchema("价值（金币）", floatPtr(0), floatPtr(100000))).
		Property("rarity", EnumSchema("稀有度", "普通", "罕见", "稀有", "史诗", "传说")).
		Property("magical", NewSchemaBuilder().Boolean().Description("是否为魔法物品").Build()).
		Build()

	return NewSchemaBuilder().Object().
		Description("游戏物品生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("物品名称，应该简洁且富有特色").
			MinLength(2).MaxLength(50).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("物品的详细描述，包含外观、材质、特殊之处").
			MinLength(50).MaxLength(400).Build()).
		Property("item_type", EnumSchema("物品类型",
			"武器", "防具", "工具", "消耗品", "材料", "宝石", "书籍", "钥匙", "装饰品", "特殊物品")).
		Property("sub_type", StringSchema("物品子类型（如：长剑、皮甲、治疗药水等）")).
		Property("properties", itemPropertiesSchema).
		Property("effects", StringArraySchema("物品效果列表")).
		Property("usage_requirements", StringArraySchema("使用要求列表")).
		Property("crafting_materials", StringArraySchema("制作材料列表（如果可制作）")).
		Property("lore", StringSchema("物品背景故事或传说")).
		Property("appearance", StringSchema("外观详细描述")).
		Property("size", EnumSchema("尺寸", "微小", "小型", "中型", "大型", "巨大")).
		Required("name", "description", "item_type", "sub_type", "properties", "size").
		Build()
}

// createWorldDescriptionSchema 创建世界描述Schema
func (gsr *GameSchemaRegistry) createWorldDescriptionSchema() *JSONSchema {
	// 世界规则Schema
	worldRulesSchema := NewSchemaBuilder().Object().
		Description("世界规则").
		Property("magic_system", StringSchema("魔法体系描述")).
		Property("technology_level", EnumSchema("科技水平", "原始", "古代", "中世纪", "文艺复兴", "工业", "现代", "未来")).
		Property("social_structure", StringSchema("社会结构描述")).
		Property("economy", StringSchema("经济体系描述")).
		Property("laws", StringArraySchema("重要法律或规则列表")).
		Build()

	return NewSchemaBuilder().Object().
		Description("游戏世界描述生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("世界名称，应该独特且富有想象力").
			MinLength(3).MaxLength(50).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("世界的总体描述，包含地理、文化、历史概况").
			MinLength(200).MaxLength(1000).Build()).
		Property("theme", EnumSchema("世界主题",
			"奇幻", "科幻", "现代", "历史", "蒸汽朋克", "赛博朋克", "后末日", "超自然", "武侠", "仙侠")).
		Property("geography", StringArraySchema("主要地理特征列表")).
		Property("climate", StringSchema("气候特征描述")).
		Property("major_locations", StringArraySchema("主要地点列表")).
		Property("cultures", StringArraySchema("主要文化或种族列表")).
		Property("history", StringSchema("重要历史事件概述")).
		Property("current_conflicts", StringArraySchema("当前冲突或问题列表")).
		Property("notable_figures", StringArraySchema("重要人物列表")).
		Property("world_rules", worldRulesSchema).
		Property("dangers", StringArraySchema("世界中的主要危险列表")).
		Property("opportunities", StringArraySchema("冒险机会列表")).
		Required("name", "description", "theme", "geography", "climate",
			"major_locations", "cultures", "world_rules").
		Build()
}

// createQuestSchema 创建任务生成Schema
func (gsr *GameSchemaRegistry) createQuestSchema() *JSONSchema {
	// 任务奖励Schema
	questRewardsSchema := NewSchemaBuilder().Object().
		Description("任务奖励").
		Property("experience", IntegerSchema("经验值奖励", floatPtr(0), floatPtr(10000))).
		Property("gold", IntegerSchema("金币奖励", floatPtr(0), floatPtr(50000))).
		Property("items", StringArraySchema("物品奖励列表")).
		Property("reputation", StringSchema("声望变化描述")).
		Property("special_rewards", StringArraySchema("特殊奖励列表")).
		Build()

	// 任务步骤Schema
	questStepSchema := NewSchemaBuilder().Object().
		Description("任务步骤").
		Property("step_number", IntegerSchema("步骤编号", floatPtr(1), floatPtr(20))).
		Property("description", StringSchema("步骤描述")).
		Property("objective", StringSchema("具体目标")).
		Property("location", StringSchema("执行地点")).
		Property("hints", StringArraySchema("提示列表")).
		Required("step_number", "description", "objective").
		Build()

	return NewSchemaBuilder().Object().
		Description("游戏任务生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("任务名称，应该吸引人且概括任务内容").
			MinLength(5).MaxLength(60).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("任务的详细描述，包含背景和目标").
			MinLength(100).MaxLength(600).Build()).
		Property("quest_type", EnumSchema("任务类型",
			"主线任务", "支线任务", "日常任务", "紧急任务", "隐藏任务", "连锁任务", "重复任务")).
		Property("difficulty", EnumSchema("难度等级", "新手", "简单", "普通", "困难", "专家", "传说")).
		Property("estimated_duration", IntegerSchema("预计完成时间（分钟）", floatPtr(5), floatPtr(480))).
		Property("prerequisites", StringArraySchema("前置条件列表")).
		Property("steps", ArraySchema("任务步骤列表", questStepSchema)).
		Property("rewards", questRewardsSchema).
		Property("failure_consequences", StringArraySchema("失败后果列表")).
		Property("time_limit", IntegerSchema("时间限制（小时，0表示无限制）", floatPtr(0), floatPtr(168))).
		Property("repeatable", NewSchemaBuilder().Boolean().Description("是否可重复完成").Build()).
		Property("quest_giver", StringSchema("任务发布者")).
		Property("location", StringSchema("任务主要执行地点")).
		Required("name", "description", "quest_type", "difficulty",
			"estimated_duration", "steps", "rewards", "quest_giver").
		Build()
}

// createEnvironmentEffectSchema 创建环境效果Schema
func (gsr *GameSchemaRegistry) createEnvironmentEffectSchema() *JSONSchema {
	return NewSchemaBuilder().Object().
		Description("环境效果生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("环境效果名称").
			MinLength(3).MaxLength(40).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("环境效果的详细描述").
			MinLength(50).MaxLength(300).Build()).
		Property("effect_type", EnumSchema("效果类型",
			"天气变化", "魔法效应", "自然现象", "超自然事件", "环境危险", "祝福效果", "诅咒效果")).
		Property("duration", IntegerSchema("持续时间（分钟，0表示永久）", floatPtr(0), floatPtr(1440))).
		Property("intensity", EnumSchema("强度等级", "微弱", "轻微", "中等", "强烈", "极强")).
		Property("affected_area", EnumSchema("影响范围", "单点", "小范围", "中等范围", "大范围", "全区域")).
		Property("visual_effects", StringArraySchema("视觉效果描述列表")).
		Property("audio_effects", StringArraySchema("音效描述列表")).
		Property("gameplay_effects", StringArraySchema("游戏机制影响列表")).
		Property("stat_modifiers", NewSchemaBuilder().Object().
			Description("属性修正").
			Property("movement_speed", NewSchemaBuilder().Number().
				Description("移动速度修正（倍数）").Minimum(0.1).Maximum(3.0).Build()).
			Property("visibility", NewSchemaBuilder().Number().
				Description("可见度修正（倍数）").Minimum(0.1).Maximum(2.0).Build()).
			Property("damage_modifier", NewSchemaBuilder().Number().
				Description("伤害修正（倍数）").Minimum(0.5).Maximum(2.0).Build()).
			Build()).
		Property("trigger_conditions", StringArraySchema("触发条件列表")).
		Property("end_conditions", StringArraySchema("结束条件列表")).
		Required("name", "description", "effect_type", "duration", "intensity", "affected_area").
		Build()
}

// floatPtr 辅助函数：创建float64指针
func floatPtr(f float64) *float64 {
	return &f
}
