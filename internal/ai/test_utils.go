package ai

import (
	"log"

	"ai-text-game-iam-npc/pkg/logger"
)

// testLogger 简单的测试日志器
type testLogger struct{}

func (l *testLogger) Debug(msg string, args ...interface{}) { log.Printf("[DEBUG] "+msg, args...) }
func (l *testLogger) Info(msg string, args ...interface{})  { log.Printf("[INFO] "+msg, args...) }
func (l *testLogger) Warn(msg string, args ...interface{})  { log.Printf("[WARN] "+msg, args...) }
func (l *testLogger) Error(msg string, args ...interface{}) { log.Printf("[ERROR] "+msg, args...) }
func (l *testLogger) Fatal(msg string, args ...interface{}) { log.Fatalf("[FATAL] "+msg, args...) }
func (l *testLogger) SetLevel(level string)                 {}

func newTestLogger() logger.Logger {
	return &testLogger{}
}
