package ai

import (
	"fmt"
	"time"

	"ai-text-game-iam-npc/internal/config"
)

/**
 * AI配置管理器
 * @description 管理AI服务的动态配置和参数调优
 */

// ModelConfig 模型配置
type ModelConfig struct {
	Name            string  `json:"name"`             // 模型名称
	MaxTokens       int     `json:"max_tokens"`       // 最大token数
	Temperature     float64 `json:"temperature"`      // 温度参数
	TopP            float64 `json:"top_p"`            // Top-p采样
	FrequencyPenalty float64 `json:"frequency_penalty"` // 频率惩罚
	PresencePenalty float64 `json:"presence_penalty"`  // 存在惩罚
	Timeout         time.Duration `json:"timeout"`    // 超时时间
}

// ContentTypeConfig 内容类型配置
type ContentTypeConfig struct {
	Model           string            `json:"model"`            // 使用的模型
	Priority        int               `json:"priority"`         // 优先级
	CacheEnabled    bool              `json:"cache_enabled"`    // 是否启用缓存
	CacheTTL        time.Duration     `json:"cache_ttl"`        // 缓存过期时间
	ValidationLevel string            `json:"validation_level"` // 验证级别：strict, normal, loose
	CustomParams    map[string]interface{} `json:"custom_params"` // 自定义参数
}

// AIConfigManager AI配置管理器
type AIConfigManager struct {
	baseConfig      *config.Config
	modelConfigs    map[string]*ModelConfig
	contentConfigs  map[string]*ContentTypeConfig
	globalSettings  map[string]interface{}
}

// NewAIConfigManager 创建AI配置管理器
func NewAIConfigManager(cfg *config.Config) *AIConfigManager {
	manager := &AIConfigManager{
		baseConfig:     cfg,
		modelConfigs:   make(map[string]*ModelConfig),
		contentConfigs: make(map[string]*ContentTypeConfig),
		globalSettings: make(map[string]interface{}),
	}
	
	// 初始化默认配置
	manager.initializeDefaultConfigs()
	
	return manager
}

// initializeDefaultConfigs 初始化默认配置
func (acm *AIConfigManager) initializeDefaultConfigs() {
	// 默认模型配置
	acm.modelConfigs["gemini-1.5-pro"] = &ModelConfig{
		Name:             "gemini-1.5-pro",
		MaxTokens:        8192,
		Temperature:      0.7,
		TopP:             0.9,
		FrequencyPenalty: 0.0,
		PresencePenalty:  0.0,
		Timeout:          30 * time.Second,
	}
	
	acm.modelConfigs["gemini-1.5-flash"] = &ModelConfig{
		Name:             "gemini-1.5-flash",
		MaxTokens:        8192,
		Temperature:      0.8,
		TopP:             0.95,
		FrequencyPenalty: 0.1,
		PresencePenalty:  0.1,
		Timeout:          15 * time.Second,
	}
	
	// 内容类型配置
	contentTypes := []string{"scene", "character", "event", "dialogue", "item", "world_description", "quest", "environment_effect"}
	
	for _, contentType := range contentTypes {
		acm.contentConfigs[contentType] = &ContentTypeConfig{
			Model:           acm.baseConfig.AI.Windmill.DefaultModel,
			Priority:        5,
			CacheEnabled:    true,
			CacheTTL:        1 * time.Hour,
			ValidationLevel: "normal",
			CustomParams:    make(map[string]interface{}),
		}
	}
	
	// 特殊配置调整
	acm.contentConfigs["scene"].Priority = 8
	acm.contentConfigs["scene"].ValidationLevel = "strict"
	
	acm.contentConfigs["character"].Priority = 9
	acm.contentConfigs["character"].ValidationLevel = "strict"
	
	acm.contentConfigs["dialogue"].Model = "gemini-1.5-flash" // 对话使用更快的模型
	acm.contentConfigs["dialogue"].CacheTTL = 30 * time.Minute
	
	acm.contentConfigs["environment_effect"].Priority = 3
	acm.contentConfigs["environment_effect"].ValidationLevel = "loose"
	
	// 全局设置
	acm.globalSettings["enable_content_filtering"] = true
	acm.globalSettings["enable_performance_monitoring"] = true
	acm.globalSettings["enable_usage_analytics"] = true
	acm.globalSettings["max_concurrent_requests"] = 10
	acm.globalSettings["request_queue_size"] = 100
}

// GetModelConfig 获取模型配置
func (acm *AIConfigManager) GetModelConfig(modelName string) (*ModelConfig, error) {
	config, exists := acm.modelConfigs[modelName]
	if !exists {
		return nil, fmt.Errorf("未找到模型 '%s' 的配置", modelName)
	}
	
	// 返回配置的副本，避免外部修改
	return &ModelConfig{
		Name:             config.Name,
		MaxTokens:        config.MaxTokens,
		Temperature:      config.Temperature,
		TopP:             config.TopP,
		FrequencyPenalty: config.FrequencyPenalty,
		PresencePenalty:  config.PresencePenalty,
		Timeout:          config.Timeout,
	}, nil
}

// GetContentTypeConfig 获取内容类型配置
func (acm *AIConfigManager) GetContentTypeConfig(contentType string) (*ContentTypeConfig, error) {
	config, exists := acm.contentConfigs[contentType]
	if !exists {
		return nil, fmt.Errorf("未找到内容类型 '%s' 的配置", contentType)
	}
	
	// 返回配置的副本
	customParams := make(map[string]interface{})
	for k, v := range config.CustomParams {
		customParams[k] = v
	}
	
	return &ContentTypeConfig{
		Model:           config.Model,
		Priority:        config.Priority,
		CacheEnabled:    config.CacheEnabled,
		CacheTTL:        config.CacheTTL,
		ValidationLevel: config.ValidationLevel,
		CustomParams:    customParams,
	}, nil
}

// UpdateModelConfig 更新模型配置
func (acm *AIConfigManager) UpdateModelConfig(modelName string, config *ModelConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	
	// 验证配置参数
	if err := acm.validateModelConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}
	
	acm.modelConfigs[modelName] = config
	return nil
}

// UpdateContentTypeConfig 更新内容类型配置
func (acm *AIConfigManager) UpdateContentTypeConfig(contentType string, config *ContentTypeConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	
	// 验证配置参数
	if err := acm.validateContentTypeConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}
	
	acm.contentConfigs[contentType] = config
	return nil
}

// validateModelConfig 验证模型配置
func (acm *AIConfigManager) validateModelConfig(config *ModelConfig) error {
	if config.Name == "" {
		return fmt.Errorf("模型名称不能为空")
	}
	
	if config.MaxTokens <= 0 || config.MaxTokens > 32768 {
		return fmt.Errorf("最大token数必须在1-32768之间")
	}
	
	if config.Temperature < 0 || config.Temperature > 2 {
		return fmt.Errorf("温度参数必须在0-2之间")
	}
	
	if config.TopP < 0 || config.TopP > 1 {
		return fmt.Errorf("Top-p参数必须在0-1之间")
	}
	
	if config.Timeout <= 0 || config.Timeout > 5*time.Minute {
		return fmt.Errorf("超时时间必须在0-5分钟之间")
	}
	
	return nil
}

// validateContentTypeConfig 验证内容类型配置
func (acm *AIConfigManager) validateContentTypeConfig(config *ContentTypeConfig) error {
	if config.Model == "" {
		return fmt.Errorf("模型名称不能为空")
	}
	
	if config.Priority < 1 || config.Priority > 10 {
		return fmt.Errorf("优先级必须在1-10之间")
	}
	
	if config.CacheTTL < 0 || config.CacheTTL > 24*time.Hour {
		return fmt.Errorf("缓存过期时间必须在0-24小时之间")
	}
	
	validationLevels := []string{"strict", "normal", "loose"}
	validLevel := false
	for _, level := range validationLevels {
		if config.ValidationLevel == level {
			validLevel = true
			break
		}
	}
	if !validLevel {
		return fmt.Errorf("验证级别必须是 strict、normal 或 loose 之一")
	}
	
	return nil
}

// GetGlobalSetting 获取全局设置
func (acm *AIConfigManager) GetGlobalSetting(key string) (interface{}, bool) {
	value, exists := acm.globalSettings[key]
	return value, exists
}

// SetGlobalSetting 设置全局设置
func (acm *AIConfigManager) SetGlobalSetting(key string, value interface{}) {
	acm.globalSettings[key] = value
}

// GetOptimalModelForContentType 获取内容类型的最优模型
func (acm *AIConfigManager) GetOptimalModelForContentType(contentType string) (string, error) {
	contentConfig, err := acm.GetContentTypeConfig(contentType)
	if err != nil {
		return "", err
	}
	
	// 检查模型是否存在
	if _, err := acm.GetModelConfig(contentConfig.Model); err != nil {
		// 如果指定的模型不存在，返回默认模型
		return acm.baseConfig.AI.Windmill.DefaultModel, nil
	}
	
	return contentConfig.Model, nil
}

// GetAllModelNames 获取所有可用的模型名称
func (acm *AIConfigManager) GetAllModelNames() []string {
	names := make([]string, 0, len(acm.modelConfigs))
	for name := range acm.modelConfigs {
		names = append(names, name)
	}
	return names
}

// GetAllContentTypes 获取所有支持的内容类型
func (acm *AIConfigManager) GetAllContentTypes() []string {
	types := make([]string, 0, len(acm.contentConfigs))
	for contentType := range acm.contentConfigs {
		types = append(types, contentType)
	}
	return types
}

// GetConfigSummary 获取配置摘要
func (acm *AIConfigManager) GetConfigSummary() map[string]interface{} {
	return map[string]interface{}{
		"total_models":       len(acm.modelConfigs),
		"total_content_types": len(acm.contentConfigs),
		"default_model":      acm.baseConfig.AI.Windmill.DefaultModel,
		"global_settings":    acm.globalSettings,
		"available_models":   acm.GetAllModelNames(),
		"supported_types":    acm.GetAllContentTypes(),
	}
}
