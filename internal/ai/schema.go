package ai

/**
 * JSON Schema接口定义
 * @description 用于定义AI服务返回数据的结构化格式
 */

// JSONSchema JSON Schema结构定义接口
type JSONSchema struct {
	// 数据类型 (object, array, string, number, boolean, integer)
	Type string `json:"type"`
	// 数组项的schema定义
	Items *JSONSchema `json:"items,omitempty"`
	// 对象属性的schema定义
	Properties map[string]*JSONSchema `json:"properties,omitempty"`
	// 必需的属性列表
	Required []string `json:"required,omitempty"`
	// 属性排序
	PropertyOrdering []string `json:"propertyOrdering,omitempty"`
	// 枚举值列表
	Enum []string `json:"enum,omitempty"`
	// 数据格式 (date-time, email等)
	Format string `json:"format,omitempty"`
	// 字段描述
	Description string `json:"description,omitempty"`
	// 数字类型的最小值
	Minimum *float64 `json:"minimum,omitempty"`
	// 数字类型的最大值
	Maximum *float64 `json:"maximum,omitempty"`
	// 字符串最小长度
	MinLength *int `json:"minLength,omitempty"`
	// 字符串最大长度
	MaxLength *int `json:"maxLength,omitempty"`
	// 数组最小项数
	MinItems *int `json:"minItems,omitempty"`
	// 数组最大项数
	MaxItems *int `json:"maxItems,omitempty"`
	// 字符串模式匹配
	Pattern string `json:"pattern,omitempty"`
}

// SchemaBuilder JSON Schema构建器
type SchemaBuilder struct {
	schema *JSONSchema
}

// NewSchemaBuilder 创建新的Schema构建器
func NewSchemaBuilder() *SchemaBuilder {
	return &SchemaBuilder{
		schema: &JSONSchema{},
	}
}

// Object 设置为对象类型
func (sb *SchemaBuilder) Object() *SchemaBuilder {
	sb.schema.Type = "object"
	if sb.schema.Properties == nil {
		sb.schema.Properties = make(map[string]*JSONSchema)
	}
	return sb
}

// Array 设置为数组类型
func (sb *SchemaBuilder) Array() *SchemaBuilder {
	sb.schema.Type = "array"
	return sb
}

// String 设置为字符串类型
func (sb *SchemaBuilder) String() *SchemaBuilder {
	sb.schema.Type = "string"
	return sb
}

// Integer 设置为整数类型
func (sb *SchemaBuilder) Integer() *SchemaBuilder {
	sb.schema.Type = "integer"
	return sb
}

// Number 设置为数字类型
func (sb *SchemaBuilder) Number() *SchemaBuilder {
	sb.schema.Type = "number"
	return sb
}

// Boolean 设置为布尔类型
func (sb *SchemaBuilder) Boolean() *SchemaBuilder {
	sb.schema.Type = "boolean"
	return sb
}

// Description 设置描述
func (sb *SchemaBuilder) Description(desc string) *SchemaBuilder {
	sb.schema.Description = desc
	return sb
}

// Required 设置必需字段
func (sb *SchemaBuilder) Required(fields ...string) *SchemaBuilder {
	sb.schema.Required = append(sb.schema.Required, fields...)
	return sb
}

// Property 添加对象属性
func (sb *SchemaBuilder) Property(name string, schema *JSONSchema) *SchemaBuilder {
	if sb.schema.Properties == nil {
		sb.schema.Properties = make(map[string]*JSONSchema)
	}
	sb.schema.Properties[name] = schema
	return sb
}

// Items 设置数组项类型
func (sb *SchemaBuilder) Items(schema *JSONSchema) *SchemaBuilder {
	sb.schema.Items = schema
	return sb
}

// Enum 设置枚举值
func (sb *SchemaBuilder) Enum(values ...string) *SchemaBuilder {
	sb.schema.Enum = values
	return sb
}

// MinLength 设置字符串最小长度
func (sb *SchemaBuilder) MinLength(length int) *SchemaBuilder {
	sb.schema.MinLength = &length
	return sb
}

// MaxLength 设置字符串最大长度
func (sb *SchemaBuilder) MaxLength(length int) *SchemaBuilder {
	sb.schema.MaxLength = &length
	return sb
}

// Minimum 设置数字最小值
func (sb *SchemaBuilder) Minimum(min float64) *SchemaBuilder {
	sb.schema.Minimum = &min
	return sb
}

// Maximum 设置数字最大值
func (sb *SchemaBuilder) Maximum(max float64) *SchemaBuilder {
	sb.schema.Maximum = &max
	return sb
}

// MinItems 设置数组最小项数
func (sb *SchemaBuilder) MinItems(count int) *SchemaBuilder {
	sb.schema.MinItems = &count
	return sb
}

// MaxItems 设置数组最大项数
func (sb *SchemaBuilder) MaxItems(count int) *SchemaBuilder {
	sb.schema.MaxItems = &count
	return sb
}

// Pattern 设置字符串模式
func (sb *SchemaBuilder) Pattern(pattern string) *SchemaBuilder {
	sb.schema.Pattern = pattern
	return sb
}

// Format 设置格式
func (sb *SchemaBuilder) Format(format string) *SchemaBuilder {
	sb.schema.Format = format
	return sb
}

// Build 构建最终的Schema
func (sb *SchemaBuilder) Build() *JSONSchema {
	return sb.schema
}

// ToMap 转换为map[string]interface{}格式（用于Windmill API）
func (schema *JSONSchema) ToMap() map[string]interface{} {
	result := make(map[string]interface{})
	
	if schema.Type != "" {
		result["type"] = schema.Type
	}
	
	if schema.Description != "" {
		result["description"] = schema.Description
	}
	
	if schema.Properties != nil {
		props := make(map[string]interface{})
		for key, prop := range schema.Properties {
			props[key] = prop.ToMap()
		}
		result["properties"] = props
	}
	
	if len(schema.Required) > 0 {
		result["required"] = schema.Required
	}
	
	if schema.Items != nil {
		result["items"] = schema.Items.ToMap()
	}
	
	if len(schema.Enum) > 0 {
		result["enum"] = schema.Enum
	}
	
	if schema.Format != "" {
		result["format"] = schema.Format
	}
	
	if schema.Minimum != nil {
		result["minimum"] = *schema.Minimum
	}
	
	if schema.Maximum != nil {
		result["maximum"] = *schema.Maximum
	}
	
	if schema.MinLength != nil {
		result["minLength"] = *schema.MinLength
	}
	
	if schema.MaxLength != nil {
		result["maxLength"] = *schema.MaxLength
	}
	
	if schema.MinItems != nil {
		result["minItems"] = *schema.MinItems
	}
	
	if schema.MaxItems != nil {
		result["maxItems"] = *schema.MaxItems
	}
	
	if schema.Pattern != "" {
		result["pattern"] = schema.Pattern
	}
	
	if len(schema.PropertyOrdering) > 0 {
		result["propertyOrdering"] = schema.PropertyOrdering
	}
	
	return result
}

// 辅助函数：创建常用的基础Schema

// StringSchema 创建字符串Schema
func StringSchema(description string) *JSONSchema {
	return NewSchemaBuilder().String().Description(description).Build()
}

// IntegerSchema 创建整数Schema
func IntegerSchema(description string, min, max *float64) *JSONSchema {
	builder := NewSchemaBuilder().Integer().Description(description)
	if min != nil {
		builder.Minimum(*min)
	}
	if max != nil {
		builder.Maximum(*max)
	}
	return builder.Build()
}

// ArraySchema 创建数组Schema
func ArraySchema(description string, itemSchema *JSONSchema) *JSONSchema {
	return NewSchemaBuilder().Array().Description(description).Items(itemSchema).Build()
}

// StringArraySchema 创建字符串数组Schema
func StringArraySchema(description string) *JSONSchema {
	return ArraySchema(description, StringSchema("数组项"))
}

// EnumSchema 创建枚举Schema
func EnumSchema(description string, values ...string) *JSONSchema {
	return NewSchemaBuilder().String().Description(description).Enum(values...).Build()
}
