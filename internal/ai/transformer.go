package ai

import (
	"fmt"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"
)

/**
 * 数据转换层
 * @description 将AI生成的结构化数据转换为项目的数据模型
 */

// DataTransformer 数据转换器
type DataTransformer struct {
	logger logger.Logger
}

// NewDataTransformer 创建数据转换器
func NewDataTransformer(log logger.Logger) *DataTransformer {
	return &DataTransformer{
		logger: log,
	}
}

// TransformToScene 将AI生成的数据转换为Scene模型
func (dt *DataTransformer) TransformToScene(data map[string]interface{}, worldID string) (*models.Scene, error) {
	scene := &models.Scene{
		WorldID:   worldID,
		SceneType: "normal",
		Status:    "active",
	}
	
	// 基本信息
	if name, ok := data["name"].(string); ok {
		scene.Name = name
	} else {
		return nil, fmt.Errorf("缺少场景名称")
	}
	
	if description, ok := data["description"].(string); ok {
		scene.Description = &description
	}
	
	// 构建属性
	properties := make(map[string]interface{})
	
	if atmosphere, ok := data["atmosphere"].(string); ok {
		properties["atmosphere"] = atmosphere
	}
	
	if sceneType, ok := data["scene_type"].(string); ok {
		properties["scene_type"] = sceneType
		// 同时设置模型的SceneType字段
		scene.SceneType = dt.mapSceneType(sceneType)
	}
	
	if keyFeatures, ok := data["key_features"].([]interface{}); ok {
		features := make([]string, 0, len(keyFeatures))
		for _, feature := range keyFeatures {
			if featureStr, ok := feature.(string); ok {
				features = append(features, featureStr)
			}
		}
		properties["key_features"] = features
	}
	
	if possibleActions, ok := data["possible_actions"].([]interface{}); ok {
		actions := make([]string, 0, len(possibleActions))
		for _, action := range possibleActions {
			if actionStr, ok := action.(string); ok {
				actions = append(actions, actionStr)
			}
		}
		properties["possible_actions"] = actions
	}
	
	if dangerLevel, ok := data["danger_level"]; ok {
		properties["danger_level"] = dangerLevel
	}
	
	if lighting, ok := data["lighting"].(string); ok {
		properties["lighting"] = lighting
	}
	
	if sounds, ok := data["sounds"].([]interface{}); ok {
		soundList := make([]string, 0, len(sounds))
		for _, sound := range sounds {
			if soundStr, ok := sound.(string); ok {
				soundList = append(soundList, soundStr)
			}
		}
		properties["sounds"] = soundList
	}
	
	if smells, ok := data["smells"].([]interface{}); ok {
		smellList := make([]string, 0, len(smells))
		for _, smell := range smells {
			if smellStr, ok := smell.(string); ok {
				smellList = append(smellList, smellStr)
			}
		}
		properties["smells"] = smellList
	}
	
	if hiddenElements, ok := data["hidden_elements"].([]interface{}); ok {
		elements := make([]string, 0, len(hiddenElements))
		for _, element := range hiddenElements {
			if elementStr, ok := element.(string); ok {
				elements = append(elements, elementStr)
			}
		}
		properties["hidden_elements"] = elements
	}
	
	scene.Properties = models.JSON(properties)
	
	// 处理连接信息
	if connections, ok := data["connections"].([]interface{}); ok {
		connectedScenes := make(map[string]interface{})
		for _, conn := range connections {
			if connMap, ok := conn.(map[string]interface{}); ok {
				if direction, ok := connMap["direction"].(string); ok {
					connectionInfo := map[string]interface{}{
						"description": connMap["description"],
						"scene_name":  connMap["scene_name"],
					}
					if isLocked, ok := connMap["is_locked"].(bool); ok {
						connectionInfo["is_locked"] = isLocked
					}
					if unlockCondition, ok := connMap["unlock_condition"].(string); ok && unlockCondition != "" {
						connectionInfo["unlock_condition"] = unlockCondition
					}
					connectedScenes[direction] = connectionInfo
				}
			}
		}
		scene.ConnectedScenes = models.JSON(connectedScenes)
	}
	
	dt.logger.Info("场景数据转换完成", "scene_name", scene.Name, "world_id", worldID)
	return scene, nil
}

// TransformToCharacter 将AI生成的数据转换为Character模型
func (dt *DataTransformer) TransformToCharacter(data map[string]interface{}, worldID string, userID *string) (*models.Character, error) {
	character := &models.Character{
		WorldID:       worldID,
		UserID:        userID,
		CharacterType: "npc", // 默认为NPC
		Status:        "active",
	}
	
	// 基本信息
	if name, ok := data["name"].(string); ok {
		character.Name = name
	} else {
		return nil, fmt.Errorf("缺少角色名称")
	}
	
	if description, ok := data["description"].(string); ok {
		character.Description = &description
	}
	
	if characterType, ok := data["character_type"].(string); ok {
		character.CharacterType = dt.mapCharacterType(characterType)
	}
	
	// 处理特质
	if personality, ok := data["personality"].([]interface{}); ok {
		traits := make([]string, 0, len(personality))
		for _, trait := range personality {
			if traitStr, ok := trait.(string); ok {
				traits = append(traits, traitStr)
			}
		}
		character.Traits = models.StringArray(traits)
	}
	
	// 构建属性
	attributes := make(map[string]interface{})
	
	if background, ok := data["background"].(string); ok {
		attributes["background"] = background
	}
	
	if skills, ok := data["skills"].([]interface{}); ok {
		skillList := make([]string, 0, len(skills))
		for _, skill := range skills {
			if skillStr, ok := skill.(string); ok {
				skillList = append(skillList, skillStr)
			}
		}
		attributes["skills"] = skillList
	}
	
	if dialogueStyle, ok := data["dialogue_style"].(string); ok {
		attributes["dialogue_style"] = dialogueStyle
	}
	
	if motivations, ok := data["motivations"].([]interface{}); ok {
		motivationList := make([]string, 0, len(motivations))
		for _, motivation := range motivations {
			if motivationStr, ok := motivation.(string); ok {
				motivationList = append(motivationList, motivationStr)
			}
		}
		attributes["motivations"] = motivationList
	}
	
	if fears, ok := data["fears"].([]interface{}); ok {
		fearList := make([]string, 0, len(fears))
		for _, fear := range fears {
			if fearStr, ok := fear.(string); ok {
				fearList = append(fearList, fearStr)
			}
		}
		attributes["fears"] = fearList
	}
	
	if goals, ok := data["goals"].([]interface{}); ok {
		goalList := make([]string, 0, len(goals))
		for _, goal := range goals {
			if goalStr, ok := goal.(string); ok {
				goalList = append(goalList, goalStr)
			}
		}
		attributes["goals"] = goalList
	}
	
	if relationships, ok := data["relationships"].([]interface{}); ok {
		relationshipList := make([]string, 0, len(relationships))
		for _, relationship := range relationships {
			if relationshipStr, ok := relationship.(string); ok {
				relationshipList = append(relationshipList, relationshipStr)
			}
		}
		attributes["relationships"] = relationshipList
	}
	
	// 处理角色属性数值
	if attributesData, ok := data["attributes"].(map[string]interface{}); ok {
		for key, value := range attributesData {
			attributes[key] = value
		}
	}
	
	// 处理外观信息
	if appearanceData, ok := data["appearance"].(map[string]interface{}); ok {
		attributes["appearance"] = appearanceData
	}
	
	if ageRange, ok := data["age_range"].(string); ok {
		attributes["age_range"] = ageRange
	}
	
	if occupation, ok := data["occupation"].(string); ok {
		attributes["occupation"] = occupation
	}
	
	if alignment, ok := data["alignment"].(string); ok {
		attributes["alignment"] = alignment
	}
	
	character.Experiences = models.JSON(attributes)
	
	dt.logger.Info("角色数据转换完成", "character_name", character.Name, "world_id", worldID)
	return character, nil
}

// TransformToEvent 将AI生成的数据转换为Event模型
func (dt *DataTransformer) TransformToEvent(data map[string]interface{}, worldID string, creatorID *string) (*models.Event, error) {
	event := &models.Event{
		WorldID:   worldID,
		CreatorID: creatorID,
		Status:    "pending",
	}
	
	// 基本信息
	if name, ok := data["name"].(string); ok {
		event.Name = &name
	} else {
		return nil, fmt.Errorf("缺少事件名称")
	}
	
	if description, ok := data["description"].(string); ok {
		event.Description = &description
	}
	
	if eventType, ok := data["event_type"].(string); ok {
		event.EventType = dt.mapEventType(eventType)
	} else {
		event.EventType = "random"
	}
	
	if priority, ok := data["priority"]; ok {
		if priorityFloat, ok := priority.(float64); ok {
			event.Priority = int(priorityFloat)
		} else if priorityInt, ok := priority.(int); ok {
			event.Priority = priorityInt
		}
	}
	
	// 处理持续时间
	if duration, ok := data["duration"]; ok {
		if durationFloat, ok := duration.(float64); ok {
			scheduledTime := time.Now().Add(time.Duration(durationFloat) * time.Minute)
			event.ScheduledAt = &scheduledTime
		}
	}
	
	// 构建事件数据
	eventData := make(map[string]interface{})
	
	// 复制所有原始数据
	for key, value := range data {
		eventData[key] = value
	}
	
	event.EventData = models.JSON(eventData)
	
	dt.logger.Info("事件数据转换完成", "event_name", *event.Name, "world_id", worldID)
	return event, nil
}

// 辅助方法

// mapSceneType 映射场景类型
func (dt *DataTransformer) mapSceneType(sceneType string) string {
	typeMap := map[string]string{
		"室内":  "indoor",
		"室外":  "outdoor",
		"地下":  "underground",
		"城镇":  "town",
		"荒野":  "wilderness",
		"森林":  "forest",
		"山脉":  "mountain",
		"海洋":  "ocean",
		"天空":  "sky",
		"特殊":  "special",
	}
	
	if mapped, ok := typeMap[sceneType]; ok {
		return mapped
	}
	return "normal"
}

// mapCharacterType 映射角色类型
func (dt *DataTransformer) mapCharacterType(characterType string) string {
	typeMap := map[string]string{
		"玩家": "player",
		"NPC": "npc",
		"敌人": "enemy",
		"商人": "merchant",
		"导师": "mentor",
		"守卫": "guard",
		"平民": "civilian",
	}
	
	if mapped, ok := typeMap[characterType]; ok {
		return mapped
	}
	return "npc"
}

// mapEventType 映射事件类型
func (dt *DataTransformer) mapEventType(eventType string) string {
	typeMap := map[string]string{
		"随机遭遇": "encounter",
		"剧情事件": "plot",
		"角色互动": "character",
		"环境变化": "environment",
		"战斗":   "combat",
		"探索":   "exploration",
		"谜题":   "puzzle",
		"交易":   "trade",
		"任务":   "quest",
	}
	
	if mapped, ok := typeMap[eventType]; ok {
		return mapped
	}
	return "random"
}
