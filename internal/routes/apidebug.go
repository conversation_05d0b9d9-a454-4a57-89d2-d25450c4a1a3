package routes

import (
	"context"
	"fmt"

	"ai-text-game-iam-npc/internal/apidebug"
	"ai-text-game-iam-npc/internal/handlers"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
)

// RegisterAPIDebugRoutes 注册API调试相关路由
func RegisterAPIDebugRoutes(r *gin.Engine, logger logger.Logger) error {
	// 创建API调试系统服务
	config := apidebug.DefaultConfig()
	apiDebugService, err := apidebug.NewService(config, logger)
	if err != nil {
		return err
	}
	
	// 初始化服务
	ctx := context.Background()
	if err := apiDebugService.Initialize(ctx); err != nil {
		logger.Error("初始化API调试服务失败", "error", err)
		// 不返回错误，允许系统继续运行
	}
	
	// 创建处理器
	apiDocHandler := handlers.NewAPIDocHandler(apiDebugService.GetAPIDocService(), logger)
	debugHandler := handlers.NewDebugHandler(apiDebugService.GetDebugService(), logger)
	systemHandler := handlers.NewAPIDebugHandler(apiDebugService, logger)
	
	// API文档路由组
	docsGroup := r.Group("/api/v1/docs")
	{
		// OpenAPI规范
		docsGroup.GET("/openapi", apiDocHandler.GetOpenAPISpec)
		
		// API端点列表
		docsGroup.GET("/endpoints", apiDocHandler.GetEndpoints)
		
		// 文档统计
		docsGroup.GET("/stats", apiDocHandler.GetDocumentationStats)
		
		// 刷新文档
		docsGroup.POST("/refresh", apiDocHandler.RefreshDocumentation)
		
		// Swagger UI
		docsGroup.GET("/swagger", apiDocHandler.GetSwaggerUI)
		
		// API文档主页
		docsGroup.GET("", apiDocHandler.GetAPIDocumentation)
	}
	
	// API调试路由组
	debugGroup := r.Group("/api/v1/debug")
	{
		// 发送调试请求
		debugGroup.POST("/request", debugHandler.SendRequest)
		
		// 获取请求历史
		debugGroup.GET("/history", debugHandler.GetHistory)
		
		// 根据ID获取历史记录
		debugGroup.GET("/history/:id", debugHandler.GetHistoryByID)
		
		// 清空历史记录
		debugGroup.DELETE("/history", debugHandler.ClearHistory)
		
		// 获取端点模板
		debugGroup.GET("/template", debugHandler.GetEndpointTemplate)
	}
	
	// 系统状态路由组
	systemGroup := r.Group("/api/v1/system")
	{
		// 系统状态
		systemGroup.GET("/status", systemHandler.GetSystemStatus)
		
		// 系统配置
		systemGroup.GET("/config", systemHandler.GetSystemConfig)
	}
	
	// 调试界面路由
	r.GET("/debug", debugHandler.GetDebugUI)
	
	// 系统信息页面路由
	r.GET("/system", systemHandler.GetSystemInfo)
	
	logger.Info("API调试路由注册完成")
	return nil
}

// SetupAPIDebugMiddleware 设置API调试中间件
func SetupAPIDebugMiddleware(r *gin.Engine, logger logger.Logger) {
	// 添加CORS中间件以支持跨域调试
	r.Use(func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		if origin != "" {
			c.Header("Access-Control-Allow-Origin", origin)
			c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, PATCH, OPTIONS")
			c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-API-Key")
			c.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
			c.Header("Access-Control-Allow-Credentials", "true")
		}
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})
	
	// 添加请求日志中间件
	r.Use(gin.Logger())
}

// CreateStandaloneAPIDebugServer 创建独立的API调试服务器
func CreateStandaloneAPIDebugServer(logger logger.Logger) (*gin.Engine, error) {
	// 创建Gin引擎
	r := gin.New()
	
	// 设置中间件
	SetupAPIDebugMiddleware(r, logger)
	
	// 注册路由
	if err := RegisterAPIDebugRoutes(r, logger); err != nil {
		return nil, err
	}
	
	// 添加根路径重定向
	r.GET("/", func(c *gin.Context) {
		c.Redirect(302, "/system")
	})
	
	// 添加健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"service": "api-debug-system",
			"version": "1.0.0",
		})
	})
	
	logger.Info("独立API调试服务器创建完成")
	return r, nil
}

// APIDebugConfig API调试配置
type APIDebugConfig struct {
	Enabled         bool   `json:"enabled"`          // 是否启用
	Port            string `json:"port"`             // 端口
	ProjectRoot     string `json:"project_root"`     // 项目根目录
	BaseURL         string `json:"base_url"`         // 基础URL
	OutputDir       string `json:"output_dir"`       // 输出目录
	CacheEnabled    bool   `json:"cache_enabled"`    // 是否启用缓存
	AutoRefresh     bool   `json:"auto_refresh"`     // 是否自动刷新
	MaxHistorySize  int    `json:"max_history_size"` // 最大历史记录数
}

// DefaultAPIDebugConfig 默认API调试配置
func DefaultAPIDebugConfig() APIDebugConfig {
	return APIDebugConfig{
		Enabled:        true,
		Port:           "8081",
		ProjectRoot:    ".",
		BaseURL:        "http://localhost:8080",
		OutputDir:      "docs/api",
		CacheEnabled:   true,
		AutoRefresh:    false,
		MaxHistorySize: 1000,
	}
}

// ValidateAPIDebugConfig 验证API调试配置
func ValidateAPIDebugConfig(config APIDebugConfig) error {
	if !config.Enabled {
		return nil
	}
	
	if config.Port == "" {
		return fmt.Errorf("端口不能为空")
	}
	
	if config.ProjectRoot == "" {
		return fmt.Errorf("项目根目录不能为空")
	}
	
	if config.BaseURL == "" {
		return fmt.Errorf("基础URL不能为空")
	}
	
	if config.MaxHistorySize <= 0 {
		return fmt.Errorf("最大历史记录数必须大于0")
	}
	
	return nil
}

// StartAPIDebugServer 启动API调试服务器
func StartAPIDebugServer(config APIDebugConfig, logger logger.Logger) error {
	if !config.Enabled {
		logger.Info("API调试服务器已禁用")
		return nil
	}
	
	if err := ValidateAPIDebugConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}
	
	server, err := CreateStandaloneAPIDebugServer(logger)
	if err != nil {
		return fmt.Errorf("创建API调试服务器失败: %w", err)
	}
	
	logger.Info("启动API调试服务器", "port", config.Port)
	
	// 在单独的goroutine中启动服务器
	go func() {
		if err := server.Run(":" + config.Port); err != nil {
			logger.Error("API调试服务器启动失败", "error", err)
		}
	}()
	
	return nil
}
