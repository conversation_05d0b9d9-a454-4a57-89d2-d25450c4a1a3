package game

import (
	"context"
	"fmt"
	"sync"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"
	"gorm.io/gorm"
)

// DynamicTimeManager 动态时间速率管理器
// 负责管理所有世界的时间速率，支持基于现实时间段的动态调整
type DynamicTimeManager struct {
	db     *gorm.DB
	logger logger.Logger
	
	// 世界时间状态缓存
	worldStates map[string]*WorldTimeState
	mutex       sync.RWMutex
	
	// 管理器状态
	isRunning   bool
	stopChan    chan struct{}
	ticker      *time.Ticker
	
	// 配置
	checkInterval    time.Duration // 检查间隔
	batchUpdateSize  int           // 批量更新大小
	maxRetries       int           // 最大重试次数
}

// WorldTimeState 世界时间状态
type WorldTimeState struct {
	WorldID         string    `json:"world_id"`
	CurrentRate     float64   `json:"current_rate"`      // 当前时间速率
	LastRate        float64   `json:"last_rate"`         // 上次时间速率
	LastUpdate      time.Time `json:"last_update"`       // 上次更新时间
	LastTick        time.Time `json:"last_tick"`         // 上次心跳时间
	GameTime        int64     `json:"game_time"`         // 游戏时间（分钟）
	TickCount       int64     `json:"tick_count"`        // 心跳计数
	IsActive        bool      `json:"is_active"`         // 是否活跃
	IsPaused        bool      `json:"is_paused"`         // 是否暂停
	PlayerCount     int       `json:"player_count"`      // 玩家数量
	ScheduleEnabled bool      `json:"schedule_enabled"`  // 是否启用时间段配置
	ActiveSchedule  string    `json:"active_schedule"`   // 当前生效的时间段名称
	ConfigVersion   int64     `json:"config_version"`    // 配置版本号
}

// TimeUpdateBatch 批量时间更新结构
type TimeUpdateBatch struct {
	Updates   []WorldTimeUpdate `json:"updates"`
	Timestamp time.Time         `json:"timestamp"`
}

// WorldTimeUpdate 世界时间更新
type WorldTimeUpdate struct {
	WorldID   string  `json:"world_id"`
	GameTime  int64   `json:"game_time"`
	TickCount int64   `json:"tick_count"`
	TimeRate  float64 `json:"time_rate"`
}

// NewDynamicTimeManager 创建动态时间管理器
func NewDynamicTimeManager(db *gorm.DB, logger logger.Logger) *DynamicTimeManager {
	return &DynamicTimeManager{
		db:              db,
		logger:          logger,
		worldStates:     make(map[string]*WorldTimeState),
		checkInterval:   30 * time.Second, // 默认30秒检查一次
		batchUpdateSize: 50,               // 默认批量更新50个世界
		maxRetries:      3,                // 默认最大重试3次
		stopChan:        make(chan struct{}),
	}
}

// Start 启动动态时间管理器
func (dtm *DynamicTimeManager) Start(ctx context.Context) error {
	dtm.mutex.Lock()
	defer dtm.mutex.Unlock()
	
	if dtm.isRunning {
		return fmt.Errorf("动态时间管理器已在运行")
	}
	
	dtm.logger.Info("启动动态时间管理器", "check_interval", dtm.checkInterval)
	
	// 初始化世界状态
	if err := dtm.initializeWorldStates(ctx); err != nil {
		return fmt.Errorf("初始化世界状态失败: %w", err)
	}
	
	// 启动定时器
	dtm.ticker = time.NewTicker(dtm.checkInterval)
	dtm.isRunning = true
	
	// 启动管理协程
	go dtm.manageTimeRates(ctx)
	
	dtm.logger.Info("动态时间管理器启动成功", "world_count", len(dtm.worldStates))
	return nil
}

// Stop 停止动态时间管理器
func (dtm *DynamicTimeManager) Stop() error {
	dtm.mutex.Lock()
	defer dtm.mutex.Unlock()
	
	if !dtm.isRunning {
		return nil
	}
	
	dtm.logger.Info("停止动态时间管理器")
	
	// 停止定时器
	if dtm.ticker != nil {
		dtm.ticker.Stop()
	}
	
	// 发送停止信号
	close(dtm.stopChan)
	dtm.isRunning = false
	
	// 执行最后一次批量更新
	if err := dtm.batchUpdateDatabase(context.Background()); err != nil {
		dtm.logger.Error("最终批量更新失败", "error", err)
	}
	
	dtm.logger.Info("动态时间管理器已停止")
	return nil
}

// initializeWorldStates 初始化世界状态
func (dtm *DynamicTimeManager) initializeWorldStates(ctx context.Context) error {
	var worlds []models.World
	
	// 查询所有活跃的世界
	if err := dtm.db.WithContext(ctx).
		Where("status = ? AND deleted_at IS NULL", "active").
		Find(&worlds).Error; err != nil {
		return fmt.Errorf("查询活跃世界失败: %w", err)
	}
	
	dtm.logger.Info("初始化世界状态", "world_count", len(worlds))
	
	// 为每个世界创建时间状态
	for _, world := range worlds {
		state := &WorldTimeState{
			WorldID:         world.ID,
			CurrentRate:     world.GetCurrentDynamicTimeRate(),
			LastRate:        world.GetCurrentDynamicTimeRate(),
			LastUpdate:      time.Now(),
			LastTick:        time.Now(),
			GameTime:        world.GameTime,
			TickCount:       world.GetCurrentTick(),
			IsActive:        world.IsActive(),
			IsPaused:        false,
			PlayerCount:     world.CurrentPlayers,
			ScheduleEnabled: world.IsTimeScheduleEnabled(),
			ConfigVersion:   0, // 将在下次更新时设置
		}
		
		// 获取当前生效的时间段
		if activeSchedule, err := world.GetActiveTimeSchedule(); err == nil && activeSchedule != nil {
			state.ActiveSchedule = activeSchedule.Name
		}
		
		dtm.worldStates[world.ID] = state
	}
	
	return nil
}

// manageTimeRates 管理时间速率的主循环
func (dtm *DynamicTimeManager) manageTimeRates(ctx context.Context) {
	defer func() {
		if r := recover(); r != nil {
			dtm.logger.Error("时间管理器发生panic", "error", r)
		}
	}()
	
	for {
		select {
		case <-ctx.Done():
			dtm.logger.Info("收到上下文取消信号，停止时间管理器")
			return
		case <-dtm.stopChan:
			dtm.logger.Info("收到停止信号，停止时间管理器")
			return
		case <-dtm.ticker.C:
			// 执行时间速率检查和更新
			if err := dtm.updateTimeRates(ctx); err != nil {
				dtm.logger.Error("更新时间速率失败", "error", err)
			}
			
			// 执行批量数据库更新
			if err := dtm.batchUpdateDatabase(ctx); err != nil {
				dtm.logger.Error("批量更新数据库失败", "error", err)
			}
		}
	}
}

// updateTimeRates 更新所有世界的时间速率
func (dtm *DynamicTimeManager) updateTimeRates(ctx context.Context) error {
	dtm.mutex.Lock()
	defer dtm.mutex.Unlock()
	
	now := time.Now()
	updatedCount := 0
	rateChangedCount := 0
	
	for worldID, state := range dtm.worldStates {
		// 检查世界是否需要更新
		if !state.IsActive || state.IsPaused {
			continue
		}
		
		// 获取世界的最新配置
		var world models.World
		if err := dtm.db.WithContext(ctx).First(&world, worldID).Error; err != nil {
			dtm.logger.Error("获取世界信息失败", "world_id", worldID, "error", err)
			continue
		}
		
		// 获取当前应该使用的时间速率
		newRate := world.GetCurrentDynamicTimeRate()
		
		// 检查速率是否发生变化
		if newRate != state.CurrentRate {
			dtm.logger.Info("世界时间速率变化",
				"world_id", worldID,
				"old_rate", state.CurrentRate,
				"new_rate", newRate)
			
			state.LastRate = state.CurrentRate
			state.CurrentRate = newRate
			rateChangedCount++
		}
		
		// 更新时间状态
		dtm.updateWorldTimeState(state, &world, now)
		updatedCount++
	}
	
	if updatedCount > 0 {
		dtm.logger.Debug("时间速率更新完成",
			"updated_count", updatedCount,
			"rate_changed_count", rateChangedCount)
	}
	
	return nil
}

// updateWorldTimeState 更新单个世界的时间状态
func (dtm *DynamicTimeManager) updateWorldTimeState(state *WorldTimeState, world *models.World, now time.Time) {
	// 计算时间增量
	timeDelta := now.Sub(state.LastTick)
	
	// 计算游戏时间增量（分钟）
	gameTimeIncrement := int64(timeDelta.Seconds() * state.CurrentRate / 60)
	
	// 更新状态
	state.GameTime += gameTimeIncrement
	state.TickCount++
	state.LastTick = now
	state.LastUpdate = now
	state.PlayerCount = world.CurrentPlayers
	state.ScheduleEnabled = world.IsTimeScheduleEnabled()
	
	// 更新当前生效的时间段
	if activeSchedule, err := world.GetActiveTimeSchedule(); err == nil && activeSchedule != nil {
		state.ActiveSchedule = activeSchedule.Name
	} else {
		state.ActiveSchedule = ""
	}
	
	// 检查是否需要暂停（无人时暂停）
	if world.ShouldPauseWhenEmpty() && state.PlayerCount == 0 {
		state.IsPaused = true
	} else {
		state.IsPaused = false
	}
}

// batchUpdateDatabase 批量更新数据库
func (dtm *DynamicTimeManager) batchUpdateDatabase(ctx context.Context) error {
	dtm.mutex.RLock()
	
	// 收集需要更新的世界
	updates := make([]WorldTimeUpdate, 0, len(dtm.worldStates))
	for _, state := range dtm.worldStates {
		if state.IsActive && !state.IsPaused {
			updates = append(updates, WorldTimeUpdate{
				WorldID:   state.WorldID,
				GameTime:  state.GameTime,
				TickCount: state.TickCount,
				TimeRate:  state.CurrentRate,
			})
		}
	}
	
	dtm.mutex.RUnlock()
	
	if len(updates) == 0 {
		return nil
	}
	
	// 分批更新数据库
	for i := 0; i < len(updates); i += dtm.batchUpdateSize {
		end := i + dtm.batchUpdateSize
		if end > len(updates) {
			end = len(updates)
		}
		
		batch := updates[i:end]
		if err := dtm.updateDatabaseBatch(ctx, batch); err != nil {
			dtm.logger.Error("批量更新数据库失败", "batch_size", len(batch), "error", err)
			return err
		}
	}
	
	dtm.logger.Debug("批量更新数据库完成", "total_updates", len(updates))
	return nil
}

// updateDatabaseBatch 更新数据库批次
func (dtm *DynamicTimeManager) updateDatabaseBatch(ctx context.Context, batch []WorldTimeUpdate) error {
	return dtm.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, update := range batch {
			// 更新世界的游戏时间和心跳计数
			if err := tx.Model(&models.World{}).
				Where("id = ?", update.WorldID).
				Updates(map[string]interface{}{
					"game_time": update.GameTime,
				}).Error; err != nil {
				return fmt.Errorf("更新世界 %s 失败: %w", update.WorldID, err)
			}
			
			// 更新世界状态中的心跳信息
			worldStateUpdates := map[string]interface{}{
				"current_tick": update.TickCount,
				"last_tick_at": time.Now(),
			}
			
			if err := tx.Model(&models.World{}).
				Where("id = ?", update.WorldID).
				Update("world_state", gorm.Expr("JSON_SET(world_state, ?, ?, ?, ?)",
					"$.current_tick", update.TickCount,
					"$.last_tick_at", time.Now())).Error; err != nil {
				// 如果JSON_SET不支持，使用传统方式
				var world models.World
				if err := tx.First(&world, update.WorldID).Error; err != nil {
					return fmt.Errorf("获取世界状态失败: %w", err)
				}
				
				if err := world.UpdateWorldState(tx, worldStateUpdates); err != nil {
					return fmt.Errorf("更新世界状态失败: %w", err)
				}
			}
		}
		return nil
	})
}

// GetWorldTimeState 获取世界时间状态
func (dtm *DynamicTimeManager) GetWorldTimeState(worldID string) (*WorldTimeState, error) {
	dtm.mutex.RLock()
	defer dtm.mutex.RUnlock()
	
	state, exists := dtm.worldStates[worldID]
	if !exists {
		return nil, fmt.Errorf("世界 %s 的时间状态不存在", worldID)
	}
	
	// 返回副本以避免外部修改
	stateCopy := *state
	return &stateCopy, nil
}

// AddWorld 添加世界到时间管理器
func (dtm *DynamicTimeManager) AddWorld(ctx context.Context, worldID string) error {
	dtm.mutex.Lock()
	defer dtm.mutex.Unlock()
	
	// 检查是否已存在
	if _, exists := dtm.worldStates[worldID]; exists {
		return nil // 已存在，无需添加
	}
	
	// 获取世界信息
	var world models.World
	if err := dtm.db.WithContext(ctx).First(&world, worldID).Error; err != nil {
		return fmt.Errorf("获取世界信息失败: %w", err)
	}
	
	// 创建时间状态
	state := &WorldTimeState{
		WorldID:         world.ID,
		CurrentRate:     world.GetCurrentDynamicTimeRate(),
		LastRate:        world.GetCurrentDynamicTimeRate(),
		LastUpdate:      time.Now(),
		LastTick:        time.Now(),
		GameTime:        world.GameTime,
		TickCount:       world.GetCurrentTick(),
		IsActive:        world.IsActive(),
		IsPaused:        false,
		PlayerCount:     world.CurrentPlayers,
		ScheduleEnabled: world.IsTimeScheduleEnabled(),
	}
	
	// 获取当前生效的时间段
	if activeSchedule, err := world.GetActiveTimeSchedule(); err == nil && activeSchedule != nil {
		state.ActiveSchedule = activeSchedule.Name
	}
	
	dtm.worldStates[worldID] = state
	dtm.logger.Info("添加世界到时间管理器", "world_id", worldID, "current_rate", state.CurrentRate)
	
	return nil
}

// RemoveWorld 从时间管理器中移除世界
func (dtm *DynamicTimeManager) RemoveWorld(worldID string) error {
	dtm.mutex.Lock()
	defer dtm.mutex.Unlock()
	
	if _, exists := dtm.worldStates[worldID]; !exists {
		return nil // 不存在，无需移除
	}
	
	delete(dtm.worldStates, worldID)
	dtm.logger.Info("从时间管理器移除世界", "world_id", worldID)
	
	return nil
}

// GetStats 获取时间管理器统计信息
func (dtm *DynamicTimeManager) GetStats() map[string]interface{} {
	dtm.mutex.RLock()
	defer dtm.mutex.RUnlock()
	
	activeWorlds := 0
	pausedWorlds := 0
	totalPlayers := 0
	scheduleEnabledWorlds := 0
	
	for _, state := range dtm.worldStates {
		if state.IsActive {
			activeWorlds++
		}
		if state.IsPaused {
			pausedWorlds++
		}
		if state.ScheduleEnabled {
			scheduleEnabledWorlds++
		}
		totalPlayers += state.PlayerCount
	}
	
	return map[string]interface{}{
		"total_worlds":             len(dtm.worldStates),
		"active_worlds":            activeWorlds,
		"paused_worlds":            pausedWorlds,
		"schedule_enabled_worlds":  scheduleEnabledWorlds,
		"total_players":            totalPlayers,
		"is_running":               dtm.isRunning,
		"check_interval_seconds":   dtm.checkInterval.Seconds(),
		"batch_update_size":        dtm.batchUpdateSize,
	}
}
