package game

import (
	"context"
	"fmt"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"
	"gorm.io/gorm"
)

// TimeConfigService 时间配置服务
// 负责管理世界的时间段配置，支持热更新和验证
type TimeConfigService struct {
	db     *gorm.DB
	logger logger.Logger
	
	// 时间管理器引用（用于通知配置变更）
	timeManager *DynamicTimeManager
}

// NewTimeConfigService 创建时间配置服务
func NewTimeConfigService(db *gorm.DB, logger logger.Logger) *TimeConfigService {
	return &TimeConfigService{
		db:     db,
		logger: logger,
	}
}

// SetTimeManager 设置时间管理器引用
func (tcs *TimeConfigService) SetTimeManager(timeManager *DynamicTimeManager) {
	tcs.timeManager = timeManager
}

// GetTimeScheduleConfig 获取世界的时间段配置
func (tcs *TimeConfigService) GetTimeScheduleConfig(ctx context.Context, worldID string) (*models.TimeScheduleConfig, error) {
	var world models.World
	if err := tcs.db.WithContext(ctx).First(&world, worldID).Error; err != nil {
		return nil, fmt.Errorf("获取世界信息失败: %w", err)
	}
	
	config, err := world.GetTimeScheduleConfig()
	if err != nil {
		return nil, fmt.Errorf("获取时间段配置失败: %w", err)
	}
	
	return config, nil
}

// UpdateTimeScheduleConfig 更新世界的时间段配置（支持热更新）
func (tcs *TimeConfigService) UpdateTimeScheduleConfig(ctx context.Context, worldID string, config *models.TimeScheduleConfig) error {
	// 验证配置
	if err := config.Validate(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}
	
	// 在事务中更新配置
	return tcs.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var world models.World
		if err := tx.First(&world, worldID).Error; err != nil {
			return fmt.Errorf("获取世界信息失败: %w", err)
		}
		
		// 记录配置变更日志
		oldConfig, _ := world.GetTimeScheduleConfig()
		tcs.logger.Info("更新世界时间段配置",
			"world_id", worldID,
			"old_enabled", oldConfig != nil && oldConfig.Enabled,
			"new_enabled", config.Enabled,
			"schedule_count", len(config.Schedules))
		
		// 更新配置
		if err := world.UpdateTimeScheduleConfig(config); err != nil {
			return fmt.Errorf("更新时间段配置失败: %w", err)
		}
		
		// 保存到数据库
		if err := tx.Model(&world).Update("time_config", world.TimeConfig).Error; err != nil {
			return fmt.Errorf("保存时间配置失败: %w", err)
		}
		
		// 通知时间管理器配置已更新
		if tcs.timeManager != nil {
			// 如果世界不在时间管理器中，添加它
			if _, err := tcs.timeManager.GetWorldTimeState(worldID); err != nil {
				if addErr := tcs.timeManager.AddWorld(ctx, worldID); addErr != nil {
					tcs.logger.Error("添加世界到时间管理器失败", "world_id", worldID, "error", addErr)
				}
			}
		}
		
		tcs.logger.Info("时间段配置更新成功", "world_id", worldID, "version", config.Version)
		return nil
	})
}

// EnableTimeSchedule 启用世界的时间段配置
func (tcs *TimeConfigService) EnableTimeSchedule(ctx context.Context, worldID string) error {
	config, err := tcs.GetTimeScheduleConfig(ctx, worldID)
	if err != nil {
		return err
	}
	
	if config.Enabled {
		return nil // 已经启用
	}
	
	config.Enabled = true
	return tcs.UpdateTimeScheduleConfig(ctx, worldID, config)
}

// DisableTimeSchedule 禁用世界的时间段配置
func (tcs *TimeConfigService) DisableTimeSchedule(ctx context.Context, worldID string) error {
	config, err := tcs.GetTimeScheduleConfig(ctx, worldID)
	if err != nil {
		return err
	}
	
	if !config.Enabled {
		return nil // 已经禁用
	}
	
	config.Enabled = false
	return tcs.UpdateTimeScheduleConfig(ctx, worldID, config)
}

// AddTimeSchedule 添加时间段到配置中
func (tcs *TimeConfigService) AddTimeSchedule(ctx context.Context, worldID string, schedule models.TimeSchedule) error {
	config, err := tcs.GetTimeScheduleConfig(ctx, worldID)
	if err != nil {
		return err
	}
	
	// 验证新时间段
	if err := schedule.Validate(); err != nil {
		return fmt.Errorf("时间段配置验证失败: %w", err)
	}
	
	// 检查名称是否重复
	for _, existing := range config.Schedules {
		if existing.Name == schedule.Name {
			return fmt.Errorf("时间段名称 '%s' 已存在", schedule.Name)
		}
	}
	
	// 添加时间段
	config.Schedules = append(config.Schedules, schedule)
	
	return tcs.UpdateTimeScheduleConfig(ctx, worldID, config)
}

// UpdateTimeSchedule 更新指定的时间段
func (tcs *TimeConfigService) UpdateTimeSchedule(ctx context.Context, worldID string, scheduleName string, updatedSchedule models.TimeSchedule) error {
	config, err := tcs.GetTimeScheduleConfig(ctx, worldID)
	if err != nil {
		return err
	}
	
	// 查找并更新时间段
	found := false
	for i, schedule := range config.Schedules {
		if schedule.Name == scheduleName {
			// 验证更新的时间段
			if err := updatedSchedule.Validate(); err != nil {
				return fmt.Errorf("时间段配置验证失败: %w", err)
			}
			
			config.Schedules[i] = updatedSchedule
			found = true
			break
		}
	}
	
	if !found {
		return fmt.Errorf("时间段 '%s' 不存在", scheduleName)
	}
	
	return tcs.UpdateTimeScheduleConfig(ctx, worldID, config)
}

// RemoveTimeSchedule 移除指定的时间段
func (tcs *TimeConfigService) RemoveTimeSchedule(ctx context.Context, worldID string, scheduleName string) error {
	config, err := tcs.GetTimeScheduleConfig(ctx, worldID)
	if err != nil {
		return err
	}
	
	// 查找并移除时间段
	found := false
	for i, schedule := range config.Schedules {
		if schedule.Name == scheduleName {
			config.Schedules = append(config.Schedules[:i], config.Schedules[i+1:]...)
			found = true
			break
		}
	}
	
	if !found {
		return fmt.Errorf("时间段 '%s' 不存在", scheduleName)
	}
	
	// 检查是否还有启用的时间段
	if config.Enabled {
		hasEnabledSchedule := false
		for _, schedule := range config.Schedules {
			if schedule.Enabled {
				hasEnabledSchedule = true
				break
			}
		}
		
		if !hasEnabledSchedule {
			tcs.logger.Warn("移除时间段后没有启用的时间段，建议禁用时间段配置", "world_id", worldID)
		}
	}
	
	return tcs.UpdateTimeScheduleConfig(ctx, worldID, config)
}

// GetCurrentTimeRate 获取世界当前的时间速率
func (tcs *TimeConfigService) GetCurrentTimeRate(ctx context.Context, worldID string) (float64, error) {
	var world models.World
	if err := tcs.db.WithContext(ctx).First(&world, worldID).Error; err != nil {
		return 0, fmt.Errorf("获取世界信息失败: %w", err)
	}
	
	return world.GetCurrentDynamicTimeRate(), nil
}

// GetActiveTimeSchedule 获取世界当前生效的时间段
func (tcs *TimeConfigService) GetActiveTimeSchedule(ctx context.Context, worldID string) (*models.TimeSchedule, error) {
	var world models.World
	if err := tcs.db.WithContext(ctx).First(&world, worldID).Error; err != nil {
		return nil, fmt.Errorf("获取世界信息失败: %w", err)
	}
	
	return world.GetActiveTimeSchedule()
}

// ValidateTimeScheduleConfig 验证时间段配置
func (tcs *TimeConfigService) ValidateTimeScheduleConfig(config *models.TimeScheduleConfig) error {
	return config.Validate()
}

// CreateDefaultTimeScheduleConfig 为世界创建默认的时间段配置
func (tcs *TimeConfigService) CreateDefaultTimeScheduleConfig(ctx context.Context, worldID string) error {
	// 检查是否已有配置
	existingConfig, err := tcs.GetTimeScheduleConfig(ctx, worldID)
	if err != nil {
		return err
	}
	
	// 如果已有自定义配置，不覆盖
	if len(existingConfig.Schedules) > 0 && existingConfig.Version > 1 {
		return fmt.Errorf("世界已有时间段配置，不能创建默认配置")
	}
	
	// 创建默认配置
	defaultConfig := models.CreateDefaultTimeScheduleConfig()
	
	return tcs.UpdateTimeScheduleConfig(ctx, worldID, defaultConfig)
}

// BatchUpdateTimeScheduleConfigs 批量更新多个世界的时间段配置
func (tcs *TimeConfigService) BatchUpdateTimeScheduleConfigs(ctx context.Context, updates map[string]*models.TimeScheduleConfig) error {
	if len(updates) == 0 {
		return nil
	}
	
	tcs.logger.Info("开始批量更新时间段配置", "world_count", len(updates))
	
	successCount := 0
	errorCount := 0
	
	for worldID, config := range updates {
		if err := tcs.UpdateTimeScheduleConfig(ctx, worldID, config); err != nil {
			tcs.logger.Error("批量更新时间段配置失败", "world_id", worldID, "error", err)
			errorCount++
		} else {
			successCount++
		}
	}
	
	tcs.logger.Info("批量更新时间段配置完成",
		"success_count", successCount,
		"error_count", errorCount)
	
	if errorCount > 0 {
		return fmt.Errorf("批量更新部分失败，成功: %d，失败: %d", successCount, errorCount)
	}
	
	return nil
}

// GetTimeScheduleStats 获取时间段配置统计信息
func (tcs *TimeConfigService) GetTimeScheduleStats(ctx context.Context) (map[string]interface{}, error) {
	var worlds []models.World
	if err := tcs.db.WithContext(ctx).
		Where("status = ? AND deleted_at IS NULL", "active").
		Find(&worlds).Error; err != nil {
		return nil, fmt.Errorf("查询世界失败: %w", err)
	}
	
	totalWorlds := len(worlds)
	enabledWorlds := 0
	totalSchedules := 0
	activeSchedules := 0
	
	for _, world := range worlds {
		config, err := world.GetTimeScheduleConfig()
		if err != nil {
			continue
		}
		
		if config.Enabled {
			enabledWorlds++
		}
		
		totalSchedules += len(config.Schedules)
		
		for _, schedule := range config.Schedules {
			if schedule.Enabled {
				activeSchedules++
			}
		}
	}
	
	return map[string]interface{}{
		"total_worlds":     totalWorlds,
		"enabled_worlds":   enabledWorlds,
		"total_schedules":  totalSchedules,
		"active_schedules": activeSchedules,
		"enabled_ratio":    float64(enabledWorlds) / float64(totalWorlds),
	}, nil
}

// ResetTimeScheduleConfig 重置世界的时间段配置为默认值
func (tcs *TimeConfigService) ResetTimeScheduleConfig(ctx context.Context, worldID string) error {
	defaultConfig := models.CreateDefaultTimeScheduleConfig()
	
	tcs.logger.Info("重置世界时间段配置为默认值", "world_id", worldID)
	
	return tcs.UpdateTimeScheduleConfig(ctx, worldID, defaultConfig)
}
