package game

import (
	"context"
	"fmt"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"gorm.io/gorm"
)

// StateService 游戏状态服务
type StateService struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewStateService 创建游戏状态服务
func NewStateService(db *gorm.DB, logger logger.Logger) *StateService {
	return &StateService{
		db:     db,
		logger: logger,
	}
}

// GameState 游戏状态结构
type GameState struct {
	WorldID       string                 `json:"world_id"`
	CurrentTime   time.Time              `json:"current_time"`
	GameTime      int64                  `json:"game_time"`      // 游戏内时间（分钟）
	Weather       string                 `json:"weather"`
	Season        string                 `json:"season"`
	GlobalEvents  []models.Event         `json:"global_events"`
	ActiveScenes  []models.Scene         `json:"active_scenes"`
	OnlineCharacters []models.Character  `json:"online_characters"`
	Variables     map[string]interface{} `json:"variables"`
}

// GetWorldState 获取世界状态
func (s *StateService) GetWorldState(ctx context.Context, worldID string) (*GameState, error) {
	var world models.World
	if err := s.db.WithContext(ctx).First(&world, worldID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("世界不存在")
		}
		return nil, fmt.Errorf("获取世界信息失败: %w", err)
	}

	// 获取活跃的全局事件
	var globalEvents []models.Event
	if err := s.db.WithContext(ctx).
		Where("world_id = ? AND status IN (?, ?)", worldID, "pending", "processing").
		Order("priority DESC, created_at ASC").
		Find(&globalEvents).Error; err != nil {
		s.logger.Error("获取全局事件失败", "error", err, "world_id", worldID)
	}

	// 获取活跃场景
	var activeScenes []models.Scene
	if err := s.db.WithContext(ctx).
		Where("world_id = ? AND status = ?", worldID, "active").
		Find(&activeScenes).Error; err != nil {
		s.logger.Error("获取活跃场景失败", "error", err, "world_id", worldID)
	}

	// 获取在线角色
	var onlineCharacters []models.Character
	if err := s.db.WithContext(ctx).
		Where("world_id = ? AND status = ? AND last_action_at > ?", 
			worldID, "active", time.Now().Add(-30*time.Minute)).
		Find(&onlineCharacters).Error; err != nil {
		s.logger.Error("获取在线角色失败", "error", err, "world_id", worldID)
	}

	// 获取天气和季节，如果不存在则使用默认值
	weather := "晴朗"
	if w := world.GetGlobalVariable("weather"); w != nil {
		weather = w.(string)
	}

	season := "春季"
	if s := world.GetGlobalVariable("season"); s != nil {
		season = s.(string)
	}

	// 构建游戏状态
	state := &GameState{
		WorldID:          worldID,
		CurrentTime:      time.Now(),
		GameTime:         world.GameTime,
		Weather:          weather,
		Season:           season,
		GlobalEvents:     globalEvents,
		ActiveScenes:     activeScenes,
		OnlineCharacters: onlineCharacters,
		Variables:        world.GetGlobalVariables(),
	}

	return state, nil
}

// UpdateWorldTime 更新世界时间
func (s *StateService) UpdateWorldTime(ctx context.Context, worldID string, minutes int64) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var world models.World
		if err := tx.First(&world, worldID).Error; err != nil {
			return fmt.Errorf("获取世界信息失败: %w", err)
		}

		// 更新游戏时间
		if err := world.UpdateGameTime(tx, minutes); err != nil {
			return fmt.Errorf("更新游戏时间失败: %w", err)
		}

		// 检查是否需要触发时间相关事件
		if err := s.checkTimeEvents(tx, &world, minutes); err != nil {
			s.logger.Error("检查时间事件失败", "error", err, "world_id", worldID)
		}

		s.logger.Info("世界时间更新成功", "world_id", worldID, "minutes", minutes, "new_time", world.GameTime)
		return nil
	})
}

// checkTimeEvents 检查时间相关事件
func (s *StateService) checkTimeEvents(tx *gorm.DB, world *models.World, minutes int64) error {
	// 检查是否需要改变天气
	if minutes >= 60 { // 每小时检查一次天气变化
		if err := s.maybeChangeWeather(tx, world); err != nil {
			return fmt.Errorf("天气变化检查失败: %w", err)
		}
	}

	// 检查是否需要改变季节
	if minutes >= 1440 { // 每天检查一次季节变化
		if err := s.maybeChangeSeason(tx, world); err != nil {
			return fmt.Errorf("季节变化检查失败: %w", err)
		}
	}

	return nil
}

// maybeChangeWeather 可能改变天气
func (s *StateService) maybeChangeWeather(tx *gorm.DB, world *models.World) error {
	// 简单的天气变化逻辑
	weathers := []string{"晴朗", "多云", "阴天", "小雨", "大雨", "雷雨"}

	currentWeather := "晴朗"
	if w := world.GetGlobalVariable("weather"); w != nil {
		currentWeather = w.(string)
	}
	
	// 30% 概率改变天气
	if time.Now().Unix()%10 < 3 {
		newWeatherIndex := (time.Now().Unix() + int64(len(currentWeather))) % int64(len(weathers))
		newWeather := weathers[newWeatherIndex]
		
		if newWeather != currentWeather {
			if err := world.SetGlobalVariable(tx, "weather", newWeather); err != nil {
				return fmt.Errorf("设置天气失败: %w", err)
			}
			s.logger.Info("天气变化", "world_id", world.ID, "from", currentWeather, "to", newWeather)
		}
	}

	return nil
}

// maybeChangeSeason 可能改变季节
func (s *StateService) maybeChangeSeason(tx *gorm.DB, world *models.World) error {
	seasons := []string{"春季", "夏季", "秋季", "冬季"}

	currentSeason := "春季"
	if s := world.GetGlobalVariable("season"); s != nil {
		currentSeason = s.(string)
	}
	
	// 根据游戏时间计算季节（假设90天一个季节）
	daysSinceStart := world.GameTime / 1440 // 分钟转天数
	seasonIndex := (daysSinceStart / 90) % 4
	newSeason := seasons[seasonIndex]
	
	if newSeason != currentSeason {
		if err := world.SetGlobalVariable(tx, "season", newSeason); err != nil {
			return fmt.Errorf("设置季节失败: %w", err)
		}
		s.logger.Info("季节变化", "world_id", world.ID, "from", currentSeason, "to", newSeason)
	}

	return nil
}

// ProcessWorldTick 处理世界时钟周期
func (s *StateService) ProcessWorldTick(ctx context.Context, worldID string) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 推进游戏时间（每次tick推进5分钟）
		if err := s.UpdateWorldTime(ctx, worldID, 5); err != nil {
			return fmt.Errorf("更新世界时间失败: %w", err)
		}

		// 处理NPC行为
		if err := s.processNPCBehaviors(tx, worldID); err != nil {
			s.logger.Error("处理NPC行为失败", "error", err, "world_id", worldID)
		}

		// 处理环境事件
		if err := s.processEnvironmentEvents(tx, worldID); err != nil {
			s.logger.Error("处理环境事件失败", "error", err, "world_id", worldID)
		}

		s.logger.Debug("世界时钟周期处理完成", "world_id", worldID)
		return nil
	})
}

// processNPCBehaviors 处理NPC行为
func (s *StateService) processNPCBehaviors(tx *gorm.DB, worldID string) error {
	// 获取所有NPC角色
	var npcs []models.Character
	if err := tx.Where("world_id = ? AND character_type = ? AND status = ?", 
		worldID, "npc", "active").Find(&npcs).Error; err != nil {
		return fmt.Errorf("获取NPC列表失败: %w", err)
	}

	for _, npc := range npcs {
		// 简单的NPC行为逻辑
		// 10% 概率执行随机行动
		if time.Now().Unix()%10 == 0 {
			if err := s.triggerNPCAction(tx, &npc); err != nil {
				s.logger.Error("触发NPC行动失败", "error", err, "npc_id", npc.ID)
			}
		}
	}

	return nil
}

// triggerNPCAction 触发NPC行动
func (s *StateService) triggerNPCAction(tx *gorm.DB, npc *models.Character) error {
	actions := []string{"wander", "rest", "interact", "think"}
	actionIndex := time.Now().Unix() % int64(len(actions))
	action := actions[actionIndex]

	// 创建NPC行动事件
	eventData := map[string]interface{}{
		"action_type":  action,
		"character_id": npc.ID,
		"auto_trigger": true,
	}

	event := &models.Event{
		WorldID:      npc.WorldID,
		EventType:    "character_action",
		Name:         &[]string{fmt.Sprintf("NPC自动行动: %s", action)}[0],
		Description:  &[]string{fmt.Sprintf("NPC %s 执行自动行动: %s", npc.Name, action)}[0],
		Priority:     1, // 低优先级
		Status:       "pending",
		Participants: models.StringArray{npc.ID},
		EventData:    models.JSON(eventData),
	}

	if err := tx.Create(event).Error; err != nil {
		return fmt.Errorf("创建NPC行动事件失败: %w", err)
	}

	s.logger.Debug("创建NPC自动行动事件", "npc_id", npc.ID, "action", action, "event_id", event.ID)
	return nil
}

// processEnvironmentEvents 处理环境事件
func (s *StateService) processEnvironmentEvents(tx *gorm.DB, worldID string) error {
	// 5% 概率触发随机环境事件
	if time.Now().Unix()%20 == 0 {
		if err := s.triggerRandomEnvironmentEvent(tx, worldID); err != nil {
			return fmt.Errorf("触发随机环境事件失败: %w", err)
		}
	}

	return nil
}

// triggerRandomEnvironmentEvent 触发随机环境事件
func (s *StateService) triggerRandomEnvironmentEvent(tx *gorm.DB, worldID string) error {
	events := []map[string]interface{}{
		{
			"name":        "微风吹过",
			"description": "一阵微风吹过，带来了花香",
			"type":        "atmosphere",
		},
		{
			"name":        "鸟儿歌唱",
			"description": "远处传来鸟儿悦耳的歌声",
			"type":        "sound",
		},
		{
			"name":        "云朵飘过",
			"description": "天空中飘过几朵白云",
			"type":        "visual",
		},
	}

	eventIndex := time.Now().Unix() % int64(len(events))
	eventInfo := events[eventIndex]

	event := &models.Event{
		WorldID:     worldID,
		EventType:   "world_event",
		Name:        &[]string{eventInfo["name"].(string)}[0],
		Description: &[]string{eventInfo["description"].(string)}[0],
		Priority:    1,
		Status:      "pending",
		EventData: models.JSON{
			"sub_type":    "environment",
			"event_type":  eventInfo["type"],
			"auto_trigger": true,
		},
	}

	if err := tx.Create(event).Error; err != nil {
		return fmt.Errorf("创建环境事件失败: %w", err)
	}

	s.logger.Debug("创建随机环境事件", "world_id", worldID, "event", eventInfo["name"], "event_id", event.ID)
	return nil
}
