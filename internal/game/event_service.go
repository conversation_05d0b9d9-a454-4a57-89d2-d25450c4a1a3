package game

import (
	"context"
	"fmt"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"gorm.io/gorm"
)

// EventService 事件处理服务
type EventService struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewEventService 创建事件处理服务
func NewEventService(db *gorm.DB, log logger.Logger) *EventService {
	return &EventService{
		db:     db,
		logger: log,
	}
}

// CreateEvent 创建事件
func (s *EventService) CreateEvent(ctx context.Context, worldID string, eventType, name, description string, priority int, participants []string, eventData map[string]interface{}) (*models.Event, error) {
	event := &models.Event{
		WorldID:      worldID,
		EventType:    eventType,
		Name:         &name,
		Description:  &description,
		Priority:     priority,
		Status:       "pending",
		Participants: models.StringArray(participants), // 修改为StringArray
		EventData:    models.JSON(eventData),
	}

	if err := s.db.WithContext(ctx).Create(event).Error; err != nil {
		s.logger.Error("创建事件失败", "error", err, "world_id", worldID, "name", name)
		return nil, fmt.Errorf("创建事件失败: %w", err)
	}

	s.logger.Info("成功创建事件", "event_id", event.ID, "world_id", worldID, "name", name, "type", eventType)
	return event, nil
}

// GetEvent 获取事件信息
func (s *EventService) GetEvent(ctx context.Context, eventID string) (*models.Event, error) {
	var event models.Event
	err := s.db.WithContext(ctx).
		Preload("World").
		First(&event, eventID).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("事件不存在")
		}
		s.logger.Error("获取事件信息失败", "error", err, "event_id", eventID)
		return nil, fmt.Errorf("获取事件信息失败: %w", err)
	}

	return &event, nil
}

// GetEventsByWorld 获取世界中的事件列表
func (s *EventService) GetEventsByWorld(ctx context.Context, worldID string, eventType, status string, limit, offset int) ([]models.Event, int64, error) {
	var events []models.Event
	var total int64

	query := s.db.WithContext(ctx).Where("world_id = ?", worldID)
	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	if err := query.Model(&models.Event{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取事件总数失败: %w", err)
	}

	// 获取事件列表
	err := query.Order("priority DESC, created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&events).Error

	if err != nil {
		s.logger.Error("获取世界事件列表失败", "error", err, "world_id", worldID)
		return nil, 0, fmt.Errorf("获取事件列表失败: %w", err)
	}

	return events, total, nil
}

// GetPendingEvents 获取待处理事件
func (s *EventService) GetPendingEvents(ctx context.Context, worldID string, limit int) ([]models.Event, error) {
	var events []models.Event
	err := s.db.WithContext(ctx).
		Where("world_id = ? AND status = ?", worldID, "pending").
		Order("priority DESC, created_at ASC").
		Limit(limit).
		Find(&events).Error

	if err != nil {
		s.logger.Error("获取待处理事件失败", "error", err, "world_id", worldID)
		return nil, fmt.Errorf("获取待处理事件失败: %w", err)
	}

	return events, nil
}

// ProcessEvent 处理事件
func (s *EventService) ProcessEvent(ctx context.Context, eventID string) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取事件信息
		var event models.Event
		if err := tx.First(&event, eventID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("事件不存在")
			}
			return fmt.Errorf("获取事件信息失败: %w", err)
		}

		// 检查事件状态
		if event.Status != "pending" {
			return fmt.Errorf("事件状态不是待处理")
		}

		// 开始处理事件
		if err := event.SetStatus(tx, "processing"); err != nil {
			return fmt.Errorf("设置事件处理状态失败: %w", err)
		}

		// 根据事件类型执行不同的处理逻辑
		if err := s.executeEventLogic(tx, &event); err != nil {
			// 处理失败，设置为失败状态
			event.SetError(tx, err.Error())
			return fmt.Errorf("执行事件逻辑失败: %w", err)
		}

		// 处理成功，设置为完成状态
		if err := event.SetStatus(tx, "completed"); err != nil {
			return fmt.Errorf("设置事件完成状态失败: %w", err)
		}

		s.logger.Info("成功处理事件", "event_id", eventID, "type", event.EventType, "name", event.Name)
		return nil
	})
}

// executeEventLogic 执行事件逻辑
func (s *EventService) executeEventLogic(tx *gorm.DB, event *models.Event) error {
	switch event.EventType {
	case "character_action":
		return s.processCharacterAction(tx, event)
	case "world_event":
		return s.processWorldEvent(tx, event)
	case "scene_event":
		return s.processSceneEvent(tx, event)
	case "system_event":
		return s.processSystemEvent(tx, event)
	default:
		s.logger.Warn("未知事件类型", "event_type", event.EventType, "event_id", event.ID)
		return nil // 未知事件类型不算错误，只是跳过处理
	}
}

// processCharacterAction 处理角色行动事件
func (s *EventService) processCharacterAction(tx *gorm.DB, event *models.Event) error {
	eventData := map[string]interface{}(event.EventData)

	// 获取行动类型
	actionType, ok := eventData["action_type"].(string)
	if !ok {
		return fmt.Errorf("缺少行动类型")
	}

	// 获取角色ID
	characterID, ok := eventData["character_id"].(string)
	if !ok {
		return fmt.Errorf("缺少角色ID")
	}

	// 获取角色信息
	var character models.Character
	if err := tx.First(&character, characterID).Error; err != nil {
		return fmt.Errorf("获取角色信息失败: %w", err)
	}

	switch actionType {
	case "move":
		return s.processCharacterMove(tx, &character, eventData)
	case "interact":
		return s.processCharacterInteract(tx, &character, eventData)
	case "speak":
		return s.processCharacterSpeak(tx, &character, eventData)
	default:
		s.logger.Warn("未知角色行动类型", "action_type", actionType, "character_id", characterID)
		return nil
	}
}

// processCharacterMove 处理角色移动
func (s *EventService) processCharacterMove(tx *gorm.DB, character *models.Character, eventData map[string]interface{}) error {
	targetSceneID, ok := eventData["target_scene_id"].(string)
	if !ok {
		return fmt.Errorf("缺少目标场景ID")
	}

	// 检查目标场景是否存在
	var targetScene models.Scene
	if err := tx.Where("id = ? AND world_id = ?", targetSceneID, character.WorldID).First(&targetScene).Error; err != nil {
		return fmt.Errorf("目标场景不存在或不属于该世界: %w", err)
	}

	// 移动角色
	if err := character.MoveTo(tx, targetSceneID); err != nil {
		return fmt.Errorf("移动角色失败: %w", err)
	}

	// 更新最后行动时间
	if err := character.UpdateLastAction(tx); err != nil {
		return fmt.Errorf("更新行动时间失败: %w", err)
	}

	s.logger.Info("角色移动成功", "character_id", character.ID, "target_scene_id", targetSceneID)
	return nil
}

// processCharacterInteract 处理角色交互
func (s *EventService) processCharacterInteract(tx *gorm.DB, character *models.Character, eventData map[string]interface{}) error {
	targetType, ok := eventData["target_type"].(string)
	if !ok {
		return fmt.Errorf("缺少交互目标类型")
	}

	targetID, ok := eventData["target_id"].(string)
	if !ok {
		return fmt.Errorf("缺少交互目标ID")
	}

	switch targetType {
	case "character":
		return s.processCharacterToCharacterInteraction(tx, character, targetID, eventData)
	case "entity":
		return s.processCharacterToEntityInteraction(tx, character, targetID, eventData)
	default:
		return fmt.Errorf("未知交互目标类型: %s", targetType)
	}
}

// processCharacterToCharacterInteraction 处理角色间交互
func (s *EventService) processCharacterToCharacterInteraction(tx *gorm.DB, character *models.Character, targetCharacterID string, eventData map[string]interface{}) error {
	// 获取目标角色
	var targetCharacter models.Character
	if err := tx.First(&targetCharacter, targetCharacterID).Error; err != nil {
		return fmt.Errorf("获取目标角色失败: %w", err)
	}

	// 检查是否在同一场景
	if character.CurrentSceneID == nil || targetCharacter.CurrentSceneID == nil ||
		*character.CurrentSceneID != *targetCharacter.CurrentSceneID {
		return fmt.Errorf("角色不在同一场景")
	}

	// 获取交互类型
	interactionType, ok := eventData["interaction_type"].(string)
	if !ok {
		return fmt.Errorf("缺少交互类型")
	}

	// 根据交互类型更新关系
	strengthDelta := 1
	if interactionType == "conflict" {
		strengthDelta = -1
	}

	if err := character.UpdateRelationship(tx, targetCharacterID, interactionType, strengthDelta); err != nil {
		return fmt.Errorf("更新角色关系失败: %w", err)
	}

	s.logger.Info("角色交互成功", "character_id", character.ID, "target_id", targetCharacterID, "type", interactionType)
	return nil
}

// processCharacterToEntityInteraction 处理角色与实体交互
func (s *EventService) processCharacterToEntityInteraction(tx *gorm.DB, character *models.Character, entityID string, eventData map[string]interface{}) error {
	// 获取实体信息
	var entity models.Entity
	if err := tx.First(&entity, entityID).Error; err != nil {
		return fmt.Errorf("获取实体信息失败: %w", err)
	}

	// 检查实体是否在角色当前场景
	if character.CurrentSceneID == nil {
		return fmt.Errorf("角色不在任何场景中")
	}

	var scene models.Scene
	if err := tx.First(&scene, *character.CurrentSceneID).Error; err != nil {
		return fmt.Errorf("获取当前场景失败: %w", err)
	}

	if !scene.HasEntity(entityID) {
		return fmt.Errorf("实体不在当前场景中")
	}

	// 获取交互类型
	interactionType, ok := eventData["interaction_type"].(string)
	if !ok {
		return fmt.Errorf("缺少交互类型")
	}

	// 根据交互类型处理
	switch interactionType {
	case "examine":
		// 添加观察记忆
		memory := models.Memory{
			Type:        "observation",
			Content:     fmt.Sprintf("观察了%s", entity.Name),
			Importance:  3,
			Metadata:    map[string]interface{}{"entity_id": entityID},
		}
		if err := character.AddMemory(tx, memory); err != nil {
			return fmt.Errorf("添加观察记忆失败: %w", err)
		}
	case "take":
		// 处理拾取逻辑（如果实体可拾取）
		if entity.EntityType == "item" && entity.HasTrait("pickable") {
			// 这里可以添加物品到角色背包的逻辑
			s.logger.Info("角色拾取物品", "character_id", character.ID, "entity_id", entityID)
		}
	}

	s.logger.Info("角色与实体交互成功", "character_id", character.ID, "entity_id", entityID, "type", interactionType)
	return nil
}

// processCharacterSpeak 处理角色说话
func (s *EventService) processCharacterSpeak(tx *gorm.DB, character *models.Character, eventData map[string]interface{}) error {
	content, ok := eventData["content"].(string)
	if !ok {
		return fmt.Errorf("缺少说话内容")
	}

	// 更新最后行动时间
	if err := character.UpdateLastAction(tx); err != nil {
		return fmt.Errorf("更新行动时间失败: %w", err)
	}

	// 添加说话记忆
	memory := models.Memory{
		Type:        "speech",
		Content:     content,
		Importance:  2,
		Metadata:    map[string]interface{}{"scene_id": character.CurrentSceneID},
	}

	if err := character.AddMemory(tx, memory); err != nil {
		return fmt.Errorf("添加说话记忆失败: %w", err)
	}

	s.logger.Info("角色说话成功", "character_id", character.ID, "content", content)
	return nil
}

// processWorldEvent 处理世界事件
func (s *EventService) processWorldEvent(tx *gorm.DB, event *models.Event) error {
	eventData := map[string]interface{}(event.EventData)

	// 获取世界信息
	var world models.World
	if err := tx.First(&world, event.WorldID).Error; err != nil {
		return fmt.Errorf("获取世界信息失败: %w", err)
	}

	// 根据世界事件类型处理
	eventSubType, ok := eventData["sub_type"].(string)
	if !ok {
		return fmt.Errorf("缺少世界事件子类型")
	}

	switch eventSubType {
	case "time_advance":
		// 推进游戏时间
		if minutes, ok := eventData["minutes"].(float64); ok {
			if err := world.UpdateGameTime(tx, int64(minutes)); err != nil {
				return fmt.Errorf("更新游戏时间失败: %w", err)
			}
		}
	case "weather_change":
		// 改变天气
		if weather, ok := eventData["weather"].(string); ok {
			if err := world.SetGlobalVariable(tx, "weather", weather); err != nil {
				return fmt.Errorf("设置天气失败: %w", err)
			}
		}
	}

	s.logger.Info("处理世界事件成功", "world_id", event.WorldID, "sub_type", eventSubType)
	return nil
}

// processSceneEvent 处理场景事件
func (s *EventService) processSceneEvent(tx *gorm.DB, event *models.Event) error {
	eventData := map[string]interface{}(event.EventData)

	sceneID, ok := eventData["scene_id"].(string)
	if !ok {
		return fmt.Errorf("缺少场景ID")
	}

	// 获取场景信息
	var scene models.Scene
	if err := tx.First(&scene, sceneID).Error; err != nil {
		return fmt.Errorf("获取场景信息失败: %w", err)
	}

	// 根据场景事件类型处理
	eventSubType, ok := eventData["sub_type"].(string)
	if !ok {
		return fmt.Errorf("缺少场景事件子类型")
	}

	switch eventSubType {
	case "environment_change":
		// 改变场景环境
		if environment, ok := eventData["environment"].(map[string]interface{}); ok {
			if err := scene.UpdateEnvironment(tx, environment); err != nil {
				return fmt.Errorf("更新场景环境失败: %w", err)
			}
		}
	}

	s.logger.Info("处理场景事件成功", "scene_id", sceneID, "sub_type", eventSubType)
	return nil
}

// processSystemEvent 处理系统事件
func (s *EventService) processSystemEvent(tx *gorm.DB, event *models.Event) error {
	eventData := map[string]interface{}(event.EventData)

	eventSubType, ok := eventData["sub_type"].(string)
	if !ok {
		return fmt.Errorf("缺少系统事件子类型")
	}

	switch eventSubType {
	case "cleanup":
		// 清理过期数据
		s.logger.Info("执行系统清理", "world_id", event.WorldID)
	case "backup":
		// 备份数据
		s.logger.Info("执行数据备份", "world_id", event.WorldID)
	}

	return nil
}

// CancelEvent 取消事件
func (s *EventService) CancelEvent(ctx context.Context, eventID string) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var event models.Event
		if err := tx.First(&event, eventID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("事件不存在")
			}
			return fmt.Errorf("获取事件信息失败: %w", err)
		}

		if event.Status != "pending" {
			return fmt.Errorf("只能取消待处理的事件")
		}

		if err := event.SetStatus(tx, "cancelled"); err != nil {
			return fmt.Errorf("取消事件失败: %w", err)
		}

		s.logger.Info("成功取消事件", "event_id", eventID)
		return nil
	})
}

// GetEventStats 获取事件统计信息
func (s *EventService) GetEventStats(ctx context.Context, worldID string) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 按状态统计事件数量
	statusStats := make(map[string]int64)
	statuses := []string{"pending", "processing", "completed", "failed", "cancelled"}

	for _, status := range statuses {
		var count int64
		s.db.WithContext(ctx).Model(&models.Event{}).
			Where("world_id = ? AND status = ?", worldID, status).
			Count(&count)
		statusStats[status] = count
	}

	// 按类型统计事件数量
	typeStats := make(map[string]int64)
	types := []string{"character_action", "world_event", "scene_event", "system_event"}

	for _, eventType := range types {
		var count int64
		s.db.WithContext(ctx).Model(&models.Event{}).
			Where("world_id = ? AND event_type = ?", worldID, eventType).
			Count(&count)
		typeStats[eventType] = count
	}

	stats["by_status"] = statusStats
	stats["by_type"] = typeStats

	return stats, nil
}
