package migration

import (
	"database/sql"
	"fmt"
	"path/filepath"

	"ai-text-game-iam-npc/internal/config"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	"github.com/golang-migrate/migrate/v4/database/sqlite3"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	_ "github.com/lib/pq"
	_ "github.com/mattn/go-sqlite3"
)

// Migrator 数据库迁移器
type Migrator struct {
	migrate *migrate.Migrate
	db      *sql.DB
}

// New 创建新的迁移器
func New(cfg *config.DatabaseConfig, migrationsPath string) (*Migrator, error) {
	// 检测数据库类型
	var driverName, databaseName string
	var db *sql.DB
	var err error

	// 如果DB_NAME以.db结尾，使用SQLite，否则使用PostgreSQL
	if len(cfg.DBName) > 3 && cfg.DBName[len(cfg.DBName)-3:] == ".db" {
		driverName = "sqlite3"
		databaseName = "sqlite3"
		db, err = sql.Open("sqlite3", cfg.DBName)
	} else {
		driverName = "postgres"
		databaseName = "postgres"
		db, err = sql.Open("postgres", cfg.DSN())
	}

	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	// 创建数据库驱动
	var driver database.Driver
	if driverName == "sqlite3" {
		driver, err = sqlite3.WithInstance(db, &sqlite3.Config{})
	} else {
		driver, err = postgres.WithInstance(db, &postgres.Config{})
	}
	if err != nil {
		return nil, fmt.Errorf("创建数据库驱动失败: %w", err)
	}

	// 获取迁移文件路径
	migrationsURL := fmt.Sprintf("file://%s", filepath.Clean(migrationsPath))

	// 创建迁移实例
	m, err := migrate.NewWithDatabaseInstance(migrationsURL, databaseName, driver)
	if err != nil {
		return nil, fmt.Errorf("创建迁移实例失败: %w", err)
	}

	return &Migrator{
		migrate: m,
		db:      db,
	}, nil
}

// Up 执行向上迁移
func (m *Migrator) Up() error {
	err := m.migrate.Up()
	if err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("执行向上迁移失败: %w", err)
	}
	return nil
}

// Down 执行向下迁移
func (m *Migrator) Down() error {
	err := m.migrate.Down()
	if err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("执行向下迁移失败: %w", err)
	}
	return nil
}

// Steps 执行指定步数的迁移
func (m *Migrator) Steps(n int) error {
	err := m.migrate.Steps(n)
	if err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("执行步数迁移失败: %w", err)
	}
	return nil
}

// Migrate 迁移到指定版本
func (m *Migrator) Migrate(version uint) error {
	err := m.migrate.Migrate(version)
	if err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("迁移到版本 %d 失败: %w", version, err)
	}
	return nil
}

// Version 获取当前数据库版本
func (m *Migrator) Version() (uint, bool, error) {
	version, dirty, err := m.migrate.Version()
	if err != nil {
		return 0, false, fmt.Errorf("获取数据库版本失败: %w", err)
	}
	return version, dirty, nil
}

// Force 强制设置数据库版本（用于修复脏状态）
func (m *Migrator) Force(version int) error {
	err := m.migrate.Force(version)
	if err != nil {
		return fmt.Errorf("强制设置版本 %d 失败: %w", version, err)
	}
	return nil
}

// Drop 删除所有表（危险操作）
func (m *Migrator) Drop() error {
	err := m.migrate.Drop()
	if err != nil {
		return fmt.Errorf("删除所有表失败: %w", err)
	}
	return nil
}

// Close 关闭迁移器
func (m *Migrator) Close() error {
	sourceErr, dbErr := m.migrate.Close()
	if sourceErr != nil {
		return fmt.Errorf("关闭迁移源失败: %w", sourceErr)
	}
	if dbErr != nil {
		return fmt.Errorf("关闭数据库连接失败: %w", dbErr)
	}
	return nil
}

// CreateMigration 创建新的迁移文件
func CreateMigration(migrationsPath, name string) error {
	// 这里可以实现创建迁移文件的逻辑
	// 由于golang-migrate没有直接的API，我们可以手动创建文件
	return fmt.Errorf("创建迁移文件功能需要手动实现")
}

// ValidateMigrations 验证迁移文件的完整性
func (m *Migrator) ValidateMigrations() error {
	// 检查是否有脏状态
	_, dirty, err := m.Version()
	if err != nil {
		return err
	}
	if dirty {
		return fmt.Errorf("数据库处于脏状态，需要手动修复")
	}
	return nil
}

// GetMigrationInfo 获取迁移信息
func (m *Migrator) GetMigrationInfo() (map[string]interface{}, error) {
	version, dirty, err := m.Version()
	if err != nil {
		return nil, err
	}

	info := map[string]interface{}{
		"current_version": version,
		"dirty":           dirty,
	}

	return info, nil
}
