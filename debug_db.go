package main

import (
	"fmt"
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"github.com/google/uuid"
)

// World 简化的世界模型用于调试
type World struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key"`
	Name        string    `gorm:"not null;size:200"`
	Description *string
	CreatorID   uuid.UUID `gorm:"type:uuid;not null"`
	Status      string    `gorm:"default:'active'"`
	CreatedAt   string    // 简化为字符串
	UpdatedAt   string    // 简化为字符串
}

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("data/dev.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 首先检查数据库中有哪些表
	fmt.Println("检查数据库中的表:")
	rows, err := db.Raw("SELECT name FROM sqlite_master WHERE type='table'").Rows()
	if err != nil {
		log.Fatal("查询表列表失败:", err)
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		rows.Scan(&tableName)
		tables = append(tables, tableName)
		fmt.Printf("  - %s\n", tableName)
	}

	if len(tables) == 0 {
		fmt.Println("❌ 数据库中没有任何表！")
		return
	}

	// 检查特定世界ID
	targetID := "7c53bb82-1047-43ea-9929-20ea4b20e169"
	fmt.Printf("\n正在查找世界ID: %s\n", targetID)

	var world World
	result := db.First(&world, "id = ?", targetID)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			fmt.Printf("❌ 世界ID %s 不存在于数据库中\n", targetID)
		} else {
			fmt.Printf("❌ 查询出错: %v\n", result.Error)
		}
	} else {
		fmt.Printf("✅ 找到世界记录:\n")
		fmt.Printf("  ID: %s\n", world.ID)
		fmt.Printf("  名称: %s\n", world.Name)
		if world.Description != nil {
			fmt.Printf("  描述: %s\n", *world.Description)
		} else {
			fmt.Printf("  描述: <空>\n")
		}
		fmt.Printf("  创建者ID: %s\n", world.CreatorID)
		fmt.Printf("  状态: %s\n", world.Status)
		fmt.Printf("  创建时间: %s\n", world.CreatedAt)
	}

	// 查看最近创建的世界
	fmt.Printf("\n最近创建的5个世界:\n")
	var recentWorlds []World
	db.Order("created_at DESC").Limit(5).Find(&recentWorlds)
	
	for i, w := range recentWorlds {
		fmt.Printf("%d. ID: %s, 名称: %s, 创建时间: %s\n", 
			i+1, w.ID, w.Name, w.CreatedAt)
	}

	// 统计总世界数
	var count int64
	db.Model(&World{}).Count(&count)
	fmt.Printf("\n数据库中总共有 %d 个世界\n", count)
}
