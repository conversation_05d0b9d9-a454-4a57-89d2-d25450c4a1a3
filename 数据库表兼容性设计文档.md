# AI文本游戏数据库兼容性设计文档

**版本**: v1.0  
**设计日期**: 2025-01-08  
**基于**: 数据库表设计文档v4.0  
**目标**: 同时支持PostgreSQL（生产环境）和SQLite（开发/简单部署环境）

## 设计概述

本文档针对AI文本游戏项目的数据库兼容性需求，设计了一套同时支持PostgreSQL和SQLite的数据库表结构方案。重点解决JSONB字段、复杂索引、分区表等PostgreSQL特有功能在SQLite中的兼容性问题。

### 核心挑战

1. **JSONB字段兼容性**：SQLite不支持JSONB类型，需要使用TEXT类型存储JSON数据
2. **索引兼容性**：SQLite不支持GIN索引，需要替代方案
3. **分区表支持**：SQLite不支持表分区，需要替代策略
4. **UUID类型**：SQLite不原生支持UUID类型
5. **时间戳类型**：时区处理差异
6. **约束支持**：CHECK约束和部分约束类型的差异

## 第一部分：兼容性方案分析

### 方案1：PostgreSQL优先方案

**设计思路**：以PostgreSQL为主要设计目标，通过程序逻辑层实现SQLite兼容性

#### 优点分析
- ✅ **功能完整性最高**：充分利用PostgreSQL的高级功能
- ✅ **生产环境性能最优**：GIN索引、分区表等提供最佳性能
- ✅ **开发效率高**：无需在数据库层面做妥协
- ✅ **扩展性强**：支持PostgreSQL的所有高级特性

#### 缺点分析
- ❌ **开发复杂度高**：需要维护两套数据库逻辑
- ❌ **SQLite性能受限**：无法使用高级索引优化
- ❌ **测试复杂度高**：需要在两种数据库上都进行测试
- ❌ **维护成本高**：代码分支较多，容易出现不一致

#### 技术实现要点
```go
// 数据库适配器接口
type DatabaseAdapter interface {
    CreateJSONIndex(table, column string) error
    QueryJSON(table, column, path string, value interface{}) ([]map[string]interface{}, error)
    CreatePartition(table string, criteria PartitionCriteria) error
}

// PostgreSQL适配器
type PostgreSQLAdapter struct {
    db *sql.DB
}

func (p *PostgreSQLAdapter) CreateJSONIndex(table, column string) error {
    query := fmt.Sprintf("CREATE INDEX idx_%s_%s_gin ON %s USING GIN (%s)", table, column, table, column)
    _, err := p.db.Exec(query)
    return err
}

// SQLite适配器
type SQLiteAdapter struct {
    db *sql.DB
}

func (s *SQLiteAdapter) CreateJSONIndex(table, column string) error {
    // SQLite使用表达式索引模拟JSON索引
    query := fmt.Sprintf("CREATE INDEX idx_%s_%s_json ON %s (json_extract(%s, '$.key'))", table, column, table, column)
    _, err := s.db.Exec(query)
    return err
}
```

### 方案2：通用SQL方案

**设计思路**：使用最小公共功能集，避免数据库特定功能，通过程序逻辑实现业务流程

#### 优点分析
- ✅ **兼容性最好**：在两种数据库上表现一致
- ✅ **维护成本低**：单一代码路径，减少分支
- ✅ **测试简单**：逻辑一致，测试复杂度低
- ✅ **迁移容易**：数据结构完全兼容

#### 缺点分析
- ❌ **性能受限**：无法利用PostgreSQL的高级功能
- ❌ **功能限制**：复杂查询需要在应用层实现
- ❌ **扩展性差**：难以利用数据库的高级特性
- ❌ **开发效率低**：需要在应用层实现更多逻辑

#### 技术实现要点
```sql
-- 通用表结构设计
CREATE TABLE users (
    id TEXT PRIMARY KEY,  -- 使用TEXT存储UUID
    external_id VARCHAR(255) NOT NULL,
    external_provider VARCHAR(50) NOT NULL,
    profile TEXT NOT NULL DEFAULT '{}',  -- JSON存储为TEXT
    preferences TEXT NOT NULL DEFAULT '{}',
    roles TEXT NOT NULL DEFAULT '["user"]',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TEXT DEFAULT (datetime('now')),  -- ISO8601格式
    updated_at TEXT DEFAULT (datetime('now')),
    last_active_at TEXT
);

-- 通用索引设计
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_provider ON users(external_provider, external_id);
-- 无法创建JSON索引，依赖应用层查询
```

### 方案3：混合适配方案（推荐）

**设计思路**：核心表结构保持兼容，通过适配层处理数据库差异，关键功能使用条件编译

#### 优点分析
- ✅ **平衡性最好**：兼顾性能和兼容性
- ✅ **渐进式优化**：可以根据需要逐步优化
- ✅ **开发友好**：核心逻辑统一，差异部分隔离
- ✅ **性能可控**：关键路径可以针对性优化

#### 缺点分析
- ❌ **实现复杂度中等**：需要设计好的抽象层
- ❌ **配置复杂**：需要管理不同数据库的配置
- ❌ **测试工作量**：需要覆盖两种数据库场景

### 方案4：ORM抽象方案

**设计思路**：使用ORM框架（如GORM）的抽象能力，通过标签和钩子处理数据库差异

#### 优点分析
- ✅ **开发效率高**：ORM处理大部分兼容性问题
- ✅ **代码简洁**：统一的模型定义
- ✅ **维护成本低**：ORM负责SQL生成和优化
- ✅ **类型安全**：编译时检查

#### 缺点分析
- ❌ **性能开销**：ORM层的性能损失
- ❌ **功能限制**：受限于ORM的抽象能力
- ❌ **调试困难**：SQL生成不透明
- ❌ **依赖风险**：依赖ORM框架的更新和维护

### 方案5：微服务数据层方案

**设计思路**：将数据访问封装为独立的微服务，通过API提供统一的数据访问接口

#### 优点分析
- ✅ **完全解耦**：应用层不感知数据库差异
- ✅ **独立扩展**：数据层可以独立优化和扩展
- ✅ **技术栈灵活**：可以使用不同技术栈实现数据层
- ✅ **缓存友好**：容易在API层添加缓存

#### 缺点分析
- ❌ **架构复杂**：增加了系统复杂度
- ❌ **网络开销**：API调用的网络延迟
- ❌ **运维复杂**：需要管理更多服务
- ❌ **事务处理**：跨服务事务处理复杂

## 第二部分：最优方案详细设计

### 基于项目现状的方案选择：改进的ORM抽象方案

基于对当前项目的深入分析，我选择**改进的ORM抽象方案**作为最优解决方案，原因如下：

#### 项目现状分析
1. **技术栈成熟**：项目已使用Go + GORM + Gin，有良好的基础
2. **兼容性基础**：已有`Compatibility`层和`SmartMigrator`
3. **JSON类型完善**：已实现`JSON`和`StringArray`类型，支持Scan/Value接口
4. **开发阶段**：项目处于积极开发阶段，需要快速迭代

#### 选择理由
1. **最小化改动**：基于现有代码增强，风险可控
2. **开发效率**：利用GORM的自动迁移和类型安全特性
3. **AI友好**：现有JSON类型完美支持AI生成的JSONB内容
4. **团队熟悉**：基于熟悉的技术栈，学习成本最低

### 基于现有架构的增强设计

```
┌─────────────────────────────────────────┐
│           Application Layer             │
│    (Gin Controllers + Services)        │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│        Enhanced Repository Layer        │
│   (基于现有Repository模式增强)          │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│      Enhanced Compatibility Layer      │
│  (增强现有Compatibility + SmartMigrator) │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│            GORM + 现有JSON类型           │
│     (JSON, StringArray, UUIDArray)     │
└─────────┬───────────────┬───────────────┘
          │               │
┌─────────▼───────┐    ┌──▼──────────────┐
│   PostgreSQL    │    │     SQLite      │
│   (Production)  │    │ (Development)   │
└─────────────────┘    └─────────────────┘
```

### 技术实现方案

#### 1. 数据库适配器接口设计

```go
package database

import (
    "context"
    "database/sql"
    "encoding/json"
)

// DatabaseType 数据库类型
type DatabaseType string

const (
    PostgreSQL DatabaseType = "postgresql"
    SQLite     DatabaseType = "sqlite"
)

// Adapter 数据库适配器接口
type Adapter interface {
    // 基础操作
    Connect(dsn string) error
    Close() error
    Begin() (Transaction, error)
    
    // JSON操作
    QueryJSON(ctx context.Context, query string, args ...interface{}) ([]map[string]interface{}, error)
    UpdateJSON(ctx context.Context, table, column, condition string, data interface{}) error
    
    // 索引管理
    CreateJSONIndex(table, column string, paths []string) error
    CreateCompositeIndex(table string, columns []string) error
    
    // 分区管理（仅PostgreSQL）
    CreatePartition(table string, criteria PartitionCriteria) error
    
    // 类型转换
    FormatUUID(uuid string) string
    FormatTimestamp(timestamp string) string
    
    // 查询构建
    BuildJSONQuery(table, column, path string, operator string, value interface{}) string
}

// Transaction 事务接口
type Transaction interface {
    Commit() error
    Rollback() error
    Exec(query string, args ...interface{}) (sql.Result, error)
    Query(query string, args ...interface{}) (*sql.Rows, error)
}

// PartitionCriteria 分区条件
type PartitionCriteria struct {
    Type     string // range, hash, list
    Column   string
    Values   []string
    Interval string // for range partitioning
}
```

#### 2. PostgreSQL适配器实现

```go
package database

import (
    "context"
    "database/sql"
    "encoding/json"
    "fmt"
    "strings"
    
    "github.com/lib/pq"
    _ "github.com/lib/pq"
)

type PostgreSQLAdapter struct {
    db *sql.DB
}

func NewPostgreSQLAdapter() *PostgreSQLAdapter {
    return &PostgreSQLAdapter{}
}

func (p *PostgreSQLAdapter) Connect(dsn string) error {
    db, err := sql.Open("postgres", dsn)
    if err != nil {
        return err
    }
    p.db = db
    return p.db.Ping()
}

func (p *PostgreSQLAdapter) CreateJSONIndex(table, column string, paths []string) error {
    // 创建GIN索引
    indexName := fmt.Sprintf("idx_%s_%s_gin", table, column)
    query := fmt.Sprintf("CREATE INDEX IF NOT EXISTS %s ON %s USING GIN (%s)", indexName, table, column)
    _, err := p.db.Exec(query)
    if err != nil {
        return err
    }
    
    // 为常用路径创建表达式索引
    for _, path := range paths {
        exprIndexName := fmt.Sprintf("idx_%s_%s_%s", table, column, strings.ReplaceAll(path, ".", "_"))
        exprQuery := fmt.Sprintf("CREATE INDEX IF NOT EXISTS %s ON %s ((%s->>'%s'))", 
            exprIndexName, table, column, path)
        _, err := p.db.Exec(exprQuery)
        if err != nil {
            return err
        }
    }
    
    return nil
}

func (p *PostgreSQLAdapter) QueryJSON(ctx context.Context, query string, args ...interface{}) ([]map[string]interface{}, error) {
    rows, err := p.db.QueryContext(ctx, query, args...)
    if err != nil {
        return nil, err
    }
    defer rows.Close()
    
    columns, err := rows.Columns()
    if err != nil {
        return nil, err
    }
    
    var results []map[string]interface{}
    for rows.Next() {
        values := make([]interface{}, len(columns))
        valuePtrs := make([]interface{}, len(columns))
        for i := range values {
            valuePtrs[i] = &values[i]
        }
        
        if err := rows.Scan(valuePtrs...); err != nil {
            return nil, err
        }
        
        row := make(map[string]interface{})
        for i, col := range columns {
            val := values[i]
            if b, ok := val.([]byte); ok {
                // 尝试解析JSON
                var jsonVal interface{}
                if err := json.Unmarshal(b, &jsonVal); err == nil {
                    row[col] = jsonVal
                } else {
                    row[col] = string(b)
                }
            } else {
                row[col] = val
            }
        }
        results = append(results, row)
    }
    
    return results, nil
}

func (p *PostgreSQLAdapter) BuildJSONQuery(table, column, path string, operator string, value interface{}) string {
    switch operator {
    case "=":
        return fmt.Sprintf("%s->>'%s' = $1", column, path)
    case "LIKE":
        return fmt.Sprintf("%s->>'%s' LIKE $1", column, path)
    case "IN":
        return fmt.Sprintf("%s->>'%s' = ANY($1)", column, path)
    case "CONTAINS":
        return fmt.Sprintf("%s @> $1", column)
    case "CONTAINED_BY":
        return fmt.Sprintf("%s <@ $1", column)
    default:
        return fmt.Sprintf("%s->>'%s' %s $1", column, path, operator)
    }
}

func (p *PostgreSQLAdapter) CreatePartition(table string, criteria PartitionCriteria) error {
    switch criteria.Type {
    case "range":
        partitionName := fmt.Sprintf("%s_%s", table, criteria.Values[0])
        query := fmt.Sprintf(`
            CREATE TABLE IF NOT EXISTS %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s')`,
            partitionName, table, criteria.Values[0], criteria.Values[1])
        _, err := p.db.Exec(query)
        return err
    default:
        return fmt.Errorf("unsupported partition type: %s", criteria.Type)
    }
}

func (p *PostgreSQLAdapter) FormatUUID(uuid string) string {
    return uuid // PostgreSQL原生支持UUID
}

func (p *PostgreSQLAdapter) FormatTimestamp(timestamp string) string {
    return timestamp // PostgreSQL支持TIMESTAMP WITH TIME ZONE
}
```

#### 3. SQLite适配器实现

```go
package database

import (
    "context"
    "database/sql"
    "encoding/json"
    "fmt"
    "strings"
    
    _ "github.com/mattn/go-sqlite3"
)

type SQLiteAdapter struct {
    db *sql.DB
}

func NewSQLiteAdapter() *SQLiteAdapter {
    return &SQLiteAdapter{}
}

func (s *SQLiteAdapter) Connect(dsn string) error {
    db, err := sql.Open("sqlite3", dsn+"?_journal_mode=WAL&_foreign_keys=on")
    if err != nil {
        return err
    }
    s.db = db
    
    // 启用JSON1扩展
    _, err = s.db.Exec("SELECT json('{}')") // 测试JSON支持
    return err
}

func (s *SQLiteAdapter) CreateJSONIndex(table, column string, paths []string) error {
    // SQLite使用表达式索引模拟JSON索引
    for _, path := range paths {
        indexName := fmt.Sprintf("idx_%s_%s_%s", table, column, strings.ReplaceAll(path, ".", "_"))
        query := fmt.Sprintf("CREATE INDEX IF NOT EXISTS %s ON %s (json_extract(%s, '$.%s'))", 
            indexName, table, column, path)
        _, err := s.db.Exec(query)
        if err != nil {
            return err
        }
    }
    return nil
}

func (s *SQLiteAdapter) QueryJSON(ctx context.Context, query string, args ...interface{}) ([]map[string]interface{}, error) {
    rows, err := s.db.QueryContext(ctx, query, args...)
    if err != nil {
        return nil, err
    }
    defer rows.Close()
    
    columns, err := rows.Columns()
    if err != nil {
        return nil, err
    }
    
    var results []map[string]interface{}
    for rows.Next() {
        values := make([]interface{}, len(columns))
        valuePtrs := make([]interface{}, len(columns))
        for i := range values {
            valuePtrs[i] = &values[i]
        }
        
        if err := rows.Scan(valuePtrs...); err != nil {
            return nil, err
        }
        
        row := make(map[string]interface{})
        for i, col := range columns {
            val := values[i]
            if str, ok := val.(string); ok {
                // 尝试解析JSON
                var jsonVal interface{}
                if err := json.Unmarshal([]byte(str), &jsonVal); err == nil {
                    row[col] = jsonVal
                } else {
                    row[col] = str
                }
            } else {
                row[col] = val
            }
        }
        results = append(results, row)
    }
    
    return results, nil
}

func (s *SQLiteAdapter) BuildJSONQuery(table, column, path string, operator string, value interface{}) string {
    switch operator {
    case "=":
        return fmt.Sprintf("json_extract(%s, '$.%s') = ?", column, path)
    case "LIKE":
        return fmt.Sprintf("json_extract(%s, '$.%s') LIKE ?", column, path)
    case "IN":
        // SQLite需要特殊处理IN操作
        return fmt.Sprintf("json_extract(%s, '$.%s') IN (%s)", column, path, strings.Repeat("?,", len(value.([]interface{}))-1)+"?")
    case "CONTAINS":
        // 使用JSON_EACH模拟包含查询
        return fmt.Sprintf("EXISTS (SELECT 1 FROM json_each(%s) WHERE value = ?)", column)
    default:
        return fmt.Sprintf("json_extract(%s, '$.%s') %s ?", column, path, operator)
    }
}

func (s *SQLiteAdapter) CreatePartition(table string, criteria PartitionCriteria) error {
    // SQLite不支持分区，返回成功但不执行任何操作
    return nil
}

func (s *SQLiteAdapter) FormatUUID(uuid string) string {
    return uuid // SQLite使用TEXT存储UUID
}

func (s *SQLiteAdapter) FormatTimestamp(timestamp string) string {
    // 转换为SQLite支持的格式
    return timestamp // 使用ISO8601格式
}
```

#### 4. 统一的数据访问层

```go
package models

import (
    "context"
    "encoding/json"
    "fmt"
    "time"
    
    "your-project/database"
)

// User 用户模型
type User struct {
    ID             string                 `json:"id"`
    ExternalID     string                 `json:"external_id"`
    ExternalProvider string               `json:"external_provider"`
    Profile        map[string]interface{} `json:"profile"`
    Preferences    map[string]interface{} `json:"preferences"`
    Roles          []string               `json:"roles"`
    Status         string                 `json:"status"`
    CreatedAt      time.Time              `json:"created_at"`
    UpdatedAt      time.Time              `json:"updated_at"`
    LastActiveAt   *time.Time             `json:"last_active_at"`
}

// UserRepository 用户仓库
type UserRepository struct {
    adapter database.Adapter
}

func NewUserRepository(adapter database.Adapter) *UserRepository {
    return &UserRepository{adapter: adapter}
}

func (r *UserRepository) Create(ctx context.Context, user *User) error {
    profileJSON, _ := json.Marshal(user.Profile)
    preferencesJSON, _ := json.Marshal(user.Preferences)
    rolesJSON, _ := json.Marshal(user.Roles)
    
    query := `
        INSERT INTO users (id, external_id, external_provider, profile, preferences, roles, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`
    
    _, err := r.adapter.Exec(ctx, query,
        r.adapter.FormatUUID(user.ID),
        user.ExternalID,
        user.ExternalProvider,
        string(profileJSON),
        string(preferencesJSON),
        string(rolesJSON),
        user.Status,
        r.adapter.FormatTimestamp(user.CreatedAt.Format(time.RFC3339)),
        r.adapter.FormatTimestamp(user.UpdatedAt.Format(time.RFC3339)),
    )
    
    return err
}

func (r *UserRepository) FindByExternalID(ctx context.Context, externalID, provider string) (*User, error) {
    query := `SELECT id, external_id, external_provider, profile, preferences, roles, status, created_at, updated_at, last_active_at
              FROM users WHERE external_id = ? AND external_provider = ?`
    
    results, err := r.adapter.QueryJSON(ctx, query, externalID, provider)
    if err != nil {
        return nil, err
    }
    
    if len(results) == 0 {
        return nil, nil
    }
    
    return r.mapToUser(results[0])
}

func (r *UserRepository) FindByProfileField(ctx context.Context, field string, value interface{}) ([]*User, error) {
    condition := r.adapter.BuildJSONQuery("users", "profile", field, "=", value)
    query := fmt.Sprintf("SELECT * FROM users WHERE %s", condition)
    
    results, err := r.adapter.QueryJSON(ctx, query, value)
    if err != nil {
        return nil, err
    }
    
    var users []*User
    for _, result := range results {
        user, err := r.mapToUser(result)
        if err != nil {
            continue
        }
        users = append(users, user)
    }
    
    return users, nil
}

func (r *UserRepository) mapToUser(data map[string]interface{}) (*User, error) {
    user := &User{}
    
    if id, ok := data["id"].(string); ok {
        user.ID = id
    }
    
    if externalID, ok := data["external_id"].(string); ok {
        user.ExternalID = externalID
    }
    
    if provider, ok := data["external_provider"].(string); ok {
        user.ExternalProvider = provider
    }
    
    if profile, ok := data["profile"]; ok {
        if profileMap, ok := profile.(map[string]interface{}); ok {
            user.Profile = profileMap
        }
    }
    
    if preferences, ok := data["preferences"]; ok {
        if preferencesMap, ok := preferences.(map[string]interface{}); ok {
            user.Preferences = preferencesMap
        }
    }
    
    if roles, ok := data["roles"]; ok {
        if rolesSlice, ok := roles.([]interface{}); ok {
            user.Roles = make([]string, len(rolesSlice))
            for i, role := range rolesSlice {
                if roleStr, ok := role.(string); ok {
                    user.Roles[i] = roleStr
                }
            }
        }
    }
    
    return user, nil
}
```

## 第三部分：兼容性表结构设计

### 核心表结构兼容性设计

#### 1. 用户表兼容性设计

```sql
-- PostgreSQL版本
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    external_id VARCHAR(255) NOT NULL,
    external_provider VARCHAR(50) NOT NULL,
    profile JSONB NOT NULL DEFAULT '{}',
    preferences JSONB NOT NULL DEFAULT '{}',
    roles JSONB NOT NULL DEFAULT '["user"]',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT unique_external_user UNIQUE (external_id, external_provider)
);

-- SQLite版本
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    external_id VARCHAR(255) NOT NULL,
    external_provider VARCHAR(50) NOT NULL,
    profile TEXT NOT NULL DEFAULT '{}',
    preferences TEXT NOT NULL DEFAULT '{}',
    roles TEXT NOT NULL DEFAULT '["user"]',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    last_active_at TEXT,
    CONSTRAINT unique_external_user UNIQUE (external_id, external_provider)
);
```

#### 2. 世界表兼容性设计

```sql
-- PostgreSQL版本
CREATE TABLE worlds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    creator_id UUID NOT NULL REFERENCES users(id),
    world_config JSONB NOT NULL DEFAULT '{}',
    world_state JSONB NOT NULL DEFAULT '{}',
    access_settings JSONB NOT NULL DEFAULT '{}',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    tags JSONB NOT NULL DEFAULT '[]',
    game_time BIGINT NOT NULL DEFAULT 0,
    time_config JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SQLite版本
CREATE TABLE worlds (
    id TEXT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    creator_id TEXT NOT NULL REFERENCES users(id),
    world_config TEXT NOT NULL DEFAULT '{}',
    world_state TEXT NOT NULL DEFAULT '{}',
    access_settings TEXT NOT NULL DEFAULT '{}',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    tags TEXT NOT NULL DEFAULT '[]',
    game_time BIGINT NOT NULL DEFAULT 0,
    time_config TEXT NOT NULL DEFAULT '{}',
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now'))
);
```

### 索引兼容性设计

#### PostgreSQL索引策略
```sql
-- GIN索引用于JSON查询
CREATE INDEX idx_users_profile_gin ON users USING GIN (profile);
CREATE INDEX idx_users_preferences_gin ON users USING GIN (preferences);
CREATE INDEX idx_worlds_config_gin ON worlds USING GIN (world_config);
CREATE INDEX idx_worlds_state_gin ON worlds USING GIN (world_state);

-- 表达式索引用于常用JSON路径
CREATE INDEX idx_users_profile_display_name ON users ((profile->>'display_name'));
CREATE INDEX idx_worlds_config_genre ON worlds ((world_config->'theme'->>'genre'));
```

#### SQLite索引策略
```sql
-- 表达式索引模拟JSON查询
CREATE INDEX idx_users_profile_display_name ON users (json_extract(profile, '$.display_name'));
CREATE INDEX idx_users_profile_locale ON users (json_extract(profile, '$.locale'));
CREATE INDEX idx_worlds_config_genre ON worlds (json_extract(world_config, '$.theme.genre'));
CREATE INDEX idx_worlds_state_time ON worlds (json_extract(world_state, '$.current_time.day'));

-- 复合索引
CREATE INDEX idx_users_status_provider ON users (status, external_provider);
CREATE INDEX idx_worlds_creator_status ON worlds (creator_id, status);
```

## 第四部分：实施指导

### 分阶段实施计划

#### 阶段1：基础架构搭建（1-2周）

**目标**：建立数据库适配器架构和基础功能

**任务清单**：
1. ✅ 设计并实现数据库适配器接口
2. ✅ 实现PostgreSQL适配器基础功能
3. ✅ 实现SQLite适配器基础功能
4. ✅ 创建配置管理系统
5. ✅ 编写单元测试

**验收标准**：
- 两种数据库适配器都能正常连接和执行基础SQL
- 配置系统能够根据环境自动选择数据库类型
- 单元测试覆盖率达到80%以上

#### 阶段2：核心表结构迁移（2-3周）

**目标**：将现有表结构迁移到兼容性设计

**任务清单**：
1. ✅ 创建兼容性表结构定义
2. ✅ 实现数据迁移脚本
3. ✅ 创建索引管理功能
4. ✅ 实现JSON查询功能
5. ✅ 编写集成测试

**验收标准**：
- 所有核心表在两种数据库中都能正常创建
- JSON查询功能在两种数据库中表现一致
- 数据迁移脚本能够无损迁移现有数据

#### 阶段3：业务逻辑适配（3-4周）

**目标**：将业务逻辑适配到新的数据访问层

**任务清单**：
1. ✅ 重构数据访问层
2. ✅ 实现仓库模式
3. ✅ 适配AI接口调用逻辑
4. ✅ 优化查询性能
5. ✅ 完善错误处理

**验收标准**：
- 所有业务功能在两种数据库中都能正常工作
- 查询性能满足AI接口调用需求
- 错误处理机制完善，能够提供有意义的错误信息

#### 阶段4：性能优化和测试（2-3周）

**目标**：优化性能并完成全面测试

**任务清单**：
1. ✅ 性能基准测试
2. ✅ 查询优化
3. ✅ 缓存策略实施
4. ✅ 压力测试
5. ✅ 文档完善

**验收标准**：
- PostgreSQL环境性能达到生产要求
- SQLite环境满足开发和简单部署需求
- 通过压力测试，系统稳定性良好

### 测试策略和验证方法

#### 1. 单元测试策略

```go
package database_test

import (
    "context"
    "testing"
    "your-project/database"
)

func TestDatabaseAdapters(t *testing.T) {
    adapters := []database.Adapter{
        database.NewPostgreSQLAdapter(),
        database.NewSQLiteAdapter(),
    }
    
    for _, adapter := range adapters {
        t.Run(fmt.Sprintf("TestAdapter_%T", adapter), func(t *testing.T) {
            testBasicOperations(t, adapter)
            testJSONOperations(t, adapter)
            testTransactions(t, adapter)
        })
    }
}

func testJSONOperations(t *testing.T, adapter database.Adapter) {
    // 测试JSON插入
    user := map[string]interface{}{
        "id": "test-uuid",
        "profile": map[string]interface{}{
            "display_name": "测试用户",
            "locale": "zh-CN",
        },
    }
    
    err := insertUser(adapter, user)
    assert.NoError(t, err)
    
    // 测试JSON查询
    results, err := adapter.QueryJSON(context.Background(),
        adapter.BuildJSONQuery("users", "profile", "display_name", "=", "测试用户"),
        "测试用户")
    assert.NoError(t, err)
    assert.Len(t, results, 1)
    
    // 测试JSON更新
    err = adapter.UpdateJSON(context.Background(), "users", "profile", "id = ?", 
        map[string]interface{}{"display_name": "更新后的用户"}, "test-uuid")
    assert.NoError(t, err)
}
```

#### 2. 集成测试策略

```go
package integration_test

import (
    "testing"
    "your-project/models"
    "your-project/database"
)

func TestUserRepository(t *testing.T) {
    // 测试PostgreSQL
    pgAdapter := setupPostgreSQL(t)
    testUserRepositoryWithAdapter(t, pgAdapter, "PostgreSQL")
    
    // 测试SQLite
    sqliteAdapter := setupSQLite(t)
    testUserRepositoryWithAdapter(t, sqliteAdapter, "SQLite")
}

func testUserRepositoryWithAdapter(t *testing.T, adapter database.Adapter, dbType string) {
    t.Run(dbType, func(t *testing.T) {
        repo := models.NewUserRepository(adapter)
        
        // 测试创建用户
        user := &models.User{
            ID: generateUUID(),
            ExternalID: "test-external-id",
            ExternalProvider: "test-provider",
            Profile: map[string]interface{}{
                "display_name": "测试用户",
                "locale": "zh-CN",
            },
        }
        
        err := repo.Create(context.Background(), user)
        assert.NoError(t, err)
        
        // 测试查询用户
        foundUser, err := repo.FindByExternalID(context.Background(), 
            "test-external-id", "test-provider")
        assert.NoError(t, err)
        assert.NotNil(t, foundUser)
        assert.Equal(t, user.ID, foundUser.ID)
        
        // 测试JSON字段查询
        users, err := repo.FindByProfileField(context.Background(), 
            "display_name", "测试用户")
        assert.NoError(t, err)
        assert.Len(t, users, 1)
    })
}
```

#### 3. 性能基准测试

```go
package benchmark_test

import (
    "testing"
    "your-project/database"
)

func BenchmarkJSONQuery(b *testing.B) {
    adapters := map[string]database.Adapter{
        "PostgreSQL": setupPostgreSQL(b),
        "SQLite": setupSQLite(b),
    }
    
    for name, adapter := range adapters {
        b.Run(name, func(b *testing.B) {
            // 准备测试数据
            setupBenchmarkData(adapter, 10000)
            
            b.ResetTimer()
            for i := 0; i < b.N; i++ {
                query := adapter.BuildJSONQuery("users", "profile", "locale", "=", "zh-CN")
                _, err := adapter.QueryJSON(context.Background(), 
                    fmt.Sprintf("SELECT * FROM users WHERE %s LIMIT 100", query), "zh-CN")
                if err != nil {
                    b.Fatal(err)
                }
            }
        })
    }
}

func BenchmarkComplexQuery(b *testing.B) {
    // 测试复杂查询性能
    // 包括多表JOIN、JSON查询、聚合等
}
```

### 潜在风险和应对措施

#### 1. 性能风险

**风险描述**：SQLite在复杂JSON查询上的性能可能不如PostgreSQL

**应对措施**：
- 在SQLite中实施更积极的缓存策略
- 对于复杂查询，在应用层进行数据预处理
- 提供性能监控和告警机制

```go
// 缓存策略示例
type CachedRepository struct {
    repo  models.Repository
    cache cache.Cache
    ttl   time.Duration
}

func (c *CachedRepository) FindByProfileField(ctx context.Context, field string, value interface{}) ([]*models.User, error) {
    cacheKey := fmt.Sprintf("users:profile:%s:%v", field, value)
    
    // 尝试从缓存获取
    if cached, err := c.cache.Get(cacheKey); err == nil {
        var users []*models.User
        if err := json.Unmarshal(cached, &users); err == nil {
            return users, nil
        }
    }
    
    // 从数据库查询
    users, err := c.repo.FindByProfileField(ctx, field, value)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    if data, err := json.Marshal(users); err == nil {
        c.cache.Set(cacheKey, data, c.ttl)
    }
    
    return users, nil
}
```

#### 2. 数据一致性风险

**风险描述**：两种数据库的事务处理和约束检查可能存在差异

**应对措施**：
- 在应用层实施统一的数据验证逻辑
- 使用分布式锁确保关键操作的原子性
- 实施数据一致性检查工具

```go
// 统一数据验证
type Validator interface {
    Validate(ctx context.Context, data interface{}) error
}

type UserValidator struct{}

func (v *UserValidator) Validate(ctx context.Context, user *models.User) error {
    if user.ID == "" {
        return errors.New("用户ID不能为空")
    }
    
    if user.ExternalID == "" {
        return errors.New("外部ID不能为空")
    }
    
    // 验证JSON字段结构
    if err := v.validateProfile(user.Profile); err != nil {
        return fmt.Errorf("用户档案验证失败: %w", err)
    }
    
    return nil
}
```

#### 3. 迁移风险

**风险描述**：从现有系统迁移到新架构可能导致数据丢失或服务中断

**应对措施**：
- 实施蓝绿部署策略
- 提供数据回滚机制
- 进行充分的迁移测试

```go
// 迁移工具
type MigrationTool struct {
    oldAdapter database.Adapter
    newAdapter database.Adapter
}

func (m *MigrationTool) MigrateUsers(ctx context.Context) error {
    // 1. 备份现有数据
    if err := m.backupData(ctx, "users"); err != nil {
        return fmt.Errorf("备份失败: %w", err)
    }
    
    // 2. 迁移数据
    users, err := m.fetchAllUsers(ctx)
    if err != nil {
        return fmt.Errorf("获取用户数据失败: %w", err)
    }
    
    for _, user := range users {
        if err := m.migrateUser(ctx, user); err != nil {
            return fmt.Errorf("迁移用户 %s 失败: %w", user.ID, err)
        }
    }
    
    // 3. 验证数据完整性
    if err := m.validateMigration(ctx, "users"); err != nil {
        return fmt.Errorf("数据验证失败: %w", err)
    }
    
    return nil
}
```

## 第八部分：项目特定实施方案

### 基于现有代码的增强实现

基于对当前AI文本游戏项目的深入分析，我们选择了**改进的ORM抽象方案**，并提供了完整的增强实现。

#### 现有基础分析

**已有的优秀基础**：
1. **完善的JSON类型**：项目已实现`JSON`和`StringArray`类型，支持Scan/Value接口
2. **智能迁移器**：`SmartMigrator`已支持基础的数据库兼容性
3. **兼容性配置**：`Compatibility`层已处理基本的数据库差异
4. **统一模型定义**：所有模型已使用兼容的字段类型

**需要增强的部分**：
1. **复杂JSON查询**：缺少跨数据库的JSON查询构建器
2. **索引管理**：缺少自动化的JSON索引创建
3. **性能监控**：缺少数据库性能监控机制
4. **AI友好接口**：缺少针对AI接口调用优化的数据访问层

### 增强实现架构

#### 1. 增强的兼容性层 (`pkg/database/enhanced_compatibility.go`)

**核心功能**：
- **JSONQueryBuilder**：跨数据库的JSON查询构建器
- **IndexManager**：自动化索引管理
- **TransactionManager**：事务管理器
- **PerformanceMonitor**：性能监控器
- **ValidationHelper**：数据验证助手

**关键特性**：
```go
// 自动适配不同数据库的JSON查询
queryBuilder := compatibility.NewJSONQueryBuilder("users", "preferences")
condition, args := queryBuilder.FieldEquals("ui.theme", "dark")

// PostgreSQL: preferences->>'ui.theme' = ?
// SQLite: JSON_EXTRACT(preferences, '$.ui.theme') = ?
```

#### 2. 增强的Repository层 (`internal/repository/enhanced_repository.go`)

**设计特点**：
- **基于现有模型**：直接使用项目现有的`models.User`、`models.World`等
- **AI友好接口**：专门为AI接口调用优化的查询方法
- **统一事务管理**：跨数据库的事务处理
- **自动索引创建**：启动时自动创建必要的JSON索引

**核心方法示例**：
```go
// 根据偏好字段查找用户（AI友好）
users, err := userRepo.FindByPreferenceField(ctx, "game.ai_speed", "balanced")

// 根据主题类型查找世界
worlds, err := worldRepo.FindByThemeGenre(ctx, "fantasy")

// 批量更新世界配置
err := worldRepo.UpdateWorldConfig(ctx, worldID, map[string]interface{}{
    "theme.mood": "dark",
    "rules.content_rating": "mature",
})
```

#### 3. 增强的服务层 (`internal/service/enhanced_service.go`)

**业务逻辑优化**：
- **AI内容生成支持**：优化的JSON数据处理
- **智能默认值**：自动设置合理的默认配置
- **数据验证**：集成的JSON Schema验证
- **性能监控**：查询性能统计和优化建议

**使用示例**：
```go
// 创建用户时自动设置AI友好的默认偏好
user, err := userService.CreateUser(ctx, &CreateUserRequest{
    ExternalID: "user123",
    ExternalProvider: "google",
    Preferences: map[string]interface{}{
        "game": map[string]interface{}{
            "ai_speed": "balanced",
            "language": "zh-CN",
        },
    },
})
```

#### 4. 增强的配置管理 (`internal/config/enhanced_config.go`)

**智能配置**：
- **自动检测数据库类型**：根据环境自动选择SQLite或PostgreSQL
- **环境相关默认值**：开发/生产环境的不同配置
- **性能优化配置**：连接池、索引、缓存等配置
- **AI接口优化**：针对AI调用的特殊配置

**配置示例**：
```go
// 开发环境自动使用SQLite
// 生产环境自动启用分区和物化视图
config := &EnhancedDatabaseConfig{
    Type: detectDatabaseType(), // 自动检测
    EnablePartitioning: environment == "production",
    JSONIndexPaths: []string{
        "ui.theme", "game.ai_speed", "theme.genre",
    },
}
```

### 核心表结构兼容性实现

#### 1. 用户表 (users) 增强实现

**现有模型保持不变**：
```go
type User struct {
    ID               string         `json:"id" gorm:"primaryKey;type:text"`
    ExternalID       string         `json:"external_id" gorm:"not null;index"`
    ExternalProvider string         `json:"external_provider" gorm:"not null;index"`
    // 现有的JSON字段完美支持
    Preferences      JSON           `json:"preferences" gorm:"type:text;default:'{}'"`
    GameRoles        StringArray    `json:"game_roles" gorm:"type:text;default:'[\"user\"]'"`
}
```

**增强的查询能力**：
```go
// 复杂偏好查询
users, err := repo.SearchUsersByPreferences(ctx, map[string]interface{}{
    "ui.theme": "dark",
    "game.language": "zh-CN",
})

// 嵌套字段更新
err := repo.UpdatePreferenceField(ctx, userID, "ui.theme", "light")
```

#### 2. 世界表 (worlds) 增强实现

**AI友好的配置管理**：
```go
// 智能默认配置
defaultConfig := map[string]interface{}{
    "theme": map[string]interface{}{
        "genre": "fantasy",
        "mood": "neutral",
    },
    "rules": map[string]interface{}{
        "ai_creativity": "balanced",
        "content_rating": "teen",
    },
}

// 批量配置更新
err := repo.UpdateWorldConfig(ctx, worldID, map[string]interface{}{
    "theme.genre": "sci-fi",
    "rules.ai_creativity": "creative",
})
```

#### 3. 场景表 (scenes) 增强实现

**动态属性管理**：
```go
// 场景属性查询
scenes, err := repo.FindByPropertyField(ctx, "environment.weather", "rainy")

// 批量属性更新
err := repo.UpdateSceneProperties(ctx, sceneID, map[string]interface{}{
    "environment.time": "night",
    "accessibility.is_accessible": true,
})
```

### 自动索引创建策略

#### PostgreSQL索引策略
```sql
-- 自动创建的GIN索引
CREATE INDEX idx_users_preferences_gin ON users USING GIN (preferences);
CREATE INDEX idx_worlds_world_config_gin ON worlds USING GIN (world_config);

-- 常用路径的表达式索引
CREATE INDEX idx_users_preferences_ui_theme ON users ((preferences->>'ui.theme'));
CREATE INDEX idx_worlds_config_theme_genre ON worlds ((world_config->'theme'->>'genre'));
```

#### SQLite索引策略
```sql
-- JSON表达式索引
CREATE INDEX idx_users_preferences_ui_theme ON users (JSON_EXTRACT(preferences, '$.ui.theme'));
CREATE INDEX idx_worlds_config_theme_genre ON worlds (JSON_EXTRACT(world_config, '$.theme.genre'));
```

### 启动和部署

#### 1. 增强服务器启动 (`cmd/enhanced-server/main.go`)

**完整的启动流程**：
1. **智能配置加载**：自动检测环境和数据库类型
2. **数据库初始化**：兼容现有的数据库连接逻辑
3. **自动迁移**：使用现有的`SmartMigrator`
4. **索引创建**：自动创建JSON索引
5. **服务启动**：提供RESTful API接口

**使用方式**：
```bash
# 开发环境（自动使用SQLite）
ENVIRONMENT=development go run cmd/enhanced-server/main.go

# 生产环境（自动使用PostgreSQL）
ENVIRONMENT=production DB_TYPE=postgresql go run cmd/enhanced-server/main.go
```

#### 2. API接口示例

**用户管理**：
```bash
# 创建用户
POST /api/v1/users
{
  "external_id": "user123",
  "external_provider": "google",
  "email": "<EMAIL>",
  "preferences": {
    "ui": {"theme": "dark"},
    "game": {"ai_speed": "balanced"}
  }
}

# 更新用户偏好
PUT /api/v1/users/user123/preferences
{
  "ui.theme": "light",
  "game.language": "en-US"
}

# 搜索用户
GET /api/v1/users/search
{
  "ui.theme": "dark",
  "game.ai_speed": "balanced"
}
```

**世界管理**：
```bash
# 创建世界
POST /api/v1/worlds
{
  "name": "魔法森林",
  "creator_id": "user123",
  "world_config": {
    "theme": {"genre": "fantasy"},
    "rules": {"ai_creativity": "creative"}
  }
}

# 更新世界配置
PUT /api/v1/worlds/world123/config
{
  "theme.mood": "dark",
  "rules.content_rating": "mature"
}
```

### 性能优化和监控

#### 1. 查询性能监控

```go
// 自动性能监控
monitor := compatibility.NewPerformanceMonitor()
stats, err := monitor.MeasureQuery(ctx, "user_search", func() *gorm.DB {
    return db.Where(condition, args...).Find(&users)
})

// 输出性能统计
// {
//   "duration": "15ms",
//   "rows_affected": 25,
//   "database_type": "postgresql",
//   "query_type": "user_search"
// }
```

#### 2. 数据库信息监控

```bash
# 获取数据库信息
GET /api/v1/database/info
{
  "success": true,
  "data": {
    "database_type": "postgresql",
    "is_postgresql": true,
    "supports_jsonb": true,
    "supports_uuid": true
  }
}
```

## 第九部分：完整实施指导

### 分阶段实施计划

#### 阶段1：基础增强（第1周）

**目标**：部署增强的兼容性层，确保基础功能正常

**具体任务**：
1. **部署增强兼容性层**
   ```bash
   # 复制增强兼容性文件
   cp pkg/database/enhanced_compatibility.go pkg/database/

   # 更新依赖
   go mod tidy
   ```

2. **测试基础功能**
   ```bash
   # 运行兼容性测试
   go run scripts/test_enhanced_compatibility.go
   ```

3. **验证现有功能**
   ```bash
   # 确保现有API仍然正常工作
   curl http://localhost:8080/health
   ```

**验收标准**：
- ✅ 增强兼容性层正常工作
- ✅ 现有功能无回归
- ✅ 基础JSON查询功能正常

#### 阶段2：Repository层增强（第2周）

**目标**：部署增强的Repository层，提供AI友好的数据访问接口

**具体任务**：
1. **部署增强Repository**
   ```bash
   # 复制增强Repository文件
   cp internal/repository/enhanced_repository.go internal/repository/
   ```

2. **更新服务层**
   ```bash
   # 复制增强服务层文件
   cp internal/service/enhanced_service.go internal/service/
   ```

3. **测试新功能**
   ```bash
   # 测试用户偏好查询
   curl -X POST http://localhost:8080/api/v1/users/search \
     -H "Content-Type: application/json" \
     -d '{"ui.theme": "dark"}'
   ```

**验收标准**：
- ✅ 复杂JSON查询正常工作
- ✅ 批量更新功能正常
- ✅ AI友好接口响应正确

#### 阶段3：配置和部署优化（第3周）

**目标**：部署增强的配置管理和自动化部署

**具体任务**：
1. **部署增强配置**
   ```bash
   # 复制增强配置文件
   cp internal/config/enhanced_config.go internal/config/
   ```

2. **部署增强服务器**
   ```bash
   # 复制增强服务器文件
   cp cmd/enhanced-server/main.go cmd/enhanced-server/
   ```

3. **测试自动化部署**
   ```bash
   # 开发环境部署
   ./deployments/scripts/deploy.sh dev -t

   # 生产环境部署（如果有PostgreSQL）
   ./deployments/scripts/deploy.sh prod -m
   ```

**验收标准**：
- ✅ 自动环境检测正常
- ✅ Docker部署成功
- ✅ 监控和管理工具正常

#### 阶段4：性能优化和监控（第4周）

**目标**：优化性能并建立监控体系

**具体任务**：
1. **性能基准测试**
   ```bash
   # 运行性能测试
   go test -bench=. ./internal/repository/
   ```

2. **监控部署**
   ```bash
   # 启用监控工具
   ./deployments/scripts/deploy.sh prod -m --tools
   ```

3. **性能调优**
   - 根据监控数据调整数据库连接池
   - 优化JSON索引策略
   - 调整缓存配置

**验收标准**：
- ✅ 性能满足预期
- ✅ 监控体系完整
- ✅ 告警机制正常

### 快速开始指南

#### 1. 开发环境快速启动

```bash
# 1. 克隆项目（如果还没有）
git clone <your-repo-url>
cd ai-text-game-iam-npc

# 2. 安装依赖
go mod tidy

# 3. 启动开发环境（自动使用SQLite）
./deployments/scripts/deploy.sh dev -t

# 4. 验证部署
curl http://localhost:8080/health
curl http://localhost:8080/api/v1/database/info
```

#### 2. 生产环境部署

```bash
# 1. 设置环境变量
export POSTGRES_PASSWORD=your_secure_password
export AI_API_KEY=your_ai_api_key

# 2. 部署生产环境（自动使用PostgreSQL）
./deployments/scripts/deploy.sh prod -m --tools

# 3. 验证部署
curl http://localhost:8080/health
curl http://localhost:8080/api/v1/database/info
```

#### 3. 测试兼容性

```bash
# 运行完整的兼容性测试
./deployments/scripts/deploy.sh test -t

# 或者直接运行测试程序
go run scripts/test_enhanced_compatibility.go
```

### API使用示例

#### 用户管理API

```bash
# 创建用户
curl -X POST http://localhost:8080/api/v1/users \
  -H "Content-Type: application/json" \
  -d '{
    "external_id": "user123",
    "external_provider": "google",
    "email": "<EMAIL>",
    "display_name": "测试用户",
    "preferences": {
      "ui": {"theme": "dark", "language": "zh-CN"},
      "game": {"ai_speed": "balanced"}
    }
  }'

# 更新用户偏好
curl -X PUT http://localhost:8080/api/v1/users/{user_id}/preferences \
  -H "Content-Type: application/json" \
  -d '{
    "ui.theme": "light",
    "game.ai_speed": "fast"
  }'

# 搜索用户
curl -X GET http://localhost:8080/api/v1/users/search \
  -H "Content-Type: application/json" \
  -d '{
    "ui.theme": "dark",
    "game.language": "zh-CN"
  }'
```

#### 世界管理API

```bash
# 创建世界
curl -X POST http://localhost:8080/api/v1/worlds \
  -H "Content-Type: application/json" \
  -d '{
    "name": "魔法森林",
    "description": "一个充满魔法的神秘森林",
    "creator_id": "user123",
    "world_config": {
      "theme": {"genre": "fantasy", "mood": "mysterious"},
      "rules": {"ai_creativity": "creative"}
    },
    "tags": ["魔法", "森林", "冒险"]
  }'

# 更新世界配置
curl -X PUT http://localhost:8080/api/v1/worlds/{world_id}/config \
  -H "Content-Type: application/json" \
  -d '{
    "theme.mood": "dark",
    "rules.content_rating": "mature"
  }'

# 搜索世界
curl -X GET "http://localhost:8080/api/v1/worlds/search?genre=fantasy"
```

### 监控和管理

#### 1. 健康检查

```bash
# 应用健康检查
curl http://localhost:8080/health

# 数据库信息
curl http://localhost:8080/api/v1/database/info
```

#### 2. 日志查看

```bash
# 查看应用日志
docker-compose logs -f ai-text-game-enhanced

# 查看数据库日志
docker-compose logs -f postgres

# 查看Redis日志
docker-compose logs -f redis
```

#### 3. 性能监控

访问监控面板：
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **数据库管理**: http://localhost:8081
- **Redis管理**: http://localhost:8082

### 故障排除

#### 常见问题和解决方案

**1. 数据库连接失败**
```bash
# 检查数据库状态
docker-compose ps postgres

# 查看数据库日志
docker-compose logs postgres

# 重启数据库
docker-compose restart postgres
```

**2. JSON查询不工作**
```bash
# 检查数据库类型
curl http://localhost:8080/api/v1/database/info

# 查看应用日志
docker-compose logs ai-text-game-enhanced
```

**3. 性能问题**
```bash
# 检查连接池配置
echo $DB_MAX_OPEN_CONNS
echo $DB_MAX_IDLE_CONNS

# 查看慢查询日志
docker-compose exec postgres psql -U postgres -d ai_text_game \
  -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"
```

### 最佳实践

#### 1. 开发环境

- 使用SQLite进行快速开发和测试
- 启用详细日志记录
- 定期运行兼容性测试

#### 2. 生产环境

- 使用PostgreSQL获得最佳性能
- 启用分区和物化视图
- 配置适当的连接池大小
- 启用监控和告警

#### 3. 数据迁移

```bash
# 从SQLite迁移到PostgreSQL
# 1. 导出SQLite数据
sqlite3 game.db .dump > backup.sql

# 2. 转换为PostgreSQL格式
# 3. 导入到PostgreSQL
psql -U postgres -d ai_text_game -f converted_backup.sql
```

#### 4. 备份策略

```bash
# PostgreSQL备份
docker-compose exec postgres pg_dump -U postgres ai_text_game > backup.sql

# SQLite备份
cp data/game.db backup/game_$(date +%Y%m%d_%H%M%S).db
```

这个增强实现完全基于项目现有的代码基础，通过最小化的改动提供了强大的数据库兼容性功能，既保证了开发环境的便利性，又满足了生产环境的高性能需求。通过分阶段实施和完整的部署指导，可以确保平滑的升级过程和系统的稳定运行。

## 第五部分：JSONB字段兼容性详细方案

### JSONB vs JSON vs TEXT 对比分析

| 特性 | PostgreSQL JSONB | SQLite JSON1 | SQLite TEXT |
|------|------------------|---------------|-------------|
| 存储格式 | 二进制压缩 | 文本格式 | 纯文本 |
| 查询性能 | 优秀（GIN索引） | 良好（表达式索引） | 较差（全文扫描） |
| 索引支持 | GIN、表达式索引 | 表达式索引 | 无JSON索引 |
| 操作符支持 | 丰富（@>, <@, ?等） | 基础（json_extract等） | 无 |
| 数据验证 | 自动JSON验证 | 函数验证 | 无验证 |
| 存储空间 | 压缩存储 | 原始JSON | 原始JSON |

### 核心JSONB字段兼容性实现

#### 1. 用户档案字段 (profile) 兼容性

**PostgreSQL实现**：
```sql
-- 创建表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    profile JSONB NOT NULL DEFAULT '{}'
);

-- 创建索引
CREATE INDEX idx_users_profile_gin ON users USING GIN (profile);
CREATE INDEX idx_users_profile_display_name ON users ((profile->>'display_name'));
CREATE INDEX idx_users_profile_locale ON users ((profile->>'locale'));

-- 查询示例
SELECT * FROM users WHERE profile->>'display_name' = '张三';
SELECT * FROM users WHERE profile @> '{"locale": "zh-CN"}';
SELECT * FROM users WHERE profile ? 'avatar';
```

**SQLite实现**：
```sql
-- 创建表
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    profile TEXT NOT NULL DEFAULT '{}'
);

-- 创建索引
CREATE INDEX idx_users_profile_display_name ON users (json_extract(profile, '$.display_name'));
CREATE INDEX idx_users_profile_locale ON users (json_extract(profile, '$.locale'));

-- 查询示例
SELECT * FROM users WHERE json_extract(profile, '$.display_name') = '张三';
SELECT * FROM users WHERE json_extract(profile, '$.locale') = 'zh-CN';
SELECT * FROM users WHERE json_extract(profile, '$.avatar') IS NOT NULL;
```

**Go语言适配层实现**：
```go
// ProfileQuery 档案查询构建器
type ProfileQuery struct {
    adapter database.Adapter
}

func (pq *ProfileQuery) ByDisplayName(name string) string {
    switch pq.adapter.Type() {
    case database.PostgreSQL:
        return "profile->>'display_name' = $1"
    case database.SQLite:
        return "json_extract(profile, '$.display_name') = ?"
    default:
        return ""
    }
}

func (pq *ProfileQuery) ByLocale(locale string) string {
    switch pq.adapter.Type() {
    case database.PostgreSQL:
        return "profile->>'locale' = $1"
    case database.SQLite:
        return "json_extract(profile, '$.locale') = ?"
    default:
        return ""
    }
}

func (pq *ProfileQuery) HasField(field string) string {
    switch pq.adapter.Type() {
    case database.PostgreSQL:
        return fmt.Sprintf("profile ? '%s'", field)
    case database.SQLite:
        return fmt.Sprintf("json_extract(profile, '$.%s') IS NOT NULL", field)
    default:
        return ""
    }
}

func (pq *ProfileQuery) ContainsObject(obj map[string]interface{}) (string, []interface{}) {
    objJSON, _ := json.Marshal(obj)

    switch pq.adapter.Type() {
    case database.PostgreSQL:
        return "profile @> $1", []interface{}{string(objJSON)}
    case database.SQLite:
        // SQLite需要逐个字段检查
        var conditions []string
        var args []interface{}
        for key, value := range obj {
            conditions = append(conditions, fmt.Sprintf("json_extract(profile, '$.%s') = ?", key))
            args = append(args, value)
        }
        return strings.Join(conditions, " AND "), args
    default:
        return "", nil
    }
}
```

#### 2. 世界配置字段 (world_config) 兼容性

**复杂嵌套JSON结构处理**：
```go
// WorldConfigQuery 世界配置查询构建器
type WorldConfigQuery struct {
    adapter database.Adapter
}

func (wcq *WorldConfigQuery) ByGenre(genre string) string {
    switch wcq.adapter.Type() {
    case database.PostgreSQL:
        return "world_config->'theme'->>'genre' = $1"
    case database.SQLite:
        return "json_extract(world_config, '$.theme.genre') = ?"
    default:
        return ""
    }
}

func (wcq *WorldConfigQuery) ByComplexity(complexity string) string {
    switch wcq.adapter.Type() {
    case database.PostgreSQL:
        return "world_config->'theme'->>'complexity' = $1"
    case database.SQLite:
        return "json_extract(world_config, '$.theme.complexity') = ?"
    default:
        return ""
    }
}

func (wcq *WorldConfigQuery) HasMechanic(mechanic string) string {
    switch wcq.adapter.Type() {
    case database.PostgreSQL:
        return "world_config->'mechanics' ? $1"
    case database.SQLite:
        return fmt.Sprintf("json_extract(world_config, '$.mechanics.%s') IS NOT NULL", mechanic)
    default:
        return ""
    }
}

func (wcq *WorldConfigQuery) ByArrayContains(path string, value interface{}) (string, []interface{}) {
    switch wcq.adapter.Type() {
    case database.PostgreSQL:
        return fmt.Sprintf("world_config->'%s' @> $1", path), []interface{}{fmt.Sprintf(`["%v"]`, value)}
    case database.SQLite:
        // SQLite使用json_each检查数组包含
        return fmt.Sprintf(`EXISTS (
            SELECT 1 FROM json_each(world_config, '$.%s')
            WHERE value = ?
        )`, path), []interface{}{value}
    default:
        return "", nil
    }
}
```

#### 3. 场景连接字段 (connections) 兼容性

**复杂数组查询处理**：
```go
// ConnectionQuery 场景连接查询构建器
type ConnectionQuery struct {
    adapter database.Adapter
}

func (cq *ConnectionQuery) ByTargetScene(sceneID string) string {
    switch cq.adapter.Type() {
    case database.PostgreSQL:
        return `EXISTS (
            SELECT 1 FROM jsonb_array_elements(connections) AS conn
            WHERE conn->>'target_scene_id' = $1
        )`
    case database.SQLite:
        return `EXISTS (
            SELECT 1 FROM json_each(connections)
            WHERE json_extract(value, '$.target_scene_id') = ?
        )`
    default:
        return ""
    }
}

func (cq *ConnectionQuery) ByDirection(direction string) string {
    switch cq.adapter.Type() {
    case database.PostgreSQL:
        return `EXISTS (
            SELECT 1 FROM jsonb_array_elements(connections) AS conn
            WHERE conn->'direction'->>'from_direction' = $1
        )`
    case database.SQLite:
        return `EXISTS (
            SELECT 1 FROM json_each(connections)
            WHERE json_extract(value, '$.direction.from_direction') = ?
        )`
    default:
        return ""
    }
}

func (cq *ConnectionQuery) AccessibleConnections() string {
    switch cq.adapter.Type() {
    case database.PostgreSQL:
        return `(
            SELECT jsonb_agg(conn) FROM jsonb_array_elements(connections) AS conn
            WHERE (conn->'accessibility'->>'is_accessible')::boolean = true
        )`
    case database.SQLite:
        return `(
            SELECT json_group_array(value) FROM json_each(connections)
            WHERE json_extract(value, '$.accessibility.is_accessible') = 'true'
        )`
    default:
        return ""
    }
}
```

### JSON数据验证和约束

#### 1. 应用层JSON Schema验证

```go
package validation

import (
    "encoding/json"
    "fmt"
    "github.com/xeipuuv/gojsonschema"
)

// JSONValidator JSON数据验证器
type JSONValidator struct {
    schemas map[string]*gojsonschema.Schema
}

func NewJSONValidator() *JSONValidator {
    validator := &JSONValidator{
        schemas: make(map[string]*gojsonschema.Schema),
    }

    // 加载预定义的Schema
    validator.loadSchemas()
    return validator
}

func (jv *JSONValidator) loadSchemas() {
    // 用户档案Schema
    profileSchema := `{
        "type": "object",
        "properties": {
            "display_name": {"type": "string", "minLength": 1, "maxLength": 50},
            "avatar": {
                "type": "object",
                "properties": {
                    "url": {"type": "string", "format": "uri"},
                    "source": {"type": "string", "enum": ["external", "uploaded", "generated"]}
                }
            },
            "locale": {"type": "string", "pattern": "^[a-z]{2}-[A-Z]{2}$"},
            "timezone": {"type": "string"},
            "bio": {"type": "string", "maxLength": 500}
        },
        "required": ["display_name"]
    }`

    schema, _ := gojsonschema.NewSchema(gojsonschema.NewStringLoader(profileSchema))
    jv.schemas["user_profile"] = schema

    // 世界配置Schema
    worldConfigSchema := `{
        "type": "object",
        "properties": {
            "theme": {
                "type": "object",
                "properties": {
                    "genre": {"type": "string", "enum": ["fantasy", "sci-fi", "historical", "modern", "custom"]},
                    "mood": {"type": "string", "enum": ["light", "dark", "neutral", "mixed"]},
                    "complexity": {"type": "string", "enum": ["simple", "medium", "complex"]}
                },
                "required": ["genre"]
            },
            "rules": {
                "type": "object",
                "properties": {
                    "narrative_style": {"type": "string"},
                    "content_rating": {"type": "string", "enum": ["family", "teen", "mature"]},
                    "ai_creativity": {"type": "string", "enum": ["conservative", "balanced", "creative"]}
                }
            }
        },
        "required": ["theme"]
    }`

    schema, _ = gojsonschema.NewSchema(gojsonschema.NewStringLoader(worldConfigSchema))
    jv.schemas["world_config"] = schema
}

func (jv *JSONValidator) ValidateUserProfile(profile map[string]interface{}) error {
    return jv.validate("user_profile", profile)
}

func (jv *JSONValidator) ValidateWorldConfig(config map[string]interface{}) error {
    return jv.validate("world_config", config)
}

func (jv *JSONValidator) validate(schemaName string, data interface{}) error {
    schema, exists := jv.schemas[schemaName]
    if !exists {
        return fmt.Errorf("未找到Schema: %s", schemaName)
    }

    dataJSON, err := json.Marshal(data)
    if err != nil {
        return fmt.Errorf("数据序列化失败: %w", err)
    }

    result, err := schema.Validate(gojsonschema.NewBytesLoader(dataJSON))
    if err != nil {
        return fmt.Errorf("验证失败: %w", err)
    }

    if !result.Valid() {
        var errors []string
        for _, desc := range result.Errors() {
            errors = append(errors, desc.String())
        }
        return fmt.Errorf("数据验证失败: %s", strings.Join(errors, "; "))
    }

    return nil
}
```

#### 2. 数据库层约束检查

**PostgreSQL约束**：
```sql
-- 使用CHECK约束验证JSON结构
ALTER TABLE users ADD CONSTRAINT check_profile_display_name
CHECK (profile->>'display_name' IS NOT NULL AND length(profile->>'display_name') > 0);

ALTER TABLE users ADD CONSTRAINT check_profile_locale
CHECK (profile->>'locale' ~ '^[a-z]{2}-[A-Z]{2}$');

ALTER TABLE worlds ADD CONSTRAINT check_world_config_theme
CHECK (world_config->'theme'->>'genre' IN ('fantasy', 'sci-fi', 'historical', 'modern', 'custom'));
```

**SQLite约束（通过触发器实现）**：
```sql
-- 创建验证触发器
CREATE TRIGGER validate_user_profile_insert
BEFORE INSERT ON users
FOR EACH ROW
BEGIN
    SELECT CASE
        WHEN json_extract(NEW.profile, '$.display_name') IS NULL OR
             length(json_extract(NEW.profile, '$.display_name')) = 0
        THEN RAISE(ABORT, '用户显示名称不能为空')

        WHEN json_extract(NEW.profile, '$.locale') IS NOT NULL AND
             json_extract(NEW.profile, '$.locale') NOT GLOB '[a-z][a-z]-[A-Z][A-Z]'
        THEN RAISE(ABORT, '用户语言设置格式不正确')
    END;
END;

CREATE TRIGGER validate_user_profile_update
BEFORE UPDATE ON users
FOR EACH ROW
BEGIN
    SELECT CASE
        WHEN json_extract(NEW.profile, '$.display_name') IS NULL OR
             length(json_extract(NEW.profile, '$.display_name')) = 0
        THEN RAISE(ABORT, '用户显示名称不能为空')

        WHEN json_extract(NEW.profile, '$.locale') IS NOT NULL AND
             json_extract(NEW.profile, '$.locale') NOT GLOB '[a-z][a-z]-[A-Z][A-Z]'
        THEN RAISE(ABORT, '用户语言设置格式不正确')
    END;
END;
```

## 第六部分：高级功能兼容性方案

### 分区表兼容性方案

#### PostgreSQL分区实现
```sql
-- 创建分区主表
CREATE TABLE game_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- 其他字段...
) PARTITION BY RANGE (created_at);

-- 创建分区
CREATE TABLE game_events_2024_01 PARTITION OF game_events
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE game_events_2024_02 PARTITION OF game_events
FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

-- 自动分区管理
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
RETURNS void AS $$
DECLARE
    partition_name text;
    end_date date;
BEGIN
    partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM');
    end_date := start_date + interval '1 month';

    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF %I
                    FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;
```

#### SQLite分区模拟实现
```go
// SQLitePartitionManager SQLite分区管理器
type SQLitePartitionManager struct {
    db *sql.DB
}

func (spm *SQLitePartitionManager) CreatePartitionedTable(tableName string, partitionColumn string) error {
    // SQLite使用视图和多个物理表模拟分区

    // 1. 创建主视图
    createViewSQL := fmt.Sprintf(`
        CREATE VIEW IF NOT EXISTS %s AS
        SELECT * FROM %s_current
        UNION ALL
        SELECT * FROM %s_archive_2024_01
        UNION ALL
        SELECT * FROM %s_archive_2024_02
    `, tableName, tableName, tableName, tableName)

    _, err := spm.db.Exec(createViewSQL)
    if err != nil {
        return err
    }

    // 2. 创建当前数据表
    createCurrentSQL := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s_current (
            id TEXT PRIMARY KEY,
            world_id TEXT NOT NULL,
            event_type VARCHAR(50) NOT NULL,
            created_at TEXT DEFAULT (datetime('now')),
            -- 其他字段...
        )
    `, tableName)

    _, err = spm.db.Exec(createCurrentSQL)
    return err
}

func (spm *SQLitePartitionManager) ArchiveOldData(tableName string, cutoffDate time.Time) error {
    // 1. 创建归档表
    archiveTableName := fmt.Sprintf("%s_archive_%s", tableName, cutoffDate.Format("2006_01"))

    createArchiveSQL := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s AS
        SELECT * FROM %s_current WHERE created_at < ?
    `, archiveTableName, tableName)

    _, err := spm.db.Exec(createArchiveSQL, cutoffDate.Format(time.RFC3339))
    if err != nil {
        return err
    }

    // 2. 从当前表删除旧数据
    deleteSQL := fmt.Sprintf("DELETE FROM %s_current WHERE created_at < ?", tableName)
    _, err = spm.db.Exec(deleteSQL, cutoffDate.Format(time.RFC3339))
    if err != nil {
        return err
    }

    // 3. 更新视图定义
    return spm.updatePartitionView(tableName)
}

func (spm *SQLitePartitionManager) updatePartitionView(tableName string) error {
    // 获取所有归档表
    archiveTables, err := spm.getArchiveTables(tableName)
    if err != nil {
        return err
    }

    // 重建视图
    dropViewSQL := fmt.Sprintf("DROP VIEW IF EXISTS %s", tableName)
    _, err = spm.db.Exec(dropViewSQL)
    if err != nil {
        return err
    }

    var unionClauses []string
    unionClauses = append(unionClauses, fmt.Sprintf("SELECT * FROM %s_current", tableName))

    for _, archiveTable := range archiveTables {
        unionClauses = append(unionClauses, fmt.Sprintf("SELECT * FROM %s", archiveTable))
    }

    createViewSQL := fmt.Sprintf("CREATE VIEW %s AS %s",
        tableName, strings.Join(unionClauses, " UNION ALL "))

    _, err = spm.db.Exec(createViewSQL)
    return err
}
```

### 物化视图兼容性方案

#### PostgreSQL物化视图
```sql
-- 创建物化视图
CREATE MATERIALIZED VIEW world_stats AS
SELECT
    w.id as world_id,
    w.name as world_name,
    COUNT(DISTINCT c.user_id) FILTER (WHERE c.status = 'active') as current_players,
    COUNT(DISTINCT c.id) FILTER (WHERE c.status = 'active') as active_characters,
    COUNT(DISTINCT s.id) as total_scenes,
    MAX(ge.game_time) as latest_game_time
FROM worlds w
LEFT JOIN characters c ON w.id = c.world_id
LEFT JOIN scenes s ON w.id = s.world_id
LEFT JOIN game_events ge ON w.id = ge.world_id
WHERE w.status = 'active'
GROUP BY w.id, w.name;

-- 创建唯一索引支持并发刷新
CREATE UNIQUE INDEX idx_world_stats_world_id ON world_stats (world_id);

-- 定期刷新
CREATE OR REPLACE FUNCTION refresh_world_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY world_stats;
END;
$$ LANGUAGE plpgsql;
```

#### SQLite物化视图模拟
```go
// MaterializedViewManager 物化视图管理器
type MaterializedViewManager struct {
    db      *sql.DB
    views   map[string]*MaterializedView
    mutex   sync.RWMutex
}

type MaterializedView struct {
    Name         string
    Query        string
    RefreshSQL   string
    LastRefresh  time.Time
    RefreshInterval time.Duration
}

func NewMaterializedViewManager(db *sql.DB) *MaterializedViewManager {
    return &MaterializedViewManager{
        db:    db,
        views: make(map[string]*MaterializedView),
    }
}

func (mvm *MaterializedViewManager) CreateMaterializedView(name, query string, refreshInterval time.Duration) error {
    mvm.mutex.Lock()
    defer mvm.mutex.Unlock()

    // 1. 创建物理表存储视图数据
    tableName := fmt.Sprintf("mv_%s", name)

    // 2. 执行查询获取结构
    rows, err := mvm.db.Query(fmt.Sprintf("SELECT * FROM (%s) LIMIT 0", query))
    if err != nil {
        return err
    }
    defer rows.Close()

    columns, err := rows.Columns()
    if err != nil {
        return err
    }

    // 3. 创建表结构
    var columnDefs []string
    for _, col := range columns {
        columnDefs = append(columnDefs, fmt.Sprintf("%s TEXT", col))
    }

    createTableSQL := fmt.Sprintf("CREATE TABLE IF NOT EXISTS %s (%s)",
        tableName, strings.Join(columnDefs, ", "))

    _, err = mvm.db.Exec(createTableSQL)
    if err != nil {
        return err
    }

    // 4. 创建视图记录
    refreshSQL := fmt.Sprintf(`
        DELETE FROM %s;
        INSERT INTO %s %s;
    `, tableName, tableName, query)

    view := &MaterializedView{
        Name:            name,
        Query:           query,
        RefreshSQL:      refreshSQL,
        RefreshInterval: refreshInterval,
    }

    mvm.views[name] = view

    // 5. 初始刷新
    return mvm.RefreshView(name)
}

func (mvm *MaterializedViewManager) RefreshView(name string) error {
    mvm.mutex.RLock()
    view, exists := mvm.views[name]
    mvm.mutex.RUnlock()

    if !exists {
        return fmt.Errorf("物化视图不存在: %s", name)
    }

    // 在事务中执行刷新
    tx, err := mvm.db.Begin()
    if err != nil {
        return err
    }
    defer tx.Rollback()

    // 执行刷新SQL
    _, err = tx.Exec(view.RefreshSQL)
    if err != nil {
        return err
    }

    err = tx.Commit()
    if err != nil {
        return err
    }

    // 更新刷新时间
    mvm.mutex.Lock()
    view.LastRefresh = time.Now()
    mvm.mutex.Unlock()

    return nil
}

func (mvm *MaterializedViewManager) StartAutoRefresh(ctx context.Context) {
    ticker := time.NewTicker(time.Minute) // 每分钟检查一次
    defer ticker.Stop()

    for {
        select {
        case <-ctx.Done():
            return
        case <-ticker.C:
            mvm.checkAndRefreshViews()
        }
    }
}

func (mvm *MaterializedViewManager) checkAndRefreshViews() {
    mvm.mutex.RLock()
    viewsToRefresh := make([]*MaterializedView, 0)

    for _, view := range mvm.views {
        if time.Since(view.LastRefresh) >= view.RefreshInterval {
            viewsToRefresh = append(viewsToRefresh, view)
        }
    }
    mvm.mutex.RUnlock()

    for _, view := range viewsToRefresh {
        if err := mvm.RefreshView(view.Name); err != nil {
            log.Printf("刷新物化视图失败 %s: %v", view.Name, err)
        }
    }
}
```

### 全文搜索兼容性方案

#### PostgreSQL全文搜索
```sql
-- 创建全文搜索索引
ALTER TABLE scenes ADD COLUMN search_vector tsvector;

-- 更新搜索向量
UPDATE scenes SET search_vector =
    to_tsvector('chinese', coalesce(name, '') || ' ' || coalesce(description, ''));

-- 创建GIN索引
CREATE INDEX idx_scenes_search_vector ON scenes USING GIN (search_vector);

-- 搜索查询
SELECT *, ts_rank(search_vector, query) as rank
FROM scenes, plainto_tsquery('chinese', '森林 神秘') query
WHERE search_vector @@ query
ORDER BY rank DESC;
```

#### SQLite全文搜索（FTS5）
```sql
-- 创建FTS5虚拟表
CREATE VIRTUAL TABLE scenes_fts USING fts5(
    scene_id UNINDEXED,
    name,
    description,
    content='scenes',
    content_rowid='rowid'
);

-- 创建触发器保持同步
CREATE TRIGGER scenes_fts_insert AFTER INSERT ON scenes BEGIN
    INSERT INTO scenes_fts(scene_id, name, description)
    VALUES (new.id, new.name, new.description);
END;

CREATE TRIGGER scenes_fts_update AFTER UPDATE ON scenes BEGIN
    UPDATE scenes_fts SET name = new.name, description = new.description
    WHERE scene_id = new.id;
END;

CREATE TRIGGER scenes_fts_delete AFTER DELETE ON scenes BEGIN
    DELETE FROM scenes_fts WHERE scene_id = old.id;
END;

-- 搜索查询
SELECT s.*, rank
FROM scenes s
JOIN (
    SELECT scene_id, rank
    FROM scenes_fts
    WHERE scenes_fts MATCH '森林 AND 神秘'
    ORDER BY rank
) fts ON s.id = fts.scene_id;
```

## 第七部分：配置管理和部署策略

### 环境配置管理

```go
package config

import (
    "fmt"
    "os"
    "strconv"
    "time"
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
    Type                string        `json:"type"`                  // postgresql, sqlite
    DSN                 string        `json:"dsn"`                   // 连接字符串
    MaxOpenConns        int           `json:"max_open_conns"`        // 最大连接数
    MaxIdleConns        int           `json:"max_idle_conns"`        // 最大空闲连接数
    ConnMaxLifetime     time.Duration `json:"conn_max_lifetime"`     // 连接最大生命周期
    EnablePartitioning  bool          `json:"enable_partitioning"`   // 是否启用分区
    EnableMaterializedViews bool      `json:"enable_materialized_views"` // 是否启用物化视图
    JSONIndexPaths      []string      `json:"json_index_paths"`      // JSON索引路径
}

// Config 应用配置
type Config struct {
    Environment string         `json:"environment"` // development, production
    Database    DatabaseConfig `json:"database"`
    Cache       CacheConfig    `json:"cache"`
    AI          AIConfig       `json:"ai"`
}

func LoadConfig() (*Config, error) {
    config := &Config{}

    // 从环境变量加载配置
    config.Environment = getEnv("ENVIRONMENT", "development")

    // 数据库配置
    config.Database = DatabaseConfig{
        Type:                getEnv("DB_TYPE", "sqlite"),
        DSN:                 getEnv("DB_DSN", "game.db"),
        MaxOpenConns:        getEnvInt("DB_MAX_OPEN_CONNS", 25),
        MaxIdleConns:        getEnvInt("DB_MAX_IDLE_CONNS", 5),
        ConnMaxLifetime:     getEnvDuration("DB_CONN_MAX_LIFETIME", time.Hour),
        EnablePartitioning:  getEnvBool("DB_ENABLE_PARTITIONING", false),
        EnableMaterializedViews: getEnvBool("DB_ENABLE_MATERIALIZED_VIEWS", false),
        JSONIndexPaths:      getEnvStringSlice("DB_JSON_INDEX_PATHS", []string{"display_name", "locale", "genre"}),
    }

    // 根据环境调整默认配置
    if config.Environment == "production" {
        if config.Database.Type == "" {
            config.Database.Type = "postgresql"
        }
        config.Database.EnablePartitioning = true
        config.Database.EnableMaterializedViews = true
        config.Database.MaxOpenConns = 100
        config.Database.MaxIdleConns = 20
    }

    return config, nil
}

func getEnv(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
    if value := os.Getenv(key); value != "" {
        if intValue, err := strconv.Atoi(value); err == nil {
            return intValue
        }
    }
    return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
    if value := os.Getenv(key); value != "" {
        if boolValue, err := strconv.ParseBool(value); err == nil {
            return boolValue
        }
    }
    return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
    if value := os.Getenv(key); value != "" {
        if duration, err := time.ParseDuration(value); err == nil {
            return duration
        }
    }
    return defaultValue
}

func getEnvStringSlice(key string, defaultValue []string) []string {
    if value := os.Getenv(key); value != "" {
        return strings.Split(value, ",")
    }
    return defaultValue
}
```

### Docker部署配置

**开发环境 (docker-compose.dev.yml)**：
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=development
      - DB_TYPE=sqlite
      - DB_DSN=./data/game.db
      - DB_ENABLE_PARTITIONING=false
      - DB_ENABLE_MATERIALIZED_VIEWS=false
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

**生产环境 (docker-compose.prod.yml)**：
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=production
      - DB_TYPE=postgresql
      - DB_DSN=**************************************/gamedb?sslmode=require
      - DB_MAX_OPEN_CONNS=100
      - DB_MAX_IDLE_CONNS=20
      - DB_ENABLE_PARTITIONING=true
      - DB_ENABLE_MATERIALIZED_VIEWS=true
      - DB_JSON_INDEX_PATHS=display_name,locale,genre,complexity
    depends_on:
      - postgres
      - redis
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=gamedb
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

volumes:
  postgres_data:
  redis_data:
```

### 数据库初始化脚本

**init.sql (PostgreSQL初始化)**：
```sql
-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建数据库用户
CREATE USER game_app WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE gamedb TO game_app;
GRANT USAGE ON SCHEMA public TO game_app;
GRANT CREATE ON SCHEMA public TO game_app;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO game_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO game_app;

-- 创建分区管理函数
CREATE OR REPLACE FUNCTION create_monthly_partitions(table_name text, months_ahead integer DEFAULT 3)
RETURNS void AS $$
DECLARE
    start_date date;
    end_date date;
    partition_name text;
    i integer;
BEGIN
    start_date := date_trunc('month', CURRENT_DATE);

    FOR i IN 0..months_ahead LOOP
        end_date := start_date + interval '1 month';
        partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM');

        EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF %I
                        FOR VALUES FROM (%L) TO (%L)',
                       partition_name, table_name, start_date, end_date);

        start_date := end_date;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 创建自动分区维护任务
CREATE OR REPLACE FUNCTION maintain_partitions()
RETURNS void AS $$
BEGIN
    -- 为游戏事件表创建未来3个月的分区
    PERFORM create_monthly_partitions('game_events', 3);

    -- 为AI交互表创建未来3个月的分区
    PERFORM create_monthly_partitions('ai_interactions', 3);

    -- 删除6个月前的旧分区
    PERFORM drop_old_partitions('game_events', 6);
    PERFORM drop_old_partitions('ai_interactions', 6);
END;
$$ LANGUAGE plpgsql;

-- 设置定时任务（需要pg_cron扩展）
-- SELECT cron.schedule('maintain-partitions', '0 2 1 * *', 'SELECT maintain_partitions();');
```

这个兼容性设计文档提供了完整的PostgreSQL和SQLite兼容性解决方案，通过混合适配方案既保证了生产环境的高性能，又满足了开发环境的轻量级需求。
