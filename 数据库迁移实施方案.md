# 数据库迁移实施方案

**版本**: v1.0  
**创建日期**: 2025-01-08  
**适用范围**: AI文本游戏数据库优化迁移

## 1. 迁移概述

### 1.1 迁移目标
- 解决数据冗余问题，优化存储结构
- 提升查询性能，添加必要索引
- 增强扩展性，支持复杂游戏场景
- 完善事件处理结果存储机制
- 优化多角色用户支持

### 1.2 迁移范围
- **表结构变更**: 12个核心表的结构优化
- **新增表**: 7个新表支持扩展功能
- **索引优化**: 新增40+个性能优化索引
- **数据迁移**: 现有数据的结构调整和清理
- **约束添加**: 数据完整性约束的完善

## 2. 迁移步骤详解

### 2.1 准备阶段 (Pre-Migration)

#### 步骤1: 环境准备
```bash
# 1. 备份现有数据库
pg_dump -h localhost -U postgres -d ai_text_game > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 创建迁移测试环境
createdb ai_text_game_migration_test
psql -d ai_text_game_migration_test -f backup_$(date +%Y%m%d_%H%M%S).sql

# 3. 验证备份完整性
psql -d ai_text_game_migration_test -c "SELECT COUNT(*) FROM users;"
```

#### 步骤2: 依赖检查
```sql
-- 检查外键依赖关系
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY';

-- 检查数据量
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples
FROM pg_stat_user_tables
ORDER BY n_live_tup DESC;
```

### 2.2 执行阶段 (Migration Execution)

#### 阶段1: 表结构优化 (预计耗时: 30分钟)
```sql
-- 1. 优化scenes表 - 添加新字段和约束
ALTER TABLE scenes 
ADD COLUMN access_level VARCHAR(20) DEFAULT 'public',
ADD COLUMN scene_state VARCHAR(20) DEFAULT 'active',
ADD COLUMN max_occupants INTEGER DEFAULT 50,
ADD COLUMN current_occupants INTEGER DEFAULT 0;

-- 添加唯一性约束
ALTER TABLE scenes 
ADD CONSTRAINT scenes_name_world_unique UNIQUE (world_id, name);

-- 2. 优化characters表 - 支持多角色用户
ALTER TABLE characters 
ADD COLUMN is_primary BOOLEAN DEFAULT false,
ADD COLUMN display_order INTEGER DEFAULT 0;

-- 添加约束
ALTER TABLE characters 
ADD CONSTRAINT characters_world_name_unique UNIQUE (world_id, name),
ADD CONSTRAINT characters_primary_unique UNIQUE (user_id, world_id, is_primary) DEFERRABLE INITIALLY DEFERRED;

-- 3. 优化entities表 - 消除位置冗余
ALTER TABLE entities 
DROP COLUMN location_type,
DROP COLUMN location_id,
ADD COLUMN current_scene_id UUID REFERENCES scenes(id),
ADD COLUMN owner_character_id UUID REFERENCES characters(id),
ADD COLUMN container_entity_id UUID REFERENCES entities(id),
ADD COLUMN visibility VARCHAR(20) DEFAULT 'visible',
ADD COLUMN version INTEGER DEFAULT 1;

-- 添加位置约束
ALTER TABLE entities 
ADD CONSTRAINT entities_single_location CHECK (
    (current_scene_id IS NOT NULL)::INTEGER + 
    (owner_character_id IS NOT NULL)::INTEGER + 
    (container_entity_id IS NOT NULL)::INTEGER <= 1
);
```

#### 阶段2: 新增专门表 (预计耗时: 20分钟)
```sql
-- 创建物品专门表
CREATE TABLE items (
    entity_id UUID PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
    item_category VARCHAR(50) NOT NULL,
    item_subcategory VARCHAR(50),
    durability INTEGER DEFAULT 100 CHECK (durability >= 0 AND durability <= 100),
    max_durability INTEGER DEFAULT 100,
    stack_size INTEGER DEFAULT 1 CHECK (stack_size >= 1),
    current_stack INTEGER DEFAULT 1,
    weight FLOAT DEFAULT 1.0 CHECK (weight >= 0),
    rarity VARCHAR(20) DEFAULT 'common',
    base_value INTEGER DEFAULT 0,
    is_craftable BOOLEAN DEFAULT false,
    crafting_recipe JSONB DEFAULT '{}',
    usage_effects JSONB DEFAULT '[]',
    usage_cooldown INTEGER DEFAULT 0,
    is_equippable BOOLEAN DEFAULT false,
    equipment_slot VARCHAR(20),
    stat_modifiers JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建事件专门表
CREATE TABLE events (
    entity_id UUID PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
    event_category VARCHAR(50) NOT NULL,
    event_subcategory VARCHAR(50),
    trigger_conditions JSONB DEFAULT '[]',
    duration_minutes INTEGER DEFAULT 0,
    max_participants INTEGER DEFAULT 1,
    min_participants INTEGER DEFAULT 1,
    success_conditions JSONB DEFAULT '[]',
    failure_conditions JSONB DEFAULT '[]',
    partial_success_conditions JSONB DEFAULT '[]',
    success_rewards JSONB DEFAULT '[]',
    failure_penalties JSONB DEFAULT '[]',
    is_repeatable BOOLEAN DEFAULT true,
    cooldown_minutes INTEGER DEFAULT 0,
    last_triggered TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建目标专门表
CREATE TABLE goals (
    entity_id UUID PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
    goal_type VARCHAR(50) NOT NULL,
    goal_scope VARCHAR(20) DEFAULT 'personal',
    priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 10),
    difficulty VARCHAR(20) DEFAULT 'normal',
    estimated_duration INTEGER,
    deadline TIMESTAMP WITH TIME ZONE,
    is_time_sensitive BOOLEAN DEFAULT false,
    completion_criteria JSONB NOT NULL,
    progress_tracking JSONB DEFAULT '{}',
    current_progress FLOAT DEFAULT 0.0 CHECK (current_progress >= 0 AND current_progress <= 1),
    rewards JSONB DEFAULT '[]',
    prerequisites JSONB DEFAULT '[]',
    goal_status VARCHAR(20) DEFAULT 'active',
    completion_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 阶段3: 事件处理结果表 (预计耗时: 15分钟)
```sql
-- 创建事件处理结果表
CREATE TABLE event_processing_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES entities(id),
    world_id UUID NOT NULL REFERENCES worlds(id),
    processing_batch_id UUID NOT NULL,
    processing_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    processing_duration_ms INTEGER,
    estimated_duration_ms INTEGER,
    success_status VARCHAR(20) NOT NULL DEFAULT 'unknown',
    result_data JSONB DEFAULT '{}',
    narrative_output TEXT,
    state_changes JSONB DEFAULT '[]',
    error_code VARCHAR(50),
    error_message TEXT,
    error_details JSONB DEFAULT '{}',
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    ai_request_id UUID,
    ai_token_usage INTEGER DEFAULT 0,
    ai_response_time_ms INTEGER,
    participating_characters JSONB DEFAULT '[]',
    affected_scenes JSONB DEFAULT '[]',
    affected_entities JSONB DEFAULT '[]',
    processing_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建事件处理步骤表
CREATE TABLE event_processing_steps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    result_id UUID NOT NULL REFERENCES event_processing_results(id) ON DELETE CASCADE,
    step_order INTEGER NOT NULL,
    step_type VARCHAR(50) NOT NULL,
    step_name VARCHAR(100) NOT NULL,
    step_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    execution_time_ms INTEGER,
    input_data JSONB DEFAULT '{}',
    output_data JSONB DEFAULT '{}',
    error_message TEXT,
    error_details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建事件耗时统计表
CREATE TABLE event_timing_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(50) NOT NULL,
    world_id UUID NOT NULL REFERENCES worlds(id),
    game_time_start BIGINT NOT NULL,
    game_time_end BIGINT,
    game_time_duration BIGINT,
    real_time_duration_ms INTEGER,
    estimated_game_duration BIGINT,
    estimated_real_duration_ms INTEGER,
    participant_count INTEGER DEFAULT 0,
    complexity_score FLOAT DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建用户角色会话表
CREATE TABLE user_character_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    active_character_id UUID REFERENCES characters(id),
    last_character_switch TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    character_preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, world_id)
);
```

#### 阶段4: 记忆和阅历系统优化 (预计耗时: 25分钟)
```sql
-- 优化character_memories表
ALTER TABLE character_memories 
ADD COLUMN clarity_level FLOAT DEFAULT 1.0 CHECK (clarity_level >= 0 AND clarity_level <= 1),
ADD COLUMN base_strength FLOAT DEFAULT 1.0,
ADD COLUMN current_strength FLOAT DEFAULT 1.0,
ADD COLUMN access_count INTEGER DEFAULT 0,
ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- 添加约束
ALTER TABLE character_memories 
ADD CONSTRAINT memories_importance_range CHECK (importance_score >= 0 AND importance_score <= 1),
ADD CONSTRAINT memories_emotional_range CHECK (emotional_impact >= -1 AND emotional_impact <= 1),
ADD CONSTRAINT memories_decay_range CHECK (decay_rate >= 0 AND decay_rate <= 1);

-- 优化character_experiences表
ALTER TABLE character_experiences 
ADD COLUMN total_attempts INTEGER DEFAULT 0,
ADD COLUMN success_rate FLOAT GENERATED ALWAYS AS (
    CASE WHEN total_attempts > 0 THEN success_count::FLOAT / total_attempts ELSE 0 END
) STORED,
ADD COLUMN mastery_threshold INTEGER DEFAULT 10,
ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- 添加约束
ALTER TABLE character_experiences 
ADD CONSTRAINT experiences_proficiency_range CHECK (proficiency_level >= 1 AND proficiency_level <= 100);

-- 更新现有数据
UPDATE character_experiences
SET total_attempts = success_count + failure_count
WHERE total_attempts = 0;
```

#### 阶段5: 索引创建 (预计耗时: 40分钟)
```sql
-- 基础性能优化索引
CREATE INDEX CONCURRENTLY idx_worlds_status_public ON worlds(status, is_public) WHERE status = 'active';
CREATE INDEX CONCURRENTLY idx_scenes_world_type ON scenes(world_id, scene_type);
CREATE INDEX CONCURRENTLY idx_characters_user_world ON characters(user_id, world_id) WHERE user_id IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_characters_primary ON characters(user_id, world_id, is_primary) WHERE is_primary = true;

-- 实体相关索引
CREATE INDEX CONCURRENTLY idx_entities_world_type ON entities(world_id, entity_type);
CREATE INDEX CONCURRENTLY idx_entities_scene ON entities(current_scene_id) WHERE current_scene_id IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_entities_owner ON entities(owner_character_id) WHERE owner_character_id IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_entities_container ON entities(container_entity_id) WHERE container_entity_id IS NOT NULL;

-- 专门实体表索引
CREATE INDEX CONCURRENTLY idx_items_category ON items(item_category);
CREATE INDEX CONCURRENTLY idx_items_rarity ON items(rarity);
CREATE INDEX CONCURRENTLY idx_events_category ON events(event_category);
CREATE INDEX CONCURRENTLY idx_goals_type_status ON goals(goal_type, goal_status);

-- 记忆和阅历系统索引
CREATE INDEX CONCURRENTLY idx_character_memories_strength ON character_memories(current_strength DESC) WHERE current_strength > 0.1;
CREATE INDEX CONCURRENTLY idx_character_experiences_success_rate ON character_experiences(success_rate DESC) WHERE total_attempts > 0;

-- 事件处理结果索引
CREATE INDEX CONCURRENTLY idx_event_results_world_status ON event_processing_results(world_id, processing_status);
CREATE INDEX CONCURRENTLY idx_event_results_batch ON event_processing_results(processing_batch_id);
CREATE INDEX CONCURRENTLY idx_event_steps_result_order ON event_processing_steps(result_id, step_order);

-- 用户会话索引
CREATE INDEX CONCURRENTLY idx_user_sessions_user_world ON user_character_sessions(user_id, world_id);
CREATE INDEX CONCURRENTLY idx_user_sessions_activity ON user_character_sessions(last_activity DESC);

-- 高性能复合索引
CREATE INDEX CONCURRENTLY idx_characters_world_user_active ON characters(world_id, user_id, status) WHERE status = 'active' AND user_id IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_entities_world_type_scene ON entities(world_id, entity_type, current_scene_id) WHERE current_scene_id IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_memories_character_type_strength ON character_memories(character_id, memory_type, current_strength DESC) WHERE current_strength > 0.1;
```

### 2.3 数据迁移阶段 (预计耗时: 60分钟)

#### 步骤1: 实体位置数据迁移
```sql
-- 迁移场景中的实体
UPDATE entities
SET current_scene_id = (location_id::UUID)
WHERE location_type = 'scene' AND location_id IS NOT NULL;

-- 迁移角色拥有的实体
UPDATE entities
SET owner_character_id = (location_id::UUID)
WHERE location_type = 'character' AND location_id IS NOT NULL;

-- 迁移容器中的实体
UPDATE entities
SET container_entity_id = (location_id::UUID)
WHERE location_type = 'inventory' AND location_id IS NOT NULL;

-- 验证迁移结果
SELECT
    COUNT(*) as total_entities,
    COUNT(current_scene_id) as scene_entities,
    COUNT(owner_character_id) as owned_entities,
    COUNT(container_entity_id) as container_entities
FROM entities;
```

#### 步骤2: 专门实体表数据填充
```sql
-- 填充items表
INSERT INTO items (entity_id, item_category, item_subcategory, durability, rarity)
SELECT
    id,
    COALESCE(base_properties->>'category', 'misc') as item_category,
    base_properties->>'subcategory' as item_subcategory,
    COALESCE((base_properties->>'durability')::INTEGER, 100) as durability,
    COALESCE(base_properties->>'rarity', 'common') as rarity
FROM entities
WHERE entity_type = 'item';

-- 填充events表
INSERT INTO events (entity_id, event_category, event_subcategory, duration_minutes, max_participants)
SELECT
    id,
    COALESCE(base_properties->>'category', 'general') as event_category,
    base_properties->>'subcategory' as event_subcategory,
    COALESCE((base_properties->>'duration')::INTEGER, 0) as duration_minutes,
    COALESCE((base_properties->>'max_participants')::INTEGER, 1) as max_participants
FROM entities
WHERE entity_type = 'event';

-- 填充goals表
INSERT INTO goals (entity_id, goal_type, goal_scope, priority, completion_criteria)
SELECT
    id,
    COALESCE(base_properties->>'type', 'personal') as goal_type,
    COALESCE(base_properties->>'scope', 'personal') as goal_scope,
    COALESCE((base_properties->>'priority')::INTEGER, 1) as priority,
    COALESCE(base_properties->'criteria', '{}') as completion_criteria
FROM entities
WHERE entity_type = 'goal';
```

#### 步骤3: 用户角色会话初始化
```sql
-- 为现有用户创建角色会话记录
INSERT INTO user_character_sessions (user_id, world_id, active_character_id)
SELECT DISTINCT
    c.user_id,
    c.world_id,
    c.id as active_character_id
FROM characters c
WHERE c.user_id IS NOT NULL
  AND c.status = 'active'
  AND c.id IN (
      SELECT id FROM characters c2
      WHERE c2.user_id = c.user_id AND c2.world_id = c.world_id
      ORDER BY c2.created_at ASC
      LIMIT 1
  );

-- 设置主角色标记
UPDATE characters
SET is_primary = true
WHERE id IN (
    SELECT active_character_id
    FROM user_character_sessions
    WHERE active_character_id IS NOT NULL
);
```

### 2.4 验证阶段 (预计耗时: 30分钟)

#### 数据完整性验证
```sql
-- 验证外键约束
SELECT
    conname as constraint_name,
    conrelid::regclass as table_name,
    confrelid::regclass as referenced_table
FROM pg_constraint
WHERE contype = 'f'
  AND connamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

-- 验证唯一性约束
SELECT
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
  AND indexdef LIKE '%UNIQUE%'
ORDER BY tablename, indexname;

-- 验证数据一致性
SELECT
    'entities' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN current_scene_id IS NOT NULL THEN 1 END) as scene_count,
    COUNT(CASE WHEN owner_character_id IS NOT NULL THEN 1 END) as owner_count,
    COUNT(CASE WHEN container_entity_id IS NOT NULL THEN 1 END) as container_count
FROM entities
UNION ALL
SELECT
    'characters' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN is_primary = true THEN 1 END) as primary_count,
    COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as player_count,
    COUNT(CASE WHEN user_id IS NULL THEN 1 END) as npc_count
FROM characters;
```

## 3. 风险识别与应对措施

### 3.1 高风险项

#### 风险1: 数据丢失
**风险等级**: 🔴 高
**影响范围**: 全系统
**应对措施**:
- 执行前完整备份数据库
- 在测试环境完整验证迁移流程
- 使用事务确保原子性操作
- 准备回滚脚本

#### 风险2: 服务中断时间过长
**风险等级**: 🟡 中
**影响范围**: 用户体验
**应对措施**:
- 使用CONCURRENTLY创建索引，避免锁表
- 分阶段执行，减少单次停机时间
- 准备快速回滚方案
- 在低峰期执行迁移

#### 风险3: 性能下降
**风险等级**: 🟡 中
**影响范围**: 系统性能
**应对措施**:
- 迁移后立即更新表统计信息
- 监控查询执行计划变化
- 准备性能调优脚本
- 设置性能监控告警

### 3.2 中风险项

#### 风险4: 外键约束冲突
**风险等级**: 🟡 中
**影响范围**: 数据完整性
**应对措施**:
- 迁移前清理孤立数据
- 使用DEFERRABLE约束
- 分步骤添加约束
- 准备约束修复脚本

#### 风险5: 应用程序兼容性
**风险等级**: 🟡 中
**影响范围**: 应用功能
**应对措施**:
- 更新应用程序代码适配新结构
- 保留兼容性视图
- 渐进式部署
- 充分的集成测试

### 3.3 低风险项

#### 风险6: 索引创建失败
**风险等级**: 🟢 低
**影响范围**: 查询性能
**应对措施**:
- 使用CONCURRENTLY避免锁表
- 监控索引创建进度
- 准备重试脚本

## 4. 回滚方案

### 4.1 快速回滚 (紧急情况)
```bash
# 1. 停止应用服务
systemctl stop ai-text-game-api

# 2. 恢复备份数据库
dropdb ai_text_game
createdb ai_text_game
psql -d ai_text_game -f backup_$(date +%Y%m%d_%H%M%S).sql

# 3. 重启应用服务
systemctl start ai-text-game-api
```

### 4.2 部分回滚 (特定问题)
```sql
-- 回滚新增表
DROP TABLE IF EXISTS event_timing_stats CASCADE;
DROP TABLE IF EXISTS event_processing_steps CASCADE;
DROP TABLE IF EXISTS event_processing_results CASCADE;
DROP TABLE IF EXISTS user_character_sessions CASCADE;
DROP TABLE IF EXISTS goals CASCADE;
DROP TABLE IF EXISTS events CASCADE;
DROP TABLE IF EXISTS items CASCADE;

-- 回滚表结构变更
ALTER TABLE entities
ADD COLUMN location_type VARCHAR(20),
ADD COLUMN location_id UUID,
DROP COLUMN current_scene_id,
DROP COLUMN owner_character_id,
DROP COLUMN container_entity_id,
DROP COLUMN visibility,
DROP COLUMN version;

-- 恢复数据
UPDATE entities
SET location_type = 'scene', location_id = current_scene_id::TEXT
WHERE current_scene_id IS NOT NULL;
```

## 5. 监控和验证

### 5.1 迁移过程监控
```sql
-- 监控长时间运行的查询
SELECT
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';

-- 监控锁等待
SELECT
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

### 5.2 迁移后验证
```sql
-- 验证表结构
SELECT
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public'
ORDER BY table_name, ordinal_position;

-- 验证索引效果
EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM characters
WHERE world_id = 'test-world-id' AND user_id = 'test-user-id' AND status = 'active';

-- 验证数据完整性
SELECT
    COUNT(*) as total_entities,
    COUNT(CASE WHEN entity_type = 'item' THEN 1 END) as items,
    COUNT(CASE WHEN entity_type = 'event' THEN 1 END) as events,
    COUNT(CASE WHEN entity_type = 'goal' THEN 1 END) as goals
FROM entities;
```

## 6. 单元测试用例

### 6.1 数据库结构测试

#### 测试用例1: 表结构完整性测试
```go
func TestDatabaseSchemaIntegrity(t *testing.T) {
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    // 测试所有必需表是否存在
    requiredTables := []string{
        "users", "user_stats", "worlds", "scenes", "characters",
        "entities", "items", "events", "goals", "character_memories",
        "character_experiences", "event_processing_results",
        "event_processing_steps", "event_timing_stats",
        "user_character_sessions", "game_events", "ai_interactions",
    }

    for _, tableName := range requiredTables {
        var exists bool
        err := db.Raw(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = ?
            )`, tableName).Scan(&exists).Error

        assert.NoError(t, err)
        assert.True(t, exists, "表 %s 应该存在", tableName)
    }
}

func TestForeignKeyConstraints(t *testing.T) {
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    // 测试外键约束
    testCases := []struct {
        table      string
        column     string
        refTable   string
        refColumn  string
    }{
        {"characters", "world_id", "worlds", "id"},
        {"characters", "user_id", "users", "id"},
        {"characters", "current_scene_id", "scenes", "id"},
        {"entities", "world_id", "worlds", "id"},
        {"entities", "current_scene_id", "scenes", "id"},
        {"entities", "owner_character_id", "characters", "id"},
        {"items", "entity_id", "entities", "id"},
        {"events", "entity_id", "entities", "id"},
        {"goals", "entity_id", "entities", "id"},
    }

    for _, tc := range testCases {
        var constraintExists bool
        err := db.Raw(`
            SELECT EXISTS (
                SELECT 1 FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage ccu
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_name = ?
                    AND kcu.column_name = ?
                    AND ccu.table_name = ?
                    AND ccu.column_name = ?
            )`, tc.table, tc.column, tc.refTable, tc.refColumn).Scan(&constraintExists).Error

        assert.NoError(t, err)
        assert.True(t, constraintExists,
            "外键约束应该存在: %s.%s -> %s.%s",
            tc.table, tc.column, tc.refTable, tc.refColumn)
    }
}

func TestUniqueConstraints(t *testing.T) {
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    // 测试唯一性约束
    testCases := []struct {
        table   string
        columns []string
        name    string
    }{
        {"scenes", []string{"world_id", "name"}, "场景名称在世界内唯一"},
        {"characters", []string{"world_id", "name"}, "角色名称在世界内唯一"},
        {"characters", []string{"user_id", "world_id", "is_primary"}, "用户在世界内只能有一个主角色"},
        {"user_character_sessions", []string{"user_id", "world_id"}, "用户在世界内只能有一个会话"},
    }

    for _, tc := range testCases {
        var constraintExists bool
        query := `
            SELECT EXISTS (
                SELECT 1 FROM pg_constraint c
                JOIN pg_class t ON c.conrelid = t.oid
                JOIN pg_namespace n ON t.relnamespace = n.oid
                WHERE n.nspname = 'public'
                    AND t.relname = ?
                    AND c.contype = 'u'
            )`

        err := db.Raw(query, tc.table).Scan(&constraintExists).Error
        assert.NoError(t, err)
        assert.True(t, constraintExists, "唯一性约束应该存在: %s", tc.name)
    }
}
```

#### 测试用例2: 索引性能测试
```go
func TestIndexPerformance(t *testing.T) {
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    // 创建测试数据
    setupLargeTestData(t, db)

    // 测试关键查询的性能
    testCases := []struct {
        name  string
        query string
        maxMs int64
    }{
        {
            name: "用户角色查询",
            query: `
                SELECT c.* FROM characters c
                WHERE c.user_id = ? AND c.world_id = ? AND c.status = 'active'
            `,
            maxMs: 10,
        },
        {
            name: "场景实体查询",
            query: `
                SELECT e.* FROM entities e
                WHERE e.world_id = ? AND e.current_scene_id = ? AND e.status = 'active'
            `,
            maxMs: 15,
        },
        {
            name: "角色记忆查询",
            query: `
                SELECT cm.* FROM character_memories cm
                WHERE cm.character_id = ? AND cm.current_strength > 0.3
                ORDER BY cm.importance_score DESC LIMIT 20
            `,
            maxMs: 20,
        },
    }

    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            start := time.Now()

            var result []map[string]interface{}
            err := db.Raw(tc.query, "test-user-id", "test-world-id").Scan(&result).Error

            duration := time.Since(start).Milliseconds()

            assert.NoError(t, err)
            assert.LessOrEqual(t, duration, tc.maxMs,
                "查询 %s 应该在 %dms 内完成，实际耗时 %dms",
                tc.name, tc.maxMs, duration)
        })
    }
}
```

### 6.2 数据完整性测试

#### 测试用例3: 实体位置约束测试
```go
func TestEntityLocationConstraints(t *testing.T) {
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    // 创建测试数据
    world := createTestWorld(t, db)
    scene := createTestScene(t, db, world.ID)
    character := createTestCharacter(t, db, world.ID)

    // 测试实体只能在一个位置
    entity := &Entity{
        ID:                world.ID + "-entity-1",
        WorldID:           world.ID,
        EntityType:        "item",
        Name:              "测试物品",
        CurrentSceneID:    &scene.ID,
        OwnerCharacterID:  &character.ID, // 同时设置两个位置，应该失败
    }

    err := db.Create(entity).Error
    assert.Error(t, err, "实体不能同时在场景和角色处")

    // 测试正确的单一位置
    entity.OwnerCharacterID = nil
    err = db.Create(entity).Error
    assert.NoError(t, err, "实体应该可以在场景中")
}

func TestCharacterPrimaryConstraint(t *testing.T) {
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    // 创建测试数据
    user := createTestUser(t, db)
    world := createTestWorld(t, db)

    // 创建第一个主角色
    char1 := &Character{
        ID:        world.ID + "-char-1",
        WorldID:   world.ID,
        UserID:    &user.ID,
        Name:      "主角色1",
        IsPrimary: true,
    }
    err := db.Create(char1).Error
    assert.NoError(t, err)

    // 尝试创建第二个主角色，应该失败
    char2 := &Character{
        ID:        world.ID + "-char-2",
        WorldID:   world.ID,
        UserID:    &user.ID,
        Name:      "主角色2",
        IsPrimary: true,
    }
    err = db.Create(char2).Error
    assert.Error(t, err, "用户在同一世界不能有两个主角色")
}
```

### 6.3 业务逻辑测试

#### 测试用例4: 多角色用户支持测试
```go
func TestMultiCharacterUserSupport(t *testing.T) {
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    // 创建测试数据
    user := createTestUser(t, db)
    world := createTestWorld(t, db)

    // 用户可以在同一世界创建多个角色
    characters := []Character{
        {
            ID:        world.ID + "-char-1",
            WorldID:   world.ID,
            UserID:    &user.ID,
            Name:      "战士",
            IsPrimary: true,
        },
        {
            ID:        world.ID + "-char-2",
            WorldID:   world.ID,
            UserID:    &user.ID,
            Name:      "法师",
            IsPrimary: false,
        },
        {
            ID:        world.ID + "-char-3",
            WorldID:   world.ID,
            UserID:    &user.ID,
            Name:      "盗贼",
            IsPrimary: false,
        },
    }

    for _, char := range characters {
        err := db.Create(&char).Error
        assert.NoError(t, err, "应该能创建角色: %s", char.Name)
    }

    // 验证用户角色会话
    session := &UserCharacterSession{
        UserID:            user.ID,
        WorldID:           world.ID,
        ActiveCharacterID: &characters[0].ID,
    }
    err := db.Create(session).Error
    assert.NoError(t, err)

    // 测试角色切换
    session.ActiveCharacterID = &characters[1].ID
    err = db.Save(session).Error
    assert.NoError(t, err)

    // 验证查询用户的所有角色
    var userChars []Character
    err = db.Where("user_id = ? AND world_id = ?", user.ID, world.ID).Find(&userChars).Error
    assert.NoError(t, err)
    assert.Len(t, userChars, 3, "用户应该有3个角色")
}
```

#### 测试用例5: 事件处理结果测试
```go
func TestEventProcessingResults(t *testing.T) {
    db := setupTestDB(t)
    defer teardownTestDB(t, db)

    // 创建测试数据
    world := createTestWorld(t, db)
    event := createTestEvent(t, db, world.ID)

    // 创建事件处理结果
    batchID := uuid.New().String()
    result := &EventProcessingResult{
        EventID:           event.ID,
        WorldID:           world.ID,
        ProcessingBatchID: batchID,
        ProcessingStatus:  "pending",
        EstimatedDurationMs: 5000,
    }

    err := db.Create(result).Error
    assert.NoError(t, err)

    // 添加处理步骤
    steps := []EventProcessingStep{
        {
            ResultID:   result.ID,
            StepOrder:  1,
            StepType:   "validation",
            StepName:   "输入验证",
            StepStatus: "completed",
        },
        {
            ResultID:   result.ID,
            StepOrder:  2,
            StepType:   "ai_call",
            StepName:   "AI处理",
            StepStatus: "running",
        },
    }

    for _, step := range steps {
        err := db.Create(&step).Error
        assert.NoError(t, err)
    }

    // 更新处理结果
    result.ProcessingStatus = "completed"
    result.SuccessStatus = "success"
    result.ProcessingDurationMs = 4500
    completedAt := time.Now()
    result.CompletedAt = &completedAt

    err = db.Save(result).Error
    assert.NoError(t, err)

    // 验证查询
    var savedResult EventProcessingResult
    err = db.Preload("Steps").First(&savedResult, "id = ?", result.ID).Error
    assert.NoError(t, err)
    assert.Len(t, savedResult.Steps, 2, "应该有2个处理步骤")
    assert.Equal(t, "completed", savedResult.ProcessingStatus)
}
```

### 6.4 性能基准测试

#### 测试用例6: 大数据量性能测试
```go
func BenchmarkCharacterQueries(b *testing.B) {
    db := setupBenchmarkDB(b)
    defer teardownBenchmarkDB(b, db)

    // 创建大量测试数据
    createBenchmarkData(b, db, 10000) // 10k characters

    b.ResetTimer()

    b.Run("查询用户角色", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            var characters []Character
            db.Where("user_id = ? AND world_id = ? AND status = ?",
                "test-user-1", "test-world-1", "active").Find(&characters)
        }
    })

    b.Run("查询场景实体", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            var entities []Entity
            db.Where("world_id = ? AND current_scene_id = ? AND status = ?",
                "test-world-1", "test-scene-1", "active").Find(&entities)
        }
    })
}

func BenchmarkMemoryQueries(b *testing.B) {
    db := setupBenchmarkDB(b)
    defer teardownBenchmarkDB(b, db)

    createBenchmarkMemoryData(b, db, 50000) // 50k memories

    b.ResetTimer()

    b.Run("查询重要记忆", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            var memories []CharacterMemory
            db.Where("character_id = ? AND current_strength > ?",
                "test-char-1", 0.3).
                Order("importance_score DESC").
                Limit(20).Find(&memories)
        }
    })
}
```

## 7. 迁移时间表

### 7.1 详细时间安排

| 阶段 | 任务 | 预计耗时 | 累计时间 | 负责人 |
|------|------|----------|----------|--------|
| 准备 | 环境准备和备份 | 30分钟 | 30分钟 | DBA |
| 准备 | 依赖检查和验证 | 15分钟 | 45分钟 | 开发 |
| 执行 | 表结构优化 | 30分钟 | 1小时15分钟 | DBA |
| 执行 | 新增专门表 | 20分钟 | 1小时35分钟 | DBA |
| 执行 | 事件处理结果表 | 15分钟 | 1小时50分钟 | DBA |
| 执行 | 记忆阅历系统优化 | 25分钟 | 2小时15分钟 | DBA |
| 执行 | 索引创建 | 40分钟 | 2小时55分钟 | DBA |
| 迁移 | 数据迁移 | 60分钟 | 3小时55分钟 | DBA+开发 |
| 验证 | 完整性验证 | 30分钟 | 4小时25分钟 | 测试 |
| 部署 | 应用程序更新 | 20分钟 | 4小时45分钟 | 开发 |
| 验证 | 功能测试 | 30分钟 | 5小时15分钟 | 测试 |

### 7.2 关键里程碑

- **T+0**: 开始迁移，停止写入服务
- **T+2小时**: 完成表结构变更
- **T+3小时**: 完成索引创建
- **T+4小时**: 完成数据迁移
- **T+4.5小时**: 完成验证，恢复服务
- **T+5小时**: 完成功能测试，迁移成功

## 8. 成功标准

### 8.1 技术指标
- ✅ 所有表结构按设计创建完成
- ✅ 所有索引创建成功，查询性能提升20%以上
- ✅ 数据完整性100%，无数据丢失
- ✅ 所有约束正常工作
- ✅ 应用程序功能正常

### 8.2 业务指标
- ✅ 用户可以正常创建和管理多个角色
- ✅ 场景连接关系支持复杂转换逻辑
- ✅ 事件处理结果完整记录
- ✅ 记忆和阅历系统性能优化
- ✅ 系统响应时间在可接受范围内

### 8.3 验收标准
- ✅ 所有单元测试通过
- ✅ 性能基准测试达标
- ✅ 功能回归测试通过
- ✅ 数据一致性检查通过
- ✅ 用户验收测试通过

通过以上详细的实施方案，可以确保数据库迁移的安全、高效执行，最大程度降低风险，提升系统性能和扩展性。
```
```
