#!/bin/bash

# AI对话接口测试脚本
# 测试流式对话功能的完整性

echo "🤖 AI对话接口测试开始"
echo "================================"

# 配置参数
API_URL="http://localhost:8080/api/chat"
MODEL="deepseek"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local test_name="$1"
    local request_data="$2"
    local expected_status="$3"
    
    echo -e "\n${BLUE}📋 测试: $test_name${NC}"
    echo "请求数据: $request_data"
    
    # 发送请求并获取响应
    response=$(curl -s -w "\n%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "$request_data" \
        "$API_URL")
    
    # 分离响应体和状态码
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "HTTP状态码: $http_code"
    echo "响应内容: $response_body"
    
    # 检查状态码
    if [ "$http_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 测试通过${NC}"
        
        # 如果是成功响应，验证JSON结构
        if [ "$http_code" = "200" ]; then
            validate_json_structure "$response_body"
        fi
    else
        echo -e "${RED}❌ 测试失败 - 期望状态码: $expected_status, 实际: $http_code${NC}"
    fi
}

# 验证JSON结构
validate_json_structure() {
    local json_response="$1"
    
    echo -e "\n${YELLOW}🔍 验证JSON结构...${NC}"
    
    # 检查是否包含必需字段
    if echo "$json_response" | jq -e '.success' > /dev/null 2>&1; then
        echo "✓ 包含success字段"
    else
        echo "✗ 缺少success字段"
    fi
    
    if echo "$json_response" | jq -e '.conversation_id' > /dev/null 2>&1; then
        echo "✓ 包含conversation_id字段"
        CONVERSATION_ID=$(echo "$json_response" | jq -r '.conversation_id')
        echo "对话ID: $CONVERSATION_ID"
    else
        echo "✗ 缺少conversation_id字段"
    fi
    
    if echo "$json_response" | jq -e '.content' > /dev/null 2>&1; then
        echo "✓ 包含content字段"
    else
        echo "✗ 缺少content字段"
    fi
    
    if echo "$json_response" | jq -e '.model' > /dev/null 2>&1; then
        echo "✓ 包含model字段"
    else
        echo "✗ 缺少model字段"
    fi
}

# 测试流式响应
test_stream_api() {
    local test_name="$1"
    local request_data="$2"
    
    echo -e "\n${BLUE}🌊 流式测试: $test_name${NC}"
    echo "请求数据: $request_data"
    
    # 创建临时文件存储响应
    temp_file=$(mktemp)
    
    # 发送流式请求
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "Accept: text/event-stream" \
        -d "$request_data" \
        "$API_URL" > "$temp_file" 2>&1 &
    
    curl_pid=$!
    
    # 等待几秒钟收集响应
    sleep 3
    
    # 检查curl进程是否还在运行
    if kill -0 $curl_pid 2>/dev/null; then
        echo "流式连接建立成功，正在接收数据..."
        sleep 2
        kill $curl_pid 2>/dev/null
    fi
    
    # 检查响应内容
    if [ -s "$temp_file" ]; then
        echo -e "${GREEN}✅ 收到流式响应${NC}"
        echo "响应内容预览:"
        head -n 10 "$temp_file"
        
        # 检查是否包含SSE格式
        if grep -q "data:" "$temp_file"; then
            echo "✓ 包含SSE格式数据"
        else
            echo "✗ 未检测到SSE格式"
        fi
        
        if grep -q "\[DONE\]" "$temp_file"; then
            echo "✓ 包含结束标记"
        else
            echo "✗ 未检测到结束标记"
        fi
    else
        echo -e "${RED}❌ 未收到流式响应${NC}"
    fi
    
    # 清理临时文件
    rm -f "$temp_file"
}

# 检查服务器是否运行
echo "🔍 检查服务器状态..."
if curl -s "$API_URL" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器正在运行${NC}"
else
    echo -e "${RED}❌ 服务器未运行或API不可访问${NC}"
    echo "请确保服务器在 http://localhost:8080 上运行"
    exit 1
fi

# 检查依赖工具
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}⚠️  警告: jq未安装，JSON验证功能将被跳过${NC}"
fi

echo -e "\n${BLUE}开始执行测试用例...${NC}"

# 测试用例1: 基础对话测试
test_api "基础对话测试" '{
    "messages": [
        {
            "role": "user",
            "content": "你好，请介绍一下你自己。"
        }
    ],
    "model": "deepseek",
    "stream": false
}' "200"

# 测试用例2: JSON结构验证测试
test_api "JSON结构验证测试" '{
    "messages": [
        {
            "role": "user",
            "content": "请返回一个包含content、emotion、intent和confidence字段的JSON响应。"
        }
    ],
    "model": "deepseek",
    "stream": false
}' "200"

# 测试用例3: 参数验证测试（缺少必需字段）
test_api "参数验证测试（缺少messages）" '{
    "model": "deepseek",
    "stream": false
}' "400"

# 测试用例4: 参数验证测试（缺少model）
test_api "参数验证测试（缺少model）" '{
    "messages": [
        {
            "role": "user",
            "content": "测试消息"
        }
    ],
    "stream": false
}' "400"

# 测试用例5: 流式响应测试
test_stream_api "流式响应测试" '{
    "messages": [
        {
            "role": "user",
            "content": "请用流式方式回答：什么是人工智能？"
        }
    ],
    "model": "deepseek",
    "stream": true
}'

# 测试用例6: 上下文对话测试（如果有对话ID）
if [ ! -z "$CONVERSATION_ID" ]; then
    test_api "上下文对话测试" '{
        "messages": [
            {
                "role": "user",
                "content": "我刚才问了什么问题？"
            }
        ],
        "conversation_id": "'$CONVERSATION_ID'",
        "model": "deepseek",
        "stream": false
    }' "200"
fi

echo -e "\n${BLUE}================================${NC}"
echo -e "${GREEN}🎉 测试完成！${NC}"
echo ""
echo "📋 测试总结:"
echo "- 基础对话功能测试"
echo "- JSON结构验证测试"
echo "- 参数验证测试"
echo "- 流式响应测试"
echo "- 上下文对话测试"
echo ""
echo "📖 使用说明:"
echo "1. 打开 test_chat_api.html 进行交互式测试"
echo "2. 查看服务器日志了解详细处理过程"
echo "3. 使用不同的模型和参数进行更多测试"
