package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/migration"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/service"
	"ai-text-game-iam-npc/pkg/database"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var (
	appConfig      *config.EnhancedConfig
	dbInstance     *gorm.DB
	serviceManager *service.ServiceManager
	appLogger      Logger
)

// Logger 简单的日志接口实现
type Logger struct{}

func (l Logger) Info(msg string, fields ...interface{}) {
	log.Printf("[INFO] %s %v", msg, fields)
}

func (l Logger) Error(msg string, fields ...interface{}) {
	log.Printf("[ERROR] %s %v", msg, fields)
}

func (l Logger) Warn(msg string, fields ...interface{}) {
	log.Printf("[WARN] %s %v", msg, fields)
}

func (l Logger) Debug(msg string, fields ...interface{}) {
	log.Printf("[DEBUG] %s %v", msg, fields)
}

func main() {
	appLogger = Logger{}
	appLogger.Info("🚀 启动AI文本游戏增强服务器")

	// 1. 加载配置
	if err := loadConfig(); err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := initDatabase(); err != nil {
		log.Fatalf("❌ 初始化数据库失败: %v", err)
	}

	// 3. 执行数据库迁移
	if err := performDatabaseMigration(); err != nil {
		log.Fatalf("❌ 数据库迁移失败: %v", err)
	}

	// 4. 初始化服务管理器
	if err := initServiceManager(); err != nil {
		log.Fatalf("❌ 初始化服务管理器失败: %v", err)
	}

	// 5. 启动HTTP服务器
	if err := startHTTPServer(); err != nil {
		log.Fatalf("❌ 启动HTTP服务器失败: %v", err)
	}
}

// loadConfig 加载配置
func loadConfig() error {
	appLogger.Info("📋 加载应用配置")

	cfg, err := config.LoadEnhancedConfig()
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	if err := cfg.Validate(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	appConfig = cfg

	appLogger.Info("配置加载成功",
		"environment", cfg.Environment,
		"database_type", cfg.Database.Type,
		"server_port", cfg.Server.Port)

	return nil
}

// initDatabase 初始化数据库
func initDatabase() error {
	appLogger.Info("🔗 初始化数据库连接")

	// 转换为旧的配置格式以兼容现有代码
	oldConfig := &config.DatabaseConfig{
		Host:         extractHostFromDSN(appConfig.Database.DSN),
		Port:         extractPortFromDSN(appConfig.Database.DSN),
		User:         extractUserFromDSN(appConfig.Database.DSN),
		Password:     extractPasswordFromDSN(appConfig.Database.DSN),
		DBName:       extractDBNameFromDSN(appConfig.Database.DSN),
		SSLMode:      appConfig.Database.SSLMode,
		MaxOpenConns: appConfig.Database.MaxOpenConns,
		MaxIdleConns: appConfig.Database.MaxIdleConns,
		MaxLifetime:  appConfig.Database.ConnMaxLifetime,
	}

	db, err := database.New(oldConfig)
	if err != nil {
		return fmt.Errorf("创建数据库连接失败: %w", err)
	}

	dbInstance = db

	// 测试数据库连接
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取底层数据库连接失败: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	appLogger.Info("数据库连接成功",
		"type", appConfig.Database.Type,
		"max_open_conns", appConfig.Database.MaxOpenConns,
		"max_idle_conns", appConfig.Database.MaxIdleConns)

	return nil
}

// performDatabaseMigration 执行数据库迁移
func performDatabaseMigration() error {
	appLogger.Info("🚀 开始执行数据库迁移")

	// 创建智能迁移器
	smartMigrator := migration.NewSmartMigrator(dbInstance, &config.DatabaseConfig{
		Host:         extractHostFromDSN(appConfig.Database.DSN),
		Port:         extractPortFromDSN(appConfig.Database.DSN),
		User:         extractUserFromDSN(appConfig.Database.DSN),
		Password:     extractPasswordFromDSN(appConfig.Database.DSN),
		DBName:       extractDBNameFromDSN(appConfig.Database.DSN),
		SSLMode:      appConfig.Database.SSLMode,
		MaxOpenConns: appConfig.Database.MaxOpenConns,
		MaxIdleConns: appConfig.Database.MaxIdleConns,
		MaxLifetime:  appConfig.Database.ConnMaxLifetime,
	}, "migrations")

	// 检查数据库兼容性
	if err := smartMigrator.CheckCompatibility(); err != nil {
		appLogger.Warn("数据库兼容性检查失败，但继续执行", "error", err)
	}

	// 获取数据库信息
	info := smartMigrator.GetDatabaseInfo()
	appLogger.Info("数据库信息",
		"type", info["database_type"],
		"supports_jsonb", info["supports_jsonb"],
		"supports_uuid", info["supports_uuid"])

	// 定义所有需要迁移的模型
	models := []interface{}{
		&models.User{},
		&models.UserStats{},
		&models.World{},
		&models.Scene{},
		&models.Character{},
		&models.Entity{},
		&models.Event{},
		&models.AIInteraction{},
	}

	// 执行自动迁移
	if err := smartMigrator.AutoMigrate(models...); err != nil {
		return fmt.Errorf("数据库迁移失败: %w", err)
	}

	appLogger.Info("数据库迁移完成")
	return nil
}

// initServiceManager 初始化服务管理器
func initServiceManager() error {
	appLogger.Info("🔧 初始化服务管理器")

	serviceManager = service.NewServiceManager(dbInstance, appLogger)

	// 初始化服务管理器（创建索引等）
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := serviceManager.Initialize(ctx); err != nil {
		return fmt.Errorf("初始化服务管理器失败: %w", err)
	}

	// 获取数据库信息
	dbInfo := serviceManager.GetDatabaseInfo()
	appLogger.Info("服务管理器初始化成功",
		"database_type", dbInfo["database_type"],
		"is_postgresql", dbInfo["is_postgresql"],
		"is_sqlite", dbInfo["is_sqlite"])

	return nil
}

// startHTTPServer 启动HTTP服务器
func startHTTPServer() error {
	appLogger.Info("🌐 启动HTTP服务器")

	// 设置Gin模式
	if appConfig.IsProduction() {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// 创建Gin路由器
	router := gin.New()

	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// 添加CORS中间件
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 注册路由
	registerRoutes(router)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", appConfig.Server.Host, appConfig.Server.Port),
		Handler:      router,
		ReadTimeout:  appConfig.Server.ReadTimeout,
		WriteTimeout: appConfig.Server.WriteTimeout,
		IdleTimeout:  appConfig.Server.IdleTimeout,
	}

	// 启动服务器
	go func() {
		appLogger.Info("HTTP服务器启动",
			"host", appConfig.Server.Host,
			"port", appConfig.Server.Port,
			"environment", appConfig.Environment)

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("HTTP服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	appLogger.Info("正在关闭服务器...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		return fmt.Errorf("服务器关闭失败: %w", err)
	}

	// 关闭数据库连接
	if err := database.Close(dbInstance); err != nil {
		appLogger.Error("关闭数据库连接失败", "error", err)
	}

	appLogger.Info("服务器已关闭")
	return nil
}

// registerRoutes 注册路由
func registerRoutes(router *gin.Engine) {
	// 健康检查
	router.GET("/health", healthCheck)

	// API路由组
	api := router.Group("/api/v1")
	{
		// 用户相关路由
		users := api.Group("/users")
		{
			users.POST("", createUser)
			users.GET("/:id", getUserByID)
			users.PUT("/:id/preferences", updateUserPreferences)
			users.GET("/search", searchUsers)
		}

		// 世界相关路由
		worlds := api.Group("/worlds")
		{
			worlds.POST("", createWorld)
			worlds.GET("/:id", getWorldByID)
			worlds.PUT("/:id/config", updateWorldConfig)
			worlds.GET("/search", searchWorlds)
		}

		// 数据库信息路由
		api.GET("/database/info", getDatabaseInfo)
	}
}

// 路由处理函数

// healthCheck 健康检查
func healthCheck(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := serviceManager.HealthCheck(ctx); err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status":  "unhealthy",
			"error":   err.Error(),
			"timestamp": time.Now().Format(time.RFC3339),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
		"database":  serviceManager.GetDatabaseInfo(),
	})
}

// createUser 创建用户
func createUser(c *gin.Context) {
	var req service.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数无效: " + err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	user, err := serviceManager.User.CreateUser(ctx, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    user,
	})
}

// getUserByID 根据ID获取用户
func getUserByID(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "用户ID不能为空",
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	user, err := serviceManager.User.GetUserByID(ctx, userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "获取用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    user,
	})
}

// updateUserPreferences 更新用户偏好
func updateUserPreferences(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "用户ID不能为空",
		})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数无效: " + err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := serviceManager.User.UpdateUserPreferences(ctx, userID, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新用户偏好失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户偏好更新成功",
	})
}

// searchUsers 搜索用户
func searchUsers(c *gin.Context) {
	var searchCriteria map[string]interface{}
	if err := c.ShouldBindJSON(&searchCriteria); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "搜索条件无效: " + err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	users, err := serviceManager.User.SearchUsersByPreferences(ctx, searchCriteria)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "搜索用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    users,
		"count":   len(users),
	})
}

// createWorld 创建世界
func createWorld(c *gin.Context) {
	var req service.CreateWorldRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数无效: " + err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	world, err := serviceManager.World.CreateWorld(ctx, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建世界失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    world,
	})
}

// getWorldByID 根据ID获取世界
func getWorldByID(c *gin.Context) {
	worldID := c.Param("id")
	if worldID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "世界ID不能为空",
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	world, err := serviceManager.World.GetWorldByID(ctx, worldID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "获取世界失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    world,
	})
}

// updateWorldConfig 更新世界配置
func updateWorldConfig(c *gin.Context) {
	worldID := c.Param("id")
	if worldID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "世界ID不能为空",
		})
		return
	}

	var configUpdates map[string]interface{}
	if err := c.ShouldBindJSON(&configUpdates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数无效: " + err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := serviceManager.World.UpdateWorldConfig(ctx, worldID, configUpdates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新世界配置失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "世界配置更新成功",
	})
}

// searchWorlds 搜索世界
func searchWorlds(c *gin.Context) {
	genre := c.Query("genre")
	if genre == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请提供搜索类型",
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	worlds, err := serviceManager.World.FindWorldsByGenre(ctx, genre)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "搜索世界失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    worlds,
		"count":   len(worlds),
	})
}

// getDatabaseInfo 获取数据库信息
func getDatabaseInfo(c *gin.Context) {
	info := serviceManager.GetDatabaseInfo()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    info,
	})
}

// DSN解析辅助函数

func extractHostFromDSN(dsn string) string {
	// 简化实现，实际应该解析DSN
	if appConfig.Database.Type == "sqlite" {
		return ""
	}
	return "localhost"
}

func extractPortFromDSN(dsn string) int {
	if appConfig.Database.Type == "sqlite" {
		return 0
	}
	return 5432
}

func extractUserFromDSN(dsn string) string {
	if appConfig.Database.Type == "sqlite" {
		return ""
	}
	return "postgres"
}

func extractPasswordFromDSN(dsn string) string {
	if appConfig.Database.Type == "sqlite" {
		return ""
	}
	return ""
}

func extractDBNameFromDSN(dsn string) string {
	if appConfig.Database.Type == "sqlite" {
		return dsn
	}
	return "ai_text_game"
}
