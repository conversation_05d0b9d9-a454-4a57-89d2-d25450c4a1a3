package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"sync"
	"time"

	"ai-text-game-iam-npc/internal/ai"
	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/handlers"
	"ai-text-game-iam-npc/internal/migration"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/routes"
	"ai-text-game-iam-npc/internal/service"
	"ai-text-game-iam-npc/pkg/database"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// APIResponse 统一响应格式
type APIResponse struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data,omitempty"`
	Error     *APIError   `json:"error,omitempty"`
	Timestamp string      `json:"timestamp"`
	RequestID string      `json:"request_id"`
}

// APIError 错误信息
type APIError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// User 用户信息
type User struct {
	ID          string `json:"id"`
	Email       string `json:"email"`
	DisplayName string `json:"display_name"`
	AvatarURL   string `json:"avatar_url"`
}

// World 游戏世界
type World struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	Description    string `json:"description"`
	Theme          string `json:"theme"`
	IsPublic       bool   `json:"is_public"`
	MaxPlayers     int    `json:"max_players"`
	CurrentPlayers int    `json:"current_players"`
	CreatorID      string `json:"creator_id"`
	CreatedAt      string `json:"created_at"`
	UpdatedAt      string `json:"updated_at"`
}

// Character 游戏角色
type Character struct {
	ID            string   `json:"id"`
	WorldID       string   `json:"world_id"`
	Name          string   `json:"name"`
	Description   string   `json:"description"`
	CharacterType string   `json:"character_type"`
	Traits        []string `json:"traits"`
	Status        string   `json:"status"`
	CreatedAt     string   `json:"created_at"`
}

// 内存存储 - 用于开发环境
var (
	worldsStore = make(map[string]World)
	worldsMutex = sync.RWMutex{}

	// 统一的业务逻辑处理器 - 与主服务器使用相同的处理器
	unifiedAIHandler *handlers.UnifiedAIHandler
	appLogger        logger.Logger

	// 数据库连接实例
	dbInstance *gorm.DB
)

// 初始化一些示例世界数据
func initSampleWorlds() {
	worldsMutex.Lock()
	defer worldsMutex.Unlock()

	currentTime := time.Now().Format(time.RFC3339)

	sampleWorlds := []World{
		{
			ID:             uuid.New().String(),
			Name:           "魔法森林",
			Description:    "一个充满魔法和神秘生物的古老森林",
			Theme:          "fantasy",
			IsPublic:       true,
			MaxPlayers:     10,
			CurrentPlayers: 3,
			CreatorID:      "test-user-123",
			CreatedAt:      currentTime,
			UpdatedAt:      currentTime,
		},
		{
			ID:             uuid.New().String(),
			Name:           "赛博朋克都市",
			Description:    "未来科技与黑暗现实交织的都市",
			Theme:          "cyberpunk",
			IsPublic:       true,
			MaxPlayers:     20,
			CurrentPlayers: 8,
			CreatorID:      "test-user-456",
			CreatedAt:      currentTime,
			UpdatedAt:      currentTime,
		},
		{
			ID:             uuid.New().String(),
			Name:           "中世纪王国",
			Description:    "骑士、城堡和龙的传奇世界",
			Theme:          "medieval",
			IsPublic:       false,
			MaxPlayers:     15,
			CurrentPlayers: 5,
			CreatorID:      "test-user-123",
			CreatedAt:      currentTime,
			UpdatedAt:      currentTime,
		},
	}

	for _, world := range sampleWorlds {
		worldsStore[world.ID] = world
	}
}

func main() {
	// 初始化日志
	appLogger = logger.New("simple-server")
	appLogger.Info("🚀 启动简化版AI文本游戏服务器")

	// 初始化配置 - 从.env文件读取数据库配置参数
	cfg, err := initConfigFromEnv()
	if err != nil {
		appLogger.Error("配置初始化失败", "error", err)
		log.Fatalf("无法初始化配置: %v", err)
	}
	appLogger.Info("配置初始化完成", "ai_mock_enabled", cfg.AI.MockEnabled, "db_name", cfg.Database.DBName)

	// 初始化数据库连接
	if err := initDatabase(cfg); err != nil {
		appLogger.Error("数据库初始化失败", "error", err)
		log.Fatalf("无法初始化数据库: %v", err)
	}
	appLogger.Info("数据库初始化成功")

	// 执行数据库迁移
	if err := performDatabaseMigration(cfg); err != nil {
		appLogger.Error("数据库迁移失败", "error", err)
		log.Fatalf("数据库迁移失败: %v", err)
	}
	appLogger.Info("数据库迁移完成")

	// 暂时禁用AI处理器初始化，专注于数据库功能测试
	// TODO: 修复AI服务的UUID类型兼容性问题后重新启用
	appLogger.Info("⚠️  AI处理器初始化已暂时禁用，专注于数据库功能测试")

	// 初始化统一的业务逻辑处理器
	// if err := initUnifiedHandlers(cfg); err != nil {
	// 	appLogger.Error("业务逻辑处理器初始化失败", "error", err)
	// 	log.Fatalf("无法初始化业务逻辑处理器: %v", err)
	// } else {
	// 	appLogger.Info("统一业务逻辑处理器初始化成功")
	// }

	// 初始化示例数据
	initSampleWorlds()

	// 检查是否启用调试日志
	enableDebugLogs := os.Getenv("DEV_ENABLE_DEBUG_LOGS") == "true"
	environment := os.Getenv("ENVIRONMENT")

	// 设置 Gin 模式 - 开发环境使用调试模式
	if environment == "development" || enableDebugLogs {
		gin.SetMode(gin.DebugMode)
		log.Printf("🐛 [DEBUG] 开发模式已启用，详细日志输出已开启")
		log.Printf("🐛 [DEBUG] 环境变量: ENVIRONMENT=%s, DEV_ENABLE_DEBUG_LOGS=%s", environment, os.Getenv("DEV_ENABLE_DEBUG_LOGS"))
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由器
	r := gin.Default()

	// 添加自定义日志中间件
	if enableDebugLogs {
		r.Use(debugLoggingMiddleware())
	}

	// 添加CORS中间件
	r.Use(corsMiddleware())

	// 设置静态文件服务
	setupStaticRoutes(r)

	// 设置 API 路由
	setupAPIRoutes(r)

	// 集成API调试系统路由
	if err := routes.RegisterAPIDebugRoutes(r, appLogger); err != nil {
		appLogger.Error("注册API调试路由失败", "error", err)
		log.Printf("⚠️  API调试系统集成失败: %v", err)
	} else {
		appLogger.Info("API调试系统已成功集成")
		log.Printf("🔧 API调试系统已集成")
		log.Printf("📚 API文档: http://localhost:8080/api/v1/docs")
		log.Printf("🐛 调试界面: http://localhost:8080/debug")
		log.Printf("📊 系统状态: http://localhost:8080/system")
	}

	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("🚀 简化版AI文本游戏服务器启动在端口 %s", port)
	log.Printf("🌐 前端应用: http://localhost:%s", port)
	log.Printf("🔧 API文档: http://localhost:%s/api/v1", port)
	log.Printf("❤️  健康检查: http://localhost:%s/health", port)

	if enableDebugLogs {
		log.Printf("🐛 [DEBUG] 调试模式已启用，将显示详细的请求日志")
		log.Printf("🐛 [DEBUG] 示例世界数据已初始化，共 %d 个世界", len(worldsStore))
	}
	
	// 启动服务器，并在退出时优雅关闭数据库连接
	if err := r.Run(":" + port); err != nil {
		log.Fatal("启动服务器失败:", err)
	}

	// 程序退出时关闭数据库连接
	defer func() {
		if dbInstance != nil {
			if err := database.Close(dbInstance); err != nil {
				appLogger.Error("关闭数据库连接失败", "error", err)
			} else {
				appLogger.Info("数据库连接已优雅关闭")
			}
		}
	}()
}

// initConfigFromEnv 从.env文件初始化配置
// 使用统一的配置加载机制，确保与主服务器配置一致
func initConfigFromEnv() (*config.Config, error) {
	appLogger.Info("📋 开始从.env文件加载配置")

	// 使用统一的配置加载器，自动读取.env文件
	cfg, err := config.Load()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	// 配置驱动：如果配置了真实的AI服务，则禁用Mock
	// 这样开发和生产环境使用相同的代码路径，只是配置不同
	if cfg.AI.BaseURL != "" && cfg.AI.Token != "" && cfg.AI.Windmill.Workspace != "" {
		cfg.AI.MockEnabled = false
		appLogger.Info("检测到完整AI服务配置，启用真实AI服务")
	} else {
		appLogger.Info("AI服务配置不完整，使用Mock模式",
			"has_base_url", cfg.AI.BaseURL != "",
			"has_token", cfg.AI.Token != "",
			"has_workspace", cfg.AI.Windmill.Workspace != "")
	}

	// 记录数据库配置信息（不包含敏感信息）
	appLogger.Info("数据库配置加载完成",
		"db_name", cfg.Database.DBName,
		"db_host", cfg.Database.Host,
		"db_port", cfg.Database.Port,
		"max_open_conns", cfg.Database.MaxOpenConns,
		"max_idle_conns", cfg.Database.MaxIdleConns)

	return cfg, nil
}

// initDatabase 初始化数据库连接
// 根据配置参数建立数据库连接并设置连接池
func initDatabase(cfg *config.Config) error {
	appLogger.Info("🔗 开始初始化数据库连接")

	// 使用统一的数据库连接器
	db, err := database.New(&cfg.Database)
	if err != nil {
		return fmt.Errorf("创建数据库连接失败: %w", err)
	}

	// 保存数据库实例供后续使用
	dbInstance = db

	// 测试数据库连接
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取底层数据库连接失败: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	// 记录连接池状态
	stats := sqlDB.Stats()
	appLogger.Info("数据库连接池状态",
		"open_connections", stats.OpenConnections,
		"in_use", stats.InUse,
		"idle", stats.Idle)

	return nil
}

// performDatabaseMigration 执行数据库迁移
// 自动创建和更新数据库表结构
func performDatabaseMigration(cfg *config.Config) error {
	appLogger.Info("🚀 开始执行数据库迁移")

	// 创建智能迁移器，支持SQLite和PostgreSQL
	smartMigrator := migration.NewSmartMigrator(dbInstance, &cfg.Database, "migrations")

	// 检查数据库兼容性
	appLogger.Info("检查数据库兼容性")
	if err := smartMigrator.CheckCompatibility(); err != nil {
		appLogger.Warn("数据库兼容性检查失败，但继续执行", "error", err)
	}

	// 获取并记录数据库信息
	info := smartMigrator.GetDatabaseInfo()
	appLogger.Info("数据库信息",
		"type", info["database_type"],
		"supports_jsonb", info["supports_jsonb"],
		"supports_uuid", info["supports_uuid"])

	// 定义所有需要迁移的数据模型
	// 这些模型定义了游戏系统的核心数据结构
	models := []interface{}{
		&models.User{},         // 用户信息表
		&models.UserStats{},    // 用户统计表
		&models.World{},        // 游戏世界表
		&models.Scene{},        // 场景表
		&models.Character{},    // 角色表
		&models.Entity{},       // 实体表
		&models.Event{},        // 事件表
		&models.AIInteraction{}, // AI交互记录表
		&models.Conversation{}, // 对话会话表
		&models.Message{},      // 消息表
	}

	// 执行自动迁移
	appLogger.Info("开始创建/更新数据库表结构")
	if err := smartMigrator.AutoMigrate(models...); err != nil {
		return fmt.Errorf("数据库表迁移失败: %w", err)
	}

	// 记录迁移完成状态
	appLogger.Info("数据库迁移成功完成",
		"migrated_models", len(models))

	return nil
}

// initUnifiedHandlers 初始化统一的业务逻辑处理器
// 这确保simple-server和main-server使用完全相同的业务逻辑代码
func initUnifiedHandlers(cfg *config.Config) error {
	// 创建AI服务实例 - 与主服务器使用相同的服务
	// 现在传入数据库实例，支持完整的数据持久化功能
	// AI服务会根据配置自动处理Mock模式
	aiService := ai.NewService(cfg, dbInstance, appLogger)

	// 创建统一的AI处理器 - 与主服务器使用相同的处理器
	unifiedAIHandler = handlers.NewUnifiedAIHandler(aiService, appLogger)

	appLogger.Info("统一业务逻辑处理器创建成功",
		"mock_enabled", cfg.AI.MockEnabled,
		"database_enabled", dbInstance != nil)

	return nil
}

// setupStaticRoutes 设置静态文件路由
func setupStaticRoutes(r *gin.Engine) {
	// 静态文件目录
	staticDir := "web/static"
	distDir := filepath.Join(staticDir, "dist")
	
	// 检查构建产物是否存在
	if _, err := os.Stat(distDir); os.IsNotExist(err) {
		log.Printf("⚠️  前端构建产物不存在: %s", distDir)
		log.Printf("请先运行: cd web/frontend && npm run build")
	}
	
	// 服务静态资源
	r.Static("/assets", filepath.Join(distDir, "assets"))
	
	// 处理前端路由 - SPA 支持
	r.NoRoute(func(c *gin.Context) {
		path := c.Request.URL.Path
		
		// API 路由返回 404
		if len(path) >= 4 && path[:4] == "/api" {
			c.JSON(http.StatusNotFound, APIResponse{
				Success: false,
				Error: &APIError{
					Code:    "NOT_FOUND",
					Message: "API端点不存在",
				},
				Timestamp: "2024-01-01T00:00:00Z",
				RequestID: "test-request",
			})
			return
		}
		
		// 其他路由返回前端应用
		indexPath := filepath.Join(distDir, "index.html")
		if _, err := os.Stat(indexPath); err == nil {
			c.File(indexPath)
		} else {
			c.String(http.StatusNotFound, "前端应用未找到")
		}
	})
}

// setupAPIRoutes 设置 API 路由
func setupAPIRoutes(r *gin.Engine) {
	// 健康检查处理函数
	healthHandler := func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"service": "ai-text-game-simple",
			"version": "1.0.0",
			"auth_mode": "disabled", // 标识认证已禁用
		})
	}

	// 注册健康检查路由 - 支持两种路径
	r.GET("/health", healthHandler)      // 原有路径
	r.GET("/api/health", healthHandler)  // 新增API路径，兼容前端调用

	// API v1 路由组 - 开发环境无需认证
	v1 := r.Group("/api/v1")
	{
		// 用户相关 - 模拟认证，无需真实token
		v1.GET("/user/profile", getUserProfile)
		v1.POST("/auth/login", mockLogin)
		v1.POST("/auth/logout", mockLogout)

		// 世界相关 - 直接访问，无需认证
		v1.GET("/worlds", getWorlds)
		v1.POST("/worlds", createWorld)
		v1.GET("/worlds/:id", getWorld)

		// 游戏相关路由 - 兼容前端API调用
		gameGroup := v1.Group("/game")
		{
			// 世界管理
			gameGroup.GET("/my-worlds", getMyWorlds)
			gameGroup.GET("/public-worlds", getPublicWorlds)
			gameGroup.POST("/worlds", createWorld)
			gameGroup.PUT("/worlds/:id", updateWorld)
			gameGroup.DELETE("/worlds/:id", deleteWorld)

			// 角色管理
			gameGroup.GET("/my-characters", getMyCharacters)
			gameGroup.POST("/characters", createCharacter)
			gameGroup.GET("/characters/:id", getCharacter)

			// 世界角色列表 - 使用不同的路径避免冲突
			gameGroup.GET("/world/:worldId/characters", getWorldCharacters)
		}

		// 游戏状态和动作 - 直接访问，无需认证
		v1.GET("/games/:worldId/status", getGameStatus)
		v1.POST("/games/:worldId/actions", performAction)

		// AI相关路由 - 暂时禁用，等待UUID兼容性修复
		// TODO: 修复AI服务的UUID类型兼容性问题后重新启用
		// aiGroup := v1.Group("/ai")
		// {
		// 	aiGroup.POST("/generate/scene", unifiedAIHandler.GenerateScene)
		// 	aiGroup.POST("/generate/character", unifiedAIHandler.GenerateCharacter)
		// 	aiGroup.GET("/interactions/history", unifiedAIHandler.GetInteractionHistory)
		// 	aiGroup.GET("/stats/token-usage", unifiedAIHandler.GetTokenUsageStats)
		// }

		// 临时AI状态接口
		v1.GET("/ai/status", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status": "disabled",
				"message": "AI功能暂时禁用，专注于数据库功能测试",
				"database_enabled": dbInstance != nil,
			})
		})

		// 添加更多开发测试接口
		v1.GET("/dev/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "开发环境测试接口",
				"auth_required": false,
				"timestamp": "2024-01-01T00:00:00Z",
			})
		})
	}

	// 对话API路由 - 直接在根路径下，兼容标准格式
	setupChatRoutes(r)
}

// setupChatRoutes 设置对话API路由
func setupChatRoutes(r *gin.Engine) {
	// 创建AI服务和对话服务
	cfg, err := initConfigFromEnv()
	if err != nil {
		appLogger.Error("初始化配置失败", "error", err)
		return
	}

	// 创建AI服务
	aiService := ai.NewService(cfg, dbInstance, appLogger)

	// 创建对话服务
	conversationService := service.NewConversationService(dbInstance, appLogger)

	// 创建对话处理器
	chatHandler := handlers.NewChatHandler(aiService, conversationService, appLogger)

	// 注册对话API路由
	r.POST("/api/chat", chatHandler.HandleChat)
	}
}

// getUserProfile 获取用户信息
func getUserProfile(c *gin.Context) {
	user := User{
		ID:          "test-user-123",
		Email:       "<EMAIL>",
		DisplayName: "测试用户",
		AvatarURL:   "https://via.placeholder.com/64",
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      user,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// mockLogin 模拟登录
func mockLogin(c *gin.Context) {
	var loginReq map[string]interface{}
	if err := c.ShouldBindJSON(&loginReq); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "INVALID_REQUEST",
				Message: "请求格式错误",
			},
			Timestamp: "2024-01-01T00:00:00Z",
			RequestID: "test-request",
		})
		return
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: gin.H{
			"token": "mock-jwt-token",
			"user": User{
				ID:          "test-user-123",
				Email:       "<EMAIL>",
				DisplayName: "测试用户",
				AvatarURL:   "https://via.placeholder.com/64",
			},
		},
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// mockLogout 模拟登出
func mockLogout(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      gin.H{"message": "登出成功"},
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getWorlds 获取世界列表
func getWorlds(c *gin.Context) {
	worldsMutex.RLock()
	defer worldsMutex.RUnlock()

	// 将map转换为slice
	worlds := make([]World, 0, len(worldsStore))
	for _, world := range worldsStore {
		worlds = append(worlds, world)
	}

	currentTime := time.Now().Format(time.RFC3339)
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      worlds,
		Timestamp: currentTime,
		RequestID: "get-worlds-" + uuid.New().String(),
	})
}

// @Summary 创建世界
// @Description 创建一个新的游戏世界
// @Tags 世界管理
// @Accept json
// @Produce json
// @Param world body object{name=string,description=string,settings=object{difficulty=string,max_players=int}} true "世界创建参数"
// @Success 200 {object} APIResponse "创建成功"
// @Router /api/v1/worlds [post]
func createWorld(c *gin.Context) {
	var worldReq map[string]interface{}
	if err := c.ShouldBindJSON(&worldReq); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "INVALID_REQUEST",
				Message: "请求格式错误",
			},
			Timestamp: "2024-01-01T00:00:00Z",
			RequestID: "test-request",
		})
		return
	}
	
	// 生成真实的UUID作为世界ID
	worldID := uuid.New().String()
	currentTime := time.Now().Format(time.RFC3339)

	// 从请求中提取更多字段
	maxPlayers := 10
	if mp, ok := worldReq["max_players"].(float64); ok {
		maxPlayers = int(mp)
	}

	theme := "fantasy"
	if t, ok := worldReq["theme"].(string); ok && t != "" {
		theme = t
	}

	world := World{
		ID:             worldID,
		Name:           worldReq["name"].(string),
		Description:    worldReq["description"].(string),
		Theme:          theme,
		IsPublic:       worldReq["is_public"].(bool),
		MaxPlayers:     maxPlayers,
		CurrentPlayers: 0,
		CreatorID:      "test-user-123", // 在开发环境中使用固定的测试用户ID
		CreatedAt:      currentTime,
		UpdatedAt:      currentTime,
	}

	// 将新世界保存到内存存储
	worldsMutex.Lock()
	worldsStore[worldID] = world
	worldsMutex.Unlock()

	log.Printf("创建新世界成功: ID=%s, Name=%s", worldID, world.Name)

	c.JSON(http.StatusCreated, APIResponse{
		Success:   true,
		Data:      world,
		Timestamp: currentTime,
		RequestID: "create-world-" + worldID,
	})
}

// getWorld 获取世界详情
func getWorld(c *gin.Context) {
	worldID := c.Param("id")

	worldsMutex.RLock()
	world, exists := worldsStore[worldID]
	worldsMutex.RUnlock()

	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "WORLD_NOT_FOUND",
				Message: "世界不存在",
			},
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: "get-world-" + worldID,
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      world,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: "get-world-" + worldID,
	})
}

// getGameStatus 获取游戏状态
func getGameStatus(c *gin.Context) {
	worldID := c.Param("worldId")
	
	status := gin.H{
		"world_id": worldID,
		"status":   "active",
		"players":  3,
		"scene": gin.H{
			"id":          "scene-1",
			"name":        "森林入口",
			"description": "你站在一片古老森林的入口处，阳光透过茂密的树叶洒下斑驳的光影。",
		},
		"character": gin.H{
			"id":   "char-1",
			"name": "冒险者",
			"hp":   100,
			"mp":   50,
		},
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      status,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// performAction 执行游戏动作
func performAction(c *gin.Context) {
	worldID := c.Param("worldId")
	
	var actionReq map[string]interface{}
	if err := c.ShouldBindJSON(&actionReq); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "INVALID_REQUEST",
				Message: "请求格式错误",
			},
			Timestamp: "2024-01-01T00:00:00Z",
			RequestID: "test-request",
		})
		return
	}
	
	result := gin.H{
		"world_id": worldID,
		"action":   actionReq["action"],
		"result":   "你的行动产生了有趣的结果...",
		"narrative": "随着你的行动，周围的环境发生了微妙的变化。",
		"effects": []gin.H{
			{
				"type":        "experience",
				"description": "获得了5点经验值",
				"value":       5,
			},
		},
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      result,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getMyWorlds 获取我的世界列表
func getMyWorlds(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	worldsMutex.RLock()
	defer worldsMutex.RUnlock()

	// 过滤出当前用户创建的世界
	userID := "test-user-123" // 开发环境固定用户ID
	var myWorlds []World
	for _, world := range worldsStore {
		if world.CreatorID == userID {
			myWorlds = append(myWorlds, world)
		}
	}

	// 计算分页
	total := len(myWorlds)
	start := (page - 1) * limit
	end := start + limit
	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	paginatedWorlds := myWorlds[start:end]

	response := gin.H{
		"worlds": paginatedWorlds,
		"total":  total,
		"page":   page,
		"limit":  limit,
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      response,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: "get-my-worlds-" + uuid.New().String(),
	})
}

// getPublicWorlds 获取公开世界列表
func getPublicWorlds(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	worldsMutex.RLock()
	defer worldsMutex.RUnlock()

	// 过滤出公开的世界
	var publicWorlds []World
	for _, world := range worldsStore {
		if world.IsPublic {
			publicWorlds = append(publicWorlds, world)
		}
	}

	// 计算分页
	total := len(publicWorlds)
	start := (page - 1) * limit
	end := start + limit
	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	paginatedWorlds := publicWorlds[start:end]

	response := gin.H{
		"worlds": paginatedWorlds,
		"total":  total,
		"page":   page,
		"limit":  limit,
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      response,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: "get-public-worlds-" + uuid.New().String(),
	})
}

// updateWorld 更新世界
func updateWorld(c *gin.Context) {
	worldID := c.Param("id")

	// 模拟更新成功
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      gin.H{"message": "世界更新成功", "world_id": worldID},
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// deleteWorld 删除世界
func deleteWorld(c *gin.Context) {
	worldID := c.Param("id")

	// 模拟删除成功
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      gin.H{"message": "世界删除成功", "world_id": worldID},
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getMyCharacters 获取我的角色列表
func getMyCharacters(c *gin.Context) {
	characters := []gin.H{
		{
			"id":          "char-1",
			"name":        "艾莉亚",
			"description": "一位勇敢的精灵法师",
			"world_id":    "world-1",
			"user_id":     "test-user-123",
			"created_at":  "2024-01-01T00:00:00Z",
			"updated_at":  "2024-01-01T00:00:00Z",
		},
		{
			"id":          "char-2",
			"name":        "赛博忍者",
			"description": "来自未来的神秘战士",
			"world_id":    "world-2",
			"user_id":     "test-user-123",
			"created_at":  "2024-01-01T00:00:00Z",
			"updated_at":  "2024-01-01T00:00:00Z",
		},
	}

	response := gin.H{
		"characters": characters,
		"total":      len(characters),
		"page":       1,
		"limit":      10,
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      response,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// createCharacter 创建角色
func createCharacter(c *gin.Context) {
	// 模拟创建成功
	character := gin.H{
		"id":          "char-new",
		"name":        "新角色",
		"description": "刚刚创建的角色",
		"world_id":    "world-1",
		"user_id":     "test-user-123",
		"created_at":  "2024-01-01T00:00:00Z",
		"updated_at":  "2024-01-01T00:00:00Z",
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      character,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getCharacter 获取角色详情
func getCharacter(c *gin.Context) {
	characterID := c.Param("id")

	character := gin.H{
		"id":          characterID,
		"name":        "示例角色",
		"description": "这是一个示例角色",
		"world_id":    "world-1",
		"user_id":     "test-user-123",
		"created_at":  "2024-01-01T00:00:00Z",
		"updated_at":  "2024-01-01T00:00:00Z",
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      character,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// 注意：原来的独立AI处理函数已被移除
// 现在simple-server使用与main-server完全相同的统一业务逻辑处理器
// 这确保了"一套代码，多种配置"的架构原则
// 所有AI相关的处理逻辑都在 internal/handlers/ai_unified.go 中统一实现






// corsMiddleware CORS中间件，允许跨域请求
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 允许所有来源（开发环境）
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// debugLoggingMiddleware 调试日志中间件
func debugLoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 自定义日志格式，包含更多调试信息
		return fmt.Sprintf("🌐 [%s] %s %s %s %d %s %s | 用户代理: %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.ClientIP,
			param.StatusCode,
			param.Latency,
			param.Request.Proto,
			param.Request.UserAgent(),
		)
	})
}

// getWorldCharacters 获取世界中的角色列表
func getWorldCharacters(c *gin.Context) {
	worldId := c.Param("worldId")
	page := c.DefaultQuery("page", "1")
	limit := c.DefaultQuery("limit", "10")
	characterType := c.Query("character_type")

	// 模拟角色数据
	characters := []Character{
		{
			ID:            "char-1",
			WorldID:       worldId,
			Name:          "艾莉娅",
			Description:   "一位年轻的精灵法师，有着银色的长发和翠绿的眼睛。她精通自然魔法，能够与森林中的生物沟通。",
			CharacterType: "npc",
			Traits:        []string{"智慧", "善良", "好奇"},
			Status:        "active",
			CreatedAt:     "2024-01-01T00:00:00Z",
		},
		{
			ID:            "char-2",
			WorldID:       worldId,
			Name:          "格雷戈里",
			Description:   "一位经验丰富的矮人铁匠，拥有强壮的体格和精湛的锻造技艺。他的胡须花白，眼中闪烁着智慧的光芒。",
			CharacterType: "npc",
			Traits:        []string{"坚韧", "诚实", "技艺精湛"},
			Status:        "active",
			CreatedAt:     "2024-01-01T00:00:00Z",
		},
		{
			ID:            "char-3",
			WorldID:       worldId,
			Name:          "暗影刺客",
			Description:   "一个神秘的刺客，身穿黑色斗篷，面容隐藏在阴影中。他的动作敏捷如风，来去无踪。",
			CharacterType: "npc",
			Traits:        []string{"敏捷", "神秘", "危险"},
			Status:        "active",
			CreatedAt:     "2024-01-01T00:00:00Z",
		},
	}

	// 根据角色类型过滤
	if characterType != "" {
		filteredCharacters := []Character{}
		for _, char := range characters {
			if char.CharacterType == characterType {
				filteredCharacters = append(filteredCharacters, char)
			}
		}
		characters = filteredCharacters
	}

	// 模拟分页
	total := len(characters)
	pageInt := 1
	limitInt := 10

	if p, err := strconv.Atoi(page); err == nil && p > 0 {
		pageInt = p
	}
	if l, err := strconv.Atoi(limit); err == nil && l > 0 {
		limitInt = l
	}

	start := (pageInt - 1) * limitInt
	end := start + limitInt

	if start >= total {
		characters = []Character{}
	} else {
		if end > total {
			end = total
		}
		characters = characters[start:end]
	}

	totalPages := (total + limitInt - 1) / limitInt

	response := gin.H{
		"items":       characters,
		"total":       total,
		"page":        pageInt,
		"limit":       limitInt,
		"total_pages": totalPages,
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      response,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}
