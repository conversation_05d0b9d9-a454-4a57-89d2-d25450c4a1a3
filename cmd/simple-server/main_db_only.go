package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/migration"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/database"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// APIResponse 统一响应格式
type APIResponse struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data,omitempty"`
	Error     *APIError   `json:"error,omitempty"`
	Timestamp string      `json:"timestamp"`
	RequestID string      `json:"request_id"`
}

// APIError 错误信息
type APIError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// World 游戏世界（简化版本，用于内存存储）
type World struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	Description    string `json:"description"`
	Theme          string `json:"theme"`
	IsPublic       bool   `json:"is_public"`
	MaxPlayers     int    `json:"max_players"`
	CurrentPlayers int    `json:"current_players"`
	CreatorID      string `json:"creator_id"`
	CreatedAt      string `json:"created_at"`
	UpdatedAt      string `json:"updated_at"`
}

// 内存存储 - 用于开发环境
var (
	worldsStore = make(map[string]World)
	worldsMutex = sync.RWMutex{}
	appLogger   logger.Logger
	dbInstance  *gorm.DB
)

// 初始化一些示例世界数据
func initSampleWorlds() {
	worldsMutex.Lock()
	defer worldsMutex.Unlock()

	currentTime := time.Now().Format(time.RFC3339)

	sampleWorlds := []World{
		{
			ID:             uuid.New().String(),
			Name:           "魔法森林",
			Description:    "一个充满魔法和神秘生物的古老森林",
			Theme:          "fantasy",
			IsPublic:       true,
			MaxPlayers:     10,
			CurrentPlayers: 3,
			CreatorID:      "test-user-123",
			CreatedAt:      currentTime,
			UpdatedAt:      currentTime,
		},
		{
			ID:             uuid.New().String(),
			Name:           "赛博朋克都市",
			Description:    "未来科技与黑暗现实交织的都市",
			Theme:          "cyberpunk",
			IsPublic:       true,
			MaxPlayers:     20,
			CurrentPlayers: 8,
			CreatorID:      "test-user-456",
			CreatedAt:      currentTime,
			UpdatedAt:      currentTime,
		},
	}

	for _, world := range sampleWorlds {
		worldsStore[world.ID] = world
	}
}

func main() {
	// 初始化日志
	appLogger = logger.New("simple-server-db-test")
	appLogger.Info("🚀 启动数据库测试版AI文本游戏服务器")

	// 初始化配置 - 从.env文件读取数据库配置参数
	cfg, err := initConfigFromEnv()
	if err != nil {
		appLogger.Error("配置初始化失败", "error", err)
		log.Fatalf("无法初始化配置: %v", err)
	}
	appLogger.Info("配置初始化完成", "db_name", cfg.Database.DBName)

	// 初始化数据库连接
	if err := initDatabase(cfg); err != nil {
		appLogger.Error("数据库初始化失败", "error", err)
		log.Fatalf("无法初始化数据库: %v", err)
	}
	appLogger.Info("数据库初始化成功")

	// 执行数据库迁移
	if err := performDatabaseMigration(cfg); err != nil {
		appLogger.Error("数据库迁移失败", "error", err)
		log.Fatalf("数据库迁移失败: %v", err)
	}
	appLogger.Info("数据库迁移完成")

	// 初始化示例数据
	initSampleWorlds()

	// 检查是否启用调试日志
	enableDebugLogs := os.Getenv("DEV_ENABLE_DEBUG_LOGS") == "true"
	environment := os.Getenv("ENVIRONMENT")

	// 设置 Gin 模式 - 开发环境使用调试模式
	if environment == "development" || enableDebugLogs {
		gin.SetMode(gin.DebugMode)
		log.Printf("🐛 [DEBUG] 开发模式已启用，详细日志输出已开启")
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由器
	r := gin.Default()

	// 添加自定义日志中间件
	if enableDebugLogs {
		r.Use(debugLoggingMiddleware())
	}

	// 添加CORS中间件
	r.Use(corsMiddleware())

	// 设置静态文件服务
	setupStaticRoutes(r)

	// 设置 API 路由
	setupAPIRoutes(r)

	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("🚀 数据库测试版AI文本游戏服务器启动在端口 %s", port)
	log.Printf("🌐 前端应用: http://localhost:%s", port)
	log.Printf("🔧 API文档: http://localhost:%s/api/v1", port)
	log.Printf("❤️  健康检查: http://localhost:%s/health", port)
	log.Printf("🗄️  数据库状态: http://localhost:%s/api/v1/database/status", port)

	if enableDebugLogs {
		log.Printf("🐛 [DEBUG] 调试模式已启用，将显示详细的请求日志")
		log.Printf("🐛 [DEBUG] 示例世界数据已初始化，共 %d 个世界", len(worldsStore))
	}

	// 启动服务器，并在退出时优雅关闭数据库连接
	if err := r.Run(":" + port); err != nil {
		log.Fatal("启动服务器失败:", err)
	}

	// 程序退出时关闭数据库连接
	defer func() {
		if dbInstance != nil {
			if err := database.Close(dbInstance); err != nil {
				appLogger.Error("关闭数据库连接失败", "error", err)
			} else {
				appLogger.Info("数据库连接已优雅关闭")
			}
		}
	}()
}

// initConfigFromEnv 从.env文件初始化配置
func initConfigFromEnv() (*config.Config, error) {
	appLogger.Info("📋 开始从.env文件加载配置")

	cfg, err := config.Load()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	appLogger.Info("数据库配置加载完成",
		"db_name", cfg.Database.DBName,
		"db_host", cfg.Database.Host,
		"db_port", cfg.Database.Port,
		"max_open_conns", cfg.Database.MaxOpenConns,
		"max_idle_conns", cfg.Database.MaxIdleConns)

	return cfg, nil
}

// initDatabase 初始化数据库连接
func initDatabase(cfg *config.Config) error {
	appLogger.Info("🔗 开始初始化数据库连接")

	db, err := database.New(&cfg.Database)
	if err != nil {
		return fmt.Errorf("创建数据库连接失败: %w", err)
	}

	dbInstance = db

	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取底层数据库连接失败: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	stats := sqlDB.Stats()
	appLogger.Info("数据库连接池状态",
		"open_connections", stats.OpenConnections,
		"in_use", stats.InUse,
		"idle", stats.Idle)

	return nil
}

// performDatabaseMigration 执行数据库迁移
func performDatabaseMigration(cfg *config.Config) error {
	appLogger.Info("🚀 开始执行数据库迁移")

	smartMigrator := migration.NewSmartMigrator(dbInstance, &cfg.Database, "migrations")

	appLogger.Info("检查数据库兼容性")
	if err := smartMigrator.CheckCompatibility(); err != nil {
		appLogger.Warn("数据库兼容性检查失败，但继续执行", "error", err)
	}

	info := smartMigrator.GetDatabaseInfo()
	appLogger.Info("数据库信息",
		"type", info["database_type"],
		"supports_jsonb", info["supports_jsonb"],
		"supports_uuid", info["supports_uuid"])

	models := []interface{}{
		&models.User{},
		&models.UserStats{},
		&models.World{},
		&models.Scene{},
		&models.Character{},
		&models.Entity{},
		&models.Event{},
		&models.AIInteraction{},
	}

	appLogger.Info("开始创建/更新数据库表结构")
	if err := smartMigrator.AutoMigrate(models...); err != nil {
		return fmt.Errorf("数据库表迁移失败: %w", err)
	}

	appLogger.Info("数据库迁移成功完成", "migrated_models", len(models))
	return nil
}

// setupStaticRoutes 设置静态文件路由
func setupStaticRoutes(r *gin.Engine) {
	staticDir := "web/static"
	distDir := filepath.Join(staticDir, "dist")

	if _, err := os.Stat(distDir); os.IsNotExist(err) {
		log.Printf("⚠️  前端构建产物不存在: %s", distDir)
	}

	r.Static("/assets", filepath.Join(distDir, "assets"))

	r.NoRoute(func(c *gin.Context) {
		path := c.Request.URL.Path

		if len(path) >= 4 && path[:4] == "/api" {
			c.JSON(http.StatusNotFound, APIResponse{
				Success: false,
				Error: &APIError{
					Code:    "NOT_FOUND",
					Message: "API端点不存在",
				},
				Timestamp: time.Now().Format(time.RFC3339),
				RequestID: uuid.New().String(),
			})
			return
		}

		indexPath := filepath.Join(distDir, "index.html")
		if _, err := os.Stat(indexPath); err == nil {
			c.File(indexPath)
		} else {
			c.String(http.StatusNotFound, "前端应用未找到")
		}
	})
}

// setupAPIRoutes 设置 API 路由
func setupAPIRoutes(r *gin.Engine) {
	healthHandler := func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"service": "ai-text-game-simple-db-test",
			"version": "1.0.0",
			"database_enabled": dbInstance != nil,
		})
	}

	r.GET("/health", healthHandler)
	r.GET("/api/health", healthHandler)

	v1 := r.Group("/api/v1")
	{
		// 数据库状态检查
		v1.GET("/database/status", getDatabaseStatus)
		v1.GET("/database/tables", getDatabaseTables)

		// 基础世界管理（内存版本）
		v1.GET("/worlds", getWorlds)
		v1.POST("/worlds", createWorld)
		v1.GET("/worlds/:id", getWorld)

		// 开发测试接口
		v1.GET("/dev/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "数据库测试环境",
				"database_enabled": dbInstance != nil,
				"timestamp": time.Now().Format(time.RFC3339),
			})
		})
	}
}

// getDatabaseStatus 获取数据库状态
func getDatabaseStatus(c *gin.Context) {
	if dbInstance == nil {
		c.JSON(http.StatusServiceUnavailable, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "DATABASE_UNAVAILABLE",
				Message: "数据库未初始化",
			},
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	sqlDB, err := dbInstance.DB()
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "DATABASE_ERROR",
				Message: "无法获取数据库连接",
				Details: err.Error(),
			},
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	stats := sqlDB.Stats()

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: gin.H{
			"status": "connected",
			"connection_pool": gin.H{
				"open_connections": stats.OpenConnections,
				"in_use": stats.InUse,
				"idle": stats.Idle,
				"max_open": stats.MaxOpenConnections,
				"max_idle_time": "5m", // 从配置获取
			},
			"database_type": "sqlite", // 从配置中获取
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: uuid.New().String(),
	})
}

// getDatabaseTables 获取数据库表信息
func getDatabaseTables(c *gin.Context) {
	if dbInstance == nil {
		c.JSON(http.StatusServiceUnavailable, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "DATABASE_UNAVAILABLE",
				Message: "数据库未初始化",
			},
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	tables := []string{
		"users", "user_stats", "worlds", "scenes",
		"characters", "entities", "events", "ai_interactions",
	}

	tableInfo := make([]gin.H, 0, len(tables))
	for _, tableName := range tables {
		exists := dbInstance.Migrator().HasTable(tableName)
		tableInfo = append(tableInfo, gin.H{
			"name": tableName,
			"exists": exists,
		})
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: gin.H{
			"tables": tableInfo,
			"total_tables": len(tables),
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: uuid.New().String(),
	})
}

// getWorlds 获取世界列表
func getWorlds(c *gin.Context) {
	worldsMutex.RLock()
	defer worldsMutex.RUnlock()

	worlds := make([]World, 0, len(worldsStore))
	for _, world := range worldsStore {
		worlds = append(worlds, world)
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      worlds,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: uuid.New().String(),
	})
}

// createWorld 创建新世界
func createWorld(c *gin.Context) {
	var worldReq map[string]interface{}
	if err := c.ShouldBindJSON(&worldReq); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "INVALID_REQUEST",
				Message: "请求格式错误",
				Details: err.Error(),
			},
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	worldID := uuid.New().String()
	currentTime := time.Now().Format(time.RFC3339)

	world := World{
		ID:             worldID,
		Name:           worldReq["name"].(string),
		Description:    worldReq["description"].(string),
		Theme:          "fantasy",
		IsPublic:       worldReq["is_public"].(bool),
		MaxPlayers:     10,
		CurrentPlayers: 0,
		CreatorID:      "test-user-123",
		CreatedAt:      currentTime,
		UpdatedAt:      currentTime,
	}

	worldsMutex.Lock()
	worldsStore[worldID] = world
	worldsMutex.Unlock()

	c.JSON(http.StatusCreated, APIResponse{
		Success:   true,
		Data:      world,
		Timestamp: currentTime,
		RequestID: uuid.New().String(),
	})
}

// getWorld 获取世界详情
func getWorld(c *gin.Context) {
	worldID := c.Param("id")

	worldsMutex.RLock()
	world, exists := worldsStore[worldID]
	worldsMutex.RUnlock()

	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "WORLD_NOT_FOUND",
				Message: "世界不存在",
			},
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      world,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: uuid.New().String(),
	})
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// debugLoggingMiddleware 调试日志中间件
func debugLoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("🌐 [%s] %s %s %s %d %s %s | 用户代理: %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.ClientIP,
			param.StatusCode,
			param.Latency,
			param.Request.Proto,
			param.Request.UserAgent(),
		)
	})
}
