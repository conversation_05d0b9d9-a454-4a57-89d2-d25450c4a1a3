package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"ai-text-game-iam-npc/migrations"
)

func main() {
	var (
		dbPath    = flag.String("db", "game.db", "数据库文件路径")
		action    = flag.String("action", "status", "操作类型: migrate, rollback, status, auto, validate, repair")
		version   = flag.String("version", "", "目标版本（用于migrate和rollback）")
		verbose   = flag.Bool("verbose", false, "详细输出")
	)
	flag.Parse()

	// 配置日志级别
	logLevel := logger.Silent
	if *verbose {
		logLevel = logger.Info
	}

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(*dbPath), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 创建迁移管理器
	manager := migrations.NewManager(db)

	// 初始化迁移系统
	if err := manager.Init(); err != nil {
		log.Fatalf("初始化迁移系统失败: %v", err)
	}

	// 执行操作
	switch *action {
	case "migrate":
		if *version == "" {
			fmt.Println("使用方法: -action=migrate -version=<版本号>")
			fmt.Println("可用版本:", manager.ListAvailableVersions())
			os.Exit(1)
		}
		if err := manager.Migrate(*version); err != nil {
			log.Fatalf("迁移失败: %v", err)
		}

	case "rollback":
		if *version == "" {
			fmt.Println("使用方法: -action=rollback -version=<版本号>")
			os.Exit(1)
		}
		if err := manager.Rollback(*version); err != nil {
			log.Fatalf("回滚失败: %v", err)
		}

	case "status":
		if err := manager.Status(); err != nil {
			log.Fatalf("查询状态失败: %v", err)
		}
		
		currentVersion, err := manager.GetCurrentVersion()
		if err != nil {
			log.Fatalf("获取当前版本失败: %v", err)
		}
		if currentVersion == "" {
			fmt.Println("当前版本: 无")
		} else {
			fmt.Printf("当前版本: %s\n", currentVersion)
		}
		
		fmt.Println("可用版本:", manager.ListAvailableVersions())

	case "auto":
		if err := manager.AutoMigrate(); err != nil {
			log.Fatalf("自动迁移失败: %v", err)
		}

	case "validate":
		if err := manager.ValidateSchema(); err != nil {
			log.Fatalf("架构验证失败: %v", err)
		}
		fmt.Println("数据库架构验证通过")

	case "repair":
		if err := manager.RepairSchema(); err != nil {
			log.Fatalf("架构修复失败: %v", err)
		}
		fmt.Println("数据库架构修复完成")

	default:
		fmt.Printf("未知操作: %s\n", *action)
		fmt.Println("支持的操作: migrate, rollback, status, auto, validate, repair")
		os.Exit(1)
	}
}
