package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ai-text-game-iam-npc/internal/routes"

	"github.com/gin-gonic/gin"
)

// DemoLogger 演示用的简单日志记录器
type DemoLogger struct{}

func (l *DemoLogger) Info(msg string, args ...interface{}) {
	log.Printf("[信息] %s %v", msg, args)
}

func (l *DemoLogger) Error(msg string, args ...interface{}) {
	log.Printf("[错误] %s %v", msg, args)
}

func (l *DemoLogger) Debug(msg string, args ...interface{}) {
	log.Printf("[调试] %s %v", msg, args)
}

func (l *DemoLogger) Warn(msg string, args ...interface{}) {
	log.Printf("[警告] %s %v", msg, args)
}

func (l *DemoLogger) Fatal(msg string, args ...interface{}) {
	log.Fatalf("[致命] %s %v", msg, args)
}

func (l *DemoLogger) FormatHTTPLog(param gin.LogFormatterParams) string {
	return fmt.Sprintf("[HTTP] %s - [%s] \"%s %s %s\" %d %s \"%s\" \"%s\" %s\n",
		param.ClientIP,
		param.TimeStamp.Format("02/Jan/2006:15:04:05 -0700"),
		param.Method,
		param.Path,
		param.Request.Proto,
		param.StatusCode,
		param.Latency,
		param.Request.UserAgent(),
		param.Request.Referer(),
		param.ErrorMessage,
	)
}

func main() {
	fmt.Println("🚀 启动API调试系统演示")
	fmt.Println("========================================")

	// 创建日志记录器
	demoLogger := &DemoLogger{}

	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 创建API调试服务器
	server, err := routes.CreateStandaloneAPIDebugServer(demoLogger)
	if err != nil {
		demoLogger.Fatal("创建API调试服务器失败", "error", err)
	}

	// 添加一些演示API端点
	addDemoEndpoints(server, demoLogger)

	// 启动HTTP服务器
	httpServer := &http.Server{
		Addr:    ":8082",
		Handler: server,
	}

	// 在goroutine中启动服务器
	go func() {
		demoLogger.Info("API调试系统启动", "port", "8082")
		fmt.Println("\n📚 访问地址:")
		fmt.Println("  - 系统状态: http://localhost:8082/system")
		fmt.Println("  - API文档: http://localhost:8082/api/v1/docs")
		fmt.Println("  - Swagger UI: http://localhost:8082/api/v1/docs/swagger")
		fmt.Println("  - 调试界面: http://localhost:8082/debug")
		fmt.Println("  - 健康检查: http://localhost:8082/health")
		fmt.Println("\n按 Ctrl+C 停止服务器")
		fmt.Println("========================================")

		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			demoLogger.Fatal("HTTP服务器启动失败", "error", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	demoLogger.Info("正在关闭服务器...")

	// 优雅关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := httpServer.Shutdown(ctx); err != nil {
		demoLogger.Fatal("服务器关闭失败", "error", err)
	}

	demoLogger.Info("服务器已关闭")
	fmt.Println("👋 API调试系统演示结束")
}

// addDemoEndpoints 添加演示API端点
func addDemoEndpoints(router *gin.Engine, logger *DemoLogger) {
	// 演示API组
	demo := router.Group("/demo")
	{
		// 简单的GET端点
		demo.GET("/hello", func(c *gin.Context) {
			name := c.DefaultQuery("name", "世界")
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": fmt.Sprintf("你好, %s!", name),
				"timestamp": time.Now().Format(time.RFC3339),
			})
		})

		// POST端点，接收JSON数据
		demo.POST("/echo", func(c *gin.Context) {
			var data map[string]interface{}
			if err := c.ShouldBindJSON(&data); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "无效的JSON数据",
					"message": err.Error(),
				})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"success":   true,
				"message":   "数据回显成功",
				"received":  data,
				"timestamp": time.Now().Format(time.RFC3339),
			})
		})

		// 模拟用户API
		demo.GET("/users", func(c *gin.Context) {
			page := c.DefaultQuery("page", "1")
			size := c.DefaultQuery("size", "10")

			users := []gin.H{
				{"id": 1, "name": "张三", "email": "<EMAIL>"},
				{"id": 2, "name": "李四", "email": "<EMAIL>"},
				{"id": 3, "name": "王五", "email": "<EMAIL>"},
			}

			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "获取用户列表成功",
				"data":    users,
				"pagination": gin.H{
					"page": page,
					"size": size,
					"total": len(users),
				},
			})
		})

		// 模拟创建用户
		demo.POST("/users", func(c *gin.Context) {
			var user struct {
				Name  string `json:"name" binding:"required"`
				Email string `json:"email" binding:"required,email"`
			}

			if err := c.ShouldBindJSON(&user); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "请求参数错误",
					"message": err.Error(),
				})
				return
			}

			// 模拟创建用户
			newUser := gin.H{
				"id":         999,
				"name":       user.Name,
				"email":      user.Email,
				"created_at": time.Now().Format(time.RFC3339),
			}

			c.JSON(http.StatusCreated, gin.H{
				"success": true,
				"message": "用户创建成功",
				"data":    newUser,
			})
		})

		// 模拟错误响应
		demo.GET("/error", func(c *gin.Context) {
			errorType := c.DefaultQuery("type", "400")

			switch errorType {
			case "400":
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "BAD_REQUEST",
					"message": "请求参数错误",
				})
			case "401":
				c.JSON(http.StatusUnauthorized, gin.H{
					"success": false,
					"error":   "UNAUTHORIZED",
					"message": "未授权访问",
				})
			case "404":
				c.JSON(http.StatusNotFound, gin.H{
					"success": false,
					"error":   "NOT_FOUND",
					"message": "资源未找到",
				})
			case "500":
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"error":   "INTERNAL_ERROR",
					"message": "服务器内部错误",
				})
			default:
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "INVALID_ERROR_TYPE",
					"message": "无效的错误类型，支持: 400, 401, 404, 500",
				})
			}
		})

		// 模拟延迟响应
		demo.GET("/delay", func(c *gin.Context) {
			seconds := c.DefaultQuery("seconds", "1")
			if delay, err := time.ParseDuration(seconds + "s"); err == nil && delay <= 10*time.Second {
				time.Sleep(delay)
			}

			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": fmt.Sprintf("延迟 %s 秒后响应", seconds),
				"timestamp": time.Now().Format(time.RFC3339),
			})
		})
	}

	logger.Info("演示API端点已添加", "group", "/demo")
}
