# AI文本游戏架构设计文档

## 1. 架构概述

### 1.1. 基础设施前置条件
在系统部署之前，需要确保以下基础设施服务已经就绪：

- **API网关**: 统一的API入口，负责路由、认证、限流和监控
  - 推荐使用Kong、Nginx或Traefik
  - 支持负载均衡和健康检查
  - 集成外部IDP认证
- **PostgreSQL数据库**: 主要的关系型数据库服务
  - 版本要求：PostgreSQL 13+
  - 支持数据库迁移和备份恢复
  - 配置主从复制和读写分离
- **Redis缓存服务**: 内存缓存和消息队列服务
  - 版本要求：Redis 6+
  - 支持集群模式和持久化
  - 用于缓存、会话存储和消息队列
- **文件存储服务**: 对象存储服务
  - 支持AWS S3、阿里云OSS或MinIO
  - 用于存储游戏资源和用户上传内容
  - 配置CDN加速
- **负载均衡器**: 分发请求到多个服务实例
  - 支持健康检查和故障转移
  - 配置SSL终止和安全策略

### 1.2. 设计原则
- **微服务架构**: 采用微服务架构模式，实现服务的独立部署和扩展
- **事件驱动**: 基于事件驱动架构，支持NPC主动事件创建，确保系统的响应性和可扩展性
- **异步处理优先**: 充分利用Go语言的goroutine和channel，所有AI接口调用采用异步处理模式
- **内容安全**: 多层输入验证和AI内容审核，确保游戏内容安全健康
- **数据一致性**: 通过事务管理和分布式锁确保数据一致性
- **高可用性**: 通过冗余部署和故障转移机制确保系统高可用
- **云原生**: 采用容器化部署和云原生技术栈
- **统一响应格式**: 所有API采用统一的JSON响应格式，简化客户端处理逻辑

### 1.2. 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │  Mobile App     │    │  Admin Panel    │
│   (React/TS)    │    │  (React Native) │    │   (React/TS)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API Gateway          │
                    │   (Kong/Nginx/Traefik)   │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────┴───────┐    ┌─────────┴─────────┐    ┌─────────┴─────────┐
│  Auth Service │    │   Game Service    │    │   AI Service      │
│  (External IDP│    │      (Go)         │    │      (Go)         │
│   Integration)│    │   Gin/Echo        │    │   HTTP Client     │
└───────┬───────┘    └─────────┬─────────┘    └─────────┬─────────┘
        │                      │                        │
        └──────────────────────┼────────────────────────┘
                               │
                    ┌─────────┴─────────┐
                    │   Message Queue   │
                    │   (Redis/NATS)    │
                    └─────────┬─────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────┴───────┐  ┌─────────┴─────────┐  ┌───────┴───────┐
│   PostgreSQL  │  │      Redis        │  │   File Storage│
│   (Primary)   │  │     (Cache)       │  │     (S3/OSS)  │
│  + Migration  │  │   + Pub/Sub       │  │               │
└───────────────┘  └───────────────────┘  └───────────────┘
```

## 2. 技术选型对比

### 2.1. 后端框架选择

#### 方案A: Python + FastAPI
**优点:**
- 与AI模型集成友好，生态丰富
- FastAPI性能优秀，支持异步处理
- 类型提示和自动文档生成
- 丰富的数据科学和机器学习库

**缺点:**
- 相对Go在高并发场景下性能较低
- GIL限制了真正的多线程并行
- 部署复杂度较高

#### 方案B: Node.js + Express/Koa
**优点:**
- 高并发处理能力强
- JavaScript全栈开发，技术栈统一
- 丰富的NPM生态系统
- 事件驱动模型适合实时应用

**缺点:**
- 与AI模型集成相对复杂
- 单线程模型在CPU密集型任务上表现不佳
- 异步编程复杂度高

#### 方案C: Go + Gin/Echo ⭐ **推荐选择**
**优点:**
- 极高的性能和并发处理能力，原生支持goroutine
- 编译型语言，部署简单，单一二进制文件
- 内存占用低，启动速度快
- 强类型系统，代码可靠性高
- 优秀的HTTP客户端库，适合AI接口调用
- 内置并发原语(channel, goroutine)，天然适合异步处理

**缺点:**
- AI生态相对较弱（但HTTP调用足够）
- 学习曲线相对陡峭
- 第三方库相对较少

**最终选择: Go + Gin/Echo**
考虑到项目对异步处理和高并发的重度依赖，以及AI能力主要通过HTTP接口调用实现，Go语言的高性能和原生并发支持使其成为最佳选择。

### 2.2. 前端框架选择

#### 方案A: React + TypeScript
**优点:**
- 生态系统成熟，社区活跃
- TypeScript提供类型安全
- 组件化开发，代码复用性高
- 丰富的UI组件库

#### 方案B: Vue.js + TypeScript
**优点:**
- 学习曲线平缓，开发效率高
- 双向数据绑定，开发体验好
- 渐进式框架，灵活性高
- 中文文档和社区支持好

**最终选择: React + TypeScript**
考虑到团队技术栈和生态系统的成熟度，选择React + TypeScript。

### 2.3. 数据库选择

#### 主数据库: PostgreSQL
**优点:**
- 支持复杂查询和事务
- JSON字段支持，适合存储游戏数据
- 扩展性好，支持分区和复制
- 开源且功能强大

#### 缓存数据库: Redis
**优点:**
- 高性能内存数据库
- 支持多种数据结构
- 支持发布订阅模式
- 持久化选项灵活

## 3. 服务架构设计

### 3.1. 微服务划分

#### 认证服务 (Auth Service)
- **职责**: 外部IDP集成、用户认证、授权管理
- **技术栈**: Go + Gin + OAuth2/OIDC客户端
- **数据存储**: PostgreSQL (用户凭证) + Redis (会话)
- **外部集成**:
  - 支持多种IDP (Auth0, Keycloak, Azure AD, Google等)
  - OAuth 2.0 / OpenID Connect协议
  - JWT Token管理和验证

#### 游戏核心服务 (Game Core Service)
- **职责**: 游戏世界管理、角色系统、场景系统、事件处理
- **技术栈**: Go + Gin + GORM + Goroutines
- **数据存储**: PostgreSQL (游戏数据) + Redis (实时状态)
- **核心特性**:
  - 高并发游戏状态管理
  - 异步事件处理
  - 实时世界演化

#### AI集成服务 (AI Integration Service)
- **职责**: AI接口调用、内容生成、结果处理、请求队列管理
- **技术栈**: Go + 原生HTTP客户端 + Goroutines + Channels
- **数据存储**: Redis (缓存/队列) + PostgreSQL (日志)
- **核心特性**:
  - 异步AI请求处理
  - 智能批量处理
  - 请求去重和缓存
  - 负载均衡和故障转移

#### 数据库迁移服务 (Migration Service)
- **职责**: 数据库schema版本管理、迁移脚本执行
- **技术栈**: Go + golang-migrate + 自定义迁移工具
- **核心特性**:
  - 版本化迁移脚本
  - 回滚机制
  - 数据完整性检查
  - 批量迁移支持

#### 通知服务 (Notification Service)
- **职责**: 实时通知、WebSocket连接管理、消息推送
- **技术栈**: Go + Gorilla WebSocket + Redis Pub/Sub
- **数据存储**: Redis (消息队列) + PostgreSQL (记录)

### 3.2. 外部IDP集成架构

#### 认证流程设计
```
前端 → API网关 → 认证服务 → 外部IDP → 认证服务 → JWT签发 → 前端
```

#### 支持的IDP类型
- **企业级IDP**: Azure AD, Okta, Auth0
- **开源IDP**: Keycloak, Ory Hydra
- **社交登录**: Google, GitHub, Microsoft
- **自定义OIDC**: 支持标准OpenID Connect协议的任何提供商

#### 用户数据映射
```go
type UserClaims struct {
    Sub       string `json:"sub"`        // 外部用户ID
    Email     string `json:"email"`      // 邮箱
    Name      string `json:"name"`       // 显示名称
    Groups    []string `json:"groups"`   // 用户组
    Roles     []string `json:"roles"`    // 角色
}

type GameUser struct {
    ID          string    `json:"id"`
    ExternalID  string    `json:"external_id"`  // 外部IDP用户ID
    Email       string    `json:"email"`
    DisplayName string    `json:"display_name"`
    GameRoles   []string  `json:"game_roles"`   // 游戏内角色
    CreatedAt   time.Time `json:"created_at"`
    LastLogin   time.Time `json:"last_login"`
}
```

### 3.3. 统一响应格式设计

#### 设计理念
为了简化客户端处理逻辑和提供更好的错误处理体验，系统采用统一的JSON响应格式，不直接依赖HTTP状态码来表示业务状态。

#### 标准响应格式
```go
type APIResponse struct {
    Success   bool        `json:"success"`
    Data      interface{} `json:"data,omitempty"`
    Error     *APIError   `json:"error,omitempty"`
    Timestamp time.Time   `json:"timestamp"`
    RequestID string      `json:"request_id"`
}

type APIError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
    Field   string `json:"field,omitempty"`
}
```

#### HTTP状态码使用策略
- **200 OK**: 所有成功的API调用都返回200状态码，业务状态通过响应体中的success字段表示
- **500 Internal Server Error**: 仅在服务器无法处理请求时返回（如服务崩溃、网络异常）
- **其他状态码**: 仅在网络层面或基础设施层面使用（如API网关、负载均衡器的路由错误）

#### 响应示例
```go
// 成功响应
func SuccessResponse(c *gin.Context, data interface{}) {
    c.JSON(http.StatusOK, APIResponse{
        Success:   true,
        Data:      data,
        Timestamp: time.Now(),
        RequestID: c.GetString("request_id"),
    })
}

// 业务错误响应
func ErrorResponse(c *gin.Context, code, message, details string) {
    c.JSON(http.StatusOK, APIResponse{
        Success: false,
        Error: &APIError{
            Code:    code,
            Message: message,
            Details: details,
        },
        Timestamp: time.Now(),
        RequestID: c.GetString("request_id"),
    })
}
```

### 3.4. 数据流设计

#### 游戏行动处理流程
```
用户输入 → 内容安全检查 → API网关 → 认证验证 → 游戏服务 → 异步队列 → AI服务 → AI内容审核 → 结果处理 → 状态更新 → WebSocket推送
```

#### 世界演化流程
```
定时器触发 → 游戏服务 → 收集变化 → AI服务 → 批量处理 → 内容审核 → 状态更新 → 通知相关用户
```

#### 多玩家世界初始化流程
```
世界创建请求 → AI生成世界规模框架 → 区域分配算法 → 玩家分散初始化 → 独立场景生成 → 汇聚点设计
```

#### NPC主动事件流程
```
NPC行为触发器 → 事件创建逻辑 → AI事件生成 → 内容审核 → 事件执行 → 状态更新 → 玩家通知
```

#### 内容安全处理流程
```
用户输入 → 关键词过滤 → 机器学习检测 → 规则引擎验证 → 人工审核队列 → 结果反馈
AI生成内容 → 安全扫描 → 逻辑一致性检查 → 质量评估 → 内容发布
```

#### 数据库迁移流程
```
代码变更 → 生成迁移脚本 → 版本检查 → 执行迁移 → 完整性验证 → 版本更新
```

## 4. 内容安全架构

### 4.1. 内容安全服务设计

#### 输入内容验证服务
```go
type ContentValidator struct {
    keywordFilter    *KeywordFilter
    mlDetector      *MLContentDetector
    ruleEngine      *RuleEngine
    auditQueue      *AuditQueue
}

type ValidationResult struct {
    IsValid     bool     `json:"is_valid"`
    Confidence  float64  `json:"confidence"`
    Violations  []string `json:"violations"`
    Suggestion  string   `json:"suggestion,omitempty"`
}

func (cv *ContentValidator) ValidateUserInput(content string) (*ValidationResult, error) {
    // 多层验证逻辑
    // 1. 关键词过滤
    // 2. 机器学习检测
    // 3. 规则引擎验证
    // 4. 人工审核队列
}
```

#### AI内容审核服务
```go
type AIContentAuditor struct {
    safetyScanner     *SafetyScanner
    consistencyChecker *ConsistencyChecker
    qualityAssessor   *QualityAssessor
}

type AuditResult struct {
    SafetyScore      float64            `json:"safety_score"`
    ConsistencyScore float64            `json:"consistency_score"`
    QualityScore     float64            `json:"quality_score"`
    Issues          []ContentIssue     `json:"issues"`
    Recommendations []string           `json:"recommendations"`
}
```

### 4.2. 多玩家世界初始化架构

#### 世界规模预估服务
```go
type WorldScaleEstimator struct {
    aiClient        *AIClient
    regionAllocator *RegionAllocator
    distanceCalc    *DistanceCalculator
}

type WorldFramework struct {
    Regions     []GeographicRegion `json:"regions"`
    Landmarks   []ImportantLandmark `json:"landmarks"`
    Distances   map[string]int     `json:"distances"`
    Boundaries  WorldBoundary      `json:"boundaries"`
    PlayerSlots int                `json:"player_slots"`
}

func (wse *WorldScaleEstimator) GenerateWorldFramework(description string) (*WorldFramework, error) {
    // AI生成世界概念框架
    // 计算区域分布和距离关系
    // 确定玩家容量和分配策略
}
```

#### 玩家分散初始化服务
```go
type PlayerDistributor struct {
    regionAllocator *RegionAllocator
    sceneGenerator  *SceneGenerator
    convergenceDesigner *ConvergenceDesigner
}

func (pd *PlayerDistributor) AllocatePlayerStartPosition(worldID string, playerID string) (*StartPosition, error) {
    // 根据世界框架分配区域
    // 确保玩家间距离
    // 生成独立起始场景
    // 设计汇聚机制
}
```

## 5. 部署架构

### 4.1. 容器化部署

#### Go服务容器化
```dockerfile
# 多阶段构建，优化镜像大小
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/
COPY --from=builder /app/main .
COPY --from=builder /app/migrations ./migrations
CMD ["./main"]
```

#### 部署配置
- **容器技术**: Docker + Docker Compose (开发) / Kubernetes (生产)
- **镜像管理**: 私有Docker Registry
- **配置管理**: ConfigMap + Secret (K8s) / .env文件 (Docker Compose)
- **健康检查**: HTTP健康检查端点
- **优雅关闭**: 支持SIGTERM信号处理

### 4.2. 云服务选择
- **计算资源**: 阿里云ECS / AWS EC2
- **数据库**: 阿里云RDS PostgreSQL / AWS RDS
- **缓存**: 阿里云Redis / AWS ElastiCache
- **对象存储**: 阿里云OSS / AWS S3
- **负载均衡**: 阿里云SLB / AWS ALB

### 4.3. 监控与日志
- **应用监控**: Prometheus + Grafana
- **日志收集**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **链路追踪**: Jaeger / Zipkin
- **告警系统**: AlertManager + 钉钉/邮件通知

## 5. 安全架构

### 5.1. 认证与授权

#### 外部IDP集成安全
- **认证方式**: OAuth 2.0 / OpenID Connect
- **Token管理**: JWT Access Token + Refresh Token
- **授权模型**: RBAC (基于角色的访问控制)
- **API安全**: Bearer Token + Rate Limiting
- **会话管理**: Redis存储会话状态，支持分布式

#### 安全配置示例
```go
type AuthConfig struct {
    IssuerURL     string `json:"issuer_url"`
    ClientID      string `json:"client_id"`
    ClientSecret  string `json:"client_secret"`
    RedirectURL   string `json:"redirect_url"`
    Scopes        []string `json:"scopes"`
    JWTSecret     string `json:"jwt_secret"`
    TokenExpiry   time.Duration `json:"token_expiry"`
}
```

### 5.2. 数据安全
- **传输加密**: HTTPS/TLS 1.3
- **存储加密**: 数据库字段级加密
- **敏感信息**: 使用专门的密钥管理服务

### 5.3. 网络安全
- **防火墙**: 云服务商安全组配置
- **DDoS防护**: 云服务商DDoS防护服务
- **WAF**: Web应用防火墙

## 6. 性能优化策略

### 6.1. 缓存策略
- **多级缓存**: 浏览器缓存 → CDN → Redis → 数据库
- **缓存模式**: Cache-Aside + Write-Through
- **缓存失效**: TTL + 主动失效

### 6.2. 数据库优化
- **索引优化**: 基于查询模式设计索引
- **分区策略**: 按时间/用户ID分区
- **读写分离**: 主从复制 + 读写分离

### 6.3. 异步处理
- **消息队列**: 异步处理耗时操作
- **批处理**: 合并相似请求，减少AI调用
- **预计算**: 预生成常用内容

## 7. 扩展性设计

### 7.1. 水平扩展
- **无状态服务**: 所有服务设计为无状态
- **负载均衡**: 基于轮询/最少连接的负载均衡
- **自动扩缩容**: 基于CPU/内存使用率的自动扩缩容

### 7.2. 数据扩展
- **数据库分片**: 按用户ID或世界ID分片
- **分布式缓存**: Redis Cluster
- **CDN加速**: 静态资源CDN分发

本架构设计确保了系统的高性能、高可用性和可扩展性，同时考虑了AI集成的特殊需求和文本游戏的业务特点。
