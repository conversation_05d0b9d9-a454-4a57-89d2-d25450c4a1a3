# .gitignore 配置说明文档

## 概述

本项目的 `.gitignore` 文件是根据AI文本游戏项目的技术栈精心设计的，涵盖了Go后端、React前端、数据库、缓存、开发工具等各个方面的忽略规则。

## 技术栈覆盖

### 🔧 后端技术栈
- **Go 1.19+**: 编译产物、测试文件、覆盖率报告
- **Gin框架**: 相关临时文件和日志
- **GORM**: 数据库连接和迁移文件
- **PostgreSQL/SQLite**: 数据库文件和备份
- **Redis**: 数据文件和配置备份
- **JWT认证**: 密钥文件

### 🎨 前端技术栈
- **React 18**: 构建产物和开发缓存
- **TypeScript**: 编译缓存和类型检查文件
- **Vite**: 构建产物、开发缓存、配置时间戳
- **Ant Design**: 主题缓存和自定义样式
- **Redux Toolkit**: 状态管理相关临时文件
- **Node.js/npm**: 依赖目录、日志文件、缓存

### 🗄️ 数据存储
- **数据库文件**: SQLite数据库、PostgreSQL备份
- **缓存文件**: Redis数据文件、应用缓存
- **用户数据**: 上传文件、游戏存档、AI模型

## 忽略规则分类

### 1. 编译和构建产物
```gitignore
# Go编译产物
*.exe, *.dll, *.so, *.dylib, *.test, *.out
/bin/, /dist/, /build/

# 前端构建产物
web/frontend/dist/, web/frontend/build/
web/static/dist/
```

### 2. 依赖和包管理
```gitignore
# Node.js依赖
node_modules/, jspm_packages/

# Go模块缓存
/pkg/mod/

# 包管理器日志
npm-debug.log*, yarn-error.log*
```

### 3. 开发环境文件
```gitignore
# 环境配置
.env, .env.local, .env.development

# IDE配置
.vscode/, .idea/, *.iml

# 开发日志
logs/, *.log, /tmp/*.log
```

### 4. 数据库和缓存
```gitignore
# 数据库文件
*.db, *.sqlite, *.sqlite3, dev.db

# Redis数据
dump.rdb, appendonly.aof

# 缓存目录
cache/, .cache/, tmp/cache/
```

### 5. 安全敏感文件
```gitignore
# 密钥文件
*.key, *.pem, *.crt, id_rsa

# 配置文件
config.json, secrets.json, oauth.json

# JWT密钥
jwt.key, jwt_*.key
```

### 6. 项目特定文件
```gitignore
# AI游戏世界数据
worlds/*.json, worlds/generated/

# 用户数据
uploads/, user_data/, saves/

# AI模型
models/, *.model, *.weights
```

## 重要保留文件

使用 `!` 前缀来确保重要文件不被忽略：

```gitignore
# 示例配置文件
!config.example.json
!.env.example

# 重要脚本
!build.sh
!deploy.sh

# 文档资源
!docs/**/*.png
!web/frontend/public/**
```

## 使用建议

### 🔍 定期检查
- 定期检查是否有新的文件类型需要忽略
- 使用 `git status` 确认没有敏感文件被意外提交
- 检查构建产物是否正确被忽略

### 🛡️ 安全考虑
- 确保所有包含密钥的文件都被忽略
- 数据库文件不应该被提交到版本控制
- 环境配置文件应该使用示例文件代替

### 📝 团队协作
- 新成员应该了解 `.gitignore` 的规则
- 添加新的忽略规则时应该通知团队
- 保持 `.gitignore` 文件的整洁和有序

### 🔧 维护更新
- 当添加新的技术栈时，及时更新忽略规则
- 定期清理不再需要的忽略规则
- 保持注释的准确性和时效性

## 常见问题

### Q: 为什么不忽略 package-lock.json？
A: package-lock.json 确保团队成员使用相同版本的依赖，建议保留在版本控制中。

### Q: 如何处理大文件？
A: 对于大文件，可以使用 Git LFS 或者将其添加到 `.gitignore` 中。

### Q: IDE配置文件是否应该忽略？
A: 个人IDE配置应该忽略，但团队共享的配置（如 .vscode/settings.json.example）可以保留。

### Q: 如何确认文件被正确忽略？
A: 使用 `git check-ignore <文件路径>` 命令检查文件是否被忽略。

## 维护记录

| 日期 | 修改内容 | 修改人 |
|------|----------|--------|
| 2024-XX-XX | 初始版本，覆盖Go+React技术栈 | AI助手 |
| | 添加项目特定的游戏数据忽略规则 | |
| | 完善安全文件和开发工具配置 | |

## 相关文档

- [Git官方文档 - gitignore](https://git-scm.com/docs/gitignore)
- [GitHub gitignore模板](https://github.com/github/gitignore)
- [项目开发环境配置指南](./开发环境配置指南.md)
- [统一启动脚本使用指南](./统一启动脚本使用指南.md)

这个 `.gitignore` 文件确保了项目的版本控制干净整洁，保护了敏感信息，并提供了良好的开发体验。
