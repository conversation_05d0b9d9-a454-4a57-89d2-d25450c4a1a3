# API 文档系统改进指南

## 概述

本文档介绍了 AI 文本游戏项目中 API 文档系统的改进，包括如何为 API 结构体添加详细的中文注释，以及如何使用改进后的文档生成系统。

## 改进内容

### 1. 增强的结构体解析能力

改进后的 API 文档生成系统现在能够：

- 自动解析 Go 结构体的字段定义
- 提取字段的中文注释作为 API 文档描述
- 识别字段的类型、是否必填等属性
- 生成完整的 OpenAPI Schema 定义

### 2. 支持的结构体类型

系统会自动解析以下类型的结构体：

- `GenerateRequest`、`GenerateResponse` - AI 生成相关
- `GenerateSceneRequest`、`GenerateCharacterRequest`、`GenerateEventRequest` - 具体生成请求
- `Response`、`UnifiedAPIResponse`、`APIResponse` - 通用响应结构
- 其他包含 "Request" 或 "Response" 的结构体

### 3. 字段注释格式

为了让 API 文档显示详细的字段描述，请按以下格式为结构体字段添加注释：

```go
type GenerateCharacterRequest struct {
    Prompt  string                 `json:"prompt" binding:"required"`  // 角色生成提示词，必填，描述要生成的角色特征，如"一个勇敢的战士"、"神秘的法师"等
    Context map[string]interface{} `json:"context"`                    // 上下文信息，可选，提供额外的角色背景信息，如世界设定、相关角色等
    WorldID *uuid.UUID             `json:"world_id"`                   // 世界ID，可选，指定角色所属的游戏世界，用于保持世界观一致性
}
```

#### 注释格式说明

每个字段的注释应包含以下信息：

1. **字段用途**：简要说明字段的作用
2. **是否必填**：明确标注"必填"或"可选"
3. **详细描述**：具体说明字段的含义和用法
4. **示例说明**：如果适用，提供具体的示例值

#### 注释示例

```go
// ✅ 好的注释示例
WorldID string `json:"world_id" binding:"required"` // 世界ID，必填，指定要生成场景的游戏世界

// ✅ 更详细的注释示例  
Theme string `json:"theme,omitempty"` // 主题风格，可选，指定场景的主题风格，如"奇幻"、"科幻"、"恐怖"、"现代"等

// ❌ 不好的注释示例
WorldID string `json:"world_id"` // 世界ID
```

## 使用指南

### 1. 添加新的 API 结构体

当添加新的 API 请求或响应结构体时：

1. **命名规范**：结构体名称应包含 "Request" 或 "Response"
2. **添加注释**：为结构体本身添加说明注释
3. **字段注释**：为每个字段添加详细的中文注释
4. **JSON 标签**：正确设置 JSON 标签和验证规则

示例：

```go
// CreateWorldRequest 创建世界请求
type CreateWorldRequest struct {
    Name        string `json:"name" binding:"required"`        // 世界名称，必填，用于标识和显示世界
    Description string `json:"description" binding:"required"` // 世界描述，必填，详细描述世界的背景和设定
    Theme       string `json:"theme" binding:"required"`       // 世界主题，必填，如"奇幻"、"科幻"、"现代"等
    IsPublic    bool   `json:"is_public"`                      // 是否公开，可选，决定其他用户是否可以查看和加入
}
```

### 2. 更新 Swagger 注解

在处理器函数中使用正确的 Swagger 注解：

```go
// @Summary 创建游戏世界
// @Description 创建一个新的游戏世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateWorldRequest true "创建世界请求"
// @Success 200 {object} Response{data=models.World}
// @Failure 400 {object} Response
// @Router /game/worlds [post]
func (h *GameHandler) CreateWorld(c *gin.Context) {
    // 处理器实现
}
```

**注意**：`@Param` 注解中直接引用结构体名称（如 `CreateWorldRequest`），系统会自动使用解析的结构体 Schema。

### 3. 生成和查看文档

#### 自动生成

API 文档会在以下情况自动生成：

- 启动 API 调试系统时
- 调用 `/api/v1/docs` 接口时
- 运行 `go run cmd/apidebug-demo/main.go` 时

#### 手动生成

如果需要手动重新生成文档：

```bash
# 启动 API 调试系统
go run cmd/apidebug-demo/main.go

# 或者启动主服务器（会自动集成 API 调试系统）
./scripts/dev_master.sh
```

#### 查看文档

生成的文档可以通过以下方式查看：

1. **Swagger UI**：`http://localhost:8080/api/v1/docs/swagger`
2. **JSON 格式**：`http://localhost:8080/api/v1/docs/openapi`
3. **本地文件**：`docs/api/openapi.json` 和 `docs/api/openapi.yaml`

## 验证改进效果

### 1. 检查结构体解析

启动系统时，查看日志中的结构体解析信息：

```
[信息] 解析结构体 [name GenerateSceneRequest package handlers]
[信息] 结构体解析完成 [name GenerateSceneRequest fields_count 9]
```

### 2. 检查 Schema 生成

查看日志中的 Schema 生成信息：

```
[信息] 添加结构体schema [name GenerateSceneRequest key handlers.GenerateSceneRequest fields_count 9]
```

### 3. 验证文档内容

在 Swagger UI 中检查：

- 请求参数是否显示完整的字段列表
- 每个字段是否有详细的中文描述
- 字段类型和是否必填的信息是否正确
- 示例值是否合理

## 故障排除

### 1. 结构体未被解析

**问题**：某个结构体没有出现在生成的文档中

**解决方案**：
- 检查结构体名称是否包含 "Request" 或 "Response"
- 确认结构体在正确的文件中（`internal/handlers/*.go` 或 `internal/ai/service.go`）
- 查看启动日志，确认结构体被扫描到

### 2. 字段描述不显示

**问题**：字段有注释但在文档中不显示描述

**解决方案**：
- 检查注释格式是否正确（使用 `//` 而不是 `/* */`）
- 确认注释紧跟在字段定义后面
- 重新生成文档

### 3. 字段类型错误

**问题**：文档中显示的字段类型不正确

**解决方案**：
- 检查 Go 类型定义是否正确
- 确认 JSON 标签设置正确
- 查看类型映射逻辑是否需要更新

## 最佳实践

### 1. 注释编写

- 使用清晰、简洁的中文描述
- 包含字段的用途、是否必填、示例值
- 保持注释的一致性和准确性

### 2. 结构体设计

- 使用有意义的字段名称
- 正确设置 JSON 标签和验证规则
- 合理使用指针类型表示可选字段

### 3. 文档维护

- 在修改结构体时同步更新注释
- 定期检查生成的文档是否准确
- 及时修复发现的问题

## 技术实现

### 1. 核心组件

- **Scanner**：负责扫描和解析 Go 源代码
- **Generator**：负责生成 OpenAPI 规范
- **Service**：协调整个文档生成流程

### 2. 解析流程

1. 扫描指定目录中的 Go 文件
2. 使用 AST 解析结构体定义
3. 提取字段信息和注释
4. 生成对应的 OpenAPI Schema
5. 整合到完整的 API 文档中

### 3. 扩展性

系统设计具有良好的扩展性，可以：

- 添加新的结构体类型支持
- 扩展字段类型映射
- 自定义文档生成规则

---

通过以上改进，API 文档现在能够自动显示详细的请求参数信息，大大提升了 API 的可用性和开发效率。
