# AI文本游戏部署指南

## 概述

本文档详细说明了如何在不同环境中部署AI文本游戏"I am NPC"后端系统，包括开发环境、测试环境和生产环境的部署方案。

## 环境要求

### 最低系统要求
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB可用空间
- **操作系统**: Linux (Ubuntu 20.04+), macOS, Windows

### 软件依赖
- **Go**: 1.19+
- **PostgreSQL**: 13+
- **Redis**: 6+
- **Docker**: 20.10+ (可选)
- **Docker Compose**: 1.29+ (可选)

## 开发环境部署

### 1. 源码部署

#### 步骤1: 克隆代码
```bash
git clone https://github.com/your-org/ai-text-game-iam-npc.git
cd ai-text-game-iam-npc
```

#### 步骤2: 安装依赖
```bash
# 安装Go依赖
go mod download

# 验证依赖
go mod verify
```

#### 步骤3: 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env
```

**环境变量配置示例**:
```bash
# 服务器配置
SERVER_PORT=8080
SERVER_HOST=localhost
GIN_MODE=debug

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=ai_text_game
DB_SSL_MODE=disable

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-key-change-in-production
JWT_EXPIRES_IN=24h

# OAuth配置
OAUTH_GOOGLE_CLIENT_ID=your_google_client_id
OAUTH_GOOGLE_CLIENT_SECRET=your_google_client_secret
OAUTH_GITHUB_CLIENT_ID=your_github_client_id
OAUTH_GITHUB_CLIENT_SECRET=your_github_client_secret

# AI配置
AI_PROVIDER=mock
AI_OPENAI_API_KEY=your_openai_api_key
AI_OPENAI_MODEL=gpt-3.5-turbo

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=text
```

#### 步骤4: 数据库设置
```bash
# 创建数据库
createdb ai_text_game

# 运行数据库迁移
go run cmd/migrate/main.go up
```

#### 步骤5: 启动服务
```bash
# 开发模式启动
go run cmd/server/main.go

# 或者使用热重载工具
go install github.com/cosmtrek/air@latest
air
```

### 2. Docker开发环境

#### 创建docker-compose.dev.yml
```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=debug
      - DB_HOST=postgres
      - REDIS_HOST=redis
    volumes:
      - .:/app
      - /app/vendor
    depends_on:
      - postgres
      - redis
    command: air

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: ai_text_game
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

#### 启动开发环境
```bash
# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f app

# 停止服务
docker-compose -f docker-compose.dev.yml down
```

## 生产环境部署

### 1. Docker生产部署

#### 创建Dockerfile
```dockerfile
# 构建阶段
FROM golang:1.19-alpine AS builder

WORKDIR /app

# 安装依赖
COPY go.mod go.sum ./
RUN go mod download

# 复制源码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main cmd/server/main.go

# 运行阶段
FROM alpine:latest

# 安装CA证书
RUN apk --no-cache add ca-certificates tzdata

WORKDIR /root/

# 复制二进制文件
COPY --from=builder /app/main .
COPY --from=builder /app/migrations ./migrations

# 暴露端口
EXPOSE 8080

# 运行应用
CMD ["./main"]
```

#### 创建docker-compose.prod.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - DB_HOST=postgres
      - REDIS_HOST=redis
    env_file:
      - .env.prod
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER}"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:6-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

#### Nginx配置 (nginx.conf)
```nginx
events {
    worker_connections 1024;
}

http {
    upstream app {
        server app:8080;
    }

    # HTTP重定向到HTTPS
    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS配置
    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # SSL配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

        # 代理配置
        location / {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

### 2. Kubernetes部署

#### 创建命名空间
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ai-text-game
```

#### 配置管理
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: ai-text-game
data:
  GIN_MODE: "release"
  DB_HOST: "postgres-service"
  REDIS_HOST: "redis-service"
  LOG_LEVEL: "info"
```

#### 密钥管理
```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: ai-text-game
type: Opaque
data:
  DB_PASSWORD: <base64-encoded-password>
  JWT_SECRET: <base64-encoded-secret>
  OAUTH_GOOGLE_CLIENT_SECRET: <base64-encoded-secret>
```

#### 应用部署
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-text-game-app
  namespace: ai-text-game
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-text-game-app
  template:
    metadata:
      labels:
        app: ai-text-game-app
    spec:
      containers:
      - name: app
        image: your-registry/ai-text-game:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: app-config
        - secretRef:
            name: app-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

#### 服务配置
```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ai-text-game-service
  namespace: ai-text-game
spec:
  selector:
    app: ai-text-game-app
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
```

#### Ingress配置
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-text-game-ingress
  namespace: ai-text-game
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.your-domain.com
    secretName: ai-text-game-tls
  rules:
  - host: api.your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-text-game-service
            port:
              number: 80
```

### 3. 部署脚本

#### 创建deploy.sh
```bash
#!/bin/bash

set -e

# 配置
ENVIRONMENT=${1:-production}
IMAGE_TAG=${2:-latest}
REGISTRY="your-registry.com"
APP_NAME="ai-text-game"

echo "部署环境: $ENVIRONMENT"
echo "镜像标签: $IMAGE_TAG"

# 构建镜像
echo "构建Docker镜像..."
docker build -t $REGISTRY/$APP_NAME:$IMAGE_TAG .

# 推送镜像
echo "推送镜像到仓库..."
docker push $REGISTRY/$APP_NAME:$IMAGE_TAG

# 部署到Kubernetes
if [ "$ENVIRONMENT" = "kubernetes" ]; then
    echo "部署到Kubernetes..."
    kubectl apply -f k8s/
    kubectl set image deployment/ai-text-game-app app=$REGISTRY/$APP_NAME:$IMAGE_TAG -n ai-text-game
    kubectl rollout status deployment/ai-text-game-app -n ai-text-game
elif [ "$ENVIRONMENT" = "docker" ]; then
    echo "使用Docker Compose部署..."
    docker-compose -f docker-compose.prod.yml down
    docker-compose -f docker-compose.prod.yml up -d
fi

echo "部署完成！"
```

## 监控和维护

### 1. 健康检查
```go
// 在main.go中添加健康检查端点
router.GET("/health", func(c *gin.Context) {
    c.JSON(200, gin.H{
        "status": "healthy",
        "timestamp": time.Now(),
        "version": "1.0.0",
    })
})

router.GET("/ready", func(c *gin.Context) {
    // 检查数据库连接
    if err := db.Ping(); err != nil {
        c.JSON(503, gin.H{"status": "not ready"})
        return
    }
    c.JSON(200, gin.H{"status": "ready"})
})
```

### 2. 日志管理
```bash
# 查看应用日志
docker-compose logs -f app

# Kubernetes日志
kubectl logs -f deployment/ai-text-game-app -n ai-text-game

# 日志轮转配置
# 在docker-compose.yml中添加
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 3. 备份策略
```bash
# 数据库备份脚本
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h postgres -U postgres ai_text_game > $BACKUP_DIR/backup_$DATE.sql
find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete
```

### 4. 更新部署
```bash
# 滚动更新
kubectl set image deployment/ai-text-game-app app=your-registry/ai-text-game:v2.0.0 -n ai-text-game

# 回滚
kubectl rollout undo deployment/ai-text-game-app -n ai-text-game
```

这个部署指南提供了从开发到生产的完整部署方案，确保系统能够稳定、安全地运行在各种环境中。
