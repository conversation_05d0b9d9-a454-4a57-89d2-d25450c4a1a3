# AI文本游戏 API 文档

## 概述

本文档描述了AI文本游戏"I am NPC"的RESTful API接口，包括认证、游戏管理、AI生成和内容校验等功能。

## 基础信息

- **基础URL**: `http://localhost:8080/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

所有API响应都遵循统一的格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "error": null,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "data": null,
  "error": {
    "code": "ERROR_CODE",
    "details": "详细错误信息"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 认证相关 API

### 1. OAuth登录

**请求**
```
GET /auth/login/{provider}
```

**参数**
- `provider`: OAuth提供商 (`google`, `github`)

**响应**
```json
{
  "success": true,
  "data": {
    "redirect_url": "https://oauth.provider.com/authorize?..."
  }
}
```

### 2. OAuth回调

**请求**
```
GET /auth/callback/{provider}?code={code}&state={state}
```

**响应**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
    "expires_in": 3600,
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "用户名",
      "avatar": "头像URL"
    }
  }
}
```

### 3. 刷新Token

**请求**
```
POST /auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "expires_in": 3600
  }
}
```

## 游戏世界 API

### 1. 创建世界

**请求**
```
POST /game/worlds
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "魔法世界",
  "description": "一个充满魔法的奇幻世界",
  "is_public": true,
  "max_players": 10,
  "config": {
    "time_speed": 1.0,
    "difficulty": "normal"
  }
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": "world-uuid",
    "name": "魔法世界",
    "description": "一个充满魔法的奇幻世界",
    "is_public": true,
    "max_players": 10,
    "current_players": 1,
    "creator_id": "user-uuid",
    "config": {
      "time_speed": 1.0,
      "difficulty": "normal"
    },
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2. 获取世界列表

**请求**
```
GET /game/worlds?page=1&page_size=10&is_public=true
Authorization: Bearer {token}
```

**参数**
- `page`: 页码 (默认: 1)
- `page_size`: 每页数量 (默认: 10, 最大: 100)
- `is_public`: 是否公开 (可选)

**响应**
```json
{
  "success": true,
  "data": {
    "worlds": [
      {
        "id": "world-uuid",
        "name": "魔法世界",
        "description": "一个充满魔法的奇幻世界",
        "is_public": true,
        "current_players": 5,
        "max_players": 10,
        "creator": {
          "id": "user-uuid",
          "name": "创建者"
        },
        "created_at": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 10,
      "total": 25,
      "total_pages": 3
    }
  }
}
```

### 3. 获取世界详情

**请求**
```
GET /game/worlds/{world_id}
Authorization: Bearer {token}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": "world-uuid",
    "name": "魔法世界",
    "description": "一个充满魔法的奇幻世界",
    "is_public": true,
    "max_players": 10,
    "current_players": 5,
    "creator_id": "user-uuid",
    "config": {
      "time_speed": 1.0,
      "difficulty": "normal"
    },
    "members": [
      {
        "user_id": "user-uuid",
        "role": "creator",
        "joined_at": "2024-01-01T12:00:00Z"
      }
    ],
    "scenes_count": 15,
    "characters_count": 8,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 4. 加入世界

**请求**
```
POST /game/worlds/{world_id}/join
Authorization: Bearer {token}
```

**响应**
```json
{
  "success": true,
  "message": "成功加入世界",
  "data": {
    "membership": {
      "world_id": "world-uuid",
      "user_id": "user-uuid",
      "role": "player",
      "joined_at": "2024-01-01T12:00:00Z"
    }
  }
}
```

## 角色管理 API

### 1. 创建角色

**请求**
```
POST /game/characters
Authorization: Bearer {token}
Content-Type: application/json

{
  "world_id": "world-uuid",
  "name": "艾莉娅",
  "description": "一位年轻的法师",
  "attributes": {
    "strength": 10,
    "intelligence": 18,
    "dexterity": 14,
    "constitution": 12
  },
  "appearance": {
    "height": "165cm",
    "hair_color": "金色",
    "eye_color": "蓝色"
  }
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": "character-uuid",
    "world_id": "world-uuid",
    "user_id": "user-uuid",
    "name": "艾莉娅",
    "description": "一位年轻的法师",
    "attributes": {
      "strength": 10,
      "intelligence": 18,
      "dexterity": 14,
      "constitution": 12
    },
    "appearance": {
      "height": "165cm",
      "hair_color": "金色",
      "eye_color": "蓝色"
    },
    "inventory": [],
    "relationships": {},
    "memory": [],
    "current_scene_id": null,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2. 获取角色详情

**请求**
```
GET /game/characters/{character_id}
Authorization: Bearer {token}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": "character-uuid",
    "world_id": "world-uuid",
    "user_id": "user-uuid",
    "name": "艾莉娅",
    "description": "一位年轻的法师",
    "attributes": {
      "strength": 10,
      "intelligence": 18,
      "dexterity": 14,
      "constitution": 12
    },
    "appearance": {
      "height": "165cm",
      "hair_color": "金色",
      "eye_color": "蓝色"
    },
    "inventory": [
      {
        "id": "item-1",
        "name": "法师袍",
        "type": "armor",
        "equipped": true
      }
    ],
    "relationships": {
      "npc-1": {
        "name": "老法师",
        "relationship": "mentor",
        "affinity": 80
      }
    },
    "memory": [
      {
        "timestamp": "2024-01-01T12:00:00Z",
        "event": "遇见了老法师",
        "importance": 8
      }
    ],
    "current_scene_id": "scene-uuid",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

## AI生成 API

### 1. 生成内容

**请求**
```
POST /ai/generate
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "scene_description",
  "context": {
    "world_id": "world-uuid",
    "character_id": "character-uuid",
    "current_scene": "森林入口",
    "action": "向北走"
  },
  "parameters": {
    "length": "medium",
    "style": "fantasy"
  }
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "content": "你沿着蜿蜒的小径向北走去，古老的橡树在你头顶形成了一个绿色的拱顶。阳光透过树叶洒下斑驳的光影，远处传来鸟儿的啁啾声。",
    "metadata": {
      "generation_time": 1.2,
      "tokens_used": 45,
      "provider": "mock"
    },
    "suggestions": [
      "继续向北探索",
      "仔细观察周围",
      "寻找可用的物品"
    ]
  }
}
```

### 2. 生成角色

**请求**
```
POST /ai/generate/character
Authorization: Bearer {token}
Content-Type: application/json

{
  "world_id": "world-uuid",
  "character_type": "npc",
  "context": {
    "location": "村庄酒馆",
    "role": "酒馆老板"
  }
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "character": {
      "name": "老汤姆",
      "description": "一位和蔼的中年男子，经营着村里唯一的酒馆",
      "attributes": {
        "charisma": 16,
        "wisdom": 14,
        "constitution": 13
      },
      "personality": {
        "traits": ["友善", "健谈", "可靠"],
        "background": "在这个村庄生活了30年的酒馆老板"
      },
      "dialogue_samples": [
        "欢迎来到我的酒馆，旅行者！",
        "这里有最好的麦酒和最新的消息。"
      ]
    }
  }
}
```

## 内容校验 API

### 1. 校验内容

**请求**
```
POST /validation/validate
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "这是需要校验的文本内容",
  "type": "user_input",
  "context": {
    "user_id": "user-uuid",
    "world_id": "world-uuid"
  }
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "is_valid": true,
    "filtered_content": "这是需要校验的文本内容",
    "violations": [],
    "score": {
      "safety": 0.95,
      "appropriateness": 0.98
    },
    "metadata": {
      "check_time": 0.1,
      "filters_applied": ["profanity", "security", "ai_moderation"]
    }
  }
}
```

### 2. 获取校验统计

**请求**
```
GET /validation/stats?period=7d
Authorization: Bearer {token}
```

**响应**
```json
{
  "success": true,
  "data": {
    "period": "7d",
    "total_validations": 1250,
    "violations": {
      "profanity": 15,
      "security": 3,
      "inappropriate": 8
    },
    "success_rate": 0.979,
    "average_response_time": 0.12
  }
}
```

## 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `INVALID_REQUEST` | 400 | 请求参数无效 |
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `CONFLICT` | 409 | 资源冲突 |
| `RATE_LIMITED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务不可用 |

## 请求限制

- **认证API**: 每分钟10次
- **游戏API**: 每分钟100次
- **AI生成API**: 每分钟20次
- **校验API**: 每分钟50次

## SDK和示例

### JavaScript示例

```javascript
// 初始化客户端
const client = new AIGameClient({
  baseURL: 'http://localhost:8080/api/v1',
  token: 'your-jwt-token'
});

// 创建世界
const world = await client.worlds.create({
  name: '我的世界',
  description: '一个神奇的世界',
  is_public: true,
  max_players: 10
});

// 生成内容
const content = await client.ai.generate({
  type: 'scene_description',
  context: {
    world_id: world.id,
    action: '探索森林'
  }
});
```

### cURL示例

```bash
# 获取世界列表
curl -X GET "http://localhost:8080/api/v1/game/worlds" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"

# 创建角色
curl -X POST "http://localhost:8080/api/v1/game/characters" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "world_id": "world-uuid",
    "name": "我的角色",
    "description": "一个勇敢的冒险者"
  }'
```

这个API文档提供了完整的接口说明，帮助开发者快速集成和使用AI文本游戏的后端服务。
