# API请求超时和性能优化配置指南

## 概述

本文档介绍如何配置和优化AI文本游戏系统的API请求超时设置，以解决请求挂起和性能问题。

## 问题分析

### 常见的API请求挂起原因

1. **AI服务响应慢**：Windmill AI服务处理复杂请求时可能需要较长时间
2. **网络连接问题**：网络延迟或不稳定导致请求超时
3. **端口转发配置**：VSCode SSH端口转发配置不当
4. **服务器资源不足**：服务器CPU或内存不足导致处理缓慢
5. **数据库查询慢**：复杂的数据库查询导致响应延迟

### 已实施的解决方案

1. **修复了服务器超时配置错误**
2. **优化了AI服务轮询逻辑**
3. **添加了请求超时中间件**
4. **增强了日志记录**
5. **修复了Swagger UI的CSP配置**

## 配置说明

### 1. 服务器超时配置

在 `internal/config/config.go` 中配置：

```go
Server: ServerConfig{
    ReadTimeout:  30 * time.Second,  // 读取超时
    WriteTimeout: 30 * time.Second,  // 写入超时
    IdleTimeout:  120 * time.Second, // 空闲超时
}
```

环境变量配置：
```bash
SERVER_READ_TIMEOUT=30s
SERVER_WRITE_TIMEOUT=30s
SERVER_IDLE_TIMEOUT=120s
```

### 2. AI服务超时配置

```go
AI: AIConfig{
    Timeout:      30 * time.Second,  // HTTP请求超时
    MaxRetries:   3,                 // 最大重试次数
    RetryDelay:   1 * time.Second,   // 重试延迟
}
```

环境变量配置：
```bash
AI_TIMEOUT=30s
AI_MAX_RETRIES=3
AI_RETRY_DELAY=1s
```

### 3. 请求超时中间件

系统自动为所有请求添加60秒超时：

```go
r.Use(auth.RequestTimeoutMiddleware(60 * time.Second))
```

### 4. AI轮询优化

- 轮询间隔：2秒（减少服务器压力）
- 最大轮询时间：2分钟（避免长时间挂起）
- 增强错误日志记录

## 诊断工具

### 1. API问题诊断脚本

使用提供的诊断脚本检查系统状态：

```bash
# 检查本地服务器
./scripts/diagnose_api_issues.sh

# 检查远程服务器
./scripts/diagnose_api_issues.sh remote-host 8080
```

### 2. 日志监控

开发模式下，系统会记录详细的请求信息：

- 请求开始时间
- 请求耗时
- 超时警告
- 慢请求提醒（>1秒）

### 3. 浏览器开发者工具

检查浏览器控制台中的网络请求：

1. 打开开发者工具（F12）
2. 切换到Network标签
3. 发送API请求
4. 查看请求状态和响应时间

## 性能优化建议

### 1. 服务器端优化

1. **增加服务器资源**：确保足够的CPU和内存
2. **优化数据库查询**：添加索引，优化复杂查询
3. **启用缓存**：对频繁访问的数据启用缓存
4. **负载均衡**：在高负载情况下使用负载均衡

### 2. 客户端优化

1. **请求去重**：避免重复发送相同请求
2. **请求队列**：限制并发请求数量
3. **超时处理**：为前端请求设置合理的超时时间
4. **错误重试**：实现智能重试机制

### 3. 网络优化

1. **CDN加速**：对静态资源使用CDN
2. **压缩传输**：启用gzip压缩
3. **HTTP/2**：使用HTTP/2协议
4. **连接复用**：启用HTTP连接复用

## 故障排除

### 1. Swagger UI无法加载

**症状**：浏览器控制台显示CSP错误

**解决方案**：
- 已修复CSP配置，允许从unpkg.com加载资源
- 确保网络可以访问外部CDN

### 2. API请求一直挂起

**症状**：请求状态显示为pending，长时间无响应

**排查步骤**：
1. 检查服务器是否正常运行
2. 验证端口转发配置
3. 查看服务器日志
4. 运行诊断脚本

**解决方案**：
- 重启服务器
- 重新配置端口转发
- 调整超时设置

### 3. AI生成请求超时

**症状**：AI相关API返回超时错误

**解决方案**：
1. 检查Windmill服务状态
2. 调整AI_TIMEOUT环境变量
3. 启用Mock模式进行测试：`AI_MOCK_ENABLED=true`

### 4. 数据库连接超时

**症状**：数据库相关操作超时

**解决方案**：
1. 检查数据库连接配置
2. 优化数据库查询
3. 调整连接池设置

## 监控和告警

### 1. 关键指标

- API响应时间
- 错误率
- 超时率
- 并发请求数
- 服务器资源使用率

### 2. 日志分析

重要的日志关键词：
- `[TIMEOUT]`：请求超时
- `[SLOW]`：慢请求
- `轮询超时`：AI服务超时
- `连接失败`：网络问题

### 3. 告警设置

建议设置以下告警：
- API响应时间 > 5秒
- 错误率 > 5%
- 服务器CPU使用率 > 80%
- 内存使用率 > 90%

## 环境变量参考

```bash
# 服务器配置
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
SERVER_READ_TIMEOUT=30s
SERVER_WRITE_TIMEOUT=30s
SERVER_IDLE_TIMEOUT=120s
ENVIRONMENT=development

# AI服务配置
AI_PROVIDER=windmill
AI_BASE_URL=https://wm.atjog.com
AI_TOKEN=your-token-here
AI_TIMEOUT=30s
AI_MAX_RETRIES=3
AI_RETRY_DELAY=1s
AI_MOCK_ENABLED=false

# 数据库配置
DB_NAME=dev.db
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_MAX_LIFETIME=5m

# 开发模式
SKIP_AUTH=true
```

## 总结

通过以上配置和优化，系统的稳定性和性能应该得到显著改善。如果仍然遇到问题，请：

1. 运行诊断脚本收集信息
2. 检查服务器日志
3. 调整相关配置参数
4. 联系技术支持团队

定期监控系统性能指标，根据实际使用情况调整配置参数，确保系统稳定运行。
