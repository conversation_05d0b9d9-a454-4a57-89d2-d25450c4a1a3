# SQLite数据库使用说明

## 📋 概述

本文档详细说明了AI文本游戏项目中SQLite数据库的使用方法、配置选项和最佳实践。SQLite作为轻量级的嵌入式数据库，为开发环境提供了便捷的数据存储解决方案。

## 🎯 SQLite的优势

### 开发环境优势
- ✅ **零配置启动**：无需安装和配置数据库服务器
- ✅ **轻量级**：单个文件存储，便于备份和迁移
- ✅ **高性能**：对于开发和测试场景性能优异
- ✅ **完全兼容**：与PostgreSQL在GORM层面完全兼容
- ✅ **事务支持**：完整的ACID事务支持

### 适用场景
- 🔧 **本地开发**：快速启动开发环境
- 🧪 **单元测试**：隔离的测试环境
- 📱 **原型开发**：快速验证功能
- 🎓 **学习演示**：教学和演示用途

## 🚀 快速开始

### 1. 使用SQLite启动开发环境

```bash
# 一键启动SQLite开发环境
./scripts/dev_with_sqlite.sh
```

这个命令会：
- 自动备份现有的.env配置文件
- 设置SQLite数据库配置
- 初始化数据库表结构
- 创建测试数据
- 启动开发服务器

### 2. 手动配置SQLite

```bash
# 复制SQLite配置模板
cp .env.development.sqlite .env

# 手动启动服务
go run cmd/simple-server/main.go
```

## ⚙️ SQLite配置详解

### 基础配置

```bash
# SQLite数据库配置
DB_NAME=dev.db                     # 数据库文件名（以.db结尾自动使用SQLite）
DB_TYPE=sqlite                     # 明确指定数据库类型
DB_AUTO_MIGRATE=true               # 自动迁移表结构
```

### 性能优化配置

```bash
# SQLite性能优化配置
SQLITE_JOURNAL_MODE=WAL            # 使用WAL模式提升并发性能
SQLITE_SYNCHRONOUS=NORMAL          # 平衡性能和安全性
SQLITE_CACHE_SIZE=10000            # 设置缓存大小（页数）
```

### 连接池配置

```bash
# 连接池配置（适用于SQLite）
DB_MAX_OPEN_CONNS=25               # 最大打开连接数
DB_MAX_IDLE_CONNS=5                # 最大空闲连接数
DB_MAX_LIFETIME=5m                 # 连接最大生存时间
```

## 🗄️ 数据库文件管理

### 数据库文件位置

```bash
# 默认数据库文件位置
data/dev.db                        # 开发环境数据库
data/dev.db-wal                    # WAL日志文件
data/dev.db-shm                    # 共享内存文件
```

### 数据库备份

```bash
# 备份数据库文件
cp data/dev.db data/dev.db.backup.$(date +%Y%m%d_%H%M%S)

# 恢复数据库
cp data/dev.db.backup.20250807_021758 data/dev.db
```

### 数据库清理

```bash
# 使用脚本清理数据库
./scripts/dev_with_sqlite.sh --clean

# 手动清理数据库文件
rm -f data/dev.db data/dev.db-wal data/dev.db-shm
```

## 🔧 数据库操作

### 1. 数据库初始化

```bash
# 仅初始化数据库，不启动服务器
./scripts/dev_with_sqlite.sh --init-only
```

初始化过程包括：
- 创建所有必要的数据表
- 设置索引和约束
- 插入测试数据
- 验证表结构

### 2. 数据库迁移

项目使用GORM的AutoMigrate功能，支持自动迁移：

```go
// 自动迁移所有模型
models := []interface{}{
    &models.User{},
    &models.UserStats{},
    &models.World{},
    &models.Scene{},
    &models.Character{},
    &models.Entity{},
    &models.Event{},
    &models.AIInteraction{},
    &models.ValidationLog{},
}

for _, model := range models {
    if err := db.AutoMigrate(model); err != nil {
        log.Fatalf("创建表失败 %T: %v", model, err)
    }
}
```

### 3. 数据库查询优化

SQLite支持的查询优化：

```sql
-- 创建索引优化查询性能
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_worlds_creator_id ON worlds(creator_id);
CREATE INDEX idx_characters_world_id ON characters(world_id);

-- JSON字段查询优化
CREATE INDEX idx_users_preferences_theme 
ON users(JSON_EXTRACT(preferences, '$.ui.theme'));
```

## 🔍 数据库调试和监控

### 1. 数据库状态检查

```bash
# 运行数据库调试脚本
go run debug_db.go
```

这个脚本会显示：
- 数据库中的所有表
- 表中的数据统计
- 连接状态信息

### 2. 连接池监控

```go
// 获取连接池状态
sqlDB, err := db.DB()
if err == nil {
    stats := sqlDB.Stats()
    fmt.Printf("连接池状态 - 打开连接: %d, 使用中: %d, 空闲: %d\n", 
        stats.OpenConnections, stats.InUse, stats.Idle)
}
```

### 3. 查询日志

开发环境默认启用查询日志：

```go
// GORM日志配置
db, err = gorm.Open(sqlite.Open(cfg.DBName), &gorm.Config{
    Logger: logger.Default.LogMode(logger.Info), // 显示所有SQL查询
})
```

## 🔄 SQLite与PostgreSQL兼容性

### 数据类型映射

| Go类型 | SQLite类型 | PostgreSQL类型 |
|--------|------------|----------------|
| string | TEXT | VARCHAR |
| int | INTEGER | INTEGER |
| bool | INTEGER | BOOLEAN |
| time.Time | TEXT | TIMESTAMP |
| []byte | BLOB | BYTEA |
| JSON | TEXT | JSONB |

### 功能兼容性

| 功能 | SQLite支持 | PostgreSQL支持 | 说明 |
|------|------------|----------------|------|
| 基础CRUD | ✅ | ✅ | 完全兼容 |
| 事务 | ✅ | ✅ | 完全兼容 |
| 外键约束 | ✅ | ✅ | 需要启用 |
| JSON查询 | ✅ | ✅ | 语法略有差异 |
| 全文搜索 | ⚠️ | ✅ | SQLite需要FTS扩展 |
| 分区表 | ❌ | ✅ | SQLite不支持 |

### 迁移注意事项

从SQLite迁移到PostgreSQL时需要注意：

1. **JSON查询语法**：
   ```sql
   -- SQLite
   JSON_EXTRACT(data, '$.field')
   
   -- PostgreSQL
   data->>'field'
   ```

2. **布尔类型**：
   ```sql
   -- SQLite存储为INTEGER (0/1)
   -- PostgreSQL存储为BOOLEAN (true/false)
   ```

3. **时间戳格式**：
   ```sql
   -- SQLite存储为TEXT
   -- PostgreSQL存储为TIMESTAMP
   ```

## 🛠️ 故障排除

### 常见问题

1. **数据库文件锁定**
   ```bash
   # 错误：database is locked
   # 解决：关闭所有连接到数据库的进程
   ./scripts/stop_dev_servers.sh
   ```

2. **WAL模式问题**
   ```bash
   # 如果遇到WAL模式问题，可以切换到DELETE模式
   sqlite3 data/dev.db "PRAGMA journal_mode=DELETE;"
   ```

3. **磁盘空间不足**
   ```bash
   # 清理WAL文件
   sqlite3 data/dev.db "PRAGMA wal_checkpoint(TRUNCATE);"
   ```

4. **表结构不匹配**
   ```bash
   # 重新初始化数据库
   ./scripts/dev_with_sqlite.sh --clean
   ./scripts/dev_with_sqlite.sh --init-only
   ```

### 性能调优

1. **启用WAL模式**（默认已启用）
   ```sql
   PRAGMA journal_mode=WAL;
   ```

2. **调整缓存大小**
   ```sql
   PRAGMA cache_size=10000;  -- 10000页缓存
   ```

3. **优化同步模式**
   ```sql
   PRAGMA synchronous=NORMAL;  -- 平衡性能和安全
   ```

4. **启用外键约束**
   ```sql
   PRAGMA foreign_keys=ON;
   ```

## 📊 性能基准

### 开发环境性能表现

| 操作类型 | SQLite性能 | 说明 |
|----------|------------|------|
| 简单查询 | 优秀 | 单表查询性能优异 |
| 复杂连接 | 良好 | 多表连接性能可接受 |
| 批量插入 | 优秀 | 事务批量插入性能好 |
| 并发读取 | 优秀 | WAL模式支持并发读 |
| 并发写入 | 一般 | 写入操作串行化 |

### 适用规模

- **用户数量**：< 1000（开发测试）
- **数据量**：< 100MB（开发测试）
- **并发连接**：< 10（开发测试）
- **QPS**：< 1000（开发测试）

## 🔗 相关资源

### 官方文档
- [SQLite官方文档](https://www.sqlite.org/docs.html)
- [GORM SQLite驱动](https://gorm.io/docs/connecting_to_the_database.html#SQLite)

### 项目相关文档
- [配置文件管理指南](./配置文件管理指南.md)
- [开发环境使用指南](./开发环境使用指南.md)
- [数据库兼容性文档](./database-compatibility.md)

## 📝 最佳实践总结

1. **开发环境首选SQLite**：快速启动，零配置
2. **定期备份数据库文件**：避免数据丢失
3. **使用WAL模式**：提升并发性能
4. **合理设置缓存**：平衡内存使用和性能
5. **生产环境迁移PostgreSQL**：更好的并发和扩展性
