# AI文本游戏系统架构文档

## 概述

本文档详细描述了AI文本游戏"I am NPC"的系统架构设计，包括技术选型、模块划分、数据流程和部署架构。

## 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   移动应用      │    │   第三方集成    │
│   (React/Vue)   │    │   (React Native)│    │   (Webhook)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API网关/负载均衡      │
                    │      (Nginx/Traefik)      │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      Go后端服务           │
                    │      (Gin Framework)      │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                        │                        │
┌───────┴───────┐    ┌───────────┴───────────┐    ┌───────┴───────┐
│   PostgreSQL   │    │       Redis          │    │   AI服务      │
│   (主数据库)   │    │   (缓存/会话/队列)   │    │  (OpenAI等)   │
└───────────────┘    └───────────────────────┘    └───────────────┘
```

## 技术栈

### 后端技术
- **编程语言**: Go 1.19+
- **Web框架**: Gin (轻量级、高性能)
- **ORM**: GORM (类型安全、功能丰富)
- **数据库**: PostgreSQL 13+ (JSONB支持、事务性)
- **缓存**: Redis 6+ (会话存储、消息队列)
- **认证**: OAuth2/OIDC + JWT
- **日志**: 结构化日志 (兼容Go 1.19)

### 开发工具
- **依赖管理**: Go Modules
- **数据库迁移**: golang-migrate
- **测试框架**: testify
- **代码质量**: go vet, go fmt, golangci-lint

## 模块架构

### 1. 认证模块 (internal/auth)
```go
// 核心功能
- OAuth2集成 (Google, GitHub, Discord等)
- JWT Token管理
- 用户会话管理
- 权限验证中间件

// 主要组件
- AuthService: 认证业务逻辑
- OAuthProvider: OAuth提供商接口
- JWTManager: Token生成和验证
- AuthMiddleware: 请求认证中间件
```

### 2. 游戏核心模块 (internal/game)
```go
// 核心功能
- 世界管理 (创建、更新、删除)
- 角色系统 (属性、技能、装备)
- 场景管理 (地图、连接、环境)
- 事件处理 (玩家行动、AI响应)

// 主要组件
- WorldService: 世界业务逻辑
- CharacterService: 角色管理
- SceneService: 场景管理
- EventService: 事件处理
```

### 3. AI集成模块 (internal/ai)
```go
// 核心功能
- 多AI提供商支持
- 内容生成 (场景、对话、事件)
- 模拟模式 (测试环境)
- 历史记录管理

// 主要组件
- AIService: AI服务接口
- ProviderManager: 提供商管理
- ContentGenerator: 内容生成器
- MockProvider: 模拟提供商
```

### 4. 内容校验模块 (internal/validation)
```go
// 核心功能
- 敏感词过滤
- 安全检查 (SQL注入、XSS)
- 频率限制
- AI内容审核

// 主要组件
- ValidationService: 校验服务
- ContentFilter: 内容过滤器
- SecurityChecker: 安全检查器
- RateLimiter: 频率限制器
```

### 5. API接口模块 (internal/handlers)
```go
// 核心功能
- RESTful API设计
- 请求验证和响应格式化
- 错误处理
- API文档生成

// 主要组件
- GameHandler: 游戏相关API
- AuthHandler: 认证相关API
- AIHandler: AI生成API
- ValidationHandler: 校验API
```

## 数据模型设计

### 核心实体关系
```
User (用户)
├── Characters (角色) [1:N]
├── Worlds (创建的世界) [1:N]
└── WorldMemberships (参与的世界) [N:N]

World (世界)
├── Scenes (场景) [1:N]
├── Characters (角色) [1:N]
├── Events (事件) [1:N]
└── Members (成员) [N:N]

Character (角色)
├── Attributes (属性) [JSONB]
├── Inventory (物品) [JSONB]
├── Relationships (关系) [JSONB]
└── Memory (记忆) [JSONB]

Scene (场景)
├── Connections (连接) [JSONB]
├── Environment (环境) [JSONB]
├── NPCs (NPC) [JSONB]
└── Items (物品) [JSONB]
```

### 数据库设计原则
- **JSONB字段**: 灵活存储复杂数据结构
- **UUID主键**: 分布式友好的唯一标识
- **软删除**: 保留数据完整性
- **时间戳**: 审计和版本控制
- **索引优化**: 查询性能优化

## 安全架构

### 认证安全
```
1. OAuth2流程
   ├── 授权码模式 (Web应用)
   ├── PKCE扩展 (移动应用)
   └── 状态参数防CSRF

2. JWT Token
   ├── 访问Token (短期有效)
   ├── 刷新Token (长期有效)
   └── Token轮换机制

3. 会话管理
   ├── Redis存储会话
   ├── 会话超时控制
   └── 并发会话限制
```

### 内容安全
```
1. 输入验证
   ├── 参数类型检查
   ├── 长度限制
   └── 格式验证

2. 内容过滤
   ├── 敏感词检测
   ├── SQL注入防护
   ├── XSS攻击防护
   └── 恶意脚本检测

3. 频率限制
   ├── IP级别限制
   ├── 用户级别限制
   └── API端点限制
```

## 性能优化

### 缓存策略
```
1. Redis缓存
   ├── 用户会话 (TTL: 24小时)
   ├── 世界数据 (TTL: 1小时)
   ├── 角色信息 (TTL: 30分钟)
   └── AI生成内容 (TTL: 1小时)

2. 数据库优化
   ├── 连接池管理
   ├── 查询优化
   ├── 索引策略
   └── 分页查询
```

### 并发处理
```
1. Goroutine池
   ├── AI请求处理
   ├── 事件处理
   └── 后台任务

2. 数据库事务
   ├── 读写分离
   ├── 事务隔离
   └── 死锁检测
```

## 部署架构

### 容器化部署
```
Docker容器
├── 应用容器 (Go应用)
├── 数据库容器 (PostgreSQL)
├── 缓存容器 (Redis)
└── 代理容器 (Nginx)

Kubernetes集群
├── Deployment (应用部署)
├── Service (服务发现)
├── ConfigMap (配置管理)
├── Secret (敏感信息)
└── Ingress (流量入口)
```

### 监控和日志
```
1. 应用监控
   ├── Prometheus (指标收集)
   ├── Grafana (可视化)
   └── AlertManager (告警)

2. 日志管理
   ├── 结构化日志
   ├── 日志聚合
   └── 错误追踪

3. 健康检查
   ├── 存活探针
   ├── 就绪探针
   └── 启动探针
```

## 扩展性设计

### 水平扩展
- **无状态设计**: 应用层无状态，支持多实例部署
- **数据库分片**: 按世界ID分片，支持大规模用户
- **缓存集群**: Redis集群模式，支持高并发访问

### 功能扩展
- **插件系统**: 支持第三方AI提供商接入
- **事件系统**: 异步事件处理，支持复杂游戏逻辑
- **API版本**: 向后兼容的API版本管理

## 开发规范

### 代码规范
- **包命名**: 小写字母，简洁明了
- **接口设计**: 单一职责，依赖注入
- **错误处理**: 统一错误类型，详细错误信息
- **测试覆盖**: 单元测试 + 集成测试

### Git工作流
- **分支策略**: GitFlow模式
- **提交规范**: 中文提交信息，清晰描述
- **代码审查**: Pull Request必须审查
- **持续集成**: 自动化测试和部署

这个架构设计确保了系统的可扩展性、安全性和可维护性，为AI文本游戏提供了坚实的技术基础。
