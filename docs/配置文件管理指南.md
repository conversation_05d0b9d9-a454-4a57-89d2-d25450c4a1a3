# 配置文件管理指南

## 📋 概述

本文档详细说明了AI文本游戏项目的配置文件管理机制，包括配置文件结构、环境变量管理和数据库连接配置。

## 🗂️ 配置文件结构

### 主要配置文件

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `.env.example` | 配置模板 | 包含所有可配置项的示例 |
| `.env.development` | 开发环境配置 | PostgreSQL开发环境 |
| `.env.development.sqlite` | SQLite开发配置 | SQLite开发环境 |
| `.env` | 当前环境配置 | 实际使用的配置文件 |

### 配置文件优先级

1. **环境变量** - 最高优先级
2. **`.env` 文件** - 中等优先级  
3. **代码默认值** - 最低优先级

## ⚙️ 配置项详解

### 服务器配置

```bash
# 服务器基础配置
SERVER_PORT=8080                    # 服务器端口
SERVER_HOST=0.0.0.0                # 服务器主机地址
SERVER_READ_TIMEOUT=30s             # 读取超时时间
SERVER_WRITE_TIMEOUT=30s            # 写入超时时间
SERVER_IDLE_TIMEOUT=120s            # 空闲超时时间
ENVIRONMENT=development             # 运行环境：development/production/test
```

### 数据库配置

#### PostgreSQL配置
```bash
# PostgreSQL数据库配置
DB_HOST=localhost                   # 数据库主机
DB_PORT=5432                       # 数据库端口
DB_USER=postgres                   # 数据库用户名
DB_PASSWORD=your_password          # 数据库密码
DB_NAME=ai_text_game               # 数据库名称
DB_SSL_MODE=disable                # SSL模式
DB_MAX_OPEN_CONNS=25               # 最大打开连接数
DB_MAX_IDLE_CONNS=5                # 最大空闲连接数
DB_MAX_LIFETIME=5m                 # 连接最大生存时间
```

#### SQLite配置
```bash
# SQLite数据库配置
DB_NAME=dev.db                     # 数据库文件名（以.db结尾自动使用SQLite）
DB_TYPE=sqlite                     # 明确指定数据库类型
DB_AUTO_MIGRATE=true               # 自动迁移
SQLITE_JOURNAL_MODE=WAL            # 日志模式
SQLITE_SYNCHRONOUS=NORMAL          # 同步模式
SQLITE_CACHE_SIZE=10000            # 缓存大小
```

### 认证配置

```bash
# 开发环境认证跳过
SKIP_AUTH=true                     # 跳过身份认证

# JWT配置
JWT_SECRET=your-secret-key         # JWT密钥
JWT_EXPIRATION=24h                 # JWT过期时间

# OAuth配置
OAUTH_GOOGLE_CLIENT_ID=your_id     # Google OAuth客户端ID
OAUTH_GOOGLE_CLIENT_SECRET=secret  # Google OAuth客户端密钥
OAUTH_GITHUB_CLIENT_ID=your_id     # GitHub OAuth客户端ID
OAUTH_GITHUB_CLIENT_SECRET=secret  # GitHub OAuth客户端密钥
```

### AI服务配置

```bash
# AI服务配置
AI_BASE_URL=https://wm.atjog.com   # AI服务基础URL
AI_TOKEN=your_token                # AI服务令牌
AI_TIMEOUT=30s                     # AI请求超时时间
AI_MAX_RETRIES=3                   # 最大重试次数
AI_RETRY_DELAY=1s                  # 重试延迟
AI_MOCK_ENABLED=true               # 开发环境启用Mock模式
```

### 游戏配置

```bash
# 游戏业务配置
GAME_MAX_WORLDS_PER_USER=100       # 每用户最大世界数
GAME_MAX_PLAYERS_PER_WORLD=100     # 每世界最大玩家数
GAME_DEFAULT_TIME_RATE=1.0         # 默认时间流速
GAME_TICK_INTERVAL=10s             # 游戏tick间隔
GAME_MAX_MEMORY_PER_CHAR=1000      # 每角色最大记忆数
GAME_MAX_EXPERIENCE_PER_CHAR=10000 # 每角色最大经验值
```

### 开发环境特殊配置

```bash
# 开发环境增强配置
DEV_ENABLE_DEBUG_LOGS=true         # 启用调试日志
DEV_ENABLE_CORS_ALL=true           # 允许所有CORS请求
DEV_DISABLE_RATE_LIMIT=true        # 禁用速率限制
```

## 🔧 配置管理机制

### 自动检测机制

项目具有智能的配置检测机制：

```go
// 数据库类型自动检测
func detectDatabaseType() string {
    if dbName := os.Getenv("DB_NAME"); dbName != "" {
        if strings.HasSuffix(dbName, ".db") {
            return "sqlite"  // 文件名以.db结尾自动使用SQLite
        }
    }
    return "postgresql"  // 默认使用PostgreSQL
}
```

### 环境变量前缀支持

支持带前缀的环境变量，提高配置的灵活性：

```go
// 支持多个前缀的环境变量
func getEnvWithPrefix(key, defaultValue string, prefixes ...string) string {
    // 优先级: 1. 带前缀的环境变量 2. 普通环境变量 3. 默认值
    for _, prefix := range prefixes {
        prefixedKey := prefix + key
        if value := os.Getenv(prefixedKey); value != "" {
            return value
        }
    }
    return getEnv(key, defaultValue)
}
```

支持的前缀：
- `AI_TEXT_GAME_`
- `AITEXTGAME_`

### 配置验证

项目包含完整的配置验证机制：

```go
func (c *EnhancedConfig) Validate() error {
    // 验证数据库配置
    if c.Database.Type == "" {
        return fmt.Errorf("数据库类型不能为空")
    }
    
    if c.Database.Type != "postgresql" && c.Database.Type != "sqlite" {
        return fmt.Errorf("不支持的数据库类型: %s", c.Database.Type)
    }
    
    // 验证服务器配置
    if c.Server.Port <= 0 || c.Server.Port > 65535 {
        return fmt.Errorf("无效的服务器端口: %d", c.Server.Port)
    }
    
    return nil
}
```

## 🚀 配置使用示例

### 1. 使用默认配置启动

```bash
# 不需要任何配置文件，使用内置默认值
go run cmd/simple-server/main.go
```

### 2. 使用SQLite配置

```bash
# 复制SQLite配置模板
cp .env.development.sqlite .env

# 启动服务
go run cmd/simple-server/main.go
```

### 3. 自定义配置

```bash
# 创建自定义配置
cat > .env << EOF
ENVIRONMENT=development
DB_NAME=my_custom.db
SERVER_PORT=9090
AI_MOCK_ENABLED=true
EOF

# 启动服务
go run cmd/simple-server/main.go
```

### 4. 环境变量覆盖

```bash
# 使用环境变量覆盖配置文件
DB_NAME=test.db SERVER_PORT=8888 go run cmd/simple-server/main.go
```

## 🔄 配置文件管理最佳实践

### 1. 开发环境配置

```bash
# 推荐的开发环境配置流程
./scripts/dev_with_sqlite.sh --status    # 查看当前配置状态
./scripts/dev_with_sqlite.sh             # 启动开发环境（自动备份配置）
./scripts/dev_with_sqlite.sh --restore   # 恢复原始配置
```

### 2. 配置文件备份

SQLite开发脚本会自动备份配置文件：

```bash
# 自动备份格式
.env.backup.20250807_021758

# 查看备份状态
./scripts/dev_with_sqlite.sh --status
```

### 3. 配置合并策略

脚本会智能合并配置，保留重要的用户自定义配置：

```bash
# 保留的用户配置项
AI_TOKEN                    # AI服务令牌
AI_BASE_URL                # AI服务地址
OAUTH_GOOGLE_CLIENT_ID     # Google OAuth配置
OAUTH_GOOGLE_CLIENT_SECRET
OAUTH_GITHUB_CLIENT_ID     # GitHub OAuth配置
OAUTH_GITHUB_CLIENT_SECRET
JWT_SECRET                 # JWT密钥
```

## ⚠️ 安全注意事项

### 1. 敏感信息保护

```bash
# 不要将敏感信息提交到版本控制
echo ".env" >> .gitignore
echo ".env.local" >> .gitignore
echo ".env.production" >> .gitignore
```

### 2. 生产环境配置

```bash
# 生产环境必须配置的项目
ENVIRONMENT=production
JWT_SECRET=strong-random-secret-key
DB_PASSWORD=strong-database-password
AI_TOKEN=real-ai-service-token
```

### 3. 开发环境隔离

```bash
# 开发环境使用独立的数据库
DB_NAME=ai_text_game_dev    # 开发数据库
DB_NAME=ai_text_game_test   # 测试数据库
DB_NAME=ai_text_game        # 生产数据库
```

## 🛠️ 故障排除

### 常见配置问题

1. **数据库连接失败**
   ```bash
   # 检查数据库配置
   echo $DB_NAME
   echo $DB_HOST
   
   # 测试数据库连接
   go run debug_db.go
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :8080
   
   # 修改端口配置
   export SERVER_PORT=8081
   ```

3. **配置文件格式错误**
   ```bash
   # 验证配置文件格式
   cat .env | grep -v '^#' | grep -v '^$'
   
   # 重新生成配置文件
   cp .env.example .env
   ```

## 📚 相关文档

- [开发环境使用指南](./开发环境使用指南.md)
- [数据库配置文档](./database-compatibility.md)
- [AI服务配置文档](./ai-modules-overview.md)

## 🔗 配置文件模板

完整的配置文件模板请参考项目根目录下的 `.env.example` 文件。
