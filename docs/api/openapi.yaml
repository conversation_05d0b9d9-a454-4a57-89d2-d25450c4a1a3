{"openapi": "3.0.3", "info": {"title": "AI文本游戏API", "description": "AI文本游戏后端API调试系统", "version": "1.0.0", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": ""}}, "servers": [{"url": "http://localhost:8080", "description": "开发环境服务器"}], "paths": {"/api/health": {"get": {"tags": ["Api"], "operationId": "get_api_health", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/ai/generate": {"post": {"tags": ["AI"], "summary": "生成AI内容", "description": "使用AI生成游戏内容，支持场景、角色、事件等多种类型的内容生成。系统会根据提供的提示词和上下文信息，调用AI服务生成结构化的游戏内容。", "operationId": "post_api_v1_ai_generate", "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"context": {"type": "object", "example": {"current_location": "精灵王国", "world_theme": "奇幻"}, "description": "上下文信息"}, "max_tokens": {"type": "integer", "format": "int32", "example": 500, "description": "最大token数"}, "prompt": {"type": "string", "example": "生成一个神秘的森林场景", "description": "提示词"}, "schema": {"type": "object", "example": {"description": "string", "name": "string", "type": "string"}, "description": "期望的响应结构"}, "temperature": {"type": "number", "format": "double", "example": 0.7, "description": "温度参数"}, "type": {"type": "string", "example": "scene", "description": "生成类型"}, "world_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************", "description": "世界ID"}}, "required": ["type", "prompt"]}}}, "required": true}, "responses": {"200": {"description": "生成成功，返回生成的内容和相关信息", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "请求参数错误，如缺少必填字段或参数格式不正确", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "未授权访问，需要有效的认证token", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "服务器内部错误，如AI服务不可用或生成失败", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/generate/character": {"post": {"tags": ["AI"], "summary": "生成游戏角色", "description": "根据提示词生成游戏角色描述和属性。AI会根据提供的角色描述生成包含姓名、外观、性格、技能、背景故事等完整信息的游戏角色。", "operationId": "post_api_v1_ai_generate_character", "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"context": {"type": "object", "example": {"faction": "精灵族", "level": "高级", "world_theme": "奇幻"}, "description": "上下文信息"}, "prompt": {"type": "string", "example": "一个勇敢的精灵战士，擅长弓箭术", "description": "角色生成提示词"}, "world_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************", "description": "世界ID"}}, "required": ["prompt"]}}}, "required": true}, "responses": {"200": {"description": "角色生成成功，返回生成的角色描述和属性信息", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "请求参数错误，如缺少必填的提示词", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "未授权访问，需要有效的认证token", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "服务器内部错误，如AI服务不可用", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/generate/event": {"post": {"tags": ["AI"], "summary": "生成游戏事件", "description": "根据提示词生成游戏事件描述和效果。AI会生成包含事件名称、详细描述、触发条件、持续时间、影响效果等完整信息的游戏事件。", "operationId": "post_api_v1_ai_generate_event", "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"context": {"type": "object", "example": {"current_scene": "村庄广场", "time_of_day": "傍晚", "weather": "晴朗"}, "description": "上下文信息"}, "prompt": {"type": "string", "example": "一场突然的暴风雨席卷了整个村庄", "description": "事件生成提示词"}, "world_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************", "description": "世界ID"}}, "required": ["prompt"]}}}, "required": true}, "responses": {"200": {"description": "事件生成成功，返回生成的事件描述和效果信息", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "请求参数错误，如缺少必填的提示词", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "未授权访问，需要有效的认证token", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "服务器内部错误，如AI服务不可用", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/generate/scene": {"post": {"tags": ["AI"], "summary": "生成游戏场景", "description": "根据场景参数生成游戏场景描述和属性。支持指定场景名称、类型、主题风格、氛围设定等参数，AI会生成包含详细描述、氛围、关键特征和可能行动的完整场景信息。", "operationId": "post_api_v1_ai_generate_scene", "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"connected_scenes": {"type": "array", "items": {"type": "object"}, "example": ["精灵村庄", "古老神庙"], "description": "连接的场景"}, "mood": {"type": "string", "example": "神秘", "description": "氛围设定"}, "scene_name": {"type": "string", "example": "神秘森林", "description": "场景名称"}, "scene_type": {"type": "string", "example": "outdoor", "description": "场景类型"}, "special_requirements": {"type": "string", "example": "需要包含一个隐藏的宝箱", "description": "特殊要求"}, "theme": {"type": "string", "example": "奇幻", "description": "主题风格"}, "world_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************", "description": "世界ID"}}, "required": ["world_id"]}}}, "required": true}, "responses": {"200": {"description": "场景生成成功，返回生成的场景描述和结构化数据", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "请求参数错误，如缺少必填的世界ID", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "未授权访问，需要有效的认证token", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "服务器内部错误，如AI服务不可用", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/history": {"get": {"tags": ["AI"], "summary": "获取AI交互历史", "description": "获取用户或世界的AI交互历史记录，包括所有的AI生成请求和响应信息。可以按世界ID过滤，支持分页查询。", "operationId": "get_api_v1_ai_history", "parameters": [{"name": "world_id", "in": "query", "required": false, "description": "世界ID", "example": "123e4567-e89b-12d3-a456-************", "schema": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "limit", "in": "query", "required": false, "description": "限制数量", "example": 50, "schema": {"type": "integer", "format": "int32", "example": 50}}], "responses": {"200": {"description": "成功返回AI交互历史记录列表", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "[]models.AIInteraction", "description": "数据对象: []models.AIInteraction"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "请求参数错误，如无效的世界ID格式", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "未授权访问，需要有效的认证token", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/stats": {"get": {"tags": ["AI"], "summary": "获取token使用统计", "description": "获取用户或世界的token使用统计信息，包括总使用量、日均使用量、按类型分类的使用情况等。用于监控AI服务使用情况和成本控制。", "operationId": "get_api_v1_ai_stats", "parameters": [{"name": "world_id", "in": "query", "required": false, "description": "世界ID", "example": "123e4567-e89b-12d3-a456-************", "schema": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "days", "in": "query", "required": false, "description": "统计天数", "example": 30, "schema": {"type": "integer", "format": "int32", "example": 30}}], "responses": {"200": {"description": "成功返回token使用统计信息", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"by_type": {"type": "object", "title": "object", "description": "对象类型: object"}, "daily_average": {"type": "number", "example": 1, "description": "数字类型"}, "total_tokens": {"type": "integer", "example": 1, "description": "整数类型"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "请求参数错误，如无效的世界ID格式或天数超出范围", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "未授权访问，需要有效的认证token", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/auth/:provider/callback": {"get": {"tags": ["用户认证"], "summary": "处理OAuth认证回调", "description": "处理OAuth认证回调，验证授权码并完成用户登录。成功后返回用户信息和JWT token。", "operationId": "get_api_v1_auth_:provider_callback", "parameters": [{"name": "provider", "in": "path", "required": true, "description": "认证提供商", "example": "google", "schema": {"type": "string", "example": "google"}}, {"name": "code", "in": "query", "required": true, "description": "授权码", "example": "4/0AX4XfWjYZ...", "schema": {"type": "string", "example": "4/0AX4XfWjYZ..."}}, {"name": "state", "in": "query", "required": false, "description": "状态参数", "example": "random-state-string", "schema": {"type": "string", "example": "random-state-string"}}], "responses": {"200": {"description": "登录成功，返回用户信息和认证token", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"expires_at": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "token": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "user": {"type": "object", "title": "models.User", "description": "对象类型: models.User"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "授权码无效或状态参数不匹配", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "服务器内部错误，如用户信息获取失败", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}}}, "/api/v1/auth/:provider/url": {"get": {"tags": ["用户认证"], "summary": "获取OAuth认证URL", "description": "获取指定提供商的OAuth认证URL，用户可以通过此URL跳转到第三方认证页面进行登录", "operationId": "get_api_v1_auth_:provider_url", "parameters": [{"name": "provider", "in": "path", "required": true, "description": "认证提供商", "example": "google", "schema": {"type": "string", "example": "google"}}, {"name": "state", "in": "query", "required": false, "description": "状态参数", "example": "random-state-string", "schema": {"type": "string", "example": "random-state-string"}}], "responses": {"200": {"description": "成功返回认证URL", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"auth_url": {"type": "string", "example": "示例文本", "description": "字符串类型"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "无效的认证提供商", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}}}, "/api/v1/auth/refresh": {"post": {"tags": ["认证"], "summary": "刷新JWT token", "description": "刷新即将过期的JWT token", "operationId": "post_api_v1_auth_refresh", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"expires_at": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "token": {"type": "string", "example": "示例文本", "description": "字符串类型"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id": {"get": {"tags": ["角色管理"], "summary": "获取指定角色的详细信息", "description": "获取指定角色的完整信息，包括角色基本信息、属性、状态、位置等。只有角色所有者或同世界成员才能访问。", "operationId": "get_api_v1_game_:character_id", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": "123e4567-e89b-12d3-a456-************", "schema": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "成功返回角色详细信息", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.Character", "description": "数据对象: models.Character"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "角色ID格式错误，必须是有效的UUID格式", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "角色不存在或无权限访问", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "put": {"tags": ["Game"], "summary": "更新角色信息", "description": "更新指定角色的基本信息", "operationId": "put_api_v1_game_:character_id", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "UpdateCharacterRequest", "title": "UpdateCharacterRequest", "description": "更新角色请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "delete": {"tags": ["Game"], "summary": "删除角色", "description": "删除指定的角色", "operationId": "delete_api_v1_game_:character_id", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/actions": {"post": {"tags": ["Game"], "summary": "执行角色行动", "description": "角色执行指定的行动，如移动、交互、说话等", "operationId": "post_api_v1_game_:character_id_actions", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "PerformActionRequest", "title": "PerformActionRequest", "description": "行动请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ActionResult", "description": "数据对象: ActionResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/experiences": {"post": {"tags": ["Game"], "summary": "添加角色阅历", "description": "为指定角色添加新的阅历", "operationId": "post_api_v1_game_:character_id_experiences", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "AddExperienceRequest", "title": "AddExperienceRequest", "description": "添加阅历请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/interact/:target_character_id": {"post": {"tags": ["Game"], "summary": "与角色交互", "description": "角色与另一个角色进行交互", "operationId": "post_api_v1_game_:character_id_interact_:target_character_id", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "target_character_id", "in": "path", "required": true, "description": "目标角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "InteractionRequest", "title": "InteractionRequest", "description": "交互请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "InteractionResult", "description": "数据对象: InteractionResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/memories": {"post": {"tags": ["Game"], "summary": "添加角色记忆", "description": "为指定角色添加新的记忆", "operationId": "post_api_v1_game_:character_id_memories", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "AddMemoryRequest", "title": "AddMemoryRequest", "description": "添加记忆请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/move": {"post": {"tags": ["Game"], "summary": "移动角色", "description": "移动角色到指定场景", "operationId": "post_api_v1_game_:character_id_move", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "MoveCharacterRequest", "title": "MoveCharacterRequest", "description": "移动角色请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/speak": {"post": {"tags": ["Game"], "summary": "在场景中说话", "description": "角色在当前场景中说话，其他角色可以听到", "operationId": "post_api_v1_game_:character_id_speak", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "SpeakRequest", "title": "SpeakRequest", "description": "说话请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "SpeechResult", "description": "数据对象: SpeechResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/traits": {"post": {"tags": ["Game"], "summary": "添加角色特质", "description": "为指定角色添加新的特质", "operationId": "post_api_v1_game_:character_id_traits", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "AddTraitRequest", "title": "AddTraitRequest", "description": "添加特质请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:event_id/process": {"post": {"tags": ["Game"], "summary": "处理事件", "description": "处理指定的事件", "operationId": "post_api_v1_game_:event_id_process", "parameters": [{"name": "event_id", "in": "path", "required": true, "description": "事件ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:scene_id": {"get": {"tags": ["Game"], "summary": "获取场景信息", "description": "获取指定场景的详细信息", "operationId": "get_api_v1_game_:scene_id", "parameters": [{"name": "scene_id", "in": "path", "required": true, "description": "场景ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.Scene", "description": "数据对象: models.Scene"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id": {"get": {"tags": ["游戏世界"], "summary": "获取指定世界的详细信息", "description": "获取指定世界的完整信息，包括世界基本信息、配置、统计数据等。只有世界成员或公开世界才能访问。", "operationId": "get_api_v1_game_:world_id", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": "123e4567-e89b-12d3-a456-************", "schema": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "成功返回世界详细信息", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.World", "description": "数据对象: models.World"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "世界ID格式错误，必须是有效的UUID格式", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "世界不存在或无权限访问", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "put": {"tags": ["Game"], "summary": "更新世界信息", "description": "更新指定世界的信息", "operationId": "put_api_v1_game_:world_id", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "UpdateWorldRequest", "title": "UpdateWorldRequest", "description": "更新世界请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "delete": {"tags": ["Game"], "summary": "删除世界", "description": "删除指定的世界", "operationId": "delete_api_v1_game_:world_id", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/characters": {"get": {"tags": ["Game"], "summary": "获取世界中的角色列表", "description": "获取指定世界中的角色列表，支持按角色类型筛选", "operationId": "get_api_v1_game_:world_id_characters", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": "123e4567-e89b-12d3-a456-************", "schema": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "page", "in": "query", "required": false, "description": "页码", "example": 1, "schema": {"type": "int", "format": "int32", "example": 1}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": 20, "schema": {"type": "int", "format": "int32", "example": 20}}, {"name": "character_type", "in": "query", "required": false, "description": "角色类型", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/join": {"post": {"tags": ["Game"], "summary": "加入世界", "description": "用户加入指定的世界", "operationId": "post_api_v1_game_:world_id_join", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/leave": {"post": {"tags": ["Game"], "summary": "离开世界", "description": "用户离开指定的世界", "operationId": "post_api_v1_game_:world_id_leave", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/state": {"get": {"tags": ["Game"], "summary": "获取世界状态", "description": "获取指定世界的当前状态信息", "operationId": "get_api_v1_game_:world_id_state", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": "123e4567-e89b-12d3-a456-************", "schema": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "game.GameState", "description": "数据对象: game.GameState"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/tick": {"post": {"tags": ["Game"], "summary": "处理世界时钟周期", "description": "执行世界的一个时钟周期，包括时间推进、NPC行为、环境事件等", "operationId": "post_api_v1_game_:world_id_tick", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/time": {"post": {"tags": ["Game"], "summary": "更新世界信息", "description": "更新指定世界的信息", "operationId": "post_api_v1_game_:world_id_time", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "UpdateWorldRequest", "title": "UpdateWorldRequest", "description": "更新世界请求"}}, "required": ["request", "request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/time-rate": {"get": {"tags": ["TimeSchedule"], "summary": "获取世界当前的时间速率", "description": "获取指定世界当前生效的时间速率", "operationId": "get_api_v1_game_:world_id_time-rate", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "map[string]interface{", "description": "数据对象: map[string]interface{"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/time-schedule": {"get": {"tags": ["TimeSchedule"], "summary": "获取世界的时间段配置", "description": "获取指定世界的时间段配置信息", "operationId": "get_api_v1_game_:world_id_time-schedule", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.TimeScheduleConfig", "description": "数据对象: models.TimeScheduleConfig"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "put": {"tags": ["TimeSchedule"], "summary": "更新世界的时间段配置", "description": "更新指定世界的时间段配置，支持热更新", "operationId": "put_api_v1_game_:world_id_time-schedule", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/time-schedule/default": {"post": {"tags": ["TimeSchedule"], "summary": "为世界创建默认时间段配置", "description": "为指定世界创建默认的时间段配置", "operationId": "post_api_v1_game_:world_id_time-schedule_default", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/time-schedule/disable": {"post": {"tags": ["TimeSchedule"], "summary": "禁用世界的时间段配置", "description": "禁用指定世界的时间段配置功能", "operationId": "post_api_v1_game_:world_id_time-schedule_disable", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/time-schedule/enable": {"post": {"tags": ["TimeSchedule"], "summary": "启用世界的时间段配置", "description": "启用指定世界的时间段配置功能", "operationId": "post_api_v1_game_:world_id_time-schedule_enable", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/time-schedule/reset": {"post": {"tags": ["TimeSchedule"], "summary": "重置世界的时间段配置", "description": "将指定世界的时间段配置重置为默认值", "operationId": "post_api_v1_game_:world_id_time-schedule_reset", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/generate": {"post": {"tags": ["游戏世界"], "summary": "AI生成世界配置方案", "description": "使用AI根据用户提供的世界名称和偏好设置，生成多个候选世界配置方案供用户选择。每个方案包含完整的世界描述、规则、环境、文化、历史和地理配置。", "operationId": "post_api_v1_game_generate", "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"worldName": {"type": "string", "example": "龙与魔法的大陆", "description": "世界名称"}, "worldSettings": {"type": "object", "example": {"complexity": "medium", "focus": "exploration", "preferred_theme": "奇幻"}, "description": "世界偏好设置"}}, "required": ["worldName"]}}}, "required": true}, "responses": {"200": {"description": "生成成功，返回多个候选世界配置方案", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "GenerateWorldsResponse", "description": "数据对象: GenerateWorldsResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "请求参数错误，如世界名称为空或格式不正确", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "用户未认证，需要有效的认证token", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "AI服务不可用或生成失败", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/my-characters": {"get": {"tags": ["角色管理"], "summary": "获取当前用户创建的角色列表", "description": "获取当前认证用户创建的所有角色列表，支持分页查询。返回的角色按创建时间倒序排列。", "operationId": "get_api_v1_game_my-characters", "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码", "example": 1, "schema": {"type": "integer", "format": "int32", "example": 1}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": 20, "schema": {"type": "integer", "format": "int32", "example": 20}}], "responses": {"200": {"description": "成功返回分页的角色列表", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "用户未认证，需要有效的认证token", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/my-worlds": {"get": {"tags": ["游戏世界"], "summary": "获取当前用户创建的世界列表", "description": "获取当前认证用户创建的所有世界列表，支持分页查询。返回的世界按创建时间倒序排列。", "operationId": "get_api_v1_game_my-worlds", "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码", "example": 1, "schema": {"type": "integer", "format": "int32", "example": 1}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": 20, "schema": {"type": "integer", "format": "int32", "example": 20}}], "responses": {"200": {"description": "成功返回分页的世界列表", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "用户未认证，需要有效的认证token", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/public-worlds": {"get": {"tags": ["游戏世界"], "summary": "获取所有公开的世界列表", "description": "获取所有设置为公开的世界列表，任何用户都可以查看和加入这些世界。支持分页查询，按创建时间倒序排列。", "operationId": "get_api_v1_game_public-worlds", "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码", "example": 1, "schema": {"type": "integer", "format": "int32", "example": 1}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": 20, "schema": {"type": "integer", "format": "int32", "example": 20}}], "responses": {"200": {"description": "成功返回分页的公开世界列表", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}}}, "/api/v1/game/stats": {"get": {"tags": ["TimeSchedule"], "summary": "获取时间段配置统计信息", "description": "获取系统中所有世界的时间段配置统计信息", "operationId": "get_api_v1_game_stats", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "map[string]interface{", "description": "数据对象: map[string]interface{"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/trigger": {"post": {"tags": ["Game"], "summary": "触发事件", "description": "手动触发一个游戏事件", "operationId": "post_api_v1_game_trigger", "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "TriggerEventRequest", "title": "TriggerEventRequest", "description": "触发事件请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "EventResult", "description": "数据对象: EventResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/validate": {"post": {"tags": ["TimeSchedule"], "summary": "验证时间段配置", "description": "验证时间段配置的有效性，不保存到数据库", "operationId": "post_api_v1_game_validate", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "map[string]interface{", "description": "数据对象: map[string]interface{"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/health": {"get": {"tags": ["Api"], "operationId": "get_api_v1_health", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/user/profile": {"get": {"tags": ["认证"], "summary": "获取当前用户资料", "description": "获取当前认证用户的详细资料信息", "operationId": "get_api_v1_user_profile", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.User", "description": "数据对象: models.User"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "put": {"tags": ["用户认证"], "summary": "更新当前用户的资料信息", "description": "更新当前认证用户的个人资料，包括昵称、头像、个人简介等信息。只能更新当前用户自己的资料。", "operationId": "put_api_v1_user_profile", "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"avatar_url": {"type": "string", "example": "https://example.com/avatar.jpg", "description": "头像URL"}, "bio": {"type": "string", "example": "热爱游戏的开发者", "description": "个人简介"}, "nickname": {"type": "string", "example": "小明", "description": "用户昵称"}, "preferences": {"type": "object", "example": {"language": "zh-CN", "theme": "dark"}, "description": "用户偏好设置"}}}}}, "required": true}, "responses": {"200": {"description": "资料更新成功，返回更新后的用户信息", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.User", "description": "数据对象: models.User"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "请求参数错误，如昵称格式不正确", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "用户未认证，需要有效的认证token", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/validation/batch-validate": {"post": {"tags": ["Validation"], "summary": "批量校验内容", "description": "批量校验多个内容项", "operationId": "post_api_v1_validation_batch-validate", "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "BatchValidateContentRequest", "title": "BatchValidateContentRequest", "description": "批量校验内容请求"}}, "required": ["request", "request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "BatchValidationResult", "description": "数据对象: BatchValidationResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/validation/stats": {"get": {"tags": ["Validation"], "summary": "获取校验统计信息", "description": "获取用户的内容校验统计信息", "operationId": "get_api_v1_validation_stats", "parameters": [{"name": "days", "in": "query", "required": false, "description": "统计天数", "example": 7, "schema": {"type": "int", "format": "int32", "example": 7}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "map[string]interface{", "description": "数据对象: map[string]interface{"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/validation/validate": {"post": {"tags": ["Validation"], "summary": "校验内容", "description": "对用户输入的内容进行校验和过滤", "operationId": "post_api_v1_validation_validate", "requestBody": {"description": "请求体参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "ValidateContentRequest", "title": "ValidateContentRequest", "description": "校验内容请求"}}, "required": ["request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "validation.ValidationResult", "description": "数据对象: validation.ValidationResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/validation/validation/config": {"get": {"tags": ["Validation"], "summary": "获取校验配置", "description": "获取当前的内容校验配置信息", "operationId": "get_api_v1_validation_validation_config", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ValidationConfigResponse", "description": "数据对象: ValidationConfigResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}}}}}, "components": {"schemas": {"EndpointsResponse": {"type": "object", "title": "EndpointsResponse", "properties": {"endpoints": {"type": "array", "items": {"type": "object", "example": {}}}, "pagination": {"type": "object", "example": {}}}, "description": "EndpointsResponse 端点列表响应"}, "Error": {"type": "object", "properties": {"code": {"type": "string", "description": "错误代码"}, "details": {"type": "object", "description": "错误详情"}, "message": {"type": "string", "description": "错误消息"}}, "required": ["code", "message"]}, "GenerateCharacterRequest": {"type": "object", "title": "GenerateCharacterRequest", "properties": {"context": {"type": "object", "example": {}, "description": "上下文信息，可选，提供额外的角色背景信息，如世界设定、相关角色、所属阵营等"}, "prompt": {"type": "string", "example": "示例文本", "description": "角色生成提示词，必填，描述要生成的角色特征，如\"一个勇敢的战士\"、\"神秘的法师\"等，越详细越好"}, "world_id": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************", "description": "世界ID，可选，指定角色所属的游戏世界，用于保持世界观一致性"}}, "required": ["prompt"], "description": "GenerateCharacterRequest 角色生成请求 @Description 角色生成请求结构，用于指定要生成的游戏角色的特征和背景信息"}, "GenerateEventRequest": {"type": "object", "title": "GenerateEventRequest", "properties": {"context": {"type": "object", "example": {}, "description": "上下文信息，可选，提供事件相关的背景信息，如当前场景、涉及角色、时间地点等"}, "prompt": {"type": "string", "example": "示例文本", "description": "事件生成提示词，必填，描述要生成的事件内容，如\"一场突然的暴风雨\"、\"神秘商人的到来\"等"}, "world_id": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************", "description": "世界ID，可选，指定事件发生的游戏世界，确保事件与世界设定相符"}}, "required": ["prompt"], "description": "GenerateEventRequest 事件生成请求 @Description 事件生成请求结构，用于指定要生成的游戏事件的内容和背景信息"}, "GenerateRequest": {"type": "object", "title": "GenerateRequest", "properties": {"context": {"type": "object", "example": {}, "description": "上下文信息，可选，提供生成内容所需的背景信息和相关数据，如世界设定、相关角色等"}, "max_tokens": {"type": "integer", "format": "int32", "example": 1, "description": "最大token数，可选，限制生成内容的长度，数值越大生成的内容越详细，默认500"}, "prompt": {"type": "string", "example": "示例文本", "description": "提示词，必填，描述要生成内容的具体要求和特征，越详细越能生成符合期望的内容"}, "schema": {"type": "object", "example": {}, "description": "期望的响应结构，可选，定义生成内容的数据结构格式，用于约束AI输出的结构"}, "temperature": {"type": "number", "format": "double", "example": 1, "description": "温度参数，可选，控制生成内容的创造性，范围0.0-1.0，0.0最保守，1.0最有创意，默认0.7"}, "type": {"type": "string", "example": "示例文本", "description": "生成类型，必填，指定要生成的内容类型：scene(场景)、character(角色)、event(事件)、dialogue(对话)、world(世界)、item(物品)等"}, "user_id": {"type": "string", "example": "示例文本", "description": "用户ID，可选，标识请求用户，用于个性化生成和使用统计，通常由系统自动填充"}, "world_id": {"type": "string", "example": "示例文本", "description": "世界ID，可选，指定内容所属的游戏世界，用于保持世界观一致性"}}, "required": ["type", "prompt"], "description": "GenerateRequest AI生成请求 @Description AI内容生成请求结构，用于指定生成内容的类型、提示词和相关参数"}, "GenerateResponse": {"type": "object", "title": "GenerateResponse", "properties": {"content": {"type": "string", "example": "示例文本", "description": "生成的文本内容，包含AI生成的主要文本描述"}, "response_time": {"type": "integer", "format": "int32", "example": 1, "description": "响应时间，单位毫秒，表示AI生成内容所用的时间"}, "structured_data": {"type": "object", "example": {}, "description": "结构化数据，包含按照指定schema格式化的生成结果"}, "token_usage": {"type": "integer", "format": "int32", "example": 1, "description": "使用的token数量，用于计费和使用统计"}}, "description": "GenerateResponse AI生成响应 @Description AI内容生成响应结构，包含生成的内容和相关统计信息"}, "GenerateSceneRequest": {"type": "object", "title": "GenerateSceneRequest", "properties": {"connected_scenes": {"type": "array", "items": {"type": "string", "example": "示例文本"}, "description": "连接的场景，可选，指定与此场景相连的其他场景名称列表"}, "context": {"type": "object", "example": {}, "description": "上下文信息，可选，用于兼容旧版本API的额外上下文数据"}, "mood": {"type": "string", "example": "示例文本", "description": "氛围设定，可选，指定场景的氛围，如\"神秘\"、\"紧张\"、\"宁静\"、\"危险\"等"}, "prompt": {"type": "string", "example": "示例文本", "description": "兼容旧格式的字段"}, "scene_name": {"type": "string", "example": "示例文本", "description": "场景名称，可选，指定要生成的场景名称，如\"神秘森林\"、\"古老城堡\"等"}, "scene_type": {"type": "string", "example": "示例文本", "description": "场景类型，可选，指定场景的类型，如\"室内\"、\"室外\"、\"地下城\"、\"城镇\"等"}, "special_requirements": {"type": "string", "example": "示例文本", "description": "特殊要求，可选，对场景生成的特殊要求或限制条件"}, "theme": {"type": "string", "example": "示例文本", "description": "主题风格，可选，指定场景的主题风格，如\"奇幻\"、\"科幻\"、\"恐怖\"、\"现代\"等"}, "world_id": {"type": "string", "example": "示例文本", "description": "世界ID，必填，指定要生成场景的游戏世界"}}, "required": ["world_id"], "description": "GenerateSceneRequest 场景生成请求 @Description 场景生成请求结构，用于指定要生成的游戏场景的各种参数和要求"}, "GenerateWorldsResponse": {"type": "object", "title": "GenerateWorldsResponse", "properties": {"candidates": {"type": "array", "items": {"type": "object", "example": {}}}}, "description": "GenerateWorldsResponse AI生成世界配置响应"}, "HistoryResponse": {"type": "object", "title": "HistoryResponse", "properties": {"pagination": {"type": "object", "example": {}}, "records": {"type": "array", "items": {"type": "object", "example": {}}}}, "description": "HistoryResponse 历史记录响应"}, "PaginatedResponse": {"type": "object", "title": "PaginatedResponse", "properties": {"items": {"type": "object", "example": {}}, "limit": {"type": "integer", "format": "int32", "example": 1}, "page": {"type": "integer", "format": "int32", "example": 1}, "total": {"type": "integer", "format": "int64", "example": 1}, "total_pages": {"type": "integer", "format": "int64", "example": 1}}, "description": "PaginatedResponse 分页响应"}, "Response": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "example": {}, "description": "响应数据，成功时包含具体的业务数据，失败时可为空"}, "error": {"type": "string", "example": "示例文本", "description": "错误信息，失败时包含详细的错误描述，成功时为空"}, "message": {"type": "string", "example": "示例文本", "description": "响应消息，提供操作结果的描述信息"}, "success": {"type": "boolean", "example": true, "description": "请求是否成功，true表示成功，false表示失败"}, "timestamp": {"type": "integer", "format": "int64", "example": 1, "description": "响应时间戳，Unix时间戳格式，表示响应生成的时间"}}, "description": "Response 统一响应结构"}, "UnifiedAPIResponse": {"type": "object", "title": "UnifiedAPIResponse", "properties": {"data": {"type": "object", "example": {}, "description": "响应数据，可选，成功时包含具体的业务数据"}, "error": {"type": "string", "example": "示例文本", "description": "错误信息，可选，失败时包含详细的错误描述"}, "message": {"type": "string", "example": "示例文本", "description": "响应消息，可选，提供操作结果的描述信息"}, "request_id": {"type": "string", "example": "示例文本", "description": "请求ID，用于追踪和调试，每个请求都有唯一的ID"}, "success": {"type": "boolean", "example": true, "description": "请求是否成功，true表示成功，false表示失败"}, "timestamp": {"type": "string", "example": "示例文本", "description": "响应时间戳，RFC3339格式，表示响应生成的时间"}}, "description": "UnifiedAPIResponse 统一的API响应格式 兼容simple-server和main-server的响应格式"}, "ValidationConfigResponse": {"type": "object", "title": "ValidationConfigResponse", "properties": {"allowed_content_types": {"type": "array", "items": {"type": "string", "example": "示例文本"}}, "features": {"type": "object", "example": {}}, "max_requests_per_hour": {"type": "integer", "format": "int32", "example": 1}, "max_requests_per_minute": {"type": "integer", "format": "int32", "example": 1}, "max_text_length": {"type": "integer", "format": "int32", "example": 1}, "min_text_length": {"type": "integer", "format": "int32", "example": 1}}, "description": "ValidationConfigResponse 校验配置响应"}, "WindmillJobResponse": {"type": "object", "title": "WindmillJobResponse", "properties": {"job_id": {"type": "string", "example": "示例文本", "description": "任务ID"}}, "description": "WindmillJobResponse Windmill任务提交响应"}, "WindmillResultResponse": {"type": "object", "title": "WindmillResultResponse", "properties": {"completed": {"type": "boolean", "example": true, "description": "是否完成"}, "error": {"type": "string", "example": "示例文本", "description": "错误信息"}, "result": {"type": "object", "example": {}, "description": "结果数据"}}, "description": "WindmillResultResponse Windmill结果查询响应"}}, "securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API Key认证", "name": "X-API-Key", "in": "header"}, "BearerAuth": {"type": "http", "description": "JWT Bearer token认证", "scheme": "bearer", "bearerFormat": "JWT"}}}, "tags": [{"name": "Api", "description": "Api相关API"}, {"name": "用户认证", "description": "用户认证相关API"}, {"name": "认证", "description": "认证相关API"}, {"name": "Validation", "description": "Validation相关API"}, {"name": "AI", "description": "AI相关API"}, {"name": "游戏世界", "description": "游戏世界相关API"}, {"name": "Game", "description": "Game相关API"}, {"name": "TimeSchedule", "description": "TimeSchedule相关API"}, {"name": "角色管理", "description": "角色管理相关API"}]}