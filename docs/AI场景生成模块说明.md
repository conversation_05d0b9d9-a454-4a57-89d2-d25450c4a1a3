# AI场景生成模块说明文档

## 模块概述

AI场景生成模块是AI文本游戏系统的核心组件之一，负责根据用户输入的参数智能生成游戏场景。该模块支持多种输入格式，能够根据世界设定、主题风格、氛围要求等参数生成丰富多样的游戏场景。

## 功能特性

### 1. 多格式输入支持

- **新格式参数**: 支持结构化的场景参数输入
  - 世界ID (world_id)
  - 场景名称 (scene_name)
  - 场景类型 (scene_type)
  - 主题风格 (theme)
  - 氛围设定 (mood)
  - 连接场景 (connected_scenes)
  - 特殊要求 (special_requirements)

- **兼容旧格式**: 保持对原有提示词格式的支持
  - 自由文本提示词 (prompt)
  - 上下文信息 (context)

### 2. 智能提示词构建

模块能够根据输入参数智能构建AI提示词，确保生成的场景符合用户期望：

```go
// 示例：构建的提示词
"请生成一个游戏场景的详细描述。场景名称：神秘森林。主题风格：fantasy。场景类型：main。氛围设定：mysterious。特殊要求：包含古老的遗迹。需要连接的场景：村庄、山洞。请生成包含场景名称、详细描述、氛围、关键特征和可能的行动的完整场景信息。"
```

### 3. 上下文信息管理

- 自动合并新旧格式的上下文信息
- 保留用户自定义的上下文数据
- 添加系统生成的元数据信息

### 4. 详细日志记录

- 记录请求参数和处理过程
- 提供中文调试信息
- 支持问题排查和性能监控

## API接口

### 场景生成接口

**端点**: `POST /api/v1/ai/generate/scene`

**请求头**:
```
Content-Type: application/json
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "world_id": "world-123",
  "scene_name": "神秘森林",
  "scene_type": "main",
  "theme": "fantasy",
  "mood": "mysterious",
  "connected_scenes": ["村庄", "山洞"],
  "special_requirements": "包含古老的遗迹和魔法元素"
}
```

**响应体**:
```json
{
  "success": true,
  "message": "生成场景成功",
  "data": {
    "content": "在这片古老的森林深处...",
    "structured_data": {
      "name": "神秘森林",
      "description": "详细的场景描述",
      "type": "forest",
      "atmosphere": "mysterious",
      "connections": {
        "north": "村庄",
        "south": "山洞"
      },
      "entities": [
        {
          "name": "古老石碑",
          "type": "landmark",
          "description": "刻有神秘符文的石碑"
        }
      ]
    },
    "token_usage": 150
  }
}
```

## 核心组件

### 1. GenerateSceneRequest 结构体

```go
type GenerateSceneRequest struct {
    WorldID              string   `json:"world_id" binding:"required"`
    SceneName            string   `json:"scene_name,omitempty"`
    SceneType            string   `json:"scene_type,omitempty"`
    Theme                string   `json:"theme,omitempty"`
    Mood                 string   `json:"mood,omitempty"`
    ConnectedScenes      []string `json:"connected_scenes,omitempty"`
    SpecialRequirements  string   `json:"special_requirements,omitempty"`
    // 兼容旧格式
    Prompt               string                 `json:"prompt,omitempty"`
    Context              map[string]interface{} `json:"context,omitempty"`
}
```

### 2. AIHandler 处理器

负责处理HTTP请求，验证参数，调用AI服务：

```go
type AIHandler struct {
    aiService *ai.Service
    logger    logger.Logger
}
```

**主要方法**:
- `GenerateScene()`: 处理场景生成请求
- `buildScenePrompt()`: 构建AI提示词
- `buildSceneContext()`: 构建上下文信息

### 3. 辅助方法

#### buildScenePrompt()
根据请求参数智能构建提示词：
- 优先使用旧格式的prompt（向后兼容）
- 根据新格式参数构建结构化提示词
- 确保提示词包含所有必要信息

#### buildSceneContext()
合并和构建上下文信息：
- 保留用户提供的context数据
- 添加新格式参数到上下文
- 提供完整的场景生成上下文

## 使用示例

### 1. 基础场景生成

```javascript
// 前端调用示例
const response = await fetch('/api/v1/ai/generate/scene', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    world_id: 'fantasy-world-001',
    scene_name: '魔法学院图书馆',
    scene_type: 'indoor',
    theme: 'magic',
    mood: 'scholarly',
    special_requirements: '包含古老的魔法书籍和神秘的法阵'
  })
});

const result = await response.json();
console.log('生成的场景:', result.data);
```

### 2. 连接场景生成

```javascript
// 生成连接多个场景的复杂场景
const response = await fetch('/api/v1/ai/generate/scene', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    world_id: 'adventure-world-002',
    scene_name: '十字路口',
    scene_type: 'crossroads',
    theme: 'medieval',
    mood: 'neutral',
    connected_scenes: ['森林小径', '山间村庄', '古老城堡', '神秘洞穴'],
    special_requirements: '提供清晰的方向指示和每个路径的简短描述'
  })
});
```

### 3. 兼容旧格式

```javascript
// 使用旧格式的提示词
const response = await fetch('/api/v1/ai/generate/scene', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    world_id: 'custom-world',
    prompt: '生成一个充满危险的地下城场景，包含陷阱和宝藏',
    context: {
      difficulty: 'hard',
      player_level: 5,
      theme: 'dungeon'
    }
  })
});
```

## 错误处理

### 常见错误类型

1. **参数验证错误** (400)
   ```json
   {
     "success": false,
     "message": "请求参数错误",
     "error": "world_id字段是必需的"
   }
   ```

2. **认证错误** (401)
   ```json
   {
     "success": false,
     "message": "未授权访问",
     "error": "无效的访问令牌"
   }
   ```

3. **AI服务错误** (500)
   ```json
   {
     "success": false,
     "message": "生成场景失败",
     "error": "AI服务暂时不可用"
   }
   ```

## 性能优化

### 1. 请求优化
- 合理设置AI模型参数（max_tokens, temperature）
- 缓存常用的场景模板
- 批量处理相关场景生成请求

### 2. 响应优化
- 压缩响应数据
- 使用适当的HTTP缓存头
- 异步处理长时间的生成任务

### 3. 资源管理
- 限制并发请求数量
- 实现请求队列和优先级
- 监控AI服务的使用配额

## 扩展功能

### 1. 场景模板系统
- 预定义常用场景类型的模板
- 支持用户自定义场景模板
- 模板参数化和复用

### 2. 场景关联分析
- 分析场景之间的逻辑关系
- 自动建议连接场景
- 生成场景地图和导航

### 3. 多语言支持
- 支持多种语言的场景生成
- 本地化的场景描述和元素
- 文化适应性的内容生成

## 维护和监控

### 1. 日志监控
- 监控API调用频率和响应时间
- 记录AI服务的使用情况
- 跟踪错误率和失败原因

### 2. 质量控制
- 定期评估生成场景的质量
- 收集用户反馈和评分
- 持续优化提示词模板

### 3. 版本管理
- 保持API版本的向后兼容性
- 渐进式升级和功能迁移
- 文档和示例的同步更新

## 总结

AI场景生成模块是一个功能强大、灵活可扩展的系统组件。通过支持多种输入格式、智能提示词构建和详细的日志记录，为用户提供了优质的场景生成体验。模块的设计充分考虑了向后兼容性和未来扩展性，能够满足不同类型游戏的场景生成需求。
