# 游戏交互API文档

## 概述

游戏交互API提供了完整的游戏玩法功能，包括角色行动、角色间交互、场景对话、事件触发、世界状态管理等。所有API都需要Bearer Token认证。

## API端点

### 基础URL
```
/api/v1/game
```

## 角色行动系统

### 1. 执行角色行动
**POST** `/api/v1/game/characters/{character_id}/actions`

角色执行各种行动，如移动、探索、使用物品等。

**请求头:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体:**
```json
{
  "world_id": "uuid",
  "action_type": "move",
  "target_type": "scene",
  "target_id": "scene-uuid",
  "parameters": {
    "direction": "north",
    "speed": "normal"
  }
}
```

**字段说明:**
- `world_id`: 世界ID
- `action_type`: 行动类型 (move, explore, use, rest, search)
- `target_type`: 目标类型 (scene, character, entity, item)
- `target_id`: 目标ID (可选)
- `parameters`: 行动参数 (可选)

**行动类型说明:**
- `move`: 移动到指定场景
- `explore`: 探索当前场景
- `use`: 使用物品或与实体交互
- `rest`: 休息恢复状态
- `search`: 搜索场景中的隐藏物品

**响应:**
```json
{
  "success": true,
  "message": "行动执行成功",
  "data": {
    "event_id": "uuid",
    "action_type": "move",
    "success": true,
    "message": "成功执行move行动",
    "effects": {
      "location_changed": true,
      "energy_consumed": 5
    },
    "timestamp": "2025-08-03T07:00:00Z"
  }
}
```

### 2. 角色间交互
**POST** `/api/v1/game/characters/{character_id}/interact/{target_character_id}`

角色与另一个角色进行交互。

**请求体:**
```json
{
  "world_id": "uuid",
  "interaction_type": "greet",
  "content": "你好！很高兴见到你。",
  "parameters": {
    "emotion": "friendly",
    "volume": "normal"
  }
}
```

**交互类型:**
- `greet`: 打招呼
- `talk`: 对话
- `trade`: 交易
- `help`: 请求帮助
- `challenge`: 挑战
- `follow`: 跟随

**响应:**
```json
{
  "success": true,
  "message": "交互执行成功",
  "data": {
    "event_id": "uuid",
    "interaction_type": "greet",
    "success": true,
    "message": "成功与角色进行greet交互",
    "response": "你好！我也很高兴见到你！",
    "effects": {
      "relationship_change": 5,
      "mood_change": "positive"
    },
    "timestamp": "2025-08-03T07:00:00Z"
  }
}
```

### 3. 场景中说话
**POST** `/api/v1/game/characters/{character_id}/speak`

角色在当前场景中说话，其他角色可以听到。

**请求体:**
```json
{
  "world_id": "uuid",
  "content": "有人知道这里发生了什么吗？",
  "speech_type": "say",
  "target_character_id": null,
  "volume": "normal",
  "emotion": "curious"
}
```

**说话类型:**
- `say`: 正常说话
- `whisper`: 悄悄话
- `shout`: 大声喊叫
- `think`: 内心独白

**音量级别:**
- `quiet`: 安静 (只有很近的角色能听到)
- `normal`: 正常 (场景中的角色都能听到)
- `loud`: 大声 (相邻场景也可能听到)

**情感类型:**
- `neutral`: 中性
- `happy`: 高兴
- `sad`: 悲伤
- `angry`: 愤怒
- `curious`: 好奇
- `worried`: 担心

**响应:**
```json
{
  "success": true,
  "message": "说话成功",
  "data": {
    "event_id": "uuid",
    "content": "有人知道这里发生了什么吗？",
    "speech_type": "say",
    "volume": "normal",
    "emotion": "curious",
    "listeners": ["uuid1", "uuid2"],
    "success": true,
    "message": "说话成功",
    "timestamp": "2025-08-03T07:00:00Z"
  }
}
```

## 事件系统

### 1. 触发事件
**POST** `/api/v1/game/events/trigger`

手动触发一个游戏事件。

**请求体:**
```json
{
  "world_id": "uuid",
  "event_type": "world_event",
  "name": "神秘光芒",
  "description": "天空中突然出现了一道神秘的光芒",
  "priority": 7,
  "participants": ["character-uuid1", "character-uuid2"],
  "event_data": {
    "sub_type": "magical_phenomenon",
    "intensity": "high",
    "duration": 300
  },
  "process_immediately": true
}
```

**事件类型:**
- `character_action`: 角色行动事件
- `world_event`: 世界事件
- `scene_event`: 场景事件
- `system_event`: 系统事件

**优先级:**
- 1-3: 低优先级 (背景事件)
- 4-6: 中优先级 (一般事件)
- 7-9: 高优先级 (重要事件)
- 10: 紧急优先级 (关键事件)

**响应:**
```json
{
  "success": true,
  "message": "事件创建并处理成功",
  "data": {
    "event_id": "uuid",
    "event_type": "world_event",
    "name": "神秘光芒",
    "status": "completed",
    "success": true,
    "message": "事件创建并处理成功",
    "effects": {
      "characters_affected": 2,
      "mood_change": "mysterious"
    },
    "timestamp": "2025-08-03T07:00:00Z"
  }
}
```

## 世界状态管理

### 1. 获取世界状态
**GET** `/api/v1/game/worlds/{world_id}/state`

获取指定世界的当前状态信息。

**响应:**
```json
{
  "success": true,
  "message": "获取世界状态成功",
  "data": {
    "world_id": "uuid",
    "current_time": "2025-08-03T07:00:00Z",
    "game_time": 14400,
    "weather": "晴朗",
    "season": "春季",
    "global_events": [
      {
        "id": "uuid",
        "event_type": "world_event",
        "name": "春季庆典准备",
        "status": "pending",
        "priority": 5
      }
    ],
    "active_scenes": [
      {
        "id": "uuid",
        "name": "中央广场",
        "status": "active",
        "character_count": 3
      }
    ],
    "online_characters": [
      {
        "id": "uuid",
        "name": "艾莉丝",
        "character_type": "player",
        "current_scene_id": "scene-uuid"
      }
    ],
    "variables": {
      "weather": "晴朗",
      "season": "春季",
      "festival_preparation": 75
    }
  }
}
```

### 2. 更新世界时间
**POST** `/api/v1/game/worlds/{world_id}/time`

推进世界的游戏时间。

**请求体:**
```json
{
  "minutes": 60
}
```

**响应:**
```json
{
  "success": true,
  "message": "成功推进世界时间 60 分钟"
}
```

### 3. 处理世界时钟周期
**POST** `/api/v1/game/worlds/{world_id}/tick`

执行世界的一个时钟周期，包括时间推进、NPC行为、环境事件等。

**响应:**
```json
{
  "success": true,
  "message": "世界时钟周期处理成功"
}
```

## 游戏机制说明

### 行动系统
1. **行动消耗**: 每个行动都会消耗角色的行动点数或能量
2. **冷却时间**: 某些行动有冷却时间限制
3. **成功率**: 行动的成功率取决于角色属性和环境因素
4. **连锁反应**: 行动可能触发其他事件或影响其他角色

### 交互系统
1. **距离限制**: 角色必须在同一场景才能直接交互
2. **关系影响**: 角色间的关系会影响交互结果
3. **情感状态**: 角色的情感状态会影响交互方式
4. **记忆系统**: 交互会被记录到角色的记忆中

### 事件系统
1. **事件队列**: 事件按优先级排队处理
2. **事件链**: 事件可能触发其他事件
3. **参与者**: 事件可以影响多个角色
4. **持续时间**: 事件可以有持续效果

### 世界状态
1. **时间流逝**: 游戏时间独立于现实时间
2. **天气系统**: 天气会影响角色行为和事件
3. **季节变化**: 季节影响世界的整体氛围
4. **全局变量**: 存储世界级别的状态信息

## 错误处理

### 常见错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "timestamp": 1691049600
}
```

**状态码说明:**
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 冲突 (如角色正在执行其他行动)
- `500`: 服务器内部错误

### 错误类型
1. **参数错误**: 缺少必要参数或参数格式错误
2. **权限错误**: 用户无权操作指定角色
3. **状态错误**: 角色状态不允许执行该行动
4. **距离错误**: 角色距离目标太远
5. **冷却错误**: 行动还在冷却期内
6. **资源错误**: 缺少必要的资源或物品

## 最佳实践

### 1. 行动设计
- 合理设置行动的消耗和冷却时间
- 考虑行动的成功率和失败后果
- 设计有意义的行动链和组合

### 2. 交互设计
- 提供多样化的交互选项
- 考虑角色性格对交互的影响
- 设计有趣的对话和反应

### 3. 事件设计
- 平衡随机事件和剧情事件
- 考虑事件对游戏平衡的影响
- 提供玩家选择和参与的机会

### 4. 状态管理
- 定期保存重要的游戏状态
- 监控世界状态的变化
- 提供状态回滚机制

### 5. 性能优化
- 批量处理相似的行动和事件
- 使用缓存减少数据库查询
- 异步处理非关键事件
