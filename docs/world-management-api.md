# 游戏世界管理API文档

## 概述

游戏世界管理API提供了完整的世界CRUD操作，包括创建、查询、更新、删除世界等功能。所有API都需要Bearer Token认证。

## API端点

### 基础URL
```
/api/v1/game/worlds
```

## 世界管理

### 1. 创建世界
**POST** `/api/v1/game/worlds`

创建一个新的游戏世界。

**请求头:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体:**
```json
{
  "name": "我的世界",
  "description": "这是一个奇幻世界",
  "config": {
    "theme": "fantasy",
    "difficulty": "normal",
    "time_rate": 1.0,
    "max_memory_per_char": 100
  }
}
```

**响应:**
```json
{
  "success": true,
  "message": "创建世界成功",
  "data": {
    "id": "uuid",
    "name": "我的世界",
    "description": "这是一个奇幻世界",
    "creator_id": "uuid",
    "world_config": {...},
    "world_state": {...},
    "status": "active",
    "is_public": false,
    "max_players": 10,
    "current_players": 0,
    "game_time": 0,
    "created_at": "2025-08-03T06:30:00Z",
    "updated_at": "2025-08-03T06:30:00Z"
  }
}
```

### 2. 获取世界信息
**GET** `/api/v1/game/worlds/{world_id}`

获取指定世界的详细信息。

**路径参数:**
- `world_id`: 世界UUID

**响应:**
```json
{
  "success": true,
  "message": "获取世界信息成功",
  "data": {
    "id": "uuid",
    "name": "我的世界",
    "description": "这是一个奇幻世界",
    "creator": {
      "id": "uuid",
      "display_name": "创建者名称"
    },
    "scenes": [...],
    "characters": [...],
    "entities": [...],
    "events": [...]
  }
}
```

### 3. 更新世界信息
**PUT** `/api/v1/game/worlds/{world_id}`

更新指定世界的信息。只有世界创建者可以更新。

**请求体:**
```json
{
  "name": "新的世界名称",
  "description": "新的描述",
  "is_public": true,
  "max_players": 20,
  "status": "active"
}
```

**响应:**
```json
{
  "success": true,
  "message": "更新世界成功"
}
```

### 4. 删除世界
**DELETE** `/api/v1/game/worlds/{world_id}`

删除指定的世界。只有世界创建者可以删除。

**响应:**
```json
{
  "success": true,
  "message": "删除世界成功"
}
```

### 5. 加入世界
**POST** `/api/v1/game/worlds/{world_id}/join`

用户加入指定的世界。

**响应:**
```json
{
  "success": true,
  "message": "加入世界成功"
}
```

### 6. 离开世界
**POST** `/api/v1/game/worlds/{world_id}/leave`

用户离开指定的世界。

**响应:**
```json
{
  "success": true,
  "message": "离开世界成功"
}
```

## 世界列表

### 1. 获取我的世界列表
**GET** `/api/v1/game/my-worlds`

获取当前用户创建的世界列表。

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)

**响应:**
```json
{
  "success": true,
  "message": "获取我的世界列表成功",
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "limit": 20,
    "total_pages": 5
  }
}
```

### 2. 获取公开世界列表
**GET** `/api/v1/game/public-worlds`

获取所有公开的世界列表。

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)

**响应:**
```json
{
  "success": true,
  "message": "获取公开世界列表成功",
  "data": {
    "items": [...],
    "total": 50,
    "page": 1,
    "limit": 20,
    "total_pages": 3
  }
}
```

## 错误响应

所有API在出错时都会返回统一的错误格式：

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

**常见状态码:**
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 无权限
- `404`: 资源不存在
- `500`: 服务器内部错误

## 世界配置说明

世界配置 (`world_config`) 支持以下字段：

```json
{
  "time_rate": 1.0,           // 时间倍率
  "tick_interval": 30,        // 心跳间隔(秒)
  "max_memory_per_char": 100, // 每个角色最大记忆数
  "rules": {},                // 自定义规则
  "theme": "fantasy",         // 世界主题
  "difficulty": "normal",     // 难度等级
  "language": "zh-CN"         // 语言设置
}
```

## 世界状态说明

世界状态 (`world_state`) 包含以下信息：

```json
{
  "current_tick": 0,          // 当前心跳数
  "last_tick_at": "2025-08-03T06:30:00Z", // 最后心跳时间
  "active_events": [],        // 活跃事件ID列表
  "global_variables": {},     // 全局变量
  "weather": {                // 天气状态
    "type": "clear",
    "temperature": 20
  },
  "season": "spring",         // 季节
  "world_goals": []           // 世界目标ID列表
}
```
