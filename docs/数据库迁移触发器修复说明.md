# 数据库迁移触发器修复说明

## 问题概述

在后端服务启动时，数据库迁移过程中出现了触发器错误，具体错误信息为：

```
error in trigger update_user_stats_updated_at: no such column: id
```

这个错误导致数据库迁移失败，服务器无法正常启动。

## 问题根本原因

### 1. 表结构差异

- **user_stats 表**：使用 `user_id` 作为主键（外键关联到 users.id）
- **其他表**：大多数表使用 `id` 作为主键

### 2. 触发器生成逻辑缺陷

原始的触发器生成代码在 `pkg/database/compatibility.go` 中，对所有表都使用了固定的 `id` 字段：

```sql
UPDATE %s SET updated_at = datetime('now') WHERE id = NEW.id;
```

但 `user_stats` 表没有 `id` 字段，导致触发器执行时找不到该列。

### 3. SQLite 迁移过程中的触发器冲突

在 GORM 的 AutoMigrate 过程中，SQLite 会：
1. 创建临时表 `table__temp`
2. 复制数据到临时表
3. 删除原表
4. 将临时表重命名为原表名

在这个过程中，如果触发器引用了错误的列名，会导致迁移失败。

## 修复方案

### 1. 智能主键字段识别

在 `pkg/database/compatibility.go` 中添加了 `getPrimaryKeyField` 方法：

```go
// getPrimaryKeyField 根据表名返回正确的主键字段名
func (c *CompatibilityConfig) getPrimaryKeyField(tableName string) string {
    switch tableName {
    case "user_stats":
        return "user_id" // user_stats 表使用 user_id 作为主键
    case "users", "worlds", "characters", "scenes", "entities", "game_events":
        return "id" // 大多数表使用 id 作为主键
    default:
        return "id" // 默认使用 id
    }
}
```

### 2. 改进的触发器生成逻辑

修改了 `GetTriggerSQL` 方法，使其能够根据表名选择正确的主键字段：

```go
func (c *CompatibilityConfig) GetTriggerSQL(tableName string) string {
    switch c.DBType {
    case SQLite:
        primaryKeyField := c.getPrimaryKeyField(tableName)
        return fmt.Sprintf(`
CREATE TRIGGER update_%s_updated_at 
    AFTER UPDATE ON %s 
    FOR EACH ROW 
    WHEN NEW.updated_at = OLD.updated_at
    BEGIN 
        UPDATE %s SET updated_at = datetime('now') WHERE %s = NEW.%s;
    END;`, tableName, tableName, tableName, primaryKeyField, primaryKeyField)
    // ...
}
```

### 3. 迁移前触发器清理

在 `internal/migration/smart_migrator.go` 中添加了 `cleanupTriggersBeforeMigration` 方法：

```go
// cleanupTriggersBeforeMigration 在迁移前清理可能冲突的触发器
func (sm *SmartMigrator) cleanupTriggersBeforeMigration() error {
    if sm.compatibility.DBType != database.SQLite {
        return nil
    }

    triggers := []string{
        "update_users_updated_at",
        "update_user_stats_updated_at",
        "update_worlds_updated_at",
        "update_characters_updated_at",
        "update_scenes_updated_at",
    }

    for _, trigger := range triggers {
        dropSQL := fmt.Sprintf("DROP TRIGGER IF EXISTS %s", trigger)
        sm.ExecuteRawSQL(dropSQL)
    }

    return nil
}
```

### 4. 改进的迁移流程

修改了 `CreateDevelopmentSchema` 方法，增加了触发器管理逻辑：

1. **迁移前清理**：删除可能冲突的旧触发器
2. **执行迁移**：使用 GORM AutoMigrate 创建表结构
3. **重建触发器**：使用正确的主键字段重新创建触发器

## 修复效果验证

### 1. 单元测试

创建了 `internal/migration/smart_migrator_test.go` 测试文件，包含：

- `TestSmartMigrator_TriggerPrimaryKeyFix`：测试触发器主键字段修复
- `TestCompatibilityConfig_GetPrimaryKeyField`：测试主键字段获取逻辑
- `TestSmartMigrator_CleanupTriggersBeforeMigration`：测试迁移前触发器清理

### 2. 实际服务器启动测试

修复后的服务器能够成功启动，数据库迁移过程中：

1. ✅ 成功清理旧触发器
2. ✅ 成功创建所有表结构
3. ✅ 成功创建正确的触发器（user_stats 表使用 user_id 字段）
4. ✅ 服务器正常启动并监听端口 8080

## 技术要点

### 1. SQLite 触发器语法

SQLite 触发器语法与 PostgreSQL 不同：

```sql
-- SQLite 语法
CREATE TRIGGER trigger_name 
    AFTER UPDATE ON table_name 
    FOR EACH ROW 
    WHEN condition
    BEGIN 
        UPDATE table_name SET column = value WHERE key = NEW.key;
    END;

-- PostgreSQL 语法
CREATE TRIGGER trigger_name 
    BEFORE UPDATE ON table_name 
    FOR EACH ROW 
    EXECUTE FUNCTION function_name();
```

### 2. GORM AutoMigrate 过程

GORM 在 SQLite 中执行 AutoMigrate 时会：
1. 分析现有表结构
2. 创建临时表（如果需要修改结构）
3. 迁移数据
4. 重命名表

在这个过程中，触发器需要能够正确处理表结构变化。

### 3. 主键字段映射策略

不同表的主键字段设计：
- **关联表**（如 user_stats）：使用外键作为主键
- **主表**（如 users, worlds）：使用自生成的 id 作为主键

## 最佳实践

### 1. 触发器设计原则

- 使用 `WHEN NEW.updated_at = OLD.updated_at` 条件避免无限递归
- 根据表结构选择正确的主键字段
- 在迁移前清理旧触发器避免冲突

### 2. 数据库兼容性处理

- 为不同数据库类型提供专门的 SQL 生成逻辑
- 使用配置化的方式管理表结构差异
- 提供回退机制处理未知表结构

### 3. 测试策略

- 为每个修复点编写单元测试
- 测试实际的数据库迁移流程
- 验证触发器功能的正确性

## 相关文件

- `pkg/database/compatibility.go`：数据库兼容性配置和触发器生成
- `internal/migration/smart_migrator.go`：智能迁移器实现
- `internal/migration/smart_migrator_test.go`：单元测试
- `internal/models/user.go`：用户和用户统计模型定义

## 总结

通过智能识别不同表的主键字段，改进触发器生成逻辑，并在迁移过程中正确处理触发器的创建和清理，成功解决了 SQLite 数据库迁移过程中的触发器错误问题。修复后的系统能够稳定运行，为不同表结构提供了灵活的触发器支持。
