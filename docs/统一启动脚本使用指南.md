# AI文本游戏统一启动脚本使用指南

## 概述

`scripts/dev_unified.sh` 是AI文本游戏项目的统一开发环境启动脚本，整合了原有的多个启动脚本功能，提供了更加完善和易用的开发体验。

## 主要特性

### 🚀 多模式支持
- **全栈模式** (`fullstack`): 同时启动前端和后端服务
- **前端模式** (`frontend`): 仅启动前端开发服务器
- **后端模式** (`backend`): 仅启动后端开发服务器

### 🐛 调试功能
- **调试日志开关**: 通过 `--debug` 参数或 `DEBUG_ENABLED` 环境变量控制
- **详细日志输出**: 通过 `--verbose` 参数或 `VERBOSE_LOGS` 环境变量控制
- **实时日志显示**: 调试模式下在控制台显示实时日志
- **日志文件记录**: 标准模式下将日志保存到文件

### 🔧 智能管理
- **端口冲突检测**: 自动检测端口占用并提供解决方案
- **依赖自动检查**: 自动检查和安装Go和Node.js依赖
- **进程监控**: 监控服务状态，异常时自动清理
- **优雅停止**: Ctrl+C时优雅停止所有服务

### 🔐 认证控制
- **认证跳过模式**: 默认启用，便于开发调试
- **正常认证模式**: 可通过 `--auth` 参数启用

### 🗄️ 数据库支持
- **SQLite模式**: 默认使用，适合开发环境
- **PostgreSQL模式**: 通过 `--postgres` 参数启用

## 使用方法

### 基本用法

```bash
# 启动完整全栈开发环境（默认模式）
./scripts/dev_unified.sh

# 启动完整全栈开发环境（显式指定）
./scripts/dev_unified.sh fullstack

# 仅启动前端开发服务器
./scripts/dev_unified.sh frontend

# 仅启动后端开发服务器
./scripts/dev_unified.sh backend
```

### 调试模式

```bash
# 启用调试日志
./scripts/dev_unified.sh --debug

# 启用详细日志
./scripts/dev_unified.sh --verbose

# 同时启用调试和详细日志
./scripts/dev_unified.sh --debug --verbose

# 仅启动前端并启用调试
./scripts/dev_unified.sh frontend --debug
```

### 数据库配置

```bash
# 使用SQLite数据库（默认）
./scripts/dev_unified.sh --sqlite

# 使用PostgreSQL数据库
./scripts/dev_unified.sh --postgres
```

### 认证配置

```bash
# 跳过认证（默认）
./scripts/dev_unified.sh --no-auth

# 启用正常认证
./scripts/dev_unified.sh --auth
```

### 端口配置

```bash
# 自定义前端端口
./scripts/dev_unified.sh --frontend-port 3001

# 自定义后端端口
./scripts/dev_unified.sh --backend-port 8081

# 同时自定义两个端口
./scripts/dev_unified.sh --frontend-port 3001 --backend-port 8081
```

### 环境变量控制

```bash
# 通过环境变量启用调试
DEBUG_ENABLED=true ./scripts/dev_unified.sh

# 通过环境变量启用详细日志
VERBOSE_LOGS=true ./scripts/dev_unified.sh

# 通过环境变量设置端口
FRONTEND_PORT=3001 BACKEND_PORT=8081 ./scripts/dev_unified.sh

# 组合使用环境变量
DEBUG_ENABLED=true SKIP_AUTH=false DATABASE_MODE=postgres ./scripts/dev_unified.sh
```

## 配置选项

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `fullstack` | 启动全栈环境 | 默认模式 |
| `frontend` | 仅启动前端 | - |
| `backend` | 仅启动后端 | - |
| `--debug` | 启用调试日志 | false |
| `--verbose` | 启用详细日志 | false |
| `--no-auth` | 跳过认证 | true |
| `--auth` | 启用认证 | false |
| `--sqlite` | 使用SQLite | 默认 |
| `--postgres` | 使用PostgreSQL | - |
| `--frontend-port` | 前端端口 | 3000 |
| `--backend-port` | 后端端口 | 8080 |
| `--no-install` | 跳过依赖安装 | false |

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DEBUG_ENABLED` | 启用调试日志 | false |
| `VERBOSE_LOGS` | 启用详细日志 | false |
| `SKIP_AUTH` | 跳过认证 | true |
| `DATABASE_MODE` | 数据库模式 | sqlite |
| `FRONTEND_PORT` | 前端端口 | 3000 |
| `BACKEND_PORT` | 后端端口 | 8080 |

## 日志管理

### 调试模式日志
- 启用调试模式时，日志会实时显示在控制台
- 包含详细的时间戳和分类标签
- 支持彩色输出，便于区分不同类型的日志

### 标准模式日志
- 后端日志: `/tmp/backend.log`
- 前端日志: `/tmp/frontend.log`
- 查看实时日志: `tail -f /tmp/backend.log`

### 日志级别
- **[错误]**: 系统错误和异常情况
- **[警告]**: 需要注意的情况
- **[信息]**: 一般操作信息
- **[成功]**: 操作成功确认
- **[调试]**: 详细的调试信息（需启用调试模式）
- **[详细]**: 更详细的操作信息（需启用详细模式）
- **[前端]**: 前端服务相关日志
- **[后端]**: 后端服务相关日志

## 故障排除

### 端口被占用
脚本会自动检测端口占用并提供解决方案：
1. 显示占用进程信息
2. 询问是否停止占用进程
3. 自动清理并继续启动

### 依赖问题
- 脚本会自动检查Go和Node.js环境
- 自动安装和更新依赖包
- 可使用 `--no-install` 跳过依赖检查

### 服务启动失败
- 检查日志文件获取详细错误信息
- 确保数据库服务正常运行（PostgreSQL模式）
- 检查防火墙和端口权限设置

### 进程异常停止
- 脚本会监控服务状态
- 异常停止时自动显示错误日志
- 执行清理操作避免僵尸进程

## 最佳实践

### 开发环境推荐配置
```bash
# 日常开发（快速启动）
./scripts/dev_unified.sh

# 调试开发（详细日志）
./scripts/dev_unified.sh --debug --verbose

# 前端开发（仅前端）
./scripts/dev_unified.sh frontend --debug
```

### 生产环境注意事项
- 此脚本仅用于开发环境
- 生产环境请使用专门的部署脚本
- 不要在生产环境启用调试模式

## 与原有脚本的对比

| 原脚本 | 统一脚本等效命令 | 说明 |
|--------|------------------|------|
| `dev_full_stack.sh` | `./scripts/dev_unified.sh fullstack` | 全栈开发 |
| `dev_frontend.sh` | `./scripts/dev_unified.sh frontend` | 前端开发 |
| `dev_start.sh` | `./scripts/dev_unified.sh` | 基础启动 |
| `dev_no_auth.sh` | `./scripts/dev_unified.sh --no-auth` | 跳过认证 |
| `dev_with_sqlite.sh` | `./scripts/dev_unified.sh --sqlite` | SQLite模式 |

## 技术实现

### 脚本架构
- 模块化设计，功能分离
- 统一的日志系统
- 完善的错误处理
- 信号处理和清理机制

### 进程管理
- PID文件管理
- 优雅停止机制
- 僵尸进程清理
- 异常监控和恢复

### 配置管理
- 环境变量支持
- 命令行参数解析
- 默认值设置
- 配置验证

这个统一启动脚本大大简化了开发环境的管理，提供了更好的开发体验和调试能力。
