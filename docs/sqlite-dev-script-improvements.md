# SQLite 开发脚本改进说明

## 问题描述

原始的 `scripts/dev_with_sqlite.sh` 脚本存在一个重要问题：它会直接覆盖 `.env` 文件，导致用户配置的重要信息（如 AI_TOKEN、OAuth 客户端 ID 等）丢失。

## 解决方案

### 1. 智能配置合并

新版本的脚本实现了智能配置合并功能，能够：

- **保留用户自定义配置**：自动识别并保留用户在 `.env` 文件中配置的重要信息
- **应用 SQLite 配置**：同时应用 SQLite 开发环境所需的配置
- **避免配置冲突**：智能处理配置项的优先级

#### 保留的配置项

脚本会自动保留以下用户自定义的配置项：

```bash
AI_TOKEN                    # AI 服务访问令牌
AI_BASE_URL                 # AI 服务基础 URL
OAUTH_GOOGLE_CLIENT_ID      # Google OAuth 客户端 ID
OAUTH_GOOGLE_CLIENT_SECRET  # Google OAuth 客户端密钥
OAUTH_GITHUB_CLIENT_ID      # GitHub OAuth 客户端 ID
OAUTH_GITHUB_CLIENT_SECRET  # GitHub OAuth 客户端密钥
JWT_SECRET                  # JWT 签名密钥
```

### 2. 配置备份机制

- **自动备份**：每次运行脚本时自动备份现有的 `.env` 文件
- **时间戳命名**：备份文件使用时间戳命名，如 `.env.backup.20250803_190954`
- **多版本保留**：支持保留多个备份版本

### 3. 新增功能选项

#### `--status` 查看配置状态

```bash
./scripts/dev_with_sqlite.sh --status
```

显示当前配置状态，包括：
- 当前 `.env` 文件中的关键配置项
- 可用的配置备份文件列表

#### `--restore` 恢复配置备份

```bash
./scripts/dev_with_sqlite.sh --restore
```

交互式恢复配置备份：
- 列出所有可用的备份文件
- 允许用户选择要恢复的特定备份
- 默认恢复最新的备份

### 4. 改进的清理机制

脚本退出时会：
- 自动恢复最新的配置备份
- 询问用户是否删除备份文件
- 提供更好的用户体验

## 使用方法

### 基本使用

```bash
# 启动 SQLite 开发环境（会自动备份和合并配置）
./scripts/dev_with_sqlite.sh

# 仅初始化数据库
./scripts/dev_with_sqlite.sh --init-only

# 查看当前配置状态
./scripts/dev_with_sqlite.sh --status

# 恢复配置备份
./scripts/dev_with_sqlite.sh --restore

# 清理数据库文件
./scripts/dev_with_sqlite.sh --clean
```

### 工作流程

1. **首次运行**：
   ```bash
   ./scripts/dev_with_sqlite.sh
   ```
   - 自动备份现有 `.env` 文件
   - 合并用户配置和 SQLite 配置
   - 初始化 SQLite 数据库
   - 启动开发服务器

2. **查看配置状态**：
   ```bash
   ./scripts/dev_with_sqlite.sh --status
   ```

3. **需要恢复原始配置时**：
   ```bash
   ./scripts/dev_with_sqlite.sh --restore
   ```

## 技术实现

### 配置合并算法

```bash
merge_env_configs() {
    # 1. 以 SQLite 配置为基础
    # 2. 从原始配置中提取用户自定义项
    # 3. 智能覆盖重要配置项
    # 4. 生成合并后的配置文件
}
```

### 备份策略

- 使用时间戳确保备份文件唯一性
- 支持多个备份版本并存
- 提供交互式恢复选择

## 测试验证

项目包含了完整的测试脚本 `scripts/test_config_merge.sh`，用于验证配置合并功能的正确性。

```bash
# 运行测试
./scripts/test_config_merge.sh
```

测试覆盖：
- 用户配置保留测试
- SQLite 配置应用测试
- 配置项优先级测试
- 边界情况处理测试

## 向后兼容性

- 完全兼容原有的使用方式
- 不影响现有的工作流程
- 新功能为可选功能，不会破坏现有行为

## 安全考虑

- 敏感配置项（如 token、密钥）得到妥善保护
- 备份文件包含敏感信息，建议定期清理
- 配置合并过程中不会泄露敏感信息到日志

## 总结

这次改进解决了原始脚本覆盖用户配置的问题，提供了更加智能和用户友好的配置管理方案。用户现在可以安全地使用 SQLite 开发脚本，而不用担心丢失重要的配置信息。
