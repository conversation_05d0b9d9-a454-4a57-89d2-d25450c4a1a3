# AI文本游戏 v4.0 架构升级文档

## 概述

v4.0架构是对AI文本游戏数据库架构的重大升级，旨在提高系统的可扩展性、性能和功能完整性。本次升级引入了多个新表，优化了现有表结构，并改进了数据关系设计。

## 主要变更

### 1. 新增表结构

#### 1.1 用户会话表 (user_sessions)
管理用户在不同世界中的会话状态，包括当前活跃角色、UI状态等。

**主要字段：**
- `id`: 会话唯一标识
- `user_id`: 用户ID
- `world_id`: 世界ID
- `active_character_id`: 当前活跃角色ID
- `session_data`: 会话数据（JSON格式）
- `last_activity_at`: 最后活跃时间

**功能特性：**
- 支持用户在多个世界间切换
- 保存UI状态和游戏进度
- 会话超时管理
- 跨设备状态同步

#### 1.2 角色记忆表 (character_memories)
存储角色的记忆信息，支持记忆强度和衰减机制。

**主要字段：**
- `id`: 记忆唯一标识
- `character_id`: 角色ID
- `content`: 记忆内容
- `memory_type`: 记忆类型（event, person, location等）
- `importance_score`: 重要性评分 (0-1)
- `current_strength`: 当前记忆强度 (0-1)
- `decay_rate`: 衰减率
- `tags`: 记忆标签
- `associated_entities`: 关联实体ID列表

**功能特性：**
- 记忆强度动态衰减
- 基于重要性的衰减抗性
- 标签化分类和检索
- 关联实体追踪

#### 1.3 角色阅历表 (character_experiences)
存储角色的技能、知识和经验，支持熟练度系统。

**主要字段：**
- `id`: 阅历唯一标识
- `character_id`: 角色ID
- `tags`: 阅历标签
- `description`: 阅历描述
- `proficiency`: 熟练度信息（JSON格式）
- `learning_info`: 学习信息
- `social_impact`: 社交影响
- `effects`: 阅历效果

**功能特性：**
- 动态熟练度系统
- 经验值和等级管理
- 技能专精追踪
- 社交影响评估

#### 1.4 专门实体表 (specialized_entities)
为不同类型的实体提供特定的属性扩展。

**主要字段：**
- `entity_id`: 关联的实体ID
- `specialization_type`: 专门化类型
- `specialized_data`: 类型特定数据（JSON格式）

**支持的专门化类型：**
- `item`: 物品（武器、道具等）
- `event`: 事件
- `goal`: 目标
- `location`: 位置
- `abstract`: 抽象概念

#### 1.5 游戏事件表 (game_events)
记录游戏中发生的所有事件，替代原来的events表。

**主要字段：**
- `id`: 事件唯一标识
- `world_id`: 世界ID
- `event_type`: 事件类型
- `tags`: 事件标签
- `primary_actor_id`: 主要触发者ID
- `participants`: 参与者信息
- `narrative_text`: AI生成的叙事文本
- `event_data`: 事件详细数据
- `game_time`: 游戏内时间

**功能特性：**
- 完整的事件追踪
- 多参与者支持
- AI叙事生成
- 游戏时间同步

### 2. 现有表结构优化

#### 2.1 用户表 (users)
**新增字段：**
- `profile`: 用户档案信息（JSON格式，合并显示名称、头像等）
- `last_active_at`: 用户最后活跃时间

**向后兼容性：**
- 保留原有的 `display_name` 和 `avatar_url` 字段
- 新的 `profile` 字段提供更丰富的用户信息结构

#### 2.2 世界表 (worlds)
**新增字段：**
- `access_settings`: 访问设置（JSON格式，合并is_public, max_players等）
- `tags`: 世界标签
- `time_config`: 时间配置

**功能增强：**
- 更灵活的访问控制
- 标签化分类
- 可配置的时间系统

#### 2.3 角色表 (characters)
**新增字段：**
- `characteristics`: 角色特征信息（JSON格式，替代traits）
- `is_primary`: 是否为用户的主要角色
- `display_order`: 显示顺序
- `last_active_at`: 角色最后活跃时间

**功能增强：**
- 更丰富的角色特征系统
- 主要角色标识
- 角色排序支持

#### 2.4 实体表 (entities)
**新增字段：**
- `container_entity_id`: 容器实体ID（支持实体包含关系）
- `version`: 实体版本号（用于版本控制）
- `tags`: 实体标签

**功能增强：**
- 实体包含关系支持
- 版本控制机制
- 标签化分类

#### 2.5 场景表 (scenes)
**新增字段：**
- `tags`: 场景标签（替代scene_type）
- `access_rules`: 访问规则
- `connections`: 场景连接信息（替代connected_scenes）

**功能增强：**
- 更灵活的场景分类
- 细粒度访问控制
- 增强的场景连接系统

## 数据迁移策略

### 1. 向后兼容性
- 保留所有原有字段，确保现有代码继续工作
- 新字段提供默认值，不影响现有数据
- 逐步迁移策略，允许新旧系统并存

### 2. 数据迁移流程
1. **创建新表**：创建所有新的表结构
2. **更新现有表**：为现有表添加新字段
3. **数据迁移**：将旧格式数据迁移到新结构
4. **索引创建**：创建必要的数据库索引
5. **验证测试**：确保迁移后数据完整性

### 3. 迁移工具
提供专门的迁移工具 `cmd/migrate`，支持：
- 自动迁移到最新版本
- 指定版本迁移
- 回滚操作
- 状态查询
- 架构验证和修复

## 性能优化

### 1. 索引策略
- **用户会话**：用户ID+世界ID复合索引，最后活跃时间索引
- **角色记忆**：角色ID索引，记忆类型索引，重要性评分索引
- **角色阅历**：角色ID索引，最后使用时间索引
- **游戏事件**：世界ID索引，事件类型索引，游戏时间索引

### 2. 查询优化
- 使用软引用减少JOIN操作
- JSON字段索引支持
- 分页查询优化
- 缓存策略支持

### 3. 存储优化
- JSON字段压缩存储
- 历史数据归档机制
- 冗余数据清理

## API变更

### 1. 新增API端点
- `/api/v4/sessions` - 用户会话管理
- `/api/v4/characters/{id}/memories` - 角色记忆管理
- `/api/v4/characters/{id}/experiences` - 角色阅历管理
- `/api/v4/events` - 游戏事件查询

### 2. 现有API增强
- 用户API支持档案信息
- 世界API支持访问设置和标签
- 角色API支持特征信息
- 实体API支持容器关系和标签

### 3. 向后兼容性
- 保持现有API端点不变
- 新字段作为可选返回
- 渐进式API版本升级

## 部署指南

### 1. 升级前准备
```bash
# 备份现有数据库
cp game.db game.db.backup

# 验证当前架构
./cmd/migrate/migrate -action=validate -db=game.db
```

### 2. 执行升级
```bash
# 自动迁移到最新版本
./cmd/migrate/migrate -action=auto -db=game.db -verbose

# 或者手动指定版本
./cmd/migrate/migrate -action=migrate -version=v4.0 -db=game.db
```

### 3. 升级后验证
```bash
# 检查迁移状态
./cmd/migrate/migrate -action=status -db=game.db

# 验证架构完整性
./cmd/migrate/migrate -action=validate -db=game.db
```

### 4. 回滚操作（如需要）
```bash
# 回滚到之前版本
./cmd/migrate/migrate -action=rollback -version=v4.0 -db=game.db
```

## 开发指南

### 1. 模型使用
```go
// 创建用户会话
session := &models.UserSession{
    UserID:  userID,
    WorldID: worldID,
}
db.Create(session)

// 管理角色记忆
memory := &models.CharacterMemory{
    CharacterID:     characterID,
    Content:         "重要的记忆内容",
    MemoryType:      "event",
    ImportanceScore: 0.8,
}
db.Create(memory)
```

### 2. 查询优化
```go
// 使用预加载优化查询
var user models.User
db.Preload("Sessions").First(&user, userID)

// 使用索引优化查询
var memories []models.CharacterMemory
db.Where("character_id = ? AND memory_type = ?", characterID, "event").
   Order("importance_score DESC").
   Find(&memories)
```

### 3. 测试支持
- 完整的单元测试覆盖
- 集成测试支持
- 性能测试基准
- 数据一致性验证

## 监控和维护

### 1. 性能监控
- 查询性能指标
- 存储空间使用
- 索引效率分析
- 缓存命中率

### 2. 数据维护
- 定期数据清理
- 索引重建
- 统计信息更新
- 备份策略

### 3. 故障排除
- 迁移失败恢复
- 数据不一致修复
- 性能问题诊断
- 架构验证工具

## 未来规划

### 1. 短期目标
- 完善监控系统
- 优化查询性能
- 增强缓存机制
- 完善文档

### 2. 长期规划
- 分布式架构支持
- 实时数据同步
- 高级分析功能
- 机器学习集成

## 总结

v4.0架构升级为AI文本游戏提供了更强大、更灵活的数据基础。通过引入新的表结构和优化现有设计，系统能够更好地支持复杂的游戏逻辑、用户交互和AI功能。完善的迁移工具和向后兼容性设计确保了升级过程的平滑进行。
