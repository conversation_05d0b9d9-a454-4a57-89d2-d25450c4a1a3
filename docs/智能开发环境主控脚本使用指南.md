# AI文本游戏智能开发环境主控脚本使用指南

## 概述

`scripts/dev_master.sh` 是AI文本游戏项目的智能开发环境主控脚本，它整合了所有原有启动脚本的功能，提供了统一、智能、功能丰富的开发环境管理体验。

## 主要特性

### 🚀 统一的命令接口
- **多模式支持**：全栈、前端、后端、构建、停止五种模式
- **一致的参数体系**：统一的选项命名和行为
- **智能参数解析**：支持短参数和长参数形式

### 🐛 增强的调试功能
- **多级日志控制**：调试、详细、静默三种日志级别
- **实时日志显示**：调试模式下实时显示服务日志
- **时间戳支持**：可选的日志时间戳显示
- **彩色输出**：不同类型信息使用不同颜色区分

### ⚙️ 灵活的配置选项
- **数据库选择**：支持SQLite和PostgreSQL
- **认证控制**：可选择跳过或启用身份认证
- **端口配置**：自定义前端和后端端口
- **环境变量**：支持环境变量和命令行参数双重配置

### 🔧 智能的服务管理
- **端口冲突检测**：自动检测并处理端口占用
- **进程监控**：实时监控服务状态，异常时自动处理
- **优雅停止**：支持优雅的服务停止和清理
- **依赖检查**：自动检查和安装必要依赖

## 基本用法

### 启动模式

```bash
# 全栈开发环境（默认）
./scripts/dev_master.sh fullstack
./scripts/dev_master.sh full
./scripts/dev_master.sh f

# 仅前端开发
./scripts/dev_master.sh frontend
./scripts/dev_master.sh front
./scripts/dev_master.sh fe

# 仅后端开发
./scripts/dev_master.sh backend
./scripts/dev_master.sh back
./scripts/dev_master.sh be

# 构建前端应用
./scripts/dev_master.sh build
./scripts/dev_master.sh b

# 停止所有服务
./scripts/dev_master.sh stop
./scripts/dev_master.sh s
```

### 调试选项

```bash
# 启用调试日志
./scripts/dev_master.sh fullstack --debug
./scripts/dev_master.sh fullstack -d

# 启用详细日志
./scripts/dev_master.sh fullstack --verbose
./scripts/dev_master.sh fullstack -v

# 静默模式
./scripts/dev_master.sh fullstack --quiet
./scripts/dev_master.sh fullstack -q

# 禁用时间戳
./scripts/dev_master.sh fullstack --no-timestamp
```

## 高级配置

### 服务配置

```bash
# 自定义端口
./scripts/dev_master.sh fullstack --frontend-port 3001 --backend-port 8081

# 设置超时时间
./scripts/dev_master.sh fullstack --timeout 60
```

### 数据库配置

```bash
# 使用SQLite（默认）
./scripts/dev_master.sh fullstack --sqlite

# 使用PostgreSQL
./scripts/dev_master.sh fullstack --postgres

# 启用Redis缓存
./scripts/dev_master.sh fullstack --redis

# 禁用Redis缓存
./scripts/dev_master.sh fullstack --no-redis
```

### 认证配置

```bash
# 跳过认证（默认，适合开发）
./scripts/dev_master.sh fullstack --no-auth

# 启用正常认证
./scripts/dev_master.sh fullstack --auth

# 设置开发用户
./scripts/dev_master.sh fullstack --dev-user <EMAIL>
```

### 环境配置

```bash
# 设置环境模式
./scripts/dev_master.sh fullstack --env development
./scripts/dev_master.sh build --env production

# 设置构建模式
./scripts/dev_master.sh build --build-mode production
```

## 实用功能

### 干运行模式

```bash
# 查看将要执行的操作，不实际执行
./scripts/dev_master.sh fullstack --dry-run
```

### 强制清理

```bash
# 强制清理端口和进程
./scripts/dev_master.sh fullstack --force-clean
```

### 跳过依赖安装

```bash
# 跳过依赖检查和安装
./scripts/dev_master.sh fullstack --no-install
```

## 环境变量控制

除了命令行参数，还可以通过环境变量控制脚本行为：

```bash
# 调试控制
DEBUG_ENABLED=true ./scripts/dev_master.sh fullstack
VERBOSE_LOGS=true ./scripts/dev_master.sh fullstack
QUIET_MODE=true ./scripts/dev_master.sh fullstack

# 服务配置
FRONTEND_PORT=3001 ./scripts/dev_master.sh frontend
BACKEND_PORT=8081 ./scripts/dev_master.sh backend

# 数据库配置
DATABASE_MODE=postgres ./scripts/dev_master.sh fullstack
ENABLE_REDIS=true ./scripts/dev_master.sh fullstack

# 认证配置
SKIP_AUTH=false ./scripts/dev_master.sh fullstack
```

## 常用场景示例

### 日常开发

```bash
# 快速启动全栈环境
./scripts/dev_master.sh fullstack

# 启动并查看详细日志
./scripts/dev_master.sh fullstack --verbose

# 仅开发前端，连接外部后端
./scripts/dev_master.sh frontend
```

### 调试开发

```bash
# 启用调试模式，查看详细信息
./scripts/dev_master.sh fullstack --debug --verbose

# 使用PostgreSQL进行数据库相关调试
./scripts/dev_master.sh fullstack --postgres --debug

# 启用认证进行认证相关调试
./scripts/dev_master.sh fullstack --auth --debug
```

### 构建部署

```bash
# 开发构建
./scripts/dev_master.sh build

# 生产构建
./scripts/dev_master.sh build --env production --build-mode production

# 查看构建过程详情
./scripts/dev_master.sh build --verbose
```

### 服务管理

```bash
# 停止所有服务
./scripts/dev_master.sh stop

# 强制清理所有相关进程
./scripts/dev_master.sh stop --force-clean
```

## 向后兼容性

### 原脚本映射

| 原脚本 | 新命令 |
|--------|--------|
| `dev_full_stack.sh` | `./scripts/dev_master.sh fullstack` |
| `dev_full_stack_verbose.sh` | `./scripts/dev_master.sh fullstack --verbose` |
| `dev_frontend.sh` | `./scripts/dev_master.sh frontend` |
| `dev_frontend_only.sh` | `./scripts/dev_master.sh frontend` |
| `dev_no_auth.sh` | `./scripts/dev_master.sh fullstack --no-auth` |
| `dev_start.sh` | `./scripts/dev_master.sh fullstack` |
| `dev_with_sqlite.sh` | `./scripts/dev_master.sh fullstack --sqlite` |
| `build_frontend.sh` | `./scripts/dev_master.sh build` |
| `stop_dev_servers.sh` | `./scripts/dev_master.sh stop` |

### 迁移工具

使用迁移工具创建向后兼容包装：

```bash
# 运行迁移工具
chmod +x scripts/migrate_to_master.sh
./scripts/migrate_to_master.sh
```

## 故障排除

### 常见问题

**端口被占用**
```bash
# 查看端口占用情况
./scripts/dev_master.sh fullstack --dry-run

# 强制清理端口
./scripts/dev_master.sh fullstack --force-clean
```

**依赖问题**
```bash
# 跳过依赖检查
./scripts/dev_master.sh fullstack --no-install

# 手动安装依赖
go mod tidy
cd web/frontend && npm install
```

**服务启动失败**
```bash
# 查看详细错误信息
./scripts/dev_master.sh fullstack --debug --verbose

# 检查日志文件
tail -f /tmp/backend.log
tail -f /tmp/frontend.log
```

### 获取帮助

```bash
# 查看完整帮助
./scripts/dev_master.sh --help

# 查看版本信息
./scripts/dev_master.sh --version

# 查看兼容性信息
./scripts/dev_master.sh --compatibility
```

## 最佳实践

### 开发环境推荐配置

```bash
# 日常开发（快速启动）
./scripts/dev_master.sh fullstack

# 调试开发（详细信息）
./scripts/dev_master.sh fullstack --debug --verbose

# 前端专项开发
./scripts/dev_master.sh frontend --debug
```

### 团队协作建议

1. **统一使用新脚本**：建议团队成员都使用新的主控脚本
2. **文档更新**：更新项目README和开发指南
3. **配置标准化**：制定团队统一的启动参数标准
4. **故障排除**：建立常见问题的解决方案文档

### 性能优化建议

1. **跳过不必要的检查**：使用 `--no-install` 跳过依赖检查
2. **使用静默模式**：在CI/CD中使用 `--quiet` 减少输出
3. **合理设置超时**：根据机器性能调整 `--timeout` 参数

这个智能主控脚本大大简化了开发环境的管理，提供了更好的开发体验和更强的功能。建议逐步迁移到新脚本以获得最佳的开发效率。
