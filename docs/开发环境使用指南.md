# AI文本游戏开发环境使用指南

## 📋 概述

本文档提供了AI文本游戏项目开发环境的完整使用指南，包括所有启动脚本的详细说明和使用方法。

## 🛠️ 环境要求

### 基础环境
- **Go**: 1.19+ 
- **Node.js**: 16+
- **npm**: 最新版本

### 数据库支持
- **SQLite**: 开发环境推荐（无需额外安装）
- **PostgreSQL**: 生产环境推荐（可选）

## 🚀 快速开始

### 1. 最简单的启动方式（推荐新手）

```bash
# 使用SQLite数据库的开发环境
./scripts/dev_with_sqlite.sh
```

**特点**：
- ✅ 无需安装PostgreSQL
- ✅ 自动初始化SQLite数据库
- ✅ 完全跳过身份认证
- ✅ 一键启动，开箱即用

### 2. 全栈开发环境

```bash
# 同时启动前端和后端
./scripts/dev_full_stack.sh
```

**特点**：
- ✅ 前后端同时启动
- ✅ 支持热重载
- ✅ 完整的开发工具
- ✅ 实时日志显示

## 📚 启动脚本详细说明

### 🔧 核心开发脚本

#### 1. `dev_with_sqlite.sh` - SQLite开发环境（推荐）

**用途**：使用SQLite数据库的轻量级开发环境

**运行前准备**：
- 确保Go环境已安装
- 项目依赖会自动安装

**执行命令**：
```bash
./scripts/dev_with_sqlite.sh
```

**可选参数**：
```bash
./scripts/dev_with_sqlite.sh --init-only    # 仅初始化数据库
./scripts/dev_with_sqlite.sh --clean        # 清理数据库文件
./scripts/dev_with_sqlite.sh --status       # 查看配置状态
./scripts/dev_with_sqlite.sh --restore      # 恢复配置备份
```

**预期结果**：
- 🔗 数据库连接：SQLite (data/dev.db)
- 🌐 服务地址：http://localhost:8080
- ❤️ 健康检查：http://localhost:8080/health
- 🔓 认证状态：已跳过

**特殊功能**：
- 自动备份和恢复.env配置文件
- 智能合并用户自定义配置
- 支持配置状态查看和管理

#### 2. `dev_full_stack.sh` - 全栈开发环境

**用途**：同时启动前端和后端开发服务器

**运行前准备**：
- 确保Go和Node.js环境已安装
- 前端依赖会自动安装

**执行命令**：
```bash
./scripts/dev_full_stack.sh
```

**可选参数**：
```bash
./scripts/dev_full_stack.sh --backend-only   # 仅启动后端
./scripts/dev_full_stack.sh --frontend-only  # 仅启动前端
```

**预期结果**：
- 🎨 前端服务：http://localhost:3000
- ⚡ 后端服务：http://localhost:8080
- 🔥 热重载：代码变更自动刷新
- 🐛 调试工具：完整的开发工具和日志

#### 3. `dev_no_auth.sh` - 无认证开发环境

**用途**：完全跳过身份认证的开发环境

**运行前准备**：
- 确保Go环境已安装
- 会自动加载.env.development配置

**执行命令**：
```bash
./scripts/dev_no_auth.sh
```

**可选参数**：
```bash
./scripts/dev_no_auth.sh --simple    # 使用简化版服务器（推荐）
./scripts/dev_no_auth.sh --full      # 使用完整服务器（开发模式）
```

**预期结果**：
- 🔓 身份认证：完全跳过
- 👤 默认用户：<EMAIL>
- 🤖 AI服务：Mock模式
- 🌍 CORS策略：允许所有来源

#### 4. `dev_start.sh` - 标准开发环境

**用途**：标准的前后端开发环境启动

**运行前准备**：
- 确保Go和Node.js环境已安装
- 需要配置数据库连接

**执行命令**：
```bash
./scripts/dev_start.sh
```

**可选参数**：
```bash
./scripts/dev_start.sh --backend-only     # 仅启动后端
./scripts/dev_start.sh --frontend-only    # 仅启动前端
./scripts/dev_start.sh --no-install       # 跳过依赖安装
```

**预期结果**：
- 🌐 前端服务：http://localhost:3000
- 🌐 后端服务：http://localhost:8080
- 📊 进程监控：自动监控服务状态
- 🔄 自动重启：服务异常时自动处理

### 🧪 测试和工具脚本

#### 5. `run_tests.sh` - 测试运行器

**用途**：运行单元测试和集成测试

**运行前准备**：
- 确保Go环境已安装
- 测试数据库配置（可选）

**执行命令**：
```bash
./scripts/run_tests.sh
```

**可选参数**：
```bash
./scripts/run_tests.sh --unit          # 仅运行单元测试
./scripts/run_tests.sh --integration   # 仅运行集成测试
./scripts/run_tests.sh --benchmark     # 仅运行基准测试
./scripts/run_tests.sh --fmt           # 仅运行代码格式检查
./scripts/run_tests.sh --vet           # 仅运行静态分析
./scripts/run_tests.sh --skip-db       # 跳过数据库相关测试
./scripts/run_tests.sh --cleanup       # 清理测试文件
```

**预期结果**：
- ✅ 代码格式检查通过
- ✅ 静态分析通过
- ✅ 单元测试通过
- 📊 测试覆盖率报告：coverage.html

#### 6. `build_frontend.sh` - 前端构建

**用途**：构建前端生产版本

**运行前准备**：
- 确保Node.js环境已安装
- 前端依赖已安装

**执行命令**：
```bash
./scripts/build_frontend.sh
```

**预期结果**：
- 📦 构建产物：web/frontend/dist/
- 🗜️ 代码压缩和优化
- 📊 构建统计信息

### 🔧 辅助工具脚本

#### 7. `stop_dev_servers.sh` - 停止开发服务

**用途**：停止所有开发服务器进程

**执行命令**：
```bash
./scripts/stop_dev_servers.sh
```

#### 8. `env_manager.sh` - 环境配置管理

**用途**：管理环境配置文件

**执行命令**：
```bash
./scripts/env_manager.sh [操作] [参数]
```

## 🎯 使用场景推荐

### 新手开发者
```bash
# 推荐使用SQLite环境，简单快速
./scripts/dev_with_sqlite.sh
```

### 前端开发者
```bash
# 全栈环境，支持前端热重载
./scripts/dev_full_stack.sh
```

### 后端开发者
```bash
# 无认证环境，专注后端API开发
./scripts/dev_no_auth.sh --simple
```

### 测试开发者
```bash
# 运行完整测试套件
./scripts/run_tests.sh
```

### 生产部署准备
```bash
# 构建前端生产版本
./scripts/build_frontend.sh

# 运行完整测试
./scripts/run_tests.sh
```

## ⚠️ 注意事项

1. **安全警告**：开发环境脚本会跳过身份认证，仅用于开发测试，请勿在生产环境使用
2. **端口占用**：脚本会自动检查和清理端口占用，如有重要进程请提前备份
3. **配置备份**：SQLite脚本会自动备份.env文件，可通过--restore参数恢复
4. **数据库文件**：SQLite数据库文件位于data/dev.db，可通过--clean参数清理

## 🆘 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8080
   lsof -i :3000
   
   # 停止所有开发服务
   ./scripts/stop_dev_servers.sh
   ```

2. **数据库连接失败**
   ```bash
   # 重新初始化SQLite数据库
   ./scripts/dev_with_sqlite.sh --clean
   ./scripts/dev_with_sqlite.sh --init-only
   ```

3. **依赖安装失败**
   ```bash
   # 手动安装Go依赖
   go mod download
   go mod tidy
   
   # 手动安装前端依赖
   cd web/frontend && npm install
   ```

4. **配置文件问题**
   ```bash
   # 查看配置状态
   ./scripts/dev_with_sqlite.sh --status
   
   # 恢复配置备份
   ./scripts/dev_with_sqlite.sh --restore
   ```

## 📞 技术支持

如遇到问题，请检查：
1. 环境要求是否满足
2. 端口是否被占用
3. 配置文件是否正确
4. 日志输出中的错误信息

更多技术细节请参考项目的其他文档文件。
