# 数据库初始化和迁移功能说明

## 概述

本文档说明了 `cmd/simple-server/main.go` 中实现的数据库配置读取、连接初始化和自动迁移功能。

## 功能特性

### 1. 配置文件读取

- **配置源**: 从 `.env` 文件读取数据库配置参数
- **配置统一**: 使用与主服务器相同的配置加载机制
- **环境变量支持**: 支持通过环境变量覆盖配置文件设置

#### 支持的数据库配置参数

```bash
# 数据库基本配置
DB_HOST=localhost          # 数据库主机地址
DB_PORT=5432              # 数据库端口
DB_USER=postgres          # 数据库用户名
DB_PASSWORD=              # 数据库密码
DB_NAME=dev.db            # 数据库名称（.db结尾自动使用SQLite）
DB_SSL_MODE=development   # SSL模式

# 连接池配置
DB_MAX_OPEN_CONNS=25      # 最大打开连接数
DB_MAX_IDLE_CONNS=5       # 最大空闲连接数
DB_MAX_LIFETIME=5m        # 连接最大生命周期

# 数据库类型和迁移配置
DB_TYPE=sqlite            # 数据库类型
DB_AUTO_MIGRATE=true      # 是否自动执行迁移
```

### 2. 数据库连接初始化

#### 智能数据库类型检测
- **SQLite**: 当 `DB_NAME` 以 `.db` 结尾时自动使用 SQLite 驱动
- **PostgreSQL**: 其他情况使用 PostgreSQL 驱动

#### 连接池配置
- 自动设置最大打开连接数、空闲连接数和连接生命周期
- 支持连接健康检查和状态监控

#### 错误处理
- 完整的错误处理和日志记录
- 连接测试确保数据库可用性

### 3. 自动数据库迁移

#### 智能迁移器
- 使用 `SmartMigrator` 根据数据库类型选择合适的迁移策略
- 支持 SQLite 和 PostgreSQL 的兼容性处理

#### 数据模型迁移
自动创建和更新以下数据表：

1. **用户相关表**
   - `users`: 用户基本信息
   - `user_stats`: 用户统计数据

2. **游戏世界表**
   - `worlds`: 游戏世界定义
   - `scenes`: 场景信息

3. **角色和实体表**
   - `characters`: 游戏角色
   - `entities`: 游戏实体

4. **事件和交互表**
   - `events`: 游戏事件
   - `ai_interactions`: AI交互记录

#### 兼容性处理
- 自动处理不同数据库间的语法差异
- JSON/JSONB 类型的智能转换
- UUID 类型的兼容性处理

## 代码结构

### 主要函数

#### `initConfigFromEnv()`
```go
// 从.env文件初始化配置
// 使用统一的配置加载机制，确保与主服务器配置一致
func initConfigFromEnv() (*config.Config, error)
```

#### `initDatabase()`
```go
// 初始化数据库连接
// 根据配置参数建立数据库连接并设置连接池
func initDatabase(cfg *config.Config) error
```

#### `performDatabaseMigration()`
```go
// 执行数据库迁移
// 自动创建和更新数据库表结构
func performDatabaseMigration(cfg *config.Config) error
```

### 启动流程

1. **配置加载**: 从 `.env` 文件读取配置
2. **数据库连接**: 建立数据库连接并配置连接池
3. **数据库迁移**: 自动创建/更新表结构
4. **业务逻辑初始化**: 初始化AI处理器等业务组件
5. **服务器启动**: 启动HTTP服务器

## 使用方法

### 1. 配置数据库

编辑 `.env` 文件，设置数据库连接参数：

```bash
# SQLite配置（开发环境推荐）
DB_NAME=dev.db
DB_TYPE=sqlite
DB_AUTO_MIGRATE=true

# PostgreSQL配置（生产环境）
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=ai_text_game
DB_TYPE=postgres
```

### 2. 启动服务器

```bash
cd cmd/simple-server
go run main.go
```

### 3. 测试数据库初始化

运行测试脚本验证数据库功能：

```bash
go run test_database_init.go
```

## 日志输出

系统会输出详细的中文日志，包括：

- 配置加载状态
- 数据库连接信息
- 迁移执行进度
- 错误和警告信息

示例日志输出：
```
🚀 启动简化版AI文本游戏服务器
📋 开始从.env文件加载配置
🔗 开始初始化数据库连接
✅ 数据库连接成功
🚀 开始执行数据库迁移
✅ 数据库迁移完成
```

## 错误处理

### 常见错误及解决方案

1. **配置文件不存在**
   - 确保 `.env` 文件存在于项目根目录
   - 检查文件权限

2. **数据库连接失败**
   - 验证数据库服务是否运行
   - 检查连接参数是否正确

3. **迁移失败**
   - 检查数据库权限
   - 查看详细错误日志

## 性能优化

- 连接池配置优化
- 批量迁移处理
- 智能重试机制
- 资源自动清理

## 安全考虑

- 敏感信息不记录到日志
- 连接参数验证
- SQL注入防护
- 权限最小化原则
