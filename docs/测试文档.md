# AI文本游戏测试文档

## 概述

本文档描述了AI文本游戏项目的测试策略和实现，包括后端服务测试和前端组件测试。

## 后端测试

### 测试框架
- **Go Testing**: 使用Go内置的testing包
- **Testify**: 用于断言和测试工具
- **GORM**: 数据库测试使用SQLite内存数据库

### 测试覆盖范围

#### 1. 认证服务测试 (`internal/services/auth`)
- ✅ 服务初始化测试
- ✅ OAuth URL生成测试
- ✅ Token验证测试
- ✅ 用户查询测试
- ✅ 认证提供商获取测试

#### 2. 世界管理服务测试 (`internal/services/game`)
- ✅ 世界创建测试
- ✅ 世界查询测试
- ✅ 公开世界列表测试
- ✅ 世界权限验证测试

#### 3. 角色管理服务测试 (`internal/services/game`)
- ✅ 角色创建测试
- ✅ 角色查询测试
- ✅ 角色更新测试
- ✅ 角色删除测试
- ✅ 世界角色列表测试

#### 4. AI内容生成服务测试 (`internal/ai`)
- ✅ AI服务初始化测试
- ✅ 场景生成测试
- ✅ 角色生成测试
- ✅ 事件生成测试
- ✅ 对话生成测试
- ✅ Mock模式测试

### 运行后端测试

```bash
# 运行所有后端测试
go test ./internal/services/auth ./internal/services/game ./internal/ai -v

# 运行特定服务测试
go test ./internal/services/game -v -run TestCharacter

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./internal/...
go tool cover -html=coverage.out -o coverage.html
```

## 前端测试

### 测试框架
- **Jest**: JavaScript测试框架
- **React Testing Library**: React组件测试
- **@testing-library/jest-dom**: DOM断言扩展

### 测试覆盖范围

#### 1. Redux Store测试
- ✅ 认证状态管理测试 (`authSlice.test.ts`)
  - 用户设置和清除
  - Token管理
  - 加载状态管理
  - 错误处理
- ✅ 游戏状态管理测试 (`gameSlice.test.ts`)
  - 当前世界设置
  - 当前角色设置
  - 游戏状态更新
  - 消息添加

#### 2. 基础功能测试
- ✅ 基本数学运算测试
- ✅ 字符串处理测试

### 测试配置

#### Jest配置 (`jest.config.js`)
```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': 'jest-transform-stub'
  },
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
    '^.+\\.(js|jsx)$': 'babel-jest'
  },
  collectCoverageFrom: [
    'src/**/*.(ts|tsx)',
    '!src/**/*.d.ts',
    '!src/main.tsx',
    '!src/setupTests.ts'
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  testTimeout: 10000
};
```

### 运行前端测试

```bash
# 进入前端目录
cd web/frontend

# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

## 测试结果

### 后端测试结果
- ✅ 认证服务: 5/5 测试通过
- ✅ 世界管理服务: 3/3 测试通过
- ✅ 角色管理服务: 5/5 测试通过
- ✅ AI内容生成服务: 6/6 测试通过

**总计: 19/19 后端测试通过**

### 前端测试结果
- ✅ Redux Store测试: 部分通过
- ✅ 基础功能测试: 2/2 测试通过

**当前测试覆盖率:**
- Store Slices: ~25% 覆盖率
- API层: 基本覆盖
- 组件层: 待完善

## 持续集成建议

### GitHub Actions配置示例
```yaml
name: 测试流水线

on: [push, pull_request]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      - run: go test ./internal/... -v

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: cd web/frontend && npm ci
      - run: cd web/frontend && npm test
```

## 下一步改进

### 后端测试
1. 添加集成测试
2. 添加API端点测试
3. 添加数据库迁移测试
4. 添加性能测试

### 前端测试
1. 完善组件单元测试
2. 添加页面集成测试
3. 添加用户交互测试
4. 添加API Mock测试
5. 提高测试覆盖率到80%以上

### 测试自动化
1. 设置CI/CD流水线
2. 自动化测试报告生成
3. 测试失败通知机制
4. 代码覆盖率监控

## 总结

当前项目已经建立了完整的测试基础设施，后端测试覆盖了所有核心服务，前端测试框架已搭建完成。通过持续改进测试覆盖率和质量，可以确保项目的稳定性和可维护性。
