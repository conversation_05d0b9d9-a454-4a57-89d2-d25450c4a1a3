# AI处理器测试说明

## 概述

本文档说明了AI处理器（AIHandler）的单元测试实现，主要测试场景生成功能的核心逻辑。

## 测试文件

- **文件位置**: `internal/handlers/ai_test.go`
- **测试包**: `handlers`

## 测试覆盖的功能

### 1. TestAIHandler_BuildScenePrompt

测试场景提示词构建功能，验证以下场景：

#### 测试用例

1. **完整参数构建提示词**
   - 测试所有参数都提供时的提示词构建
   - 验证场景名称、类型、主题、氛围、连接场景和特殊要求的正确组合
   - 期望输出包含所有参数信息的完整提示词

2. **使用旧格式提示词**
   - 测试向后兼容性
   - 当提供旧格式的`prompt`字段时，直接使用该提示词
   - 验证新旧格式的兼容性

3. **最小参数构建提示词**
   - 测试只提供场景名称时的提示词构建
   - 验证基础提示词的生成逻辑

#### 核心逻辑

```go
// buildScenePrompt 构建场景生成提示词
func (h *AIHandler) buildScenePrompt(req GenerateSceneRequest) string {
    // 如果提供了旧格式的prompt，直接使用
    if req.Prompt != "" {
        return req.Prompt
    }

    // 构建新格式的提示词
    prompt := "请生成一个游戏场景的详细描述。"
    
    // 根据各个参数动态构建提示词
    // ...
    
    return prompt
}
```

### 2. TestAIHandler_BuildSceneContext

测试场景上下文构建功能，验证以下场景：

#### 测试用例

1. **构建完整上下文**
   - 测试所有新格式参数的上下文构建
   - 验证所有字段都正确映射到上下文中
   - 确保上下文包含所有必要的键值对

2. **合并旧格式上下文**
   - 测试新旧格式上下文的合并逻辑
   - 验证旧格式的`context`字段与新格式参数的正确合并
   - 确保不会丢失任何上下文信息

#### 核心逻辑

```go
// buildSceneContext 构建场景生成上下文
func (h *AIHandler) buildSceneContext(req GenerateSceneRequest) map[string]interface{} {
    // 如果提供了旧格式的context，合并使用
    context := make(map[string]interface{})
    if req.Context != nil {
        for k, v := range req.Context {
            context[k] = v
        }
    }

    // 添加新格式的上下文信息
    // ...
    
    return context
}
```

## 运行测试

### 运行AI处理器测试

```bash
# 运行所有AI处理器测试
go test -run TestAIHandler ./internal/handlers/ -v

# 运行特定测试文件（避免其他测试文件的编译错误）
go test -run TestAIHandler ./internal/handlers/ai_test.go ./internal/handlers/ai.go ./internal/handlers/auth.go ./internal/handlers/validation.go -v
```

### 测试输出示例

```
=== RUN   TestAIHandler_BuildScenePrompt
=== RUN   TestAIHandler_BuildScenePrompt/完整参数构建提示词
=== RUN   TestAIHandler_BuildScenePrompt/使用旧格式提示词
=== RUN   TestAIHandler_BuildScenePrompt/最小参数构建提示词
--- PASS: TestAIHandler_BuildScenePrompt (0.00s)
    --- PASS: TestAIHandler_BuildScenePrompt/完整参数构建提示词 (0.00s)
    --- PASS: TestAIHandler_BuildScenePrompt/使用旧格式提示词 (0.00s)
    --- PASS: TestAIHandler_BuildScenePrompt/最小参数构建提示词 (0.00s)
=== RUN   TestAIHandler_BuildSceneContext
=== RUN   TestAIHandler_BuildSceneContext/构建完整上下文
=== RUN   TestAIHandler_BuildSceneContext/合并旧格式上下文
--- PASS: TestAIHandler_BuildSceneContext (0.00s)
    --- PASS: TestAIHandler_BuildSceneContext/构建完整上下文 (0.00s)
    --- PASS: TestAIHandler_BuildSceneContext/合并旧格式上下文 (0.00s)
PASS
```

## 测试设计原则

### 1. 单元测试隔离
- 每个测试函数专注于测试一个特定的方法
- 使用真实的AIHandler实例，但不依赖外部服务
- 测试辅助方法而不是完整的HTTP处理流程

### 2. 全面的场景覆盖
- 测试正常情况和边界情况
- 验证新旧格式的兼容性
- 确保所有参数组合都能正确处理

### 3. 清晰的测试结构
- 使用表驱动测试模式
- 每个测试用例都有清晰的名称和描述
- 测试断言具有详细的错误信息

## 注意事项

1. **依赖管理**: 测试只依赖必要的包，避免复杂的外部依赖
2. **类型安全**: 确保所有类型定义都正确，避免编译错误
3. **测试隔离**: 每个测试都是独立的，不依赖其他测试的状态
4. **中文注释**: 所有测试用例和断言都使用中文描述，便于理解

## 未来扩展

可以考虑添加以下测试：

1. **HTTP集成测试**: 测试完整的HTTP请求处理流程
2. **错误处理测试**: 测试各种错误情况的处理
3. **性能测试**: 测试大量请求的处理性能
4. **并发测试**: 测试并发请求的处理能力
