# Windmill结构化文本接口实现说明

## 概述

本项目已成功将AI文本生成功能从通用AI接口迁移到Windmill结构化文本接口。新的实现提供了更好的结构化数据支持和异步处理能力。

## 接口规范

### 提交任务接口
- **端点**: `POST /api/w/{workspace}/jobs/run/p/f/gemini/js_structured_output`
- **方法**: POST
- **认证**: Bearer Token

#### 请求参数
```json
{
  "model": "gemini-1.5-pro",                    // 模型名称，必填
  "prompt": "用户提示词",                        // 主提示词，必填
  "system_instruction": "系统指令",              // 系统指令，可选
  "responseSchema": {                           // 响应结构定义，必填
    "type": "object",
    "properties": {
      "name": {"type": "string", "description": "名称"},
      "description": {"type": "string", "description": "描述"}
    },
    "required": ["name", "description"]
  }
}
```

#### 响应
```json
"job-uuid-string"  // 返回任务UUID字符串
```

### 查询结果接口
- **端点**: `GET /api/w/{workspace}/jobs_u/completed/get_result_maybe/{job_id}`
- **方法**: GET
- **认证**: Bearer Token

#### 响应
```json
{
  "completed": true,                            // 是否完成
  "result": {                                   // 结果数据（结构化）
    "name": "生成的名称",
    "description": "生成的描述",
    // ... 其他根据responseSchema定义的字段
  },
  "error": ""                                   // 错误信息（如果有）
}
```

## 实现架构

### 核心组件

#### 1. WindmillClient
位置：`internal/ai/service.go`

负责与Windmill API的直接交互，包括：
- 任务提交
- 结果轮询
- 错误处理和重试机制

主要方法：
- `GenerateStructuredContent()`: 主入口方法
- `submitJob()`: 提交任务到Windmill
- `pollResult()`: 轮询获取任务结果

#### 2. Service
位置：`internal/ai/service.go`

AI服务的主要接口，集成了WindmillClient：
- `generateRealContent()`: 调用Windmill接口生成内容
- `buildResponseSchema()`: 构建不同内容类型的响应结构
- `getModelForType()`: 根据内容类型选择合适的模型
- `extractContentFromResult()`: 从结构化结果中提取文本内容

### 配置管理

#### 环境变量
```bash
# Windmill基础配置
AI_BASE_URL=https://wm.atjog.com
AI_TOKEN=your_windmill_api_token
AI_TIMEOUT=30s
AI_MAX_RETRIES=3
AI_RETRY_DELAY=1s

# Windmill专用配置
WINDMILL_WORKSPACE=my-workspace
WINDMILL_DEFAULT_MODEL=gemini-1.5-pro
```

#### 配置结构
```go
type AIConfig struct {
    BaseURL      string        `json:"base_url"`
    Token        string        `json:"token"`
    Timeout      time.Duration `json:"timeout"`
    MaxRetries   int           `json:"max_retries"`
    RetryDelay   time.Duration `json:"retry_delay"`
    // ... 其他配置
    
    // Windmill专用配置
    Windmill WindmillConfig `json:"windmill"`
}

type WindmillConfig struct {
    Workspace    string `json:"workspace"`     // 工作空间名称
    DefaultModel string `json:"default_model"` // 默认模型
}
```

## 异步处理流程

1. **任务提交**: 客户端调用`GenerateStructuredContent()`
2. **API调用**: 向Windmill提交任务，获取任务UUID
3. **轮询等待**: 定期查询任务状态，直到完成或超时
4. **结果返回**: 返回结构化数据给调用方

### 轮询参数
- 轮询间隔：1秒
- 最大轮询时间：5分钟
- 超时处理：返回超时错误

## 结构化数据支持

### 场景生成
```json
{
  "name": "场景名称",
  "description": "详细场景描述",
  "atmosphere": "场景氛围",
  "key_features": ["特征1", "特征2"],
  "possible_actions": ["行动1", "行动2"],
  "connections": [
    {
      "direction": "北",
      "description": "连接描述",
      "scene_name": "连接场景名"
    }
  ]
}
```

### 角色生成
```json
{
  "name": "角色名称",
  "description": "角色外观描述",
  "personality": ["性格特征1", "性格特征2"],
  "background": "背景故事",
  "skills": ["技能1", "技能2"],
  "dialogue_style": "对话风格",
  "motivations": ["动机1", "动机2"]
}
```

### 事件生成
```json
{
  "name": "事件名称",
  "description": "事件描述",
  "type": "事件类型",
  "priority": 5,
  "duration": 30,
  "effects": {
    "description": "效果描述",
    "consequences": ["后果1", "后果2"]
  }
}
```

## 前端适配

### API超时配置
前端API调用已更新为支持更长的超时时间（2分钟），以适应Windmill接口的异步特性。

### 用户体验优化
- 更新加载提示信息，告知用户需要耐心等待
- 增强错误处理，提供更详细的错误信息
- 支持超时和网络错误的友好提示

## 测试覆盖

### 单元测试
- WindmillClient创建和配置测试
- 响应结构构建测试
- 内容提取和token估算测试
- 集成测试（Mock模式）

### 测试运行
```bash
go test ./internal/ai -v
```

## 错误处理

### 常见错误类型
1. **认证错误** (401): Token无效或过期
2. **频率限制** (429): 请求过于频繁
3. **服务错误** (5xx): Windmill服务不可用
4. **超时错误**: 轮询超时或网络超时
5. **格式错误**: 响应格式不符合预期

### 重试机制
- 最大重试次数：3次
- 重试延迟：递增延迟（1s, 2s, 3s）
- 重试条件：5xx错误和网络错误

## 监控和日志

### 关键日志点
- 任务提交成功/失败
- 轮询进度和结果
- 错误和重试情况
- 性能指标（响应时间、token使用量）

### 日志级别
- INFO: 正常操作流程
- WARN: 重试和恢复操作
- ERROR: 失败和异常情况
- DEBUG: 详细的请求/响应数据

## 迁移说明

### 已完成的更改
1. ✅ 实现WindmillClient和相关方法
2. ✅ 更新AI服务以使用新接口
3. ✅ 配置管理更新
4. ✅ 前端超时和错误处理优化
5. ✅ 单元测试覆盖
6. ✅ 文档和注释更新

### 兼容性
- 保持了原有的`GenerateContent()`接口不变
- 所有现有的AI生成功能自动使用新的Windmill接口
- Mock模式仍然可用于测试环境

## 性能考虑

### 优化建议
1. 合理设置轮询间隔，避免过于频繁的查询
2. 监控任务完成时间，调整超时设置
3. 考虑实现任务队列，避免并发限制
4. 缓存常用的响应结构定义

### 资源使用
- 内存：轮询期间保持连接状态
- 网络：定期HTTP请求开销
- CPU：JSON序列化/反序列化处理
