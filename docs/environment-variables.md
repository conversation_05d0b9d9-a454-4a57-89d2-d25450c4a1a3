# 环境变量配置指南

## 概述

本项目支持多种方式配置环境变量，包括带前缀的环境变量，以避免在多项目环境中的配置冲突。

## 优先级顺序

配置加载的优先级从高到低：

1. **带 `AI_TEXT_GAME_` 前缀的环境变量** (最高优先级)
2. **带 `AITEXTGAME_` 前缀的环境变量** (中等优先级)
3. **普通环境变量** (低优先级)
4. **`.env` 文件中的配置** (更低优先级)
5. **代码中的默认值** (最低优先级)

## 支持的环境变量

### 核心配置

| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `AI_TOKEN` | AI服务访问令牌 | - | `your_ai_token_here` |
| `AI_BASE_URL` | AI服务基础URL | `https://wm.atjog.com` | `https://api.example.com` |
| `SERVER_PORT` | 服务器端口 | `8080` | `3000` |
| `ENVIRONMENT` | 运行环境 | `development` | `production` |

### 数据库配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DB_HOST` | 数据库主机 | `localhost` |
| `DB_PORT` | 数据库端口 | `5432` |
| `DB_USER` | 数据库用户名 | `postgres` |
| `DB_PASSWORD` | 数据库密码 | - |
| `DB_NAME` | 数据库名称 | `ai_text_game` |
| `DB_SSL_MODE` | SSL模式 | `disable` |

### 认证配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `JWT_SECRET` | JWT签名密钥 | `your-secret-key` |
| `JWT_EXPIRATION` | JWT过期时间 | `24h` |
| `OAUTH_GOOGLE_CLIENT_ID` | Google OAuth客户端ID | - |
| `OAUTH_GOOGLE_CLIENT_SECRET` | Google OAuth客户端密钥 | - |
| `OAUTH_GITHUB_CLIENT_ID` | GitHub OAuth客户端ID | - |
| `OAUTH_GITHUB_CLIENT_SECRET` | GitHub OAuth客户端密钥 | - |

## 使用方式

### 1. 开发环境

**使用 `.env` 文件**：
```bash
# .env
AI_TOKEN=dev_token
AI_BASE_URL=https://dev-api.example.com
DB_NAME=ai_text_game_dev
```

### 2. 测试环境

**使用普通环境变量**：
```bash
export AI_TOKEN=test_token
export DB_NAME=ai_text_game_test
go run cmd/server/main.go
```

### 3. 生产环境

**使用项目前缀避免冲突**：
```bash
export AI_TEXT_GAME_AI_TOKEN=prod_token_secure
export AI_TEXT_GAME_DB_NAME=ai_text_game_prod
export AI_TEXT_GAME_DB_PASSWORD=secure_password
./server
```

### 4. Docker 容器

**使用环境变量**：
```bash
docker run \
  -e AI_TEXT_GAME_AI_TOKEN=container_token \
  -e AI_TEXT_GAME_DB_HOST=db.example.com \
  -e AI_TEXT_GAME_DB_PASSWORD=secure_password \
  my-app:latest
```

**使用 docker-compose.yml**：
```yaml
version: '3.8'
services:
  app:
    image: my-app:latest
    environment:
      - AI_TEXT_GAME_AI_TOKEN=container_token
      - AI_TEXT_GAME_DB_HOST=postgres
      - AI_TEXT_GAME_DB_PASSWORD=secure_password
```

### 5. CI/CD 管道

**GitHub Actions**：
```yaml
- name: Run tests
  env:
    AI_TEXT_GAME_AI_TOKEN: ${{ secrets.AI_TOKEN }}
    AI_TEXT_GAME_DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
  run: go test ./...
```

**GitLab CI**：
```yaml
test:
  variables:
    AI_TEXT_GAME_AI_TOKEN: $AI_TOKEN
    AI_TEXT_GAME_DB_PASSWORD: $DB_PASSWORD
  script:
    - go test ./...
```

## 实用工具

### 环境变量管理脚本

项目提供了 `scripts/env_manager.sh` 工具来管理环境变量：

```bash
# 查看当前环境变量状态
./scripts/env_manager.sh status

# 设置普通环境变量
./scripts/env_manager.sh set AI_TOKEN your_token_here

# 设置带前缀的环境变量
./scripts/env_manager.sh set AI_TOKEN your_token_here AI_TEXT_GAME_

# 查看使用示例
./scripts/env_manager.sh examples
```

### 测试脚本

```bash
# 测试环境变量覆盖机制
./scripts/test_env_override.sh

# 测试带前缀的环境变量
./scripts/test_prefixed_env.sh
```

## 最佳实践

### 1. 环境隔离

- **开发环境**: 使用 `.env` 文件
- **测试环境**: 使用普通环境变量
- **生产环境**: 使用带前缀的环境变量

### 2. 安全考虑

- 敏感信息（token、密码）不要提交到版本控制
- 生产环境使用强密码和安全的token
- 定期轮换敏感配置

### 3. 多项目环境

当同一台机器上运行多个项目时，使用项目前缀避免冲突：

```bash
# 项目A
export AI_TEXT_GAME_AI_TOKEN=project_a_token
export AI_TEXT_GAME_DB_NAME=project_a_db

# 项目B
export OTHER_PROJECT_AI_TOKEN=project_b_token
export OTHER_PROJECT_DB_NAME=project_b_db
```

### 4. 容器化部署

在容器环境中，推荐使用环境变量而不是配置文件：

```dockerfile
# Dockerfile
ENV AI_TEXT_GAME_ENVIRONMENT=production
ENV AI_TEXT_GAME_SERVER_PORT=8080
```

## 故障排除

### 1. 检查配置优先级

使用环境变量管理工具查看当前生效的配置：

```bash
./scripts/env_manager.sh status
```

### 2. 验证环境变量

```bash
# 检查特定环境变量
echo $AI_TEXT_GAME_AI_TOKEN

# 检查所有相关环境变量
env | grep -E "(AI_TEXT_GAME_|AITEXTGAME_|AI_TOKEN|DB_)"
```

### 3. 测试配置加载

```bash
# 创建测试程序验证配置
go run -c 'package main
import (
    "fmt"
    "ai-text-game-iam-npc/internal/config"
)
func main() {
    cfg, _ := config.Load()
    fmt.Printf("AI_TOKEN: %s\n", cfg.AI.Token)
    fmt.Printf("DB_NAME: %s\n", cfg.Database.DBName)
}'
```

## 示例配置

### 开发环境 (.env)

```bash
# 服务器配置
SERVER_PORT=8080
ENVIRONMENT=development

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=dev_password
DB_NAME=ai_text_game_dev

# AI服务配置
AI_BASE_URL=https://dev-api.example.com
AI_TOKEN=dev_token_12345
AI_MOCK_ENABLED=true

# 认证配置
JWT_SECRET=dev-secret-key
SKIP_AUTH=true
```

### 生产环境 (环境变量)

```bash
export AI_TEXT_GAME_ENVIRONMENT=production
export AI_TEXT_GAME_SERVER_PORT=8080

export AI_TEXT_GAME_DB_HOST=prod-db.example.com
export AI_TEXT_GAME_DB_PASSWORD=secure_prod_password
export AI_TEXT_GAME_DB_NAME=ai_text_game_prod

export AI_TEXT_GAME_AI_TOKEN=prod_token_secure_12345
export AI_TEXT_GAME_AI_BASE_URL=https://api.example.com

export AI_TEXT_GAME_JWT_SECRET=super-secure-jwt-secret
```

通过这种灵活的环境变量配置系统，你可以轻松地在不同环境中管理配置，避免配置冲突，并确保敏感信息的安全。
