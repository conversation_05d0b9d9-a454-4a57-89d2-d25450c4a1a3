# AI内容生成API文档

## 概述

AI内容生成API提供了强大的AI驱动内容生成功能，包括场景描述、角色创建、事件生成、对话生成等。支持Mock模式和真实AI API调用两种模式。所有API都需要Bearer Token认证。

## API端点

### 基础URL
```
/api/v1/ai
```

## 配置说明

### 环境变量配置
```bash
# AI服务配置
AI_BASE_URL=https://wm.atjog.com          # AI API基础URL
AI_TOKEN=your-ai-api-token                # AI API访问令牌
AI_TIMEOUT=30s                            # 请求超时时间
AI_MAX_RETRIES=3                          # 最大重试次数
AI_RETRY_DELAY=1s                         # 重试延迟
AI_QUEUE_SIZE=1000                        # 队列大小
AI_BATCH_SIZE=10                          # 批处理大小
AI_BATCH_TIMEOUT=5s                       # 批处理超时
AI_MOCK_ENABLED=true                      # 是否启用Mock模式（开发/测试用）
```

### Mock模式 vs 真实API模式
- **Mock模式**: 用于开发和测试，返回预定义的示例内容
- **真实API模式**: 调用实际的AI服务，生成真实的内容

## 通用内容生成

### 1. 生成内容
**POST** `/api/v1/ai/generate`

通用的AI内容生成接口，支持多种内容类型。

**请求头:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体:**
```json
{
  "type": "description",
  "prompt": "生成一个神秘森林的描述",
  "context": {
    "location": "森林",
    "mood": "神秘",
    "time": "黄昏"
  },
  "schema": {
    "description": "string",
    "mood": "string",
    "atmosphere": "string"
  },
  "max_tokens": 500,
  "temperature": 0.7,
  "world_id": "uuid",
  "user_id": "uuid"
}
```

**字段说明:**
- `type`: 生成类型 (scene, character, event, dialogue, description)
- `prompt`: 生成提示词
- `context`: 上下文信息，帮助AI理解生成需求
- `schema`: 期望的响应结构定义
- `max_tokens`: 最大token数 (默认: 500)
- `temperature`: 创造性参数 0.0-1.0 (默认: 0.7)
- `world_id`: 关联的世界ID (可选)
- `user_id`: 用户ID (自动从认证信息获取)

**响应:**
```json
{
  "success": true,
  "message": "生成内容成功",
  "data": {
    "content": "在这片古老的森林深处，高大的橡树和松树交织成一片绿色的天幕...",
    "structured_data": {
      "description": "在这片古老的森林深处...",
      "mood": "神秘",
      "atmosphere": "幽静而充满魔力"
    },
    "token_usage": 150,
    "response_time": 1200
  }
}
```

## 专用内容生成

### 1. 生成场景
**POST** `/api/v1/ai/generate/scene`

专门用于生成游戏场景的接口。

**请求体:**
```json
{
  "prompt": "创建一个古老的图书馆场景",
  "context": {
    "theme": "魔法",
    "size": "large",
    "atmosphere": "神秘"
  },
  "world_id": "uuid"
}
```

**响应:**
```json
{
  "success": true,
  "message": "生成场景成功",
  "data": {
    "content": "这是一座宏伟的古老图书馆...",
    "structured_data": {
      "name": "古老的魔法图书馆",
      "description": "这是一座宏伟的古老图书馆...",
      "type": "library",
      "atmosphere": "神秘而庄严",
      "connections": {
        "north": "阅读室",
        "east": "魔法实验室"
      },
      "entities": [
        {
          "name": "古老的书架",
          "type": "furniture",
          "description": "高耸入云的书架，装满了古老的魔法书籍"
        }
      ]
    },
    "token_usage": 200,
    "response_time": 1500
  }
}
```

### 2. 生成角色
**POST** `/api/v1/ai/generate/character`

专门用于生成游戏角色的接口。

**请求体:**
```json
{
  "prompt": "创建一个智慧的老法师角色",
  "context": {
    "age": "old",
    "class": "wizard",
    "gender": "male",
    "personality": "wise"
  },
  "world_id": "uuid"
}
```

**响应:**
```json
{
  "success": true,
  "message": "生成角色成功",
  "data": {
    "content": "一位年迈的法师，有着银白的长须和深邃的眼睛...",
    "structured_data": {
      "name": "阿尔卡纳大师",
      "description": "一位年迈的法师，有着银白的长须和深邃的眼睛...",
      "type": "npc",
      "personality": ["智慧", "耐心", "神秘"],
      "skills": ["高级魔法", "古代知识", "炼金术"],
      "background": "曾是王室的首席法师，现在隐居在这座图书馆中研究古老的魔法"
    },
    "token_usage": 180,
    "response_time": 1300
  }
}
```

### 3. 生成事件
**POST** `/api/v1/ai/generate/event`

专门用于生成游戏事件的接口。

**请求体:**
```json
{
  "prompt": "创建一个神秘的魔法事件",
  "context": {
    "type": "magical",
    "severity": "medium",
    "location": "图书馆"
  },
  "world_id": "uuid"
}
```

**响应:**
```json
{
  "success": true,
  "message": "生成事件成功",
  "data": {
    "content": "突然，图书馆中的一本古老魔法书开始发出微弱的蓝光...",
    "structured_data": {
      "name": "古书觉醒",
      "description": "突然，图书馆中的一本古老魔法书开始发出微弱的蓝光...",
      "type": "magical_discovery",
      "priority": 5,
      "duration": 3600,
      "effects": {
        "knowledge_gain": 10,
        "magic_power": 5,
        "curiosity": 15
      }
    },
    "token_usage": 120,
    "response_time": 1000
  }
}
```

## AI交互管理

### 1. 获取交互历史
**GET** `/api/v1/ai/history`

获取用户的AI交互历史记录。

**查询参数:**
- `world_id`: 世界ID (可选，筛选特定世界的交互)
- `limit`: 返回数量限制 (默认: 50, 最大: 100)

**响应:**
```json
{
  "success": true,
  "message": "获取交互历史成功",
  "data": [
    {
      "id": "uuid",
      "world_id": "uuid",
      "user_id": "uuid",
      "interaction_type": "scene",
      "prompt": "创建一个古老的图书馆场景",
      "response_content": "这是一座宏伟的古老图书馆...",
      "token_usage": 200,
      "response_time": 1500,
      "status": "completed",
      "created_at": "2025-08-03T07:00:00Z"
    }
  ]
}
```

### 2. 获取Token使用统计
**GET** `/api/v1/ai/stats`

获取用户的AI Token使用统计信息。

**响应:**
```json
{
  "success": true,
  "message": "获取统计信息成功",
  "data": {
    "total_interactions": 150,
    "total_tokens_used": 25000,
    "tokens_by_type": {
      "scene": 8000,
      "character": 6000,
      "event": 4000,
      "dialogue": 3000,
      "description": 4000
    },
    "average_response_time": 1200,
    "success_rate": 0.98,
    "last_30_days": {
      "interactions": 45,
      "tokens": 7500
    }
  }
}
```

## 内容类型说明

### 支持的生成类型
1. **scene**: 场景生成 - 创建游戏场景、地点描述
2. **character**: 角色生成 - 创建NPC角色、人物设定
3. **event**: 事件生成 - 创建游戏事件、情节发展
4. **dialogue**: 对话生成 - 生成角色对话、交互内容
5. **description**: 描述生成 - 生成各种描述性文本

### 上下文参数建议

**场景生成上下文:**
```json
{
  "theme": "fantasy|sci-fi|modern|historical",
  "size": "small|medium|large",
  "atmosphere": "peaceful|tense|mysterious|dangerous",
  "time": "morning|afternoon|evening|night",
  "weather": "sunny|rainy|stormy|foggy"
}
```

**角色生成上下文:**
```json
{
  "age": "young|adult|middle-aged|old",
  "gender": "male|female|other",
  "class": "warrior|mage|rogue|merchant|noble",
  "personality": "brave|wise|cunning|kind|mysterious",
  "role": "ally|enemy|neutral|quest-giver"
}
```

**事件生成上下文:**
```json
{
  "type": "combat|social|exploration|magical|political",
  "severity": "low|medium|high|critical",
  "duration": "instant|short|medium|long",
  "scope": "personal|local|regional|global"
}
```

## 错误处理

### 常见错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "timestamp": 1691049600
}
```

**状态码说明:**
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `429`: 请求频率限制
- `500`: 服务器内部错误
- `503`: AI服务不可用

### 错误类型
1. **参数验证错误**: 缺少必要参数或参数格式错误
2. **认证错误**: Token无效或过期
3. **配额限制**: 超出Token使用限制或请求频率限制
4. **AI服务错误**: AI API返回错误或超时
5. **系统错误**: 数据库连接失败等系统级错误

## 最佳实践

### 1. 提示词编写
- 使用清晰、具体的描述
- 提供足够的上下文信息
- 避免过于复杂或矛盾的要求

### 2. 参数设置
- **temperature**: 0.3-0.5 用于一致性内容，0.7-0.9 用于创意内容
- **max_tokens**: 根据内容类型调整，描述类200-500，详细内容500-1000
- **context**: 提供相关的世界观、角色背景等信息

### 3. 错误处理
- 实现重试机制处理临时错误
- 缓存生成结果避免重复调用
- 监控Token使用量避免超出配额

### 4. 性能优化
- 批量处理多个生成请求
- 使用适当的缓存策略
- 监控响应时间和成功率
