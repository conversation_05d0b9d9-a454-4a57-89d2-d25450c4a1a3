# 数据库兼容性指南

## 概述

本项目实现了SQLite和PostgreSQL的完全兼容，允许在开发环境使用SQLite，在生产环境使用PostgreSQL，确保开发和生产环境的一致性。

## 🎯 设计目标

- **开发便利性**: 使用SQLite进行本地开发，无需安装和配置PostgreSQL
- **生产稳定性**: 使用PostgreSQL获得更好的性能和企业级功能
- **完全兼容**: 确保代码在两种数据库间无缝切换
- **数据一致性**: 保证数据结构和行为在两种数据库中一致

## 🏗️ 架构设计

### 1. 兼容层架构

```
应用层 (GORM Models)
        ↓
兼容性层 (Compatibility Layer)
        ↓
数据库驱动层 (SQLite/PostgreSQL)
```

### 2. 核心组件

- **`pkg/database/compatibility.go`**: 数据库兼容性配置和转换
- **`internal/migration/smart_migrator.go`**: 智能迁移管理器
- **`internal/models/user.go`**: 兼容的模型定义
- **`migrations/sqlite/`**: SQLite专用迁移文件
- **`migrations/postgres/`**: PostgreSQL专用迁移文件

## 🔧 技术实现

### 1. 数据类型映射

| 功能 | PostgreSQL | SQLite | 兼容方案 |
|------|------------|--------|----------|
| UUID | `uuid` | `text` | 使用`string`类型存储UUID |
| JSON | `jsonb` | `text` | 使用`type:text`标签 |
| 时间戳 | `timestamp with time zone` | `datetime` | 使用GORM的`time.Time` |
| 布尔值 | `boolean` | `boolean` | 直接兼容 |
| 自增ID | `serial` | `integer` | 使用GORM的`AutoIncrement` |

### 2. 模型定义兼容性

```go
// 兼容的用户模型定义
type User struct {
    // 使用string类型存储UUID，兼容SQLite
    ID               string         `json:"id" gorm:"primaryKey;type:text"`
    ExternalID       string         `json:"external_id" gorm:"not null;index"`
    ExternalProvider string         `json:"external_provider" gorm:"not null;index"`
    Email            string         `json:"email" gorm:"not null;index"`
    // JSON字段使用text类型，兼容SQLite
    GameRoles        StringArray    `json:"game_roles" gorm:"type:text;default:'[\"user\"]'"`
    Preferences      JSON           `json:"preferences" gorm:"type:text;default:'{}'"`
    CreatedAt        time.Time      `json:"created_at" gorm:"index"`
    UpdatedAt        time.Time      `json:"updated_at"`
}

// GORM钩子：创建前生成UUID
func (u *User) BeforeCreate(tx *gorm.DB) error {
    if u.ID == "" {
        u.ID = uuid.New().String()
    }
    return nil
}
```

### 3. 智能迁移系统

```go
// 智能迁移器根据数据库类型选择合适的策略
type SmartMigrator struct {
    db             *gorm.DB
    config         *config.DatabaseConfig
    compatibility  *database.CompatibilityConfig
}

// 自动选择迁移路径
func (sm *SmartMigrator) GetMigrationsPath() string {
    dbType := database.GetDatabaseType(sm.db)
    switch dbType {
    case database.SQLite:
        return filepath.Join(sm.migrationsPath, "sqlite")
    case database.PostgreSQL:
        return filepath.Join(sm.migrationsPath, "postgres")
    }
}
```

## 🚀 使用方法

### 1. SQLite开发环境

```bash
# 使用SQLite启动开发环境
./scripts/dev_with_sqlite.sh

# 仅初始化SQLite数据库
./scripts/dev_with_sqlite.sh --init-only

# 清理SQLite数据库
./scripts/dev_with_sqlite.sh --clean
```

### 2. PostgreSQL生产环境

```bash
# 使用PostgreSQL配置
cp .env.production .env

# 运行PostgreSQL迁移
go run cmd/migrate/main.go up

# 启动生产服务
go run cmd/server/main.go
```

### 3. 数据库兼容性测试

```bash
# 运行兼容性测试
./scripts/test_database_compatibility.sh
```

## 📁 文件结构

```
├── pkg/database/
│   ├── database.go              # 数据库连接逻辑
│   └── compatibility.go         # 兼容性配置
├── internal/migration/
│   ├── migration.go             # 传统迁移器
│   └── smart_migrator.go        # 智能迁移器
├── migrations/
│   ├── sqlite/                  # SQLite迁移文件
│   │   ├── 000001_create_users_table.up.sql
│   │   └── 000001_create_users_table.down.sql
│   └── postgres/                # PostgreSQL迁移文件
│       ├── 000001_create_users_table.up.sql
│       └── 000001_create_users_table.down.sql
├── scripts/
│   ├── dev_with_sqlite.sh       # SQLite开发环境启动
│   └── test_database_compatibility.sh  # 兼容性测试
├── .env.development.sqlite      # SQLite开发环境配置
└── docs/
    └── database-compatibility.md  # 本文档
```

## ⚙️ 配置说明

### SQLite配置 (.env.development.sqlite)

```env
# 数据库配置 - SQLite
DB_NAME=data/dev.db              # .db结尾自动使用SQLite
DB_HOST=                         # SQLite不需要
DB_PORT=                         # SQLite不需要
DB_USER=                         # SQLite不需要
DB_PASSWORD=                     # SQLite不需要
DB_SSL_MODE=development

# SQLite特有配置
SQLITE_JOURNAL_MODE=WAL          # 写前日志模式
SQLITE_SYNCHRONOUS=NORMAL        # 同步模式
SQLITE_CACHE_SIZE=10000          # 缓存大小
```

### PostgreSQL配置 (.env.production)

```env
# 数据库配置 - PostgreSQL
DB_NAME=ai_text_game             # 不以.db结尾使用PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_SSL_MODE=require
```

## 🔍 兼容性检查

### 自动检测数据库类型

```go
// 根据DB_NAME自动检测数据库类型
func detectDatabaseType(dbName string) DatabaseType {
    if len(dbName) > 3 && dbName[len(dbName)-3:] == ".db" {
        return SQLite
    }
    return PostgreSQL
}
```

### 兼容性验证

```go
// 验证数据库兼容性
func (c *CompatibilityConfig) ValidateCompatibility() error {
    switch c.DBType {
    case PostgreSQL, SQLite:
        return nil
    default:
        return fmt.Errorf("不支持的数据库类型: %s", c.DBType)
    }
}
```

## 🧪 测试策略

### 1. 单元测试

- 测试模型的CRUD操作
- 测试JSON字段的序列化/反序列化
- 测试UUID生成和存储

### 2. 集成测试

- 测试完整的业务流程
- 测试数据库迁移
- 测试两种数据库的行为一致性

### 3. 兼容性测试

```bash
# 运行完整的兼容性测试套件
./scripts/test_database_compatibility.sh
```

## 📊 性能对比

| 特性 | SQLite | PostgreSQL |
|------|--------|------------|
| 启动速度 | 极快 | 中等 |
| 内存使用 | 低 | 中等 |
| 并发性能 | 读优秀，写一般 | 读写都优秀 |
| 功能丰富度 | 基础 | 企业级 |
| 运维复杂度 | 极低 | 中等 |

## 🚨 注意事项

### 1. 功能限制

- SQLite不支持某些PostgreSQL高级功能（如数组类型）
- JSON查询语法在两种数据库中略有不同
- SQLite的并发写入性能有限

### 2. 开发建议

- 开发阶段使用SQLite进行快速迭代
- 定期在PostgreSQL环境中测试
- CI/CD中同时测试两种数据库
- 避免使用数据库特有的功能

### 3. 生产部署

- 生产环境强烈建议使用PostgreSQL
- 使用正式的迁移文件管理数据库变更
- 定期备份数据库
- 监控数据库性能

## 🔄 迁移策略

### 从SQLite迁移到PostgreSQL

1. 导出SQLite数据
2. 转换数据格式
3. 在PostgreSQL中创建表结构
4. 导入转换后的数据
5. 验证数据完整性

### 从PostgreSQL迁移到SQLite

1. 简化数据结构（如果使用了PostgreSQL特有功能）
2. 导出数据
3. 在SQLite中创建表结构
4. 导入数据
5. 验证功能正常

## 📚 相关资源

- [GORM官方文档](https://gorm.io/docs/)
- [SQLite文档](https://www.sqlite.org/docs.html)
- [PostgreSQL文档](https://www.postgresql.org/docs/)
- [golang-migrate文档](https://github.com/golang-migrate/migrate)

## 🤝 贡献指南

1. 确保新功能在两种数据库中都能正常工作
2. 添加相应的测试用例
3. 更新文档
4. 运行兼容性测试套件

---

通过这套完整的数据库兼容性方案，项目可以在开发和生产环境中无缝切换数据库，既保证了开发效率，又确保了生产环境的稳定性和性能。
