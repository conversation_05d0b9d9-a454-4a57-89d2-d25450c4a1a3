# 动态游戏时间速率控制系统

## 概述

动态游戏时间速率控制系统是AI文本游戏的核心功能之一，它允许每个游戏世界根据现实时间段自动调整游戏时间流逝速率，以优化玩家体验和服务器资源使用。

## 功能特性

### 🕒 时间段配置系统
- **灵活的时间段定义**：支持24小时制时间段配置（如：00:00-08:00, 08:00-18:00, 18:00-24:00）
- **独立的世界配置**：每个游戏世界可以独立配置多个时间段及其对应的时间速率
- **动态速率调整**：每个时间段可设置不同的游戏时间速率倍数（如：1倍、2倍、5倍）
- **星期过滤器**：支持按星期设置时间段生效条件

### ⚡ 动态时间管理器
- **集中式管理**：统一管理所有世界的时间速率，避免资源浪费
- **实时速率切换**：根据现实时间自动切换游戏时间速率
- **批量数据库更新**：优化数据库写入性能，减少系统负载
- **平滑过渡**：确保时间速率变化时游戏状态的一致性

### 🔧 配置管理功能
- **热更新支持**：配置变更无需重启服务，立即生效
- **配置验证**：完整的配置验证机制，防止无效配置
- **版本控制**：配置版本管理，支持配置回滚
- **统计监控**：提供详细的时间管理统计信息

## 系统架构

### 核心组件

1. **TimeScheduleConfig**：时间段配置数据结构
2. **DynamicTimeManager**：动态时间速率管理器
3. **TimeConfigService**：时间配置服务
4. **TimeScheduleHandler**：API处理器

### 数据流程

```
现实时间 → 时间段匹配 → 速率计算 → 游戏时间更新 → 数据库同步
```

## 配置格式

### 时间段配置结构

```json
{
  "enabled": true,
  "timezone": "Asia/Shanghai",
  "default_rate": 1.0,
  "version": 1,
  "last_updated": "2024-01-01T00:00:00Z",
  "schedules": [
    {
      "name": "深夜低速",
      "start_time": "00:00",
      "end_time": "08:00",
      "time_rate": 1.0,
      "enabled": true,
      "description": "凌晨时段，玩家较少，使用正常速率",
      "weekdays": []
    },
    {
      "name": "白天高速",
      "start_time": "08:00",
      "end_time": "18:00",
      "time_rate": 5.0,
      "enabled": true,
      "description": "白天时段，玩家活跃，加快游戏进度",
      "weekdays": [1, 2, 3, 4, 5]
    },
    {
      "name": "晚上中速",
      "start_time": "18:00",
      "end_time": "24:00",
      "time_rate": 2.0,
      "enabled": true,
      "description": "晚上时段，玩家次活跃，中等速率",
      "weekdays": []
    }
  ]
}
```

### 配置字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `enabled` | boolean | 是 | 是否启用时间段配置 |
| `timezone` | string | 否 | 时区设置，默认"Asia/Shanghai" |
| `default_rate` | number | 是 | 默认时间速率（当没有匹配的时间段时使用） |
| `schedules` | array | 是 | 时间段列表 |
| `schedules[].name` | string | 是 | 时间段名称 |
| `schedules[].start_time` | string | 是 | 开始时间（HH:MM格式） |
| `schedules[].end_time` | string | 是 | 结束时间（HH:MM格式） |
| `schedules[].time_rate` | number | 是 | 时间速率倍数 |
| `schedules[].enabled` | boolean | 是 | 是否启用该时间段 |
| `schedules[].description` | string | 否 | 描述信息 |
| `schedules[].weekdays` | array | 否 | 星期过滤器（1-7表示周一到周日，空表示每天生效） |

## API接口

### 获取时间段配置

```http
GET /api/v1/game/worlds/{world_id}/time-schedule
```

**响应示例：**
```json
{
  "success": true,
  "message": "获取时间段配置成功",
  "data": {
    "enabled": true,
    "timezone": "Asia/Shanghai",
    "default_rate": 1.0,
    "schedules": [...]
  }
}
```

### 更新时间段配置

```http
PUT /api/v1/game/worlds/{world_id}/time-schedule
```

**请求体：**
```json
{
  "enabled": true,
  "timezone": "Asia/Shanghai",
  "default_rate": 1.0,
  "schedules": [...]
}
```

### 获取当前时间速率

```http
GET /api/v1/game/worlds/{world_id}/time-rate
```

**响应示例：**
```json
{
  "success": true,
  "message": "获取当前时间速率成功",
  "data": {
    "world_id": "world-001",
    "time_rate": 5.0,
    "timestamp": "2024-01-01 10:30:00",
    "active_schedule": {
      "name": "白天高速",
      "start_time": "08:00",
      "end_time": "18:00",
      "description": "白天时段，玩家活跃，加快游戏进度"
    }
  }
}
```

### 启用/禁用时间段配置

```http
POST /api/v1/game/worlds/{world_id}/time-schedule/enable
POST /api/v1/game/worlds/{world_id}/time-schedule/disable
```

### 创建默认配置

```http
POST /api/v1/game/worlds/{world_id}/time-schedule/default
```

### 验证配置

```http
POST /api/v1/game/time-schedule/validate
```

### 获取统计信息

```http
GET /api/v1/game/time-schedule/stats
```

### 重置配置

```http
POST /api/v1/game/worlds/{world_id}/time-schedule/reset
```

## 使用示例

### 1. 基本配置示例

为世界创建一个简单的时间段配置：

```bash
# 1. 创建默认配置
curl -X POST "http://localhost:8080/api/v1/game/worlds/world-001/time-schedule/default" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. 启用时间段配置
curl -X POST "http://localhost:8080/api/v1/game/worlds/world-001/time-schedule/enable" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. 查看当前时间速率
curl -X GET "http://localhost:8080/api/v1/game/worlds/world-001/time-rate" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 自定义配置示例

创建一个工作日和周末不同速率的配置：

```json
{
  "enabled": true,
  "timezone": "Asia/Shanghai",
  "default_rate": 1.0,
  "schedules": [
    {
      "name": "工作日白天",
      "start_time": "09:00",
      "end_time": "18:00",
      "time_rate": 3.0,
      "enabled": true,
      "description": "工作日白天，中等速率",
      "weekdays": [1, 2, 3, 4, 5]
    },
    {
      "name": "周末白天",
      "start_time": "09:00",
      "end_time": "18:00",
      "time_rate": 5.0,
      "enabled": true,
      "description": "周末白天，高速率",
      "weekdays": [6, 7]
    },
    {
      "name": "夜间",
      "start_time": "22:00",
      "end_time": "08:00",
      "time_rate": 1.0,
      "enabled": true,
      "description": "夜间时段，正常速率"
    }
  ]
}
```

### 3. 跨天时间段示例

配置跨天的时间段（如夜间22:00-06:00）：

```json
{
  "name": "深夜时段",
  "start_time": "22:00",
  "end_time": "06:00",
  "time_rate": 0.5,
  "enabled": true,
  "description": "深夜时段，慢速率节省资源"
}
```

## 最佳实践

### 1. 时间段设计原则

- **避免重叠**：确保同一时间只有一个时间段生效
- **合理速率**：时间速率建议在0.1-10倍之间
- **考虑用户体验**：频繁的速率变化可能影响用户体验
- **资源优化**：在低活跃时段使用较低的时间速率

### 2. 配置管理建议

- **渐进式部署**：先在测试世界验证配置，再应用到生产世界
- **监控统计**：定期查看时间管理统计信息，优化配置
- **备份配置**：重要配置变更前先备份当前配置
- **文档记录**：为每个时间段添加清晰的描述信息

### 3. 性能优化

- **批量更新**：使用批量API更新多个世界的配置
- **合理间隔**：避免设置过短的时间段，减少频繁切换
- **监控资源**：关注时间管理器的CPU和内存使用情况

## 故障排除

### 常见问题

1. **配置验证失败**
   - 检查时间格式是否为HH:MM
   - 确认时间速率大于0
   - 验证时间段是否重叠

2. **时间速率未生效**
   - 确认时间段配置已启用
   - 检查当前时间是否在配置的时间段内
   - 验证星期过滤器设置

3. **性能问题**
   - 检查时间管理器统计信息
   - 优化时间段配置，减少复杂度
   - 调整批量更新大小

### 调试工具

- **配置验证API**：验证配置有效性
- **统计信息API**：监控系统运行状态
- **日志输出**：查看详细的运行日志

## 技术实现

### 核心算法

1. **时间匹配算法**：高效匹配当前时间到对应时间段
2. **跨天处理**：正确处理跨天时间段（如22:00-06:00）
3. **批量更新**：优化数据库写入性能
4. **内存管理**：高效的世界状态缓存机制

### 数据库设计

时间配置存储在World表的`time_config`字段中，使用JSON格式：

```sql
-- 查询启用时间段配置的世界
SELECT id, name, time_config 
FROM worlds 
WHERE JSON_EXTRACT(time_config, '$.time_schedule.enabled') = true;

-- 更新世界的时间段配置
UPDATE worlds 
SET time_config = JSON_SET(time_config, '$.time_schedule', ?) 
WHERE id = ?;
```

### 并发安全

- **读写锁**：保护世界状态缓存的并发访问
- **事务处理**：确保数据库更新的原子性
- **优雅关闭**：支持服务的优雅启动和关闭

## 扩展功能

### 未来规划

1. **节假日支持**：支持节假日特殊时间段配置
2. **动态调整**：根据服务器负载自动调整时间速率
3. **用户偏好**：允许用户设置个人时间速率偏好
4. **历史统计**：提供时间速率变化的历史统计
5. **预测分析**：基于历史数据预测最优时间段配置

### 集成建议

- **监控系统**：集成到现有的监控和告警系统
- **管理界面**：开发Web管理界面，方便配置管理
- **API网关**：通过API网关统一管理时间相关接口
- **缓存系统**：使用Redis缓存热点配置数据

## 完整示例

### 游戏运营商配置示例

以下是一个完整的游戏运营商时间段配置示例，适用于中国时区的MMORPG游戏：

```json
{
  "enabled": true,
  "timezone": "Asia/Shanghai",
  "default_rate": 1.0,
  "version": 1,
  "schedules": [
    {
      "name": "深夜维护",
      "start_time": "02:00",
      "end_time": "06:00",
      "time_rate": 0.5,
      "enabled": true,
      "description": "深夜维护时段，降低服务器负载",
      "weekdays": []
    },
    {
      "name": "早晨预热",
      "start_time": "06:00",
      "end_time": "08:00",
      "time_rate": 1.5,
      "enabled": true,
      "description": "早晨预热时段，适度加速",
      "weekdays": []
    },
    {
      "name": "工作日上午",
      "start_time": "08:00",
      "end_time": "12:00",
      "time_rate": 3.0,
      "enabled": true,
      "description": "工作日上午，中等活跃度",
      "weekdays": [1, 2, 3, 4, 5]
    },
    {
      "name": "周末上午",
      "start_time": "08:00",
      "end_time": "12:00",
      "time_rate": 4.0,
      "enabled": true,
      "description": "周末上午，高活跃度",
      "weekdays": [6, 7]
    },
    {
      "name": "午休时段",
      "start_time": "12:00",
      "end_time": "14:00",
      "time_rate": 2.0,
      "enabled": true,
      "description": "午休时段，中低活跃度",
      "weekdays": []
    },
    {
      "name": "工作日下午",
      "start_time": "14:00",
      "end_time": "18:00",
      "time_rate": 3.5,
      "enabled": true,
      "description": "工作日下午，中高活跃度",
      "weekdays": [1, 2, 3, 4, 5]
    },
    {
      "name": "周末下午",
      "start_time": "14:00",
      "end_time": "18:00",
      "time_rate": 5.0,
      "enabled": true,
      "description": "周末下午，最高活跃度",
      "weekdays": [6, 7]
    },
    {
      "name": "黄金时段",
      "start_time": "18:00",
      "end_time": "22:00",
      "time_rate": 4.5,
      "enabled": true,
      "description": "晚间黄金时段，高活跃度",
      "weekdays": []
    },
    {
      "name": "夜间时段",
      "start_time": "22:00",
      "end_time": "02:00",
      "time_rate": 2.0,
      "enabled": true,
      "description": "夜间时段，中等活跃度",
      "weekdays": []
    }
  ]
}
```

### 部署脚本示例

```bash
#!/bin/bash
# 动态时间系统部署脚本

API_BASE="http://localhost:8080/api/v1/game"
TOKEN="your_auth_token_here"

# 函数：发送API请求
send_request() {
    local method=$1
    local endpoint=$2
    local data=$3

    if [ -n "$data" ]; then
        curl -X "$method" "$API_BASE$endpoint" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d "$data"
    else
        curl -X "$method" "$API_BASE$endpoint" \
            -H "Authorization: Bearer $TOKEN"
    fi
}

# 1. 为所有活跃世界创建默认配置
echo "正在为活跃世界创建默认时间段配置..."
WORLDS=$(send_request "GET" "/worlds" | jq -r '.data[].id')

for world_id in $WORLDS; do
    echo "处理世界: $world_id"
    send_request "POST" "/worlds/$world_id/time-schedule/default"
    send_request "POST" "/worlds/$world_id/time-schedule/enable"
done

# 2. 验证配置
echo "验证时间段配置..."
CONFIG_FILE="time_schedule_config.json"
send_request "POST" "/time-schedule/validate" "@$CONFIG_FILE"

# 3. 应用自定义配置到特定世界
PREMIUM_WORLDS=("world-001" "world-002" "world-003")
for world_id in "${PREMIUM_WORLDS[@]}"; do
    echo "应用高级配置到世界: $world_id"
    send_request "PUT" "/worlds/$world_id/time-schedule" "@$CONFIG_FILE"
done

# 4. 检查系统状态
echo "检查时间管理系统状态..."
send_request "GET" "/time-schedule/stats"

echo "部署完成！"
```

### 监控脚本示例

```bash
#!/bin/bash
# 时间系统监控脚本

API_BASE="http://localhost:8080/api/v1/game"
TOKEN="your_auth_token_here"
LOG_FILE="/var/log/time_schedule_monitor.log"

# 记录日志
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查系统统计
check_stats() {
    local stats=$(curl -s -X GET "$API_BASE/time-schedule/stats" \
        -H "Authorization: Bearer $TOKEN")

    local total_worlds=$(echo "$stats" | jq -r '.data.total_worlds')
    local enabled_worlds=$(echo "$stats" | jq -r '.data.enabled_worlds')
    local enabled_ratio=$(echo "$stats" | jq -r '.data.enabled_ratio')

    log "系统统计: 总世界数=$total_worlds, 启用时间段配置=$enabled_worlds, 启用比例=$enabled_ratio"

    # 检查启用比例是否过低
    if (( $(echo "$enabled_ratio < 0.5" | bc -l) )); then
        log "警告: 启用时间段配置的世界比例过低 ($enabled_ratio)"
    fi
}

# 检查特定世界的时间速率
check_world_rates() {
    local worlds=("world-001" "world-002" "world-003")

    for world_id in "${worlds[@]}"; do
        local rate_info=$(curl -s -X GET "$API_BASE/worlds/$world_id/time-rate" \
            -H "Authorization: Bearer $TOKEN")

        local time_rate=$(echo "$rate_info" | jq -r '.data.time_rate')
        local active_schedule=$(echo "$rate_info" | jq -r '.data.active_schedule.name // "默认"')

        log "世界 $world_id: 当前速率=$time_rate, 活跃时间段=$active_schedule"
    done
}

# 主监控循环
main() {
    log "开始时间系统监控..."

    while true; do
        check_stats
        check_world_rates

        # 每5分钟检查一次
        sleep 300
    done
}

# 信号处理
trap 'log "监控脚本退出"; exit 0' SIGINT SIGTERM

main
