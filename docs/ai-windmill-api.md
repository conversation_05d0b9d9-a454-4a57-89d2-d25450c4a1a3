# AI Windmill 接口 API 文档

## 概述

本文档描述了基于 Windmill 的 JSON 结构化输出接口，用于生成 AI 文本游戏中的各种内容。该系统提供了完整的 AI 内容生成、验证、转换和同步功能。

## 核心功能

### 1. JSON Schema 管理

#### 支持的内容类型

- `scene` - 游戏场景
- `character` - 游戏角色  
- `event` - 游戏事件
- `dialogue` - 角色对话
- `item` - 游戏物品
- `world_description` - 世界描述
- `quest` - 游戏任务
- `environment_effect` - 环境效果

#### API 接口

##### 获取支持的内容类型
```http
GET /api/ai/content-types
```

**响应示例：**
```json
{
  "content_types": [
    "scene",
    "character", 
    "event",
    "dialogue",
    "item",
    "world_description",
    "quest",
    "environment_effect"
  ]
}
```

##### 获取内容类型的 Schema
```http
GET /api/ai/schema/{content_type}
```

**参数：**
- `content_type` - 内容类型名称

**响应示例：**
```json
{
  "type": "object",
  "description": "游戏场景生成结果",
  "properties": {
    "name": {
      "type": "string",
      "description": "场景名称，应该简洁且富有想象力",
      "minLength": 2,
      "maxLength": 50
    },
    "description": {
      "type": "string", 
      "description": "详细的场景描述",
      "minLength": 50,
      "maxLength": 500
    }
  },
  "required": ["name", "description"]
}
```

### 2. AI 内容生成

#### 同步生成内容
```http
POST /api/ai/generate
```

**请求体：**
```json
{
  "type": "scene",
  "prompt": "生成一个神秘的森林场景",
  "world_id": "uuid",
  "user_id": "uuid",
  "context": {
    "world_theme": "奇幻",
    "target_atmosphere": "神秘"
  },
  "schema": {}
}
```

**响应示例：**
```json
{
  "content": "生成的文本内容",
  "structured_data": {
    "name": "神秘森林",
    "description": "一片充满魔法气息的古老森林...",
    "atmosphere": "神秘",
    "scene_type": "森林"
  },
  "token_usage": 1250,
  "response_time": 2.5
}
```

#### 异步生成内容
```http
POST /api/ai/generate-async
```

**请求体：** 同同步接口

**响应示例：**
```json
{
  "task_id": "task-uuid",
  "status": "pending",
  "created_at": "2024-01-01T12:00:00Z"
}
```

#### 获取异步任务状态
```http
GET /api/ai/tasks/{task_id}
```

**响应示例：**
```json
{
  "id": "task-uuid",
  "type": "scene",
  "status": "completed",
  "progress": 100.0,
  "response": {
    "content": "生成的内容",
    "structured_data": {}
  },
  "created_at": "2024-01-01T12:00:00Z",
  "completed_at": "2024-01-01T12:00:05Z"
}
```

#### 取消异步任务
```http
DELETE /api/ai/tasks/{task_id}
```

### 3. 数据验证和转换

#### 验证生成的数据
```http
POST /api/ai/validate
```

**请求体：**
```json
{
  "content_type": "scene",
  "data": {
    "name": "测试场景",
    "description": "场景描述"
  }
}
```

**响应示例：**
```json
{
  "valid": true,
  "errors": []
}
```

#### 转换数据为模型
```http
POST /api/ai/transform
```

**请求体：**
```json
{
  "content_type": "scene",
  "data": {},
  "world_id": "uuid"
}
```

### 4. 配置管理

#### 获取 AI 配置
```http
GET /api/ai/config
```

**响应示例：**
```json
{
  "total_models": 2,
  "total_content_types": 8,
  "default_model": "gemini-1.5-pro",
  "available_models": ["gemini-1.5-pro", "gemini-1.5-flash"],
  "supported_types": ["scene", "character", "event"]
}
```

#### 更新模型配置
```http
PUT /api/ai/config/models/{model_name}
```

**请求体：**
```json
{
  "name": "gemini-1.5-pro",
  "max_tokens": 8192,
  "temperature": 0.7,
  "top_p": 0.9,
  "timeout": "30s"
}
```

#### 更新内容类型配置
```http
PUT /api/ai/config/content-types/{content_type}
```

**请求体：**
```json
{
  "model": "gemini-1.5-pro",
  "priority": 8,
  "cache_enabled": true,
  "cache_ttl": "1h",
  "validation_level": "strict"
}
```

### 5. 实时同步

#### WebSocket 连接
```
ws://localhost:8080/api/ai/ws?user_id={user_id}&world_id={world_id}
```

#### 消息格式

**任务状态更新：**
```json
{
  "id": "msg-uuid",
  "type": "task_status",
  "event": "task_update",
  "data": {
    "task_id": "task-uuid",
    "status": "completed",
    "progress": 100.0
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**内容生成完成：**
```json
{
  "id": "msg-uuid", 
  "type": "content_generated",
  "event": "scene_created",
  "data": {
    "content_type": "scene",
    "content_id": "scene-uuid",
    "data": {}
  }
}
```

**错误通知：**
```json
{
  "id": "msg-uuid",
  "type": "error", 
  "event": "error_occurred",
  "data": {
    "error_type": "validation_failed",
    "message": "数据验证失败",
    "details": {}
  }
}
```

### 6. 统计和监控

#### 获取任务统计
```http
GET /api/ai/stats/tasks
```

**响应示例：**
```json
{
  "total_tasks": 1250,
  "completed_tasks": 1200,
  "failed_tasks": 30,
  "active_tasks": 20,
  "queue_size": 5,
  "worker_count": 5
}
```

#### 获取连接统计
```http
GET /api/ai/stats/connections
```

**响应示例：**
```json
{
  "total_connections": 150,
  "users_online": 120,
  "active_worlds": 25,
  "max_connections": 1000
}
```

#### 获取 AI 使用统计
```http
GET /api/ai/stats/usage
```

**响应示例：**
```json
{
  "total_interactions": 5000,
  "total_tokens": 2500000,
  "avg_tokens_per_request": 500
}
```

## 错误处理

### 错误响应格式
```json
{
  "error": {
    "type": "validation_error",
    "message": "请求参数验证失败",
    "details": {
      "field": "content_type",
      "reason": "不支持的内容类型"
    }
  }
}
```

### 常见错误类型

- `validation_error` - 参数验证错误
- `auth_error` - 认证错误
- `rate_limit_error` - 频率限制错误
- `server_error` - 服务器内部错误
- `timeout_error` - 请求超时错误
- `network_error` - 网络连接错误

## 使用示例

### 生成游戏场景

```javascript
// 同步生成
const response = await fetch('/api/ai/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token'
  },
  body: JSON.stringify({
    type: 'scene',
    prompt: '生成一个神秘的地下洞穴场景',
    world_id: 'world-uuid',
    context: {
      world_theme: '奇幻',
      target_atmosphere: '神秘',
      danger_level: 5
    }
  })
});

const result = await response.json();
console.log('生成的场景:', result.structured_data);
```

### 异步生成角色

```javascript
// 提交异步任务
const taskResponse = await fetch('/api/ai/generate-async', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token'
  },
  body: JSON.stringify({
    type: 'character',
    prompt: '生成一个智慧的老法师角色',
    world_id: 'world-uuid',
    user_id: 'user-uuid'
  })
});

const task = await taskResponse.json();

// 监听任务完成
const ws = new WebSocket(`ws://localhost:8080/api/ai/ws?user_id=user-uuid`);
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  if (message.type === 'task_status' && message.data.task_id === task.task_id) {
    if (message.data.status === 'completed') {
      console.log('角色生成完成:', message.data.data);
    }
  }
};
```

### 验证和转换数据

```javascript
// 验证数据
const validationResponse = await fetch('/api/ai/validate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    content_type: 'item',
    data: {
      name: '魔法剑',
      description: '一把散发着蓝光的魔法剑',
      item_type: '武器'
    }
  })
});

const validation = await validationResponse.json();
if (validation.valid) {
  // 转换为数据模型
  const transformResponse = await fetch('/api/ai/transform', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      content_type: 'item',
      data: validation.data,
      world_id: 'world-uuid'
    })
  });
}
```

## 最佳实践

1. **使用异步生成** - 对于复杂内容生成，建议使用异步接口避免超时
2. **启用缓存** - 为重复的生成请求启用缓存以提高性能
3. **监控错误** - 实现完善的错误处理和重试机制
4. **验证数据** - 始终验证生成的数据以确保质量
5. **合理配置** - 根据使用场景调整模型参数和超时设置
6. **实时同步** - 使用 WebSocket 获取实时状态更新
