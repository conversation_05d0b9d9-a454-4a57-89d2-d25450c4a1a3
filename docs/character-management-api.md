# 角色管理API文档

## 概述

角色管理API提供了完整的角色CRUD操作，包括创建、查询、更新、删除角色，以及角色特质、记忆、阅历管理等高级功能。所有API都需要Bearer Token认证。

## API端点

### 基础URL
```
/api/v1/game/characters
```

## 角色基础管理

### 1. 创建角色
**POST** `/api/v1/game/characters`

在指定世界中创建新角色。

**请求头:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体:**
```json
{
  "world_id": "uuid",
  "name": "角色名称",
  "description": "角色描述",
  "character_type": "player",
  "traits": ["勇敢", "聪明", "善良"]
}
```

**响应:**
```json
{
  "success": true,
  "message": "创建角色成功",
  "data": {
    "id": "uuid",
    "world_id": "uuid",
    "user_id": "uuid",
    "name": "角色名称",
    "description": "角色描述",
    "character_type": "player",
    "current_scene_id": null,
    "traits": ["勇敢", "聪明", "善良"],
    "memories": {"memories": []},
    "experiences": {},
    "relationships": {},
    "status": "active",
    "last_action_at": null,
    "created_at": "2025-08-03T06:30:00Z",
    "updated_at": "2025-08-03T06:30:00Z"
  }
}
```

### 2. 获取角色信息
**GET** `/api/v1/game/characters/{character_id}`

获取指定角色的详细信息。

**路径参数:**
- `character_id`: 角色UUID

**响应:**
```json
{
  "success": true,
  "message": "获取角色信息成功",
  "data": {
    "id": "uuid",
    "name": "角色名称",
    "description": "角色描述",
    "character_type": "player",
    "world": {
      "id": "uuid",
      "name": "世界名称"
    },
    "user": {
      "id": "uuid",
      "display_name": "用户名称"
    },
    "current_scene": {
      "id": "uuid",
      "name": "场景名称"
    },
    "traits": ["勇敢", "聪明", "善良"],
    "memories": {...},
    "experiences": {...},
    "relationships": {...},
    "status": "active",
    "created_at": "2025-08-03T06:30:00Z"
  }
}
```

### 3. 更新角色信息
**PUT** `/api/v1/game/characters/{character_id}`

更新指定角色的基本信息。只有角色拥有者可以更新。

**请求体:**
```json
{
  "name": "新的角色名称",
  "description": "新的角色描述",
  "traits": ["勇敢", "聪明", "善良", "坚韧"]
}
```

**响应:**
```json
{
  "success": true,
  "message": "更新角色成功"
}
```

### 4. 删除角色
**DELETE** `/api/v1/game/characters/{character_id}`

删除指定的角色。只有角色拥有者可以删除。

**响应:**
```json
{
  "success": true,
  "message": "删除角色成功"
}
```

### 5. 移动角色
**POST** `/api/v1/game/characters/{character_id}/move`

移动角色到指定场景。

**请求体:**
```json
{
  "scene_id": "uuid"
}
```

**响应:**
```json
{
  "success": true,
  "message": "移动角色成功"
}
```

## 角色列表

### 1. 获取我的角色列表
**GET** `/api/v1/game/my-characters`

获取当前用户的角色列表。

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)

**响应:**
```json
{
  "success": true,
  "message": "获取角色列表成功",
  "data": {
    "items": [...],
    "total": 50,
    "page": 1,
    "limit": 20,
    "total_pages": 3
  }
}
```

## 角色高级管理

### 1. 添加角色特质
**POST** `/api/v1/game/characters/{character_id}/traits`

为指定角色添加新的特质。

**请求体:**
```json
{
  "trait": "坚韧"
}
```

**响应:**
```json
{
  "success": true,
  "message": "添加特质成功"
}
```

### 2. 添加角色记忆
**POST** `/api/v1/game/characters/{character_id}/memories`

为指定角色添加新的记忆。

**请求体:**
```json
{
  "type": "event",
  "content": "在森林中遇到了一只神秘的白鹿",
  "importance": 8,
  "clarity": 0.9,
  "tags": ["森林", "白鹿", "神秘"],
  "related_ids": ["scene_uuid", "entity_uuid"],
  "metadata": {
    "location": "神秘森林",
    "weather": "晴朗"
  }
}
```

**字段说明:**
- `type`: 记忆类型 (event, person, place, item)
- `content`: 记忆内容
- `importance`: 重要性 (1-10)
- `clarity`: 清晰度 (0.0-1.0)
- `tags`: 标签列表
- `related_ids`: 相关实体ID列表
- `metadata`: 额外元数据

**响应:**
```json
{
  "success": true,
  "message": "添加记忆成功"
}
```

### 3. 添加角色阅历
**POST** `/api/v1/game/characters/{character_id}/experiences`

为指定角色添加新的阅历。

**请求体:**
```json
{
  "type": "combat",
  "category": "sword_fighting"
}
```

**字段说明:**
- `type`: 阅历类型 (combat, social, exploration, crafting, event)
- `category`: 具体分类

**响应:**
```json
{
  "success": true,
  "message": "添加阅历成功"
}
```

## 角色类型说明

### 角色类型 (character_type)
- `player`: 玩家角色，由用户控制
- `npc`: 非玩家角色，由AI控制
- `collective`: 集体实体，如组织、势力等

### 角色状态 (status)
- `active`: 活跃状态
- `inactive`: 非活跃状态
- `dead`: 死亡状态

## 特质系统

特质是角色的个性标签，影响角色的行为和AI生成的内容。常见特质包括：

**性格特质:**
- 勇敢、胆怯、聪明、愚钝
- 善良、邪恶、正义、狡猾
- 开朗、内向、冷静、暴躁

**技能特质:**
- 剑术精通、魔法天赋、盗贼技巧
- 商业头脑、外交手腕、学者气质

**背景特质:**
- 贵族出身、平民背景、流浪者
- 军人经历、学者背景、商人家庭

## 记忆系统

记忆系统用于存储角色的经历和知识，影响角色的决策和对话。

### 记忆类型
- `event`: 事件记忆（发生的事情）
- `person`: 人物记忆（认识的人）
- `place`: 地点记忆（去过的地方）
- `item`: 物品记忆（见过的物品）

### 记忆属性
- **重要性**: 1-10，影响记忆的保留优先级
- **清晰度**: 0.0-1.0，影响记忆的准确性
- **访问次数**: 记忆被回忆的次数
- **最后访问**: 最后一次回忆的时间

## 阅历系统

阅历系统记录角色的经验和技能发展。

### 阅历类型
- `combat`: 战斗阅历
- `social`: 社交阅历
- `exploration`: 探索阅历
- `crafting`: 制作阅历
- `event`: 特殊事件阅历

### 阅历属性
- **等级**: 该类型阅历的熟练程度
- **次数**: 经历的总次数
- **详情**: 具体经历的描述列表

## 错误响应

所有API在出错时都会返回统一的错误格式：

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

**常见状态码:**
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 无权限（不是角色拥有者）
- `404`: 角色不存在
- `500`: 服务器内部错误
