# 开发环境认证跳过指南

本文档说明如何在开发环境中跳过身份认证和API网关限制，以便进行快速开发和测试。

## 🚀 快速开始

### 方法一：全栈开发环境（推荐）

```bash
# 同时启动前端和后端，完全跳过认证
./scripts/dev_full_stack.sh

# 访问 http://localhost:3000 开始开发
# 前端会自动检测开发模式并提供快速登录
```

### 方法二：仅启动后端服务器

```bash
# 启动简化版服务器，完全跳过认证
./scripts/dev_no_auth.sh

# 或者直接运行
go run cmd/simple-server/main.go
```

### 方法三：仅启动前端开发服务器

```bash
# 启动前端开发服务器（需要后端已运行）
./scripts/dev_frontend.sh

# 前端会自动检测后端开发模式状态
```

### 方法四：使用完整服务器的开发模式

```bash
# 启动完整服务器，但认证中间件会跳过验证
./scripts/dev_no_auth.sh --full

# 或者设置环境变量后运行
export ENVIRONMENT=development
export SKIP_AUTH=true
go run cmd/server/main.go
```

## 🔧 配置说明

### 环境变量配置

在开发环境中，以下环境变量会影响认证行为：

```bash
# 设置为开发环境
ENVIRONMENT=development

# 强制跳过认证（优先级最高）
SKIP_AUTH=true

# 启用开发特性
DEV_ENABLE_DEBUG_LOGS=true
DEV_ENABLE_CORS_ALL=true
DEV_DISABLE_RATE_LIMIT=true
AI_MOCK_ENABLED=true
```

### 前端开发模式配置

前端开发模式通过以下方式检测和配置：

```bash
# 前端环境变量 (.env.development)
NODE_ENV=development
VITE_DEV_MODE=true
VITE_SHOW_DEV_INDICATOR=true
VITE_SKIP_AUTH=true
VITE_AUTO_LOGIN=true
```

前端开发模式特性：
- **自动检测开发环境**：通过 `NODE_ENV`、`hostname` 或 URL 参数检测
- **开发模式指示器**：右上角显示 DEV 按钮，点击查看开发状态
- **快速登录按钮**：登录页面显示开发模式快速登录选项
- **自动模拟用户**：无需真实 OAuth 流程，自动登录开发用户
- **后端状态检测**：自动检测后端是否启用开发模式
- **API代理**：前端开发服务器自动代理 API 请求到后端

### 配置文件

项目提供了 `.env.development` 配置文件，包含了开发环境的所有配置：

```bash
# 使用开发环境配置文件
cp .env.development .env
```

## 🛡️ 开发模式特性

### 1. 身份认证跳过

- **完全跳过JWT验证**：不需要提供Authorization header
- **自动设置开发用户**：系统会自动设置一个开发测试用户
- **全权限访问**：开发用户拥有admin、premium、user所有权限

### 2. 默认开发用户信息

```json
{
  "user_id": "00000000-0000-0000-0000-000000000001",
  "email": "<EMAIL>",
  "display_name": "开发测试用户",
  "external_id": "dev-user-001",
  "external_provider": "development",
  "game_roles": ["admin", "premium", "user"]
}
```

### 3. 安全策略放宽

- **CORS策略**：允许所有来源访问
- **CSP策略**：允许内联脚本和样式
- **HSTS**：开发环境不启用
- **X-Frame-Options**：允许同源嵌入

### 4. API网关限制禁用

- **速率限制**：完全禁用
- **请求大小限制**：放宽限制
- **超时设置**：延长超时时间

## 📚 API测试示例

### 健康检查

```bash
curl http://localhost:8080/health
```

响应示例：
```json
{
  "status": "ok",
  "service": "ai-text-game-iam-npc",
  "version": "1.0.0",
  "environment": "development",
  "auth_mode": "disabled",
  "dev_features": [
    "认证跳过",
    "模拟用户登录",
    "放宽安全策略",
    "详细错误信息"
  ]
}
```

### 用户信息

```bash
# 无需Authorization header
curl http://localhost:8080/api/v1/user/profile
```

### 游戏世界

```bash
# 获取世界列表
curl http://localhost:8080/api/v1/game/my-worlds

# 创建世界
curl -X POST http://localhost:8080/api/v1/game/worlds \
  -H "Content-Type: application/json" \
  -d '{"name":"测试世界","description":"开发测试用世界"}'
```

### AI服务

```bash
# AI内容生成（Mock模式）
curl -X POST http://localhost:8080/api/v1/ai/generate \
  -H "Content-Type: application/json" \
  -d '{"prompt":"生成一个魔法森林的描述","type":"scene"}'
```

## 🔍 调试和监控

### 日志输出

开发模式下，日志会包含额外的调试信息：

```
[2024-01-01 12:00:00] 开发模式(认证已跳过) GET /api/v1/user/profile 200 1.234ms 127.0.0.1
```

### 响应头

开发模式下，API响应会包含特殊的头信息：

```
X-Dev-Mode: true
X-Auth-Bypass: enabled
```

## ⚠️ 注意事项

### 安全警告

1. **仅用于开发环境**：此模式完全跳过了安全验证，绝不能在生产环境使用
2. **数据安全**：开发环境的数据可能被任何人访问
3. **网络安全**：开发服务器不应暴露到公网

### 环境隔离

1. **数据库隔离**：使用独立的开发数据库
2. **Redis隔离**：使用不同的Redis数据库编号
3. **AI服务**：使用Mock模式，避免消耗生产配额

### 切换到生产模式

要切换回生产模式，只需：

```bash
# 删除或重命名开发配置
mv .env.development .env.development.bak

# 使用生产配置
cp .env.example .env

# 设置正确的环境变量
export ENVIRONMENT=production
unset SKIP_AUTH
```

## 🎨 前端开发模式使用指南

### 开发模式特性

1. **开发指示器**
   - 右上角显示红色 DEV 徽章
   - 点击查看详细的开发状态信息
   - 显示前后端连接状态和认证模式

2. **快速登录**
   - 登录页面显示橙色的"🚀 开发模式快速登录"按钮
   - 一键登录，无需 OAuth 流程
   - 自动创建开发测试用户

3. **自动检测**
   - 前端自动检测是否为开发环境
   - 检查后端开发模式状态
   - 根据后端状态决定是否启用开发特性

### 前端开发工作流

1. **启动开发环境**
   ```bash
   # 全栈启动（推荐）
   ./scripts/dev_full_stack.sh

   # 或分别启动
   ./scripts/dev_no_auth.sh        # 后端
   ./scripts/dev_frontend.sh       # 前端
   ```

2. **访问应用**
   - 打开浏览器访问 http://localhost:3000
   - 点击"🚀 开发模式快速登录"按钮
   - 或点击右上角 DEV 按钮进行快速操作

3. **开发调试**
   - 使用浏览器开发工具 (F12)
   - 查看控制台的详细开发日志
   - 使用 DEV 指示器监控状态

### 前端配置选项

```typescript
// 开发模式检测
AuthService.isDevelopmentMode()

// 开发模式自动登录
AuthService.developmentAutoLogin()

// 检查后端开发模式状态
fetch('/health').then(res => res.json())
```

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :8080  # 后端
   lsof -i :3000  # 前端

   # 杀死进程
   kill -9 <PID>

   # 或使用脚本自动处理
   ./scripts/dev_full_stack.sh  # 会自动清理端口
   ```

2. **权限不足**
   ```bash
   # 确保脚本有执行权限
   chmod +x scripts/dev_no_auth.sh
   chmod +x scripts/dev_frontend.sh
   chmod +x scripts/dev_full_stack.sh
   ```

3. **环境变量未生效**
   ```bash
   # 检查后端环境变量
   env | grep -E "(ENVIRONMENT|SKIP_AUTH)"

   # 检查前端环境变量
   cat web/frontend/.env.development

   # 重新加载配置
   source .env.development
   ```

4. **前端无法连接后端**
   ```bash
   # 检查后端状态
   curl http://localhost:8080/health

   # 检查前端代理配置
   cat web/frontend/vite.config.ts
   ```

5. **开发模式未启用**
   - 检查 URL 是否为 localhost
   - 检查 NODE_ENV 是否为 development
   - 检查后端 /health 接口返回的 auth_mode 字段

### 验证开发模式

访问健康检查接口确认开发模式已启用：

```bash
curl http://localhost:8080/health | jq '.auth_mode'
# 应该返回: "disabled"
```

## 📝 开发工作流

1. **启动开发环境**
   ```bash
   ./scripts/dev_no_auth.sh
   ```

2. **进行开发和测试**
   - 无需处理认证逻辑
   - 直接调用API接口
   - 使用Mock数据进行测试

3. **集成测试**
   ```bash
   # 运行测试套件
   go test ./...
   
   # 运行集成测试
   ./scripts/test_app.sh
   ```

4. **切换到生产模式验证**
   ```bash
   export ENVIRONMENT=production
   unset SKIP_AUTH
   go run cmd/server/main.go
   ```

通过以上配置，您可以在开发环境中完全跳过身份认证和API网关限制，大大提高开发效率。

## 🛠️ 开发工具脚本

### 测试脚本

```bash
# 测试后端开发模式
./scripts/test_dev_mode.sh

# 测试前端开发模式
./scripts/test_frontend_dev_mode.sh

# 测试完整的全栈开发环境
./scripts/test_frontend_dev_mode.sh && ./scripts/test_dev_mode.sh
```

### 管理脚本

```bash
# 启动全栈开发环境
./scripts/dev_full_stack.sh

# 停止所有开发服务器
./scripts/stop_dev_servers.sh

# 停止服务器并清理日志
./scripts/stop_dev_servers.sh --clean-logs

# 强制停止所有相关进程
./scripts/stop_dev_servers.sh --force
```

### 日志查看

```bash
# 查看后端日志
tail -f logs/backend.log

# 查看前端日志
tail -f logs/frontend.log

# 同时查看两个日志
tail -f logs/backend.log logs/frontend.log
```

## 📋 开发环境检查清单

在开始开发前，请确保：

- [ ] 已安装 Go 1.19+
- [ ] 已安装 Node.js 18+
- [ ] 已安装 npm 或 yarn
- [ ] 已配置 `.env.development` 文件
- [ ] 已配置 `web/frontend/.env.development` 文件
- [ ] 数据库服务正在运行
- [ ] Redis 服务正在运行（可选）

## 🔄 开发工作流程

1. **启动开发环境**
   ```bash
   ./scripts/dev_full_stack.sh
   ```

2. **访问应用**
   - 前端：http://localhost:3000
   - 后端：http://localhost:8080
   - 健康检查：http://localhost:8080/health

3. **开发和测试**
   - 前端支持热重载
   - 后端需要手动重启
   - 使用开发模式快速登录

4. **停止开发环境**
   ```bash
   ./scripts/stop_dev_servers.sh
   ```

这样，您就可以享受高效的全栈开发体验了！
