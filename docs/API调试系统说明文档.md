# API调试系统说明文档

## 概述

API调试系统是一个完整的后端API开发和调试工具，提供自动化的API文档生成、可视化调试界面和完整的请求测试功能。该系统专为开发环境设计，帮助开发者快速理解、测试和调试后端API接口。

## 核心功能

### 1. API文档自动生成

#### 功能特性
- **自动扫描**: 扫描Go源代码中的路由定义和处理器函数
- **注释解析**: 提取函数注释中的API文档信息（支持Swagger注解格式）
- **OpenAPI规范**: 生成标准的OpenAPI 3.0规范文档
- **多格式输出**: 支持JSON和YAML格式的文档输出
- **实时更新**: 支持手动和自动刷新文档内容

#### 技术实现
- 使用Go的AST（抽象语法树）解析源代码
- 支持Gin框架的路由定义识别
- 自动提取HTTP方法、路径、参数和响应信息
- 智能分类和标签生成

#### 使用方式
```go
// 创建API文档服务
config := apidoc.DefaultServiceConfig()
service := apidoc.NewService(config, logger)

// 生成文档
spec, err := service.GenerateDocumentation(ctx)
```

### 2. 可视化调试界面

#### 界面特性
- **现代化UI**: 基于HTML5和CSS3的响应式设计
- **直观操作**: 简单易用的表单界面
- **实时预览**: 即时显示请求和响应信息
- **历史记录**: 保存和管理调试历史
- **模板支持**: 根据API文档自动生成请求模板

#### 功能模块
1. **请求构建器**
   - HTTP方法选择（GET、POST、PUT、DELETE等）
   - URL路径输入
   - 请求头配置
   - 请求体编辑（支持JSON格式）
   - 参数验证和提示

2. **响应查看器**
   - 状态码显示
   - 响应头信息
   - 响应体格式化显示
   - 错误信息展示
   - 性能指标（响应时间、数据大小）

3. **历史管理**
   - 请求历史记录
   - 快速重放功能
   - 历史搜索和过滤
   - 批量操作支持

### 3. API调试功能

#### 核心能力
- **HTTP客户端**: 内置高性能HTTP客户端
- **认证支持**: 支持Bearer Token、API Key等认证方式
- **请求拦截**: 可配置请求和响应拦截器
- **错误处理**: 完善的错误处理和重试机制
- **并发支持**: 支持并发请求测试

#### 高级特性
- **请求模板**: 基于API文档自动生成请求模板
- **参数验证**: 根据API规范验证请求参数
- **响应验证**: 验证响应格式和内容
- **性能监控**: 记录请求耗时和性能指标

## 系统架构

### 模块结构

```
internal/
├── apidoc/          # API文档生成模块
│   ├── scanner.go   # 代码扫描器
│   ├── openapi.go   # OpenAPI规范生成器
│   ├── service.go   # 文档服务
│   └── logger.go    # 日志适配器
├── debug/           # API调试模块
│   └── service.go   # 调试服务
├── apidebug/        # 系统集成模块
│   └── service.go   # 主服务
└── handlers/        # HTTP处理器
    ├── apidoc.go    # 文档处理器
    ├── debug.go     # 调试处理器
    └── apidebug.go  # 系统处理器
```

### 数据流程

1. **文档生成流程**
   ```
   源代码 → AST解析 → 路由提取 → 注释解析 → OpenAPI生成 → 文档输出
   ```

2. **调试请求流程**
   ```
   用户输入 → 请求构建 → HTTP发送 → 响应处理 → 结果展示 → 历史保存
   ```

3. **系统监控流程**
   ```
   状态收集 → 数据聚合 → 指标计算 → 状态展示 → 实时更新
   ```

## API接口文档

### 文档相关接口

#### 获取OpenAPI规范
```http
GET /api/v1/docs/openapi
```
返回完整的OpenAPI 3.0规范文档。

#### 获取API端点列表
```http
GET /api/v1/docs/endpoints?page=1&size=20&method=GET&tag=用户
```
获取分页的API端点列表，支持方法和标签过滤。

#### 获取文档统计信息
```http
GET /api/v1/docs/stats
```
返回API文档的统计信息，包括端点数量、方法分布等。

#### 刷新文档
```http
POST /api/v1/docs/refresh
```
手动触发文档重新生成。

### 调试相关接口

#### 发送调试请求
```http
POST /api/v1/debug/request
Content-Type: application/json

{
  "method": "GET",
  "url": "/api/v1/users",
  "headers": {
    "Authorization": "Bearer token"
  },
  "body": null,
  "description": "获取用户列表",
  "tags": ["测试"],
  "save_to_history": true
}
```

#### 获取请求历史
```http
GET /api/v1/debug/history?limit=20&offset=0
```

#### 清空历史记录
```http
DELETE /api/v1/debug/history
```

#### 获取端点模板
```http
GET /api/v1/debug/template?method=GET&path=/api/v1/users
```

### 系统状态接口

#### 获取系统状态
```http
GET /api/v1/system/status
```

#### 获取系统配置
```http
GET /api/v1/system/config
```

## 配置说明

### 服务配置

```go
type Config struct {
    // API文档配置
    ProjectRoot     string        // 项目根目录
    OutputDir       string        // 文档输出目录
    CacheEnabled    bool          // 是否启用缓存
    CacheTTL        time.Duration // 缓存过期时间
    AutoRefresh     bool          // 是否自动刷新
    RefreshInterval time.Duration // 刷新间隔
    
    // 调试配置
    BaseURL         string        // 基础URL
    RequestTimeout  time.Duration // 请求超时时间
    MaxHistorySize  int           // 最大历史记录数
    DefaultHeaders  map[string]string // 默认请求头
    
    // 生成器配置
    APITitle       string // API标题
    APIDescription string // API描述
    APIVersion     string // API版本
    ServerURL      string // 服务器URL
    ContactName    string // 联系人姓名
    ContactEmail   string // 联系人邮箱
}
```

### 默认配置

```go
config := apidebug.DefaultConfig()
// 项目根目录: "."
// 输出目录: "docs/api"
// 缓存启用: true
// 缓存TTL: 5分钟
// 基础URL: "http://localhost:8080"
// 请求超时: 30秒
// 最大历史: 1000条
```

## 使用指南

### 快速开始

1. **集成到现有项目**
   ```go
   import "ai-text-game-iam-npc/internal/routes"
   
   // 在路由设置中添加
   err := routes.RegisterAPIDebugRoutes(router, logger)
   if err != nil {
       log.Fatal("注册API调试路由失败:", err)
   }
   ```

2. **独立运行**
   ```go
   import "ai-text-game-iam-npc/internal/routes"
   
   // 创建独立服务器
   server, err := routes.CreateStandaloneAPIDebugServer(logger)
   if err != nil {
       log.Fatal("创建服务器失败:", err)
   }
   
   server.Run(":8081")
   ```

3. **访问界面**
   - API文档: http://localhost:8080/api/v1/docs
   - Swagger UI: http://localhost:8080/api/v1/docs/swagger
   - 调试界面: http://localhost:8080/debug
   - 系统状态: http://localhost:8080/system

### 最佳实践

1. **API注释规范**
   ```go
   // GetUsers 获取用户列表
   // @Summary 获取用户列表
   // @Description 获取所有用户的分页列表
   // @Tags 用户管理
   // @Accept json
   // @Produce json
   // @Param page query int false "页码" default(1)
   // @Param size query int false "每页大小" default(20)
   // @Success 200 {object} Response{data=[]User}
   // @Failure 400 {object} Response
   // @Router /api/v1/users [get]
   func GetUsers(c *gin.Context) {
       // 实现逻辑
   }
   ```

2. **错误处理**
   - 使用统一的错误响应格式
   - 提供详细的错误信息和错误代码
   - 记录调试日志便于问题排查

3. **性能优化**
   - 启用文档缓存减少重复扫描
   - 合理设置历史记录大小限制
   - 使用异步刷新避免阻塞主流程

## 故障排除

### 常见问题

1. **文档生成失败**
   - 检查项目根目录配置是否正确
   - 确认源代码文件存在且可读
   - 查看日志中的详细错误信息

2. **调试请求失败**
   - 验证目标服务器是否可访问
   - 检查请求URL和参数格式
   - 确认认证信息是否正确

3. **界面无法访问**
   - 检查服务器是否正常启动
   - 确认端口是否被占用
   - 验证路由注册是否成功

### 调试技巧

1. **启用详细日志**
   ```go
   logger.SetLevel(logger.DebugLevel)
   ```

2. **检查系统状态**
   ```bash
   curl http://localhost:8080/api/v1/system/status
   ```

3. **验证API文档**
   ```bash
   curl http://localhost:8080/api/v1/docs/openapi
   ```

## 扩展开发

### 自定义扫描器

```go
type CustomScanner struct {
    *apidoc.Scanner
}

func (s *CustomScanner) ScanCustomFormat(file string) error {
    // 实现自定义扫描逻辑
    return nil
}
```

### 自定义处理器

```go
func CustomDebugHandler(service *debug.Service) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 实现自定义调试逻辑
    }
}
```

### 插件系统

系统支持通过接口扩展功能：

```go
type Plugin interface {
    Name() string
    Initialize(config map[string]interface{}) error
    Process(request *DebugRequest) (*DebugResponse, error)
}
```

## 版本历史

- **v1.0.0**: 初始版本，包含基础的文档生成和调试功能
- 后续版本将添加更多高级特性和性能优化

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进本项目。在贡献代码前，请确保：

1. 代码符合项目的编码规范
2. 添加了相应的单元测试
3. 更新了相关文档
4. 通过了所有测试用例

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

*本文档最后更新时间: 2025-08-07*
