# 开发环境日志配置指南

## 概述

本指南详细说明了AI文本游戏项目开发环境的日志配置和使用方法，帮助开发者获得完整的调试信息。

## 🚀 快速开始

### 方式一：使用详细日志版本启动脚本（推荐）

```bash
# 启动详细日志版本的开发环境
./scripts/dev_full_stack_verbose.sh
```

**特性：**
- ✅ 前后端所有日志实时显示在控制台
- ✅ 日志同时保存到文件（logs/backend.log, logs/frontend.log）
- ✅ 彩色日志输出，便于区分前后端
- ✅ 详细的启动过程信息
- ✅ 环境变量配置显示

### 方式二：使用标准启动脚本

```bash
# 启动标准开发环境
./scripts/dev_full_stack.sh
```

**特性：**
- ✅ 改进的日志输出格式
- ✅ 前后端日志带颜色标识
- ✅ 基本的调试信息

## 📝 日志配置详解

### 后端日志配置

#### 1. 环境变量控制

```bash
# 启用调试日志
export DEV_ENABLE_DEBUG_LOGS=true
export ENVIRONMENT=development
export SKIP_AUTH=true
```

#### 2. Gin 框架日志模式

- **开发模式**：`gin.DebugMode` - 显示详细的请求日志
- **生产模式**：`gin.ReleaseMode` - 只显示基本信息

#### 3. 自定义日志中间件

后端使用自定义的调试日志中间件，显示：
- 请求时间戳
- HTTP 方法和路径
- 客户端IP
- 响应状态码
- 请求延迟
- 用户代理信息

#### 4. 日志格式示例

```
🐛 [DEBUG] 开发模式已启用，详细日志输出已开启
🐛 [DEBUG] 环境变量: ENVIRONMENT=development, DEV_ENABLE_DEBUG_LOGS=true
🌐 [2025-08-05 01:37:32] GET /health 127.0.0.1 200 32.673µs HTTP/1.1 | 用户代理: curl/7.68.0
```

### 前端日志配置

#### 1. Vite 开发服务器配置

```typescript
// vite.config.ts
export default defineConfig(({ mode }) => ({
  server: {
    logLevel: mode === 'development' ? 'info' : 'warn',
    proxy: {
      '/api': {
        // 启用代理日志
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('🔄 [PROXY REQ]', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('✅ [PROXY RES]', req.method, req.url, proxyRes.statusCode);
          });
        },
      },
    },
  },
}));
```

#### 2. 环境变量配置

```bash
# .env.development
NODE_ENV=development
VITE_DEV_MODE=true
VITE_DEBUG_LOGS=true
VITE_VERBOSE_LOGS=true
```

#### 3. NPM 脚本配置

```json
{
  "scripts": {
    "dev": "vite --logLevel info",
    "dev:debug": "vite --logLevel info --debug"
  }
}
```

## 🔍 日志查看方法

### 实时查看日志

```bash
# 查看后端日志
tail -f logs/backend.log

# 查看前端日志
tail -f logs/frontend.log

# 同时查看所有日志
tail -f logs/*.log
```

### 日志文件位置

- **后端日志**：`logs/backend.log`
- **前端日志**：`logs/frontend.log`

### 控制台日志格式

- **后端日志**：紫色 `[BACKEND]` 标签
- **前端日志**：青色 `[FRONTEND]` 标签
- **系统信息**：蓝色 `[INFO]` 标签
- **成功信息**：绿色 `[SUCCESS]` 标签
- **警告信息**：黄色 `[WARNING]` 标签
- **错误信息**：红色 `[ERROR]` 标签

## 🛠️ 故障排除

### 问题1：看不到后端调试日志

**解决方案：**
1. 确认环境变量设置：
   ```bash
   echo $DEV_ENABLE_DEBUG_LOGS  # 应该输出 true
   echo $ENVIRONMENT            # 应该输出 development
   ```

2. 使用详细日志版本启动脚本：
   ```bash
   ./scripts/dev_full_stack_verbose.sh
   ```

### 问题2：前端日志不够详细

**解决方案：**
1. 使用调试模式启动：
   ```bash
   cd web/frontend
   npm run dev:debug
   ```

2. 检查环境变量：
   ```bash
   cat web/frontend/.env.development
   ```

### 问题3：日志输出混乱

**解决方案：**
1. 使用详细日志版本，它会将日志保存到文件：
   ```bash
   ./scripts/dev_full_stack_verbose.sh
   ```

2. 在另一个终端查看特定日志：
   ```bash
   tail -f logs/backend.log
   ```

## 📊 日志级别说明

### 后端日志级别

- **DEBUG**：详细的调试信息，包括请求详情
- **INFO**：一般信息，如服务启动、配置加载
- **WARN**：警告信息，不影响功能但需要注意
- **ERROR**：错误信息，可能影响功能

### 前端日志级别

- **info**：显示详细的构建和热重载信息
- **warn**：只显示警告和错误
- **error**：只显示错误信息
- **silent**：不显示任何日志

## 🎯 最佳实践

### 1. 开发时使用详细日志

```bash
# 推荐：使用详细日志版本
./scripts/dev_full_stack_verbose.sh
```

### 2. 调试API问题时查看代理日志

前端的API代理会显示所有请求和响应：
```
🔄 [PROXY REQ] GET /api/v1/user/profile → http://localhost:8080/api/v1/user/profile
✅ [PROXY RES] GET /api/v1/user/profile ← 200
```

### 3. 使用日志文件进行问题分析

```bash
# 搜索特定错误
grep -i error logs/backend.log

# 查看最近的API调用
grep "GET\|POST\|PUT\|DELETE" logs/backend.log | tail -20
```

### 4. 监控性能问题

后端日志包含请求延迟信息，可以用来监控性能：
```bash
# 查找慢请求（延迟超过100ms）
grep -E "[0-9]+\.[0-9]+ms" logs/backend.log | grep -v "µs"
```

## 🔧 自定义日志配置

### 修改日志级别

编辑 `pkg/logger/logger.go` 中的日志级别：
```go
// 创建日志实例时指定级别
logger := logger.New("debug") // debug, info, warn, error
```

### 添加自定义日志格式

在 `cmd/simple-server/main.go` 中修改 `debugLoggingMiddleware` 函数。

### 配置日志轮转

对于生产环境，可以配置日志轮转：
```bash
# 使用 logrotate 配置
sudo nano /etc/logrotate.d/ai-text-game
```

## 📚 相关文档

- [开发环境认证跳过指南](./开发环境认证跳过指南.md)
- [部署指南](./DEPLOYMENT.md)
- [API文档](./API.md)
