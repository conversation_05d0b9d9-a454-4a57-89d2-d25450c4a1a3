# AI Windmill 接口使用说明

## 简介

AI Windmill 接口是一个基于 Windmill 平台的 JSON 结构化输出系统，专为 AI 文本游戏设计。它提供了完整的 AI 内容生成、数据验证、异步处理和实时同步功能。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   AI 服务       │    │   Windmill API  │
│                 │    │                 │    │                 │
│ - 用户界面      │◄──►│ - 请求处理      │◄──►│ - AI 模型调用   │
│ - WebSocket     │    │ - 数据验证      │    │ - 结构化输出    │
│ - 状态管理      │    │ - 异步任务      │    │ - 错误处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   数据库        │
                       │                 │
                       │ - 场景数据      │
                       │ - 角色数据      │
                       │ - 事件数据      │
                       └─────────────────┘
```

## 快速开始

### 1. 环境配置

在 `config.yaml` 中配置 Windmill 相关参数：

```yaml
ai:
  base_url: "https://your-windmill-instance.com"
  token: "your-windmill-token"
  timeout: 30s
  max_retries: 3
  retry_delay: 1s
  windmill:
    workspace: "your-workspace"
    default_model: "gemini-1.5-pro"
```

### 2. 初始化服务

```go
package main

import (
    "ai-text-game-iam-npc/internal/ai"
    "ai-text-game-iam-npc/internal/config"
    "ai-text-game-iam-npc/pkg/logger"
)

func main() {
    // 加载配置
    cfg := config.Load()
    
    // 创建日志器
    log := logger.New()
    
    // 创建数据库连接
    db := setupDatabase()
    
    // 创建 AI 服务
    aiService := ai.NewService(cfg, db, log)
    
    // 启动服务
    startServer(aiService)
}
```

### 3. 基本使用

#### 同步生成内容

```go
// 创建生成请求
req := &ai.GenerateRequest{
    Type:    "scene",
    Prompt:  "生成一个神秘的魔法森林场景",
    WorldID: &worldID,
    UserID:  &userID,
    Context: map[string]interface{}{
        "world_theme": "奇幻",
        "target_atmosphere": "神秘",
        "danger_level": 3,
    },
}

// 生成内容
response, err := aiService.GenerateContent(ctx, req)
if err != nil {
    log.Error("内容生成失败", "error", err)
    return
}

// 使用生成的数据
fmt.Printf("生成的场景: %s\n", response.StructuredData["name"])
```

#### 异步生成内容

```go
// 提交异步任务
task, err := aiService.GenerateContentAsync(req)
if err != nil {
    log.Error("提交异步任务失败", "error", err)
    return
}

// 监听任务完成
go func() {
    for {
        currentTask, err := aiService.GetAsyncTask(task.ID)
        if err != nil {
            break
        }
        
        if currentTask.Status == ai.TaskStatusCompleted {
            fmt.Printf("任务完成: %v\n", currentTask.Response)
            break
        } else if currentTask.Status == ai.TaskStatusFailed {
            fmt.Printf("任务失败: %s\n", currentTask.Error)
            break
        }
        
        time.Sleep(1 * time.Second)
    }
}()
```

## 内容类型详解

### 1. 场景生成 (scene)

场景是游戏世界的基本组成单元，包含环境描述、连接信息和互动元素。

**使用示例：**
```go
req := &ai.GenerateRequest{
    Type:   "scene",
    Prompt: "生成一个古老的图书馆场景",
    Context: map[string]interface{}{
        "world_theme": "奇幻",
        "scene_type": "室内",
        "target_atmosphere": "宁静",
        "connected_scenes": []string{"大厅", "密室"},
    },
}
```

**生成的数据结构：**
- `name` - 场景名称
- `description` - 详细描述
- `atmosphere` - 氛围类型
- `scene_type` - 场景类型
- `key_features` - 关键特征
- `possible_actions` - 可能的行动
- `connections` - 场景连接
- `danger_level` - 危险等级

### 2. 角色生成 (character)

角色包含完整的属性、背景和行为模式定义。

**使用示例：**
```go
req := &ai.GenerateRequest{
    Type:   "character",
    Prompt: "生成一个智慧的老法师角色",
    Context: map[string]interface{}{
        "character_role": "导师",
        "target_personality": "智慧、神秘",
        "location_context": "魔法塔",
    },
}
```

**生成的数据结构：**
- `name` - 角色名称
- `description` - 基本描述
- `character_type` - 角色类型
- `personality` - 性格特征
- `background` - 背景故事
- `skills` - 技能列表
- `attributes` - 数值属性
- `appearance` - 外观描述

### 3. 事件生成 (event)

事件定义游戏中发生的各种情况和玩家选择。

**使用示例：**
```go
req := &ai.GenerateRequest{
    Type:   "event",
    Prompt: "生成一个神秘商人到访的事件",
    Context: map[string]interface{}{
        "event_category": "随机遭遇",
        "target_difficulty": "普通",
        "involved_characters": []string{"神秘商人", "村民"},
    },
}
```

## 高级功能

### 1. 数据验证

系统提供完整的数据验证功能，确保生成的内容符合预定义的 Schema。

```go
// 获取 Schema
schema, err := aiService.GetSchemaForContentType("scene")
if err != nil {
    return err
}

// 创建验证器
validator := ai.NewSchemaValidator(schema)

// 验证数据
result := validator.Validate(generatedData)
if !result.Valid {
    for _, err := range result.Errors {
        log.Warn("验证错误", "field", err.Field, "message", err.Message)
    }
}
```

### 2. 数据转换

将 AI 生成的数据转换为项目的数据模型。

```go
// 创建转换器
transformer := ai.NewDataTransformer(log)

// 转换场景数据
scene, err := transformer.TransformToScene(generatedData, worldID)
if err != nil {
    return err
}

// 保存到数据库
if err := db.Create(scene).Error; err != nil {
    return err
}
```

### 3. 实时同步

使用 WebSocket 实现前端实时状态同步。

```go
// 获取前端同步管理器
frontendSync := aiService.GetFrontendSyncManager()

// 创建连接
conn := frontendSync.AddConnection(userID, worldID)

// 发送消息
message := frontendSync.CreateTaskStatusMessage(
    taskID, 
    ai.TaskStatusCompleted, 
    100.0,
    map[string]interface{}{
        "content": generatedContent,
    },
)

frontendSync.SendToUser(userID, message)
```

### 4. 配置管理

动态调整 AI 生成参数和模型配置。

```go
// 获取配置管理器
configManager := aiService.GetConfigManager()

// 更新模型配置
modelConfig := &ai.ModelConfig{
    Name:        "gemini-1.5-pro",
    MaxTokens:   8192,
    Temperature: 0.7,
    TopP:        0.9,
    Timeout:     30 * time.Second,
}

err := configManager.UpdateModelConfig("gemini-1.5-pro", modelConfig)
if err != nil {
    return err
}

// 更新内容类型配置
contentConfig := &ai.ContentTypeConfig{
    Model:           "gemini-1.5-pro",
    Priority:        8,
    CacheEnabled:    true,
    CacheTTL:        1 * time.Hour,
    ValidationLevel: "strict",
}

err = configManager.UpdateContentTypeConfig("scene", contentConfig)
```

## 错误处理

### 1. 错误分类

系统将错误分为以下类型：
- `network` - 网络错误
- `auth` - 认证错误
- `rate_limit` - 频率限制
- `server` - 服务器错误
- `timeout` - 超时错误
- `validation` - 验证错误

### 2. 重试机制

```go
// 配置重试策略
retryStrategy := &ai.RetryStrategy{
    MaxRetries:    3,
    BaseDelay:     1 * time.Second,
    MaxDelay:      30 * time.Second,
    BackoffFactor: 2.0,
    JitterEnabled: true,
    RetryableErrors: []ai.ErrorType{
        ai.ErrorTypeNetwork,
        ai.ErrorTypeServer,
        ai.ErrorTypeTimeout,
    },
}

// 创建错误处理器
errorHandler := ai.NewErrorHandler(retryStrategy, log)

// 执行带重试的操作
err := errorHandler.ExecuteWithRetry(ctx, func() error {
    return performAIRequest()
})
```

### 3. 错误监控

```go
// 记录错误
errorHandler.LogError(err, map[string]interface{}{
    "operation": "generate_scene",
    "user_id":   userID,
    "world_id":  worldID,
})

// 获取错误统计
stats := errorHandler.GetRetryStatistics()
log.Info("重试统计", "stats", stats)
```

## 性能优化

### 1. 缓存策略

```go
// 启用内容类型缓存
contentConfig.CacheEnabled = true
contentConfig.CacheTTL = 1 * time.Hour

// 对话类型使用较短的缓存时间
dialogueConfig.CacheTTL = 30 * time.Minute
```

### 2. 异步处理

```go
// 对于复杂内容，使用异步生成
if contentType == "world_description" || contentType == "quest" {
    task, err := aiService.GenerateContentAsync(req)
    return task, err
} else {
    response, err := aiService.GenerateContent(ctx, req)
    return response, err
}
```

### 3. 连接池管理

```go
// 配置连接池
frontendSync.SetMaxConnections(1000)
frontendSync.SetMessageBuffer(100)

// 定期清理过期连接
go func() {
    ticker := time.NewTicker(5 * time.Minute)
    defer ticker.Stop()
    
    for range ticker.C {
        cleaned := frontendSync.Cleanup(10 * time.Minute)
        log.Info("清理过期连接", "count", cleaned)
    }
}()
```

## 监控和调试

### 1. 日志记录

```go
// 启用详细日志
log.SetLevel(logger.DebugLevel)

// 记录关键操作
log.Info("开始生成内容", 
    "type", req.Type, 
    "user_id", req.UserID,
    "world_id", req.WorldID)
```

### 2. 指标收集

```go
// 获取任务统计
taskStats := aiService.GetAsyncTaskStatistics()
log.Info("任务统计", "stats", taskStats)

// 获取连接统计
connStats := aiService.GetConnectionStatistics()
log.Info("连接统计", "stats", connStats)

// 获取使用统计
usageStats, err := aiService.GetUsageStatistics()
if err == nil {
    log.Info("使用统计", "stats", usageStats)
}
```

### 3. 健康检查

```go
// 检查服务状态
func healthCheck(aiService *ai.Service) error {
    // 检查配置
    config := aiService.GetAIConfiguration()
    if config["total_models"].(int) == 0 {
        return fmt.Errorf("没有可用的模型")
    }
    
    // 检查连接
    stats := aiService.GetConnectionStatistics()
    if stats["total_connections"].(int) > stats["max_connections"].(int) {
        return fmt.Errorf("连接数超限")
    }
    
    return nil
}
```

## 故障排除

### 常见问题

1. **生成超时**
   - 检查网络连接
   - 调整超时配置
   - 使用异步生成

2. **验证失败**
   - 检查 Schema 定义
   - 调整提示词
   - 降低验证级别

3. **连接断开**
   - 检查 WebSocket 配置
   - 实现重连机制
   - 监控连接状态

4. **内存泄漏**
   - 定期清理任务
   - 限制连接数
   - 监控资源使用

### 调试技巧

1. **启用详细日志**
2. **使用测试模式**
3. **监控系统指标**
4. **分析错误模式**
5. **性能分析**
