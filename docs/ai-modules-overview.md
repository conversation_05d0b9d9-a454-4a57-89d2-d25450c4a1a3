# AI 模块架构说明

## 模块概览

AI Windmill 接口系统由多个核心模块组成，每个模块负责特定的功能领域。以下是各模块的详细说明。

## 核心模块

### 1. Schema 管理模块 (`schema.go`)

**功能：** JSON Schema 定义和构建

**主要组件：**
- `JSONSchema` - Schema 结构定义
- `SchemaBuilder` - Schema 构建器
- `GameSchemaRegistry` - 游戏 Schema 注册表

**核心功能：**
```go
// 创建 Schema
schema := NewSchemaBuilder().
    Object().
    Property("name", StringSchema("名称")).
    Required("name").
    Build()

// 转换为 Map 格式
schemaMap := schema.ToMap()

// 注册表使用
registry := NewGameSchemaRegistry()
sceneSchema, err := registry.GetSchema("scene")
```

**设计特点：**
- 链式调用构建 Schema
- 支持复杂嵌套结构
- 自动类型验证
- 可扩展的注册表机制

### 2. 游戏 Schema 定义模块 (`game_schemas.go`)

**功能：** 预定义的游戏内容 Schema

**支持的内容类型：**
- 场景 (Scene)
- 角色 (Character)  
- 事件 (Event)
- 对话 (Dialogue)
- 物品 (Item)
- 世界描述 (World Description)
- 任务 (Quest)
- 环境效果 (Environment Effect)

**Schema 特点：**
```go
// 场景 Schema 示例
sceneSchema := NewSchemaBuilder().Object().
    Property("name", StringSchema("场景名称").MinLength(2).MaxLength(50)).
    Property("description", StringSchema("场景描述").MinLength(50).MaxLength(500)).
    Property("atmosphere", EnumSchema("氛围", "神秘", "温馨", "紧张")).
    Required("name", "description", "atmosphere").
    Build()
```

### 3. 数据验证模块 (`validator.go`)

**功能：** JSON Schema 数据验证

**主要组件：**
- `SchemaValidator` - 验证器
- `ValidationResult` - 验证结果
- `ValidationError` - 验证错误

**验证功能：**
```go
validator := NewSchemaValidator(schema)
result := validator.Validate(data)

if !result.Valid {
    for _, err := range result.Errors {
        fmt.Printf("字段 %s 验证失败: %s\n", err.Field, err.Message)
    }
}
```

**验证类型：**
- 数据类型检查
- 字符串长度验证
- 数值范围验证
- 枚举值验证
- 数组长度验证
- 必需字段检查

### 4. 数据转换模块 (`transformer.go`)

**功能：** AI 生成数据到数据模型的转换

**主要组件：**
- `DataTransformer` - 数据转换器
- 类型映射函数
- 数据清洗和格式化

**转换示例：**
```go
transformer := NewDataTransformer(log)

// 转换场景数据
scene, err := transformer.TransformToScene(aiData, worldID)

// 转换角色数据  
character, err := transformer.TransformToCharacter(aiData, worldID, userID)

// 转换事件数据
event, err := transformer.TransformToEvent(aiData, worldID, creatorID)
```

**转换特性：**
- 中英文类型映射
- 数据结构适配
- 默认值填充
- 错误处理和验证

### 5. 提示词模板模块 (`prompt_templates.go`)

**功能：** 智能提示词生成和管理

**主要组件：**
- `PromptTemplate` - 提示词模板
- `PromptTemplateManager` - 模板管理器
- 模板渲染引擎

**模板结构：**
```go
type PromptTemplate struct {
    SystemInstruction string                 // 系统指令
    PromptTemplate    string                 // 提示词模板
    DefaultContext    map[string]interface{} // 默认上下文
    Examples          []string               // 示例
}
```

**使用示例：**
```go
manager := NewPromptTemplateManager()
prompt, systemInst, err := manager.BuildPrompt(
    "scene", 
    "生成森林场景", 
    map[string]interface{}{
        "world_theme": "奇幻",
        "target_atmosphere": "神秘",
    },
)
```

### 6. 错误处理模块 (`error_handler.go`)

**功能：** 智能错误处理和重试机制

**主要组件：**
- `ErrorHandler` - 错误处理器
- `RetryStrategy` - 重试策略
- `AIError` - 结构化错误

**错误分类：**
```go
const (
    ErrorTypeNetwork    ErrorType = "network"
    ErrorTypeAuth       ErrorType = "auth"
    ErrorTypeRateLimit  ErrorType = "rate_limit"
    ErrorTypeServer     ErrorType = "server"
    ErrorTypeTimeout    ErrorType = "timeout"
    ErrorTypeValidation ErrorType = "validation"
)
```

**重试机制：**
```go
strategy := &RetryStrategy{
    MaxRetries:    3,
    BaseDelay:     1 * time.Second,
    BackoffFactor: 2.0,
    JitterEnabled: true,
}

errorHandler := NewErrorHandler(strategy, log)
err := errorHandler.ExecuteWithRetry(ctx, operation)
```

### 7. 配置管理模块 (`config_manager.go`)

**功能：** 动态配置管理和参数调优

**主要组件：**
- `AIConfigManager` - 配置管理器
- `ModelConfig` - 模型配置
- `ContentTypeConfig` - 内容类型配置

**配置类型：**
```go
// 模型配置
type ModelConfig struct {
    Name            string
    MaxTokens       int
    Temperature     float64
    TopP            float64
    Timeout         time.Duration
}

// 内容类型配置
type ContentTypeConfig struct {
    Model           string
    Priority        int
    CacheEnabled    bool
    CacheTTL        time.Duration
    ValidationLevel string
}
```

### 8. 异步任务模块 (`async_manager.go`)

**功能：** 异步任务管理和执行

**主要组件：**
- `AsyncTaskManager` - 任务管理器
- `AsyncTask` - 异步任务
- 工作协程池

**任务状态：**
```go
const (
    TaskStatusPending   TaskStatus = "pending"
    TaskStatusRunning   TaskStatus = "running"
    TaskStatusCompleted TaskStatus = "completed"
    TaskStatusFailed    TaskStatus = "failed"
    TaskStatusCancelled TaskStatus = "cancelled"
)
```

**使用示例：**
```go
manager := NewAsyncTaskManager(db, aiService, 5, 100, log)
task, err := manager.SubmitTask(request)

// 添加回调
manager.AddTaskCallback(task.ID, func(task *AsyncTask) {
    if task.Status == TaskStatusCompleted {
        handleTaskCompletion(task)
    }
})
```

### 9. 数据流处理模块 (`data_flow.go`)

**功能：** 事件驱动的数据流处理

**主要组件：**
- `DataFlowProcessor` - 数据流处理器
- `DataFlowEvent` - 数据流事件
- 发布-订阅机制

**事件类型：**
- `ai_content_generated` - AI 内容生成完成
- `scene_created` - 场景创建
- `character_created` - 角色创建
- `event_created` - 事件创建
- `validation_failed` - 验证失败

**使用示例：**
```go
processor := NewDataFlowProcessor(db, transformer, log)
processor.Start()

// 订阅事件
eventChan := processor.Subscribe("scene_created")
go func() {
    for event := range eventChan {
        handleSceneCreated(event)
    }
}()

// 发布事件
event := &DataFlowEvent{
    Type: "ai_content_generated",
    Data: generatedData,
}
processor.PublishEvent(event)
```

### 10. 前端同步模块 (`frontend_sync.go`)

**功能：** 实时前端状态同步

**主要组件：**
- `FrontendSyncManager` - 同步管理器
- `WebSocketConnection` - WebSocket 连接
- `FrontendMessage` - 前端消息

**连接管理：**
```go
manager := NewFrontendSyncManager(log)
conn := manager.AddConnection(userID, worldID)

// 发送消息
message := manager.CreateTaskStatusMessage(taskID, status, progress, data)
manager.SendToUser(userID, message)

// 广播消息
manager.BroadcastToAll(systemMessage)
```

**消息类型：**
- 任务状态更新
- 内容生成完成
- 错误通知
- 系统消息

### 11. 主服务模块 (`service.go`)

**功能：** 核心 AI 服务协调

**主要功能：**
- 模块集成和协调
- API 接口提供
- 生命周期管理
- 统计和监控

**服务结构：**
```go
type Service struct {
    config          *config.Config
    db              *gorm.DB
    windmillClient  *WindmillClient
    schemaRegistry  *GameSchemaRegistry
    promptManager   *PromptTemplateManager
    configManager   *AIConfigManager
    transformer     *DataTransformer
    asyncManager    *AsyncTaskManager
    dataFlow        *DataFlowProcessor
    frontendSync    *FrontendSyncManager
    logger          logger.Logger
}
```

## 模块交互流程

### 1. 同步内容生成流程

```
用户请求 → Schema验证 → 提示词构建 → Windmill调用 → 数据验证 → 数据转换 → 数据库保存 → 返回结果
```

### 2. 异步内容生成流程

```
用户请求 → 任务创建 → 队列提交 → 工作协程处理 → 数据流事件 → 前端通知 → 数据库保存
```

### 3. 错误处理流程

```
错误发生 → 错误分类 → 重试判断 → 重试执行 → 错误记录 → 用户通知
```

## 扩展指南

### 1. 添加新的内容类型

1. 在 `game_schemas.go` 中定义 Schema
2. 在 `transformer.go` 中添加转换逻辑
3. 在 `prompt_templates.go` 中添加提示词模板
4. 更新相关测试

### 2. 自定义验证规则

1. 扩展 `JSONSchema` 结构
2. 在 `validator.go` 中实现验证逻辑
3. 更新 Schema 构建器

### 3. 添加新的错误类型

1. 在 `error_handler.go` 中定义错误类型
2. 实现错误分类逻辑
3. 配置重试策略

### 4. 扩展配置选项

1. 在 `config_manager.go` 中添加配置结构
2. 实现配置验证
3. 更新默认配置

## 最佳实践

### 1. 模块设计原则

- **单一职责** - 每个模块专注特定功能
- **松耦合** - 模块间通过接口交互
- **高内聚** - 相关功能集中在同一模块
- **可扩展** - 支持功能扩展和定制

### 2. 错误处理

- 使用结构化错误类型
- 实现智能重试机制
- 记录详细错误信息
- 提供用户友好的错误消息

### 3. 性能优化

- 使用异步处理复杂任务
- 实现智能缓存策略
- 优化数据库查询
- 监控系统性能指标

### 4. 测试策略

- 单元测试覆盖核心逻辑
- 集成测试验证模块协作
- 性能测试确保系统稳定
- 端到端测试验证用户体验
