#!/bin/bash

# 动态时间速率控制系统设置脚本
# 用法: ./setup_time_schedule.sh [world_id] [config_file]

set -e

# 配置
API_BASE="${API_BASE:-http://localhost:8080/api/v1/game}"
TOKEN="${AUTH_TOKEN:-your_auth_token_here}"
WORLD_ID="${1:-world-001}"
CONFIG_FILE="${2:-examples/time_schedule_config.json}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# API请求函数
api_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local response
    
    if [ -n "$data" ]; then
        response=$(curl -s -X "$method" "$API_BASE$endpoint" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -X "$method" "$API_BASE$endpoint" \
            -H "Authorization: Bearer $TOKEN")
    fi
    
    echo "$response"
}

# 检查API连接
check_api_connection() {
    log_info "检查API连接..."
    
    local response=$(api_request "GET" "/time-schedule/stats")
    local success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" = "true" ]; then
        log_success "API连接正常"
        return 0
    else
        log_error "API连接失败: $response"
        return 1
    fi
}

# 验证配置文件
validate_config() {
    log_info "验证配置文件: $CONFIG_FILE"
    
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        return 1
    fi
    
    # 检查JSON格式
    if ! jq empty "$CONFIG_FILE" 2>/dev/null; then
        log_error "配置文件JSON格式错误"
        return 1
    fi
    
    # 通过API验证配置
    local config_data=$(cat "$CONFIG_FILE")
    local response=$(api_request "POST" "/time-schedule/validate" "$config_data")
    local success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" = "true" ]; then
        log_success "配置文件验证通过"
        local schedule_count=$(echo "$response" | jq -r '.data.schedule_count')
        local current_rate=$(echo "$response" | jq -r '.data.current_rate')
        log_info "时间段数量: $schedule_count, 当前速率: $current_rate"
        return 0
    else
        log_error "配置验证失败: $(echo "$response" | jq -r '.message')"
        return 1
    fi
}

# 检查世界是否存在
check_world_exists() {
    log_info "检查世界是否存在: $WORLD_ID"
    
    local response=$(api_request "GET" "/worlds/$WORLD_ID/time-rate")
    local success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" = "true" ]; then
        log_success "世界存在"
        return 0
    else
        log_error "世界不存在或无权限访问: $WORLD_ID"
        return 1
    fi
}

# 应用配置
apply_config() {
    log_info "应用时间段配置到世界: $WORLD_ID"
    
    local config_data=$(cat "$CONFIG_FILE")
    local response=$(api_request "PUT" "/worlds/$WORLD_ID/time-schedule" "$config_data")
    local success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" = "true" ]; then
        log_success "配置应用成功"
        return 0
    else
        log_error "配置应用失败: $(echo "$response" | jq -r '.message')"
        return 1
    fi
}

# 启用时间段配置
enable_schedule() {
    log_info "启用时间段配置..."
    
    local response=$(api_request "POST" "/worlds/$WORLD_ID/time-schedule/enable")
    local success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" = "true" ]; then
        log_success "时间段配置已启用"
        return 0
    else
        log_warning "启用失败或已经启用: $(echo "$response" | jq -r '.message')"
        return 0  # 不作为错误处理
    fi
}

# 检查当前状态
check_current_status() {
    log_info "检查当前时间速率状态..."
    
    local response=$(api_request "GET" "/worlds/$WORLD_ID/time-rate")
    local success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" = "true" ]; then
        local time_rate=$(echo "$response" | jq -r '.data.time_rate')
        local active_schedule=$(echo "$response" | jq -r '.data.active_schedule.name // "默认"')
        local timestamp=$(echo "$response" | jq -r '.data.timestamp')
        
        log_success "当前状态:"
        echo "  时间速率: ${time_rate}x"
        echo "  活跃时间段: $active_schedule"
        echo "  检查时间: $timestamp"
        return 0
    else
        log_error "获取状态失败: $(echo "$response" | jq -r '.message')"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "动态时间速率控制系统设置脚本"
    echo ""
    echo "用法: $0 [world_id] [config_file]"
    echo ""
    echo "参数:"
    echo "  world_id     目标世界ID (默认: world-001)"
    echo "  config_file  配置文件路径 (默认: examples/time_schedule_config.json)"
    echo ""
    echo "环境变量:"
    echo "  API_BASE     API基础URL (默认: http://localhost:8080/api/v1/game)"
    echo "  AUTH_TOKEN   认证令牌"
    echo ""
    echo "示例:"
    echo "  $0 world-001 examples/time_schedule_config.json"
    echo "  AUTH_TOKEN=abc123 $0 world-002 examples/advanced_time_schedule_config.json"
}

# 主函数
main() {
    echo "========================================"
    echo "动态时间速率控制系统设置"
    echo "========================================"
    echo "世界ID: $WORLD_ID"
    echo "配置文件: $CONFIG_FILE"
    echo "API地址: $API_BASE"
    echo "========================================"
    
    # 检查依赖
    if ! command -v jq &> /dev/null; then
        log_error "需要安装 jq 工具"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        log_error "需要安装 curl 工具"
        exit 1
    fi
    
    # 执行设置步骤
    check_api_connection || exit 1
    validate_config || exit 1
    check_world_exists || exit 1
    apply_config || exit 1
    enable_schedule || exit 1
    check_current_status || exit 1
    
    echo "========================================"
    log_success "时间段配置设置完成！"
    echo "========================================"
}

# 处理命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main
        ;;
esac
