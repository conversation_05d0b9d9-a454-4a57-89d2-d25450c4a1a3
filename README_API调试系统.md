# API调试系统

一个完整的后端API开发和调试工具，提供自动化的API文档生成、可视化调试界面和完整的请求测试功能。

## 🚀 快速开始

### 运行演示

```bash
# 构建并运行演示程序
go run cmd/apidebug-demo/main.go
```

演示程序将启动在 `http://localhost:8081`，提供以下功能：

- **系统状态**: http://localhost:8081/system
- **API文档**: http://localhost:8081/api/v1/docs  
- **Swagger UI**: http://localhost:8081/api/v1/docs/swagger
- **调试界面**: http://localhost:8081/debug
- **健康检查**: http://localhost:8081/health

### 运行测试

```bash
# 运行所有测试
./scripts/run_apidebug_tests.sh

# 只运行单元测试
./scripts/run_apidebug_tests.sh -u

# 生成覆盖率报告
./scripts/run_apidebug_tests.sh -c
```

## 📋 核心功能

### 1. API文档自动生成

- ✅ 自动扫描Go源代码中的路由定义
- ✅ 解析函数注释中的API文档信息
- ✅ 生成标准的OpenAPI 3.0规范文档
- ✅ 支持JSON和YAML格式输出
- ✅ 实时文档更新和缓存

### 2. 可视化调试界面

- ✅ 现代化的Web界面
- ✅ 支持所有HTTP方法（GET、POST、PUT、DELETE等）
- ✅ 请求头和请求体编辑
- ✅ 实时响应展示
- ✅ 请求历史记录管理
- ✅ 基于API文档的请求模板

### 3. 完整的调试功能

- ✅ 内置HTTP客户端
- ✅ 认证支持（Bearer Token、API Key）
- ✅ 请求和响应拦截
- ✅ 错误处理和重试机制
- ✅ 性能监控和统计

## 🏗️ 系统架构

```
internal/
├── apidoc/          # API文档生成模块
│   ├── scanner.go   # 代码扫描器
│   ├── openapi.go   # OpenAPI规范生成器
│   ├── service.go   # 文档服务
│   └── logger.go    # 日志适配器
├── debug/           # API调试模块
│   └── service.go   # 调试服务
├── apidebug/        # 系统集成模块
│   └── service.go   # 主服务
├── handlers/        # HTTP处理器
│   ├── apidoc.go    # 文档处理器
│   ├── debug.go     # 调试处理器
│   └── apidebug.go  # 系统处理器
└── routes/          # 路由配置
    └── apidebug.go  # API调试路由
```

## 🔧 集成到现有项目

### 方法1: 添加到现有路由

```go
import "ai-text-game-iam-npc/internal/routes"

// 在你的路由设置中添加
err := routes.RegisterAPIDebugRoutes(router, logger)
if err != nil {
    log.Fatal("注册API调试路由失败:", err)
}
```

### 方法2: 独立运行

```go
import "ai-text-game-iam-npc/internal/routes"

// 创建独立服务器
server, err := routes.CreateStandaloneAPIDebugServer(logger)
if err != nil {
    log.Fatal("创建服务器失败:", err)
}

server.Run(":8081")
```

## 📝 API注释规范

为了获得最佳的文档生成效果，请按照以下格式编写API注释：

```go
// GetUsers 获取用户列表
// @Summary 获取用户列表
// @Description 获取所有用户的分页列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(20)
// @Success 200 {object} Response{data=[]User}
// @Failure 400 {object} Response
// @Router /api/v1/users [get]
func GetUsers(c *gin.Context) {
    // 实现逻辑
}
```

## 🎯 使用示例

### 1. 测试GET请求

1. 打开调试界面: http://localhost:8081/debug
2. 选择HTTP方法: `GET`
3. 输入URL: `/demo/hello?name=张三`
4. 点击"发送请求"
5. 查看响应结果

### 2. 测试POST请求

1. 选择HTTP方法: `POST`
2. 输入URL: `/demo/users`
3. 设置请求头:
   ```json
   {
     "Content-Type": "application/json"
   }
   ```
4. 设置请求体:
   ```json
   {
     "name": "新用户",
     "email": "<EMAIL>"
   }
   ```
5. 点击"发送请求"

### 3. 查看API文档

1. 访问: http://localhost:8081/api/v1/docs
2. 或使用Swagger UI: http://localhost:8081/api/v1/docs/swagger
3. 浏览自动生成的API文档

## 📊 系统监控

访问系统状态页面查看详细信息：
- 服务运行状态
- API端点统计
- 请求历史统计
- 系统配置信息

URL: http://localhost:8081/system

## 🔍 故障排除

### 常见问题

1. **文档生成失败**
   - 检查项目根目录配置
   - 确认源代码文件可读
   - 查看日志中的错误信息

2. **调试请求失败**
   - 验证目标服务器可访问性
   - 检查URL和参数格式
   - 确认认证信息正确

3. **界面无法访问**
   - 检查服务器启动状态
   - 确认端口未被占用
   - 验证路由注册成功

### 调试技巧

```bash
# 检查系统状态
curl http://localhost:8081/api/v1/system/status

# 验证API文档
curl http://localhost:8081/api/v1/docs/openapi

# 测试健康检查
curl http://localhost:8081/health
```

## 🧪 测试覆盖率

运行测试并查看覆盖率：

```bash
./scripts/run_apidebug_tests.sh -c
open coverage/coverage.html
```

当前测试覆盖率：
- API文档模块: >80%
- 调试模块: >75%
- 集成测试: 完整覆盖

## 📈 性能指标

- 文档生成时间: <2秒（中等规模项目）
- 请求响应时间: <100ms（本地调试）
- 内存使用: <50MB（基础运行）
- 并发支持: >100个并发请求

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 开发要求

- 代码必须通过所有测试
- 新功能需要添加相应测试
- 遵循项目的编码规范
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Gin](https://github.com/gin-gonic/gin) - HTTP Web框架
- [Testify](https://github.com/stretchr/testify) - 测试工具包
- [UUID](https://github.com/google/uuid) - UUID生成器

---

**开发团队**: AI文本游戏项目组  
**最后更新**: 2025-08-07  
**版本**: v1.0.0
