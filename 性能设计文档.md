# AI文本游戏性能设计文档

## 1. 性能设计概述

### 1.1. 性能目标
- **响应时间**: API响应时间 < 200ms (P95)
- **吞吐量**: 支持10,000并发用户
- **可用性**: 99.9%系统可用性
- **AI响应**: AI生成响应时间 < 3秒
- **数据库**: 查询响应时间 < 50ms

### 1.2. 性能架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    负载均衡层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Nginx     │  │  Cloudflare │  │   HAProxy   │         │
│  │  负载均衡   │  │    CDN      │  │   健康检查  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    缓存加速层                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Redis     │  │  Memcached  │  │  本地缓存   │         │
│  │   集群      │  │   热点数据  │  │   应用缓存  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    应用服务层                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Go服务实例 │  │  连接池管理 │  │  异步处理   │         │
│  │  水平扩展   │  │  资源复用   │  │  队列系统   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    数据存储层                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ PostgreSQL  │  │   读写分离  │  │   分库分表  │         │
│  │   主从集群  │  │   查询优化  │  │   索引优化  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 2. 数据库性能优化

### 2.1. PostgreSQL优化策略

#### 连接池配置
```go
// Go语言数据库连接池配置
func setupDBPool() *sql.DB {
    config := pgxpool.Config{
        MaxConns:        100,           // 最大连接数
        MinConns:        10,            // 最小连接数
        MaxConnLifetime: time.Hour,     // 连接最大生命周期
        MaxConnIdleTime: time.Minute * 30, // 连接最大空闲时间
        HealthCheckPeriod: time.Minute * 5, // 健康检查周期
    }
    
    db, err := pgxpool.ConnectConfig(context.Background(), &config)
    if err != nil {
        log.Fatal("Failed to create connection pool:", err)
    }
    
    return db
}
```

#### 索引优化策略
```sql
-- 1. 用户查询优化
CREATE INDEX CONCURRENTLY idx_users_external_id ON users(external_id);
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_users_created_at ON users(created_at);

-- 2. 游戏世界查询优化
CREATE INDEX CONCURRENTLY idx_worlds_creator_id ON worlds(creator_id);
CREATE INDEX CONCURRENTLY idx_worlds_status_created ON worlds(status, created_at);
CREATE INDEX CONCURRENTLY idx_worlds_is_public ON worlds(is_public) WHERE is_public = true;

-- 3. 场景查询优化
CREATE INDEX CONCURRENTLY idx_scenes_world_id ON scenes(world_id);
CREATE INDEX CONCURRENTLY idx_scenes_coordinates ON scenes(x_coordinate, y_coordinate);

-- 4. 角色查询优化
CREATE INDEX CONCURRENTLY idx_characters_user_world ON characters(user_id, world_id);
CREATE INDEX CONCURRENTLY idx_characters_scene_id ON characters(current_scene_id);

-- 5. 事件查询优化
CREATE INDEX CONCURRENTLY idx_events_character_time ON events(character_id, event_time);
CREATE INDEX CONCURRENTLY idx_events_scene_time ON events(scene_id, event_time);

-- 6. 复合索引优化
CREATE INDEX CONCURRENTLY idx_memories_character_type_time 
ON memories(character_id, memory_type, created_at);

CREATE INDEX CONCURRENTLY idx_experiences_character_category_time 
ON experiences(character_id, category, created_at);
```

#### 查询优化
```sql
-- 1. 分页查询优化 (使用游标分页)
SELECT id, name, description, created_at
FROM worlds 
WHERE is_public = true 
  AND created_at < $1  -- 游标位置
ORDER BY created_at DESC 
LIMIT 20;

-- 2. 聚合查询优化
SELECT 
    w.id,
    w.name,
    COUNT(c.id) as player_count,
    MAX(e.event_time) as last_activity
FROM worlds w
LEFT JOIN characters c ON w.id = c.world_id
LEFT JOIN events e ON c.id = e.character_id
WHERE w.is_public = true
GROUP BY w.id, w.name
HAVING COUNT(c.id) > 0
ORDER BY last_activity DESC;

-- 3. 使用物化视图优化复杂查询
CREATE MATERIALIZED VIEW world_statistics AS
SELECT 
    w.id as world_id,
    w.name,
    COUNT(DISTINCT c.id) as player_count,
    COUNT(DISTINCT s.id) as scene_count,
    MAX(e.event_time) as last_activity,
    AVG(EXTRACT(EPOCH FROM (e.event_time - c.created_at))/3600) as avg_play_hours
FROM worlds w
LEFT JOIN characters c ON w.id = c.world_id
LEFT JOIN scenes s ON w.id = s.world_id
LEFT JOIN events e ON c.id = e.character_id
GROUP BY w.id, w.name;

-- 定期刷新物化视图
REFRESH MATERIALIZED VIEW CONCURRENTLY world_statistics;
```

### 2.2. 读写分离架构

#### 主从配置
```yaml
# PostgreSQL主从配置
database:
  master:
    host: "postgres-master.internal"
    port: 5432
    max_connections: 100
    
  slaves:
    - host: "postgres-slave-1.internal"
      port: 5432
      weight: 1
    - host: "postgres-slave-2.internal"
      port: 5432
      weight: 1
      
  routing:
    read_queries:
      - "SELECT"
      - "WITH ... SELECT"
    write_queries:
      - "INSERT"
      - "UPDATE"
      - "DELETE"
      - "CREATE"
      - "ALTER"
```

#### Go语言读写分离实现
```go
type DatabaseManager struct {
    master *sql.DB
    slaves []*sql.DB
    router *QueryRouter
}

func (dm *DatabaseManager) Query(query string, args ...interface{}) (*sql.Rows, error) {
    if dm.router.IsReadQuery(query) {
        // 使用从库进行读操作
        slave := dm.selectSlave()
        return slave.Query(query, args...)
    } else {
        // 使用主库进行写操作
        return dm.master.Query(query, args...)
    }
}

func (dm *DatabaseManager) selectSlave() *sql.DB {
    // 简单轮询策略
    index := atomic.AddUint64(&dm.slaveIndex, 1) % uint64(len(dm.slaves))
    return dm.slaves[index]
}
```

### 2.3. 分库分表策略

#### 水平分表方案
```sql
-- 按世界ID分表 (events表)
CREATE TABLE events_0 (LIKE events INCLUDING ALL);
CREATE TABLE events_1 (LIKE events INCLUDING ALL);
CREATE TABLE events_2 (LIKE events INCLUDING ALL);
CREATE TABLE events_3 (LIKE events INCLUDING ALL);

-- 分表函数
CREATE OR REPLACE FUNCTION get_events_table_name(world_id UUID)
RETURNS TEXT AS $$
BEGIN
    RETURN 'events_' || (hashtext(world_id::text) % 4);
END;
$$ LANGUAGE plpgsql;

-- 应用层分表逻辑
func (r *EventRepository) Insert(event *Event) error {
    tableName := fmt.Sprintf("events_%d", 
        hash(event.WorldID.String()) % 4)
    
    query := fmt.Sprintf(`
        INSERT INTO %s (id, character_id, scene_id, event_type, content, event_time)
        VALUES ($1, $2, $3, $4, $5, $6)
    `, tableName)
    
    _, err := r.db.Exec(query, event.ID, event.CharacterID, 
        event.SceneID, event.EventType, event.Content, event.EventTime)
    return err
}
```

## 3. 缓存策略设计

### 3.1. 多层缓存架构

#### 缓存层次结构
```
应用层缓存 (L1)
├── 本地内存缓存 (Go map + sync.RWMutex)
├── 进程内缓存 (BigCache/FreeCache)
└── 热点数据缓存 (1-5秒TTL)

分布式缓存 (L2)
├── Redis集群 (主要缓存)
├── 用户会话数据 (30分钟TTL)
├── 游戏状态数据 (10分钟TTL)
└── 查询结果缓存 (5分钟TTL)

CDN缓存 (L3)
├── 静态资源缓存 (24小时TTL)
├── API响应缓存 (1分钟TTL)
└── 图片资源缓存 (7天TTL)
```

#### Redis集群配置
```yaml
# Redis集群配置
redis:
  cluster:
    nodes:
      - "redis-1.internal:6379"
      - "redis-2.internal:6379"
      - "redis-3.internal:6379"
      - "redis-4.internal:6379"
      - "redis-5.internal:6379"
      - "redis-6.internal:6379"
    
  pool:
    max_active: 100
    max_idle: 50
    idle_timeout: "300s"
    
  settings:
    maxmemory: "2gb"
    maxmemory_policy: "allkeys-lru"
    timeout: "5s"
```

### 3.2. 缓存策略实现

#### Go语言缓存实现
```go
type CacheManager struct {
    local  *LocalCache
    redis  *RedisCache
    config *CacheConfig
}

// 多级缓存获取
func (cm *CacheManager) Get(key string) (interface{}, error) {
    // L1: 本地缓存
    if value, found := cm.local.Get(key); found {
        return value, nil
    }
    
    // L2: Redis缓存
    value, err := cm.redis.Get(key)
    if err == nil {
        // 回写到本地缓存
        cm.local.Set(key, value, cm.config.LocalTTL)
        return value, nil
    }
    
    return nil, ErrCacheNotFound
}

// 缓存更新策略
func (cm *CacheManager) Set(key string, value interface{}, ttl time.Duration) error {
    // 同时更新本地和Redis缓存
    cm.local.Set(key, value, ttl)
    return cm.redis.Set(key, value, ttl)
}

// 缓存失效策略
func (cm *CacheManager) Invalidate(pattern string) error {
    // 清除本地缓存
    cm.local.DeletePattern(pattern)
    
    // 清除Redis缓存
    return cm.redis.DeletePattern(pattern)
}
```

#### 缓存键设计规范
```go
// 缓存键命名规范
const (
    // 用户相关
    UserProfileKey = "user:profile:%s"           // user:profile:uuid
    UserSessionKey = "user:session:%s"           // user:session:token
    
    // 世界相关
    WorldInfoKey = "world:info:%s"               // world:info:uuid
    WorldPlayersKey = "world:players:%s"         // world:players:uuid
    WorldScenesKey = "world:scenes:%s"           // world:scenes:uuid
    
    // 角色相关
    CharacterKey = "character:%s"                // character:uuid
    CharacterMemoriesKey = "character:memories:%s" // character:memories:uuid
    
    // 场景相关
    SceneKey = "scene:%s"                        // scene:uuid
    SceneEventsKey = "scene:events:%s"           // scene:events:uuid
    
    // AI相关
    AIResponseKey = "ai:response:%s"             // ai:response:hash
    AIContextKey = "ai:context:%s"               // ai:context:character_id
)

// 缓存TTL配置
var CacheTTL = map[string]time.Duration{
    "user:profile":     time.Hour * 24,      // 用户资料24小时
    "user:session":     time.Minute * 30,    // 用户会话30分钟
    "world:info":       time.Hour * 2,       // 世界信息2小时
    "character":        time.Minute * 30,    // 角色信息30分钟
    "scene":           time.Minute * 10,     // 场景信息10分钟
    "ai:response":     time.Minute * 5,      // AI响应5分钟
}
```

### 3.3. 缓存预热与更新

#### 缓存预热策略
```go
// 系统启动时预热热点数据
func (cm *CacheManager) WarmupCache() error {
    // 1. 预热公共世界列表
    publicWorlds, err := cm.worldRepo.GetPublicWorlds(100)
    if err != nil {
        return err
    }
    
    for _, world := range publicWorlds {
        key := fmt.Sprintf(WorldInfoKey, world.ID)
        cm.Set(key, world, CacheTTL["world:info"])
    }
    
    // 2. 预热热门场景
    hotScenes, err := cm.sceneRepo.GetHotScenes(500)
    if err != nil {
        return err
    }
    
    for _, scene := range hotScenes {
        key := fmt.Sprintf(SceneKey, scene.ID)
        cm.Set(key, scene, CacheTTL["scene"])
    }
    
    return nil
}

// 定时刷新缓存
func (cm *CacheManager) StartCacheRefresh() {
    ticker := time.NewTicker(time.Minute * 5)
    go func() {
        for range ticker.C {
            cm.refreshExpiredCache()
        }
    }()
}
```

## 4. 并发处理优化

### 4.1. Goroutine池管理

#### 工作池实现
```go
type WorkerPool struct {
    workers    int
    jobQueue   chan Job
    workerPool chan chan Job
    quit       chan bool
}

type Job struct {
    ID       string
    Type     string
    Payload  interface{}
    Callback func(interface{}, error)
}

func NewWorkerPool(workers int, queueSize int) *WorkerPool {
    return &WorkerPool{
        workers:    workers,
        jobQueue:   make(chan Job, queueSize),
        workerPool: make(chan chan Job, workers),
        quit:       make(chan bool),
    }
}

func (wp *WorkerPool) Start() {
    for i := 0; i < wp.workers; i++ {
        worker := NewWorker(wp.workerPool, wp.quit)
        worker.Start()
    }
    
    go wp.dispatch()
}

func (wp *WorkerPool) dispatch() {
    for {
        select {
        case job := <-wp.jobQueue:
            // 获取可用的worker
            jobChannel := <-wp.workerPool
            // 分发任务
            jobChannel <- job
        case <-wp.quit:
            return
        }
    }
}

// AI处理专用工作池
var AIWorkerPool = NewWorkerPool(50, 1000)

func ProcessAIRequest(request *AIRequest) {
    job := Job{
        ID:   request.ID,
        Type: "ai_generation",
        Payload: request,
        Callback: func(result interface{}, err error) {
            if err != nil {
                log.Printf("AI request failed: %v", err)
                return
            }
            // 处理AI响应
            handleAIResponse(result.(*AIResponse))
        },
    }
    
    AIWorkerPool.AddJob(job)
}
```

### 4.2. 连接池优化

#### HTTP客户端连接池
```go
// AI接口HTTP客户端优化
func setupAIClient() *http.Client {
    transport := &http.Transport{
        MaxIdleConns:        100,              // 最大空闲连接数
        MaxIdleConnsPerHost: 20,               // 每个主机最大空闲连接数
        MaxConnsPerHost:     50,               // 每个主机最大连接数
        IdleConnTimeout:     90 * time.Second, // 空闲连接超时
        TLSHandshakeTimeout: 10 * time.Second, // TLS握手超时
        ResponseHeaderTimeout: 30 * time.Second, // 响应头超时
    }
    
    return &http.Client{
        Transport: transport,
        Timeout:   60 * time.Second, // 总超时时间
    }
}

// WebSocket连接池
type WSConnectionPool struct {
    connections map[string]*websocket.Conn
    mutex       sync.RWMutex
    maxConns    int
}

func (pool *WSConnectionPool) GetConnection(userID string) (*websocket.Conn, error) {
    pool.mutex.RLock()
    conn, exists := pool.connections[userID]
    pool.mutex.RUnlock()
    
    if exists && conn != nil {
        return conn, nil
    }
    
    // 创建新连接
    return pool.createConnection(userID)
}
```

### 4.3. 异步处理机制

#### 消息队列处理
```go
// Redis消息队列实现
type MessageQueue struct {
    redis  *redis.Client
    topics map[string]chan Message
    mutex  sync.RWMutex
}

type Message struct {
    ID      string      `json:"id"`
    Topic   string      `json:"topic"`
    Payload interface{} `json:"payload"`
    Retry   int         `json:"retry"`
}

func (mq *MessageQueue) Publish(topic string, payload interface{}) error {
    message := Message{
        ID:      uuid.New().String(),
        Topic:   topic,
        Payload: payload,
        Retry:   0,
    }
    
    data, err := json.Marshal(message)
    if err != nil {
        return err
    }
    
    return mq.redis.LPush(context.Background(), topic, data).Err()
}

func (mq *MessageQueue) Subscribe(topic string, handler func(Message) error) {
    go func() {
        for {
            result, err := mq.redis.BRPop(context.Background(), 
                time.Second*5, topic).Result()
            if err != nil {
                continue
            }
            
            var message Message
            if err := json.Unmarshal([]byte(result[1]), &message); err != nil {
                log.Printf("Failed to unmarshal message: %v", err)
                continue
            }
            
            // 异步处理消息
            go func(msg Message) {
                if err := handler(msg); err != nil {
                    // 重试机制
                    if msg.Retry < 3 {
                        msg.Retry++
                        mq.Publish(topic, msg)
                    }
                }
            }(message)
        }
    }()
}

// 使用示例
func setupMessageQueues() {
    mq := NewMessageQueue(redisClient)
    
    // AI处理队列
    mq.Subscribe("ai_requests", handleAIRequest)
    
    // 事件处理队列
    mq.Subscribe("game_events", handleGameEvent)
    
    // 通知队列
    mq.Subscribe("notifications", handleNotification)
}
```

## 5. AI接口调用优化

### 5.1. AI请求优化策略

#### 请求合并与批处理
```go
type AIRequestBatcher struct {
    requests    []AIRequest
    mutex       sync.Mutex
    batchSize   int
    flushTimer  *time.Timer
    flushPeriod time.Duration
}

func (b *AIRequestBatcher) AddRequest(request AIRequest) {
    b.mutex.Lock()
    defer b.mutex.Unlock()
    
    b.requests = append(b.requests, request)
    
    // 达到批次大小或定时器触发时处理
    if len(b.requests) >= b.batchSize {
        b.flush()
    } else if b.flushTimer == nil {
        b.flushTimer = time.AfterFunc(b.flushPeriod, b.flush)
    }
}

func (b *AIRequestBatcher) flush() {
    b.mutex.Lock()
    requests := make([]AIRequest, len(b.requests))
    copy(requests, b.requests)
    b.requests = b.requests[:0]
    
    if b.flushTimer != nil {
        b.flushTimer.Stop()
        b.flushTimer = nil
    }
    b.mutex.Unlock()
    
    // 批量处理AI请求
    go b.processBatch(requests)
}

func (b *AIRequestBatcher) processBatch(requests []AIRequest) {
    // 将多个请求合并为一个批次请求
    batchRequest := createBatchRequest(requests)
    
    response, err := callAIAPI(batchRequest)
    if err != nil {
        // 处理错误，可能需要重试单个请求
        for _, req := range requests {
            go processSingleRequest(req)
        }
        return
    }
    
    // 分发批次响应到各个请求
    distributeBatchResponse(requests, response)
}
```

#### AI响应缓存
```go
type AIResponseCache struct {
    cache *CacheManager
    hasher hash.Hash
}

func (arc *AIResponseCache) GetCachedResponse(prompt string, context map[string]interface{}) (*AIResponse, bool) {
    // 生成缓存键
    key := arc.generateCacheKey(prompt, context)
    
    if cached, err := arc.cache.Get(key); err == nil {
        if response, ok := cached.(*AIResponse); ok {
            return response, true
        }
    }
    
    return nil, false
}

func (arc *AIResponseCache) CacheResponse(prompt string, context map[string]interface{}, response *AIResponse) {
    key := arc.generateCacheKey(prompt, context)
    
    // 缓存5分钟，避免完全相同的请求重复调用AI
    arc.cache.Set(key, response, time.Minute*5)
}

func (arc *AIResponseCache) generateCacheKey(prompt string, context map[string]interface{}) string {
    arc.hasher.Reset()
    arc.hasher.Write([]byte(prompt))
    
    // 将context序列化并加入hash
    if contextBytes, err := json.Marshal(context); err == nil {
        arc.hasher.Write(contextBytes)
    }
    
    return fmt.Sprintf("ai:response:%x", arc.hasher.Sum(nil))
}
```

### 5.2. AI接口熔断与降级

#### 熔断器实现
```go
type CircuitBreaker struct {
    maxFailures int
    resetTimeout time.Duration
    failures    int
    lastFailTime time.Time
    state       CircuitState
    mutex       sync.RWMutex
}

type CircuitState int

const (
    StateClosed CircuitState = iota
    StateOpen
    StateHalfOpen
)

func (cb *CircuitBreaker) Call(fn func() (interface{}, error)) (interface{}, error) {
    cb.mutex.Lock()
    defer cb.mutex.Unlock()
    
    if cb.state == StateOpen {
        if time.Since(cb.lastFailTime) > cb.resetTimeout {
            cb.state = StateHalfOpen
            cb.failures = 0
        } else {
            return nil, ErrCircuitBreakerOpen
        }
    }
    
    result, err := fn()
    
    if err != nil {
        cb.failures++
        cb.lastFailTime = time.Now()
        
        if cb.failures >= cb.maxFailures {
            cb.state = StateOpen
        }
        
        return nil, err
    }
    
    // 成功调用，重置状态
    cb.failures = 0
    cb.state = StateClosed
    
    return result, nil
}

// AI接口调用包装
func CallAIWithCircuitBreaker(request *AIRequest) (*AIResponse, error) {
    result, err := aiCircuitBreaker.Call(func() (interface{}, error) {
        return callAIAPI(request)
    })
    
    if err != nil {
        // 降级处理：返回预设响应或简化响应
        return getFallbackResponse(request), nil
    }
    
    return result.(*AIResponse), nil
}
```

## 6. 性能监控与调优

### 6.1. 性能指标监控

#### 关键性能指标
```go
type PerformanceMetrics struct {
    // API性能指标
    APIResponseTime    prometheus.HistogramVec  // API响应时间
    APIRequestRate     prometheus.CounterVec    // API请求速率
    APIErrorRate       prometheus.CounterVec    // API错误率
    
    // 数据库性能指标
    DBQueryTime        prometheus.HistogramVec  // 数据库查询时间
    DBConnectionPool   prometheus.GaugeVec      // 数据库连接池状态
    DBSlowQueries      prometheus.CounterVec    // 慢查询计数
    
    // 缓存性能指标
    CacheHitRate       prometheus.GaugeVec      // 缓存命中率
    CacheResponseTime  prometheus.HistogramVec  // 缓存响应时间
    
    // AI接口性能指标
    AIResponseTime     prometheus.HistogramVec  // AI响应时间
    AIRequestQueue     prometheus.GaugeVec      // AI请求队列长度
    AIErrorRate        prometheus.CounterVec    // AI接口错误率
    
    // 系统资源指标
    CPUUsage          prometheus.GaugeVec       // CPU使用率
    MemoryUsage       prometheus.GaugeVec       // 内存使用率
    GoroutineCount    prometheus.GaugeVec       // Goroutine数量
}

// 性能监控中间件
func PerformanceMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        start := time.Now()
        
        // 包装ResponseWriter以捕获状态码
        wrapped := &responseWriter{ResponseWriter: w, statusCode: 200}
        
        next.ServeHTTP(wrapped, r)
        
        duration := time.Since(start)
        
        // 记录性能指标
        metrics.APIResponseTime.WithLabelValues(
            r.Method, r.URL.Path, strconv.Itoa(wrapped.statusCode),
        ).Observe(duration.Seconds())
        
        metrics.APIRequestRate.WithLabelValues(
            r.Method, r.URL.Path,
        ).Inc()
        
        if wrapped.statusCode >= 400 {
            metrics.APIErrorRate.WithLabelValues(
                r.Method, r.URL.Path, strconv.Itoa(wrapped.statusCode),
            ).Inc()
        }
    })
}
```

### 6.2. 性能分析工具

#### Go性能分析
```go
// 启用pprof性能分析
import _ "net/http/pprof"

func setupProfiling() {
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
}

// 自定义性能分析
func ProfileFunction(name string, fn func()) {
    start := time.Now()
    defer func() {
        duration := time.Since(start)
        log.Printf("Function %s took %v", name, duration)
        
        // 记录到监控系统
        functionDuration.WithLabelValues(name).Observe(duration.Seconds())
    }()
    
    fn()
}

// 内存使用分析
func MonitorMemoryUsage() {
    var m runtime.MemStats
    runtime.ReadMemStats(&m)
    
    metrics.MemoryUsage.WithLabelValues("heap_alloc").Set(float64(m.HeapAlloc))
    metrics.MemoryUsage.WithLabelValues("heap_sys").Set(float64(m.HeapSys))
    metrics.MemoryUsage.WithLabelValues("heap_idle").Set(float64(m.HeapIdle))
    metrics.MemoryUsage.WithLabelValues("heap_inuse").Set(float64(m.HeapInuse))
    
    metrics.GoroutineCount.WithLabelValues("total").Set(float64(runtime.NumGoroutine()))
}
```

### 6.3. 自动化性能调优

#### 自适应连接池
```go
type AdaptiveConnectionPool struct {
    pool        *sql.DB
    metrics     *PoolMetrics
    minConns    int
    maxConns    int
    currentSize int
    mutex       sync.RWMutex
}

func (acp *AdaptiveConnectionPool) adjustPoolSize() {
    acp.mutex.Lock()
    defer acp.mutex.Unlock()
    
    // 根据性能指标调整连接池大小
    avgResponseTime := acp.metrics.GetAvgResponseTime()
    activeConnections := acp.metrics.GetActiveConnections()
    queueLength := acp.metrics.GetQueueLength()
    
    if avgResponseTime > 100*time.Millisecond && queueLength > 10 {
        // 响应时间过长且有排队，增加连接数
        if acp.currentSize < acp.maxConns {
            acp.currentSize = min(acp.currentSize+5, acp.maxConns)
            acp.pool.SetMaxOpenConns(acp.currentSize)
        }
    } else if avgResponseTime < 50*time.Millisecond && activeConnections < acp.currentSize/2 {
        // 响应时间良好且连接利用率低，减少连接数
        if acp.currentSize > acp.minConns {
            acp.currentSize = max(acp.currentSize-2, acp.minConns)
            acp.pool.SetMaxOpenConns(acp.currentSize)
        }
    }
}

// 定期调整
func (acp *AdaptiveConnectionPool) startAutoTuning() {
    ticker := time.NewTicker(time.Minute * 5)
    go func() {
        for range ticker.C {
            acp.adjustPoolSize()
        }
    }()
}
```

这份性能设计文档为AI文本游戏项目提供了全面的性能优化方案，涵盖了数据库优化、缓存策略、并发处理、AI接口优化等关键性能领域，确保系统能够高效稳定地支持大规模用户访问。
