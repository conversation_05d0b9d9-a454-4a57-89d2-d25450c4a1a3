# AI文本游戏数据设计文档

**版本**: v3.0
**更新日期**: 2025-01-08
**更新说明**: 基于需求文档深度分析，完善数据库设计，添加详细中文说明和优化建议

## 文档更新记录

### v3.0 更新内容 (2025-01-08)
- ✅ **实体表设计评估**: 确认保留通用实体表的必要性，完善混合设计方案
- ✅ **角色阅历表优化**: 添加传承、声誉、教学等缺失字段，完善阅历系统
- ✅ **事件处理系统**: 确认事件日志表拆分的合理性，优化性能监控
- ✅ **特殊表机制说明**: 详细解释世界规模框架和玩家分配的业务流程
- ✅ **JSONB字段规范**: 为所有核心JSONB字段提供详细的JSON结构规范
- ✅ **场景连接增强**: 优化connections字段，添加动态状态和智能路径功能
- ✅ **中文说明完善**: 为所有数据库表添加详细的中文说明和业务场景
- ✅ **关联关系图**: 完整描述表间关联关系、数据流向和约束规则

## 1. 数据架构概述

### 1.0 设计优化总结

本次优化解决了以下关键问题：
- ✅ **数据冗余问题**: 优化记忆/阅历存储，消除场景实体关系冗余
- ✅ **性能瓶颈问题**: 添加必要索引，优化JSON字段使用
- ✅ **扩展性限制**: 设计混合实体表结构，支持动态扩展
- ✅ **场景连接关系**: 完善connections字段结构，支持复杂场景转换
- ✅ **多角色用户支持**: 优化角色管理机制
- ✅ **事件处理结果**: 设计完整的事件处理和耗时记录系统

### 1.1 数据库设计核心原则

#### 实体表设计原则
**保留通用实体表的必要性**：
- **统一实体生态**: 支持需求文档中的"统一的实体-特质"模型
- **位置管理统一**: 提供一致的位置追踪机制（场景、角色、容器）
- **扩展性支持**: 为未来新实体类型（天气、环境效果等）提供基础
- **查询效率**: 支持跨类型的统一查询操作

**混合设计优势**：
- 通用实体表处理共同属性（位置、状态、基础属性）
- 专门表处理特定类型的详细属性（物品耐久度、事件触发条件等）
- 平衡了灵活性和性能需求

### 1.1. 数据分层设计
```
┌─────────────────────────────────────────────────────────────┐
│                    界面层 (Presentation Layer)                │
│  前端组件状态、UI数据模型、表单验证数据                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    传输层 (Transport Layer)                  │
│  API请求/响应数据、WebSocket消息、HTTP状态码                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    业务层 (Business Layer)                   │
│  游戏逻辑数据模型、业务规则、数据转换                         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    缓存层 (Cache Layer)                      │
│  Redis缓存、会话数据、临时状态、队列数据                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    存储层 (Storage Layer)                    │
│  PostgreSQL数据库、文件存储、备份数据                        │
└─────────────────────────────────────────────────────────────┘
```

### 1.2. 数据一致性策略
- **强一致性**: 用户账户、游戏核心状态
- **最终一致性**: 统计数据、日志记录
- **会话一致性**: 用户界面状态、临时数据

## 2. 存储层数据设计

### 2.1. 核心数据表结构

#### 用户相关表

```sql
-- 用户基础信息表 (外部IDP集成)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    external_id VARCHAR(255) UNIQUE NOT NULL,  -- 外部IDP用户ID
    external_provider VARCHAR(50) NOT NULL,    -- IDP提供商 (auth0, keycloak, etc.)
    email VARCHAR(255) NOT NULL,               -- 从IDP获取的邮箱
    display_name VARCHAR(100),                 -- 显示名称
    avatar_url TEXT,                           -- 头像URL
    game_roles JSONB DEFAULT '["user"]',       -- 游戏内角色 ["user", "premium", "admin", "developer"]
    status VARCHAR(20) DEFAULT 'active',       -- active, suspended, deleted
    preferences JSONB DEFAULT '{}',            -- 用户偏好设置
    idp_claims JSONB DEFAULT '{}',             -- 从IDP获取的额外声明
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,

    -- 索引
    CONSTRAINT unique_external_user UNIQUE (external_id, external_provider)
);

-- 用户统计信息表
CREATE TABLE user_stats (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    total_play_time INTEGER DEFAULT 0, -- 总游戏时间(分钟)
    worlds_created INTEGER DEFAULT 0, -- 创建的世界数量
    worlds_joined INTEGER DEFAULT 0, -- 加入的世界数量
    achievements JSONB DEFAULT '[]', -- 成就列表
    level INTEGER DEFAULT 1, -- 用户等级
    experience INTEGER DEFAULT 0, -- 经验值
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 游戏世界相关表

```sql
-- 游戏世界表
CREATE TABLE worlds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    creator_id UUID NOT NULL REFERENCES users(id),
    world_config JSONB NOT NULL, -- 世界配置(时间倍率、规则等)
    world_state JSONB NOT NULL, -- 当前世界状态
    status VARCHAR(20) DEFAULT 'active', -- active, paused, archived
    is_public BOOLEAN DEFAULT false, -- 是否公开
    max_players INTEGER DEFAULT 10, -- 最大玩家数
    current_players INTEGER DEFAULT 0, -- 当前玩家数
    game_time BIGINT DEFAULT 0, -- 游戏内时间(分钟)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 场景表 (优化后)
CREATE TABLE scenes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    scene_type VARCHAR(50) DEFAULT 'normal', -- normal, special, hidden, dungeon, etc.

    -- 优化后的连接关系结构 - 支持复杂场景转换逻辑
    connections JSONB DEFAULT '[]', -- 详细的场景连接信息，包含方向性、条件、权限等

    -- 场景属性和环境信息
    properties JSONB DEFAULT '{}', -- 场景基础属性和特质
    environment JSONB DEFAULT '{}', -- 环境信息(天气、光照、温度等)

    -- 访问控制和状态
    access_level VARCHAR(20) DEFAULT 'public', -- public, restricted, private, hidden
    scene_state VARCHAR(20) DEFAULT 'active', -- active, locked, destroyed, under_construction

    -- 容量和限制
    max_occupants INTEGER DEFAULT 50, -- 最大容纳人数
    current_occupants INTEGER DEFAULT 0, -- 当前人数

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 索引
    CONSTRAINT scenes_name_world_unique UNIQUE (world_id, name)
);

-- 角色表 (优化后 - 支持多角色用户)
CREATE TABLE characters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id), -- NULL表示NPC，支持一个用户多个角色
    name VARCHAR(100) NOT NULL,
    description TEXT,
    character_type VARCHAR(20) NOT NULL DEFAULT 'player', -- player, npc, collective
    current_scene_id UUID REFERENCES scenes(id),

    -- 基础属性 (保留核心JSON字段，详细数据存储在专门表中)
    traits JSONB DEFAULT '[]', -- 特质列表
    basic_stats JSONB DEFAULT '{}', -- 基础统计信息

    -- 状态管理
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, dead, suspended
    last_action_at TIMESTAMP WITH TIME ZONE,

    -- 角色管理
    is_primary BOOLEAN DEFAULT false, -- 是否为用户在该世界的主角色
    display_order INTEGER DEFAULT 0, -- 显示顺序

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 约束：角色名称在同一世界内唯一
    CONSTRAINT characters_world_name_unique UNIQUE (world_id, name),
    -- 约束：每个用户在每个世界最多有一个主角色
    CONSTRAINT characters_primary_unique UNIQUE (user_id, world_id, is_primary) DEFERRABLE INITIALLY DEFERRED
);

-- 角色记忆详细表 (优化后 - 解决数据冗余问题)
CREATE TABLE character_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    memory_type VARCHAR(50) NOT NULL, -- event, person, location, item, knowledge
    content TEXT NOT NULL,

    -- 记忆强度和重要性
    importance_score FLOAT DEFAULT 0.5 CHECK (importance_score >= 0 AND importance_score <= 1),
    emotional_impact FLOAT DEFAULT 0.0 CHECK (emotional_impact >= -1 AND emotional_impact <= 1),
    clarity_level FLOAT DEFAULT 1.0 CHECK (clarity_level >= 0 AND clarity_level <= 1), -- 清晰度

    -- 关联信息
    associated_entities JSONB DEFAULT '[]', -- 关联的实体ID
    tags JSONB DEFAULT '[]', -- 记忆标签

    -- 遗忘机制
    decay_rate FLOAT DEFAULT 0.1 CHECK (decay_rate >= 0 AND decay_rate <= 1),
    base_strength FLOAT DEFAULT 1.0, -- 基础强度
    current_strength FLOAT DEFAULT 1.0, -- 当前强度

    -- 访问统计
    access_count INTEGER DEFAULT 0,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 角色阅历详细表 (优化后 - 支持传承、声誉、教学系统)
CREATE TABLE character_experiences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    experience_category VARCHAR(50) NOT NULL, -- combat, social, exploration, crafting, event
    experience_type VARCHAR(100) NOT NULL, -- 具体阅历类型
    description TEXT NOT NULL,

    -- 熟练度系统
    proficiency_level INTEGER DEFAULT 1 CHECK (proficiency_level >= 1 AND proficiency_level <= 100),
    total_attempts INTEGER DEFAULT 0, -- 总尝试次数
    success_count INTEGER DEFAULT 0, -- 成功次数
    failure_count INTEGER DEFAULT 0, -- 失败次数
    success_rate FLOAT GENERATED ALWAYS AS (
        CASE WHEN total_attempts > 0 THEN success_count::FLOAT / total_attempts ELSE 0 END
    ) STORED, -- 成功率（计算字段）

    -- 技能和应用
    related_skills JSONB DEFAULT '[]', -- 相关技能
    applicable_contexts JSONB DEFAULT '[]', -- 适用场景
    mastery_threshold INTEGER DEFAULT 10, -- 精通阈值

    -- 传承相关 (新增)
    is_teachable BOOLEAN DEFAULT false, -- 是否可以教授给他人
    teaching_difficulty INTEGER DEFAULT 1 CHECK (teaching_difficulty >= 1 AND teaching_difficulty <= 10), -- 教学难度
    learned_from_character_id UUID REFERENCES characters(id), -- 从哪个角色学习获得

    -- 声誉和关系影响 (新增)
    reputation_impact JSONB DEFAULT '{}', -- 对不同群体声誉的影响
    relationship_modifiers JSONB DEFAULT '{}', -- 对角色关系的修正

    -- 阅历质量和深度 (新增)
    experience_depth VARCHAR(20) DEFAULT 'basic', -- basic, intermediate, advanced, master
    memorable_events JSONB DEFAULT '[]', -- 相关的重要事件记录

    -- 应用效果 (新增)
    passive_effects JSONB DEFAULT '[]', -- 被动效果（如提升某些判定成功率）
    active_abilities JSONB DEFAULT '[]', -- 主动能力（如特殊技能）

    -- 衰减和维护 (新增)
    decay_rate FLOAT DEFAULT 0.05 CHECK (decay_rate >= 0 AND decay_rate <= 1), -- 技能衰减率
    maintenance_requirement JSONB DEFAULT '{}', -- 维持熟练度的要求

    -- 时间信息
    acquired_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 游戏内容相关表

```sql
-- 实体表 (优化后 - 混合设计解决复杂度问题)
CREATE TABLE entities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL, -- item, event, goal, weather, etc.
    name VARCHAR(200) NOT NULL,
    description TEXT,

    -- 基础属性 (通用字段)
    base_properties JSONB DEFAULT '{}', -- 基础属性
    traits JSONB DEFAULT '[]', -- 特质列表

    -- 位置信息 (优化后 - 消除冗余)
    current_scene_id UUID REFERENCES scenes(id), -- 当前所在场景
    owner_character_id UUID REFERENCES characters(id), -- 拥有者角色
    container_entity_id UUID REFERENCES entities(id), -- 容器实体（如背包中的物品）

    -- 状态管理
    status VARCHAR(20) DEFAULT 'active', -- active, consumed, destroyed, hidden
    visibility VARCHAR(20) DEFAULT 'visible', -- visible, hidden, secret

    -- 元数据
    created_by_character_id UUID REFERENCES characters(id), -- 创建者
    version INTEGER DEFAULT 1, -- 版本号，支持实体演化

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 约束：实体只能在一个位置
    CONSTRAINT entities_single_location CHECK (
        (current_scene_id IS NOT NULL)::INTEGER +
        (owner_character_id IS NOT NULL)::INTEGER +
        (container_entity_id IS NOT NULL)::INTEGER <= 1
    )
);

-- 物品专门表 (混合设计 - 解决通用实体表复杂度问题)
CREATE TABLE items (
    entity_id UUID PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
    item_category VARCHAR(50) NOT NULL, -- weapon, tool, consumable, material, treasure, etc.
    item_subcategory VARCHAR(50), -- sword, potion, wood, gem, etc.

    -- 物品属性
    durability INTEGER DEFAULT 100 CHECK (durability >= 0 AND durability <= 100),
    max_durability INTEGER DEFAULT 100,
    stack_size INTEGER DEFAULT 1 CHECK (stack_size >= 1),
    current_stack INTEGER DEFAULT 1,
    weight FLOAT DEFAULT 1.0 CHECK (weight >= 0),

    -- 稀有度和价值
    rarity VARCHAR(20) DEFAULT 'common', -- common, uncommon, rare, epic, legendary
    base_value INTEGER DEFAULT 0, -- 基础价值

    -- 制作和使用
    is_craftable BOOLEAN DEFAULT false,
    crafting_recipe JSONB DEFAULT '{}', -- 制作配方
    usage_effects JSONB DEFAULT '[]', -- 使用效果
    usage_cooldown INTEGER DEFAULT 0, -- 使用冷却时间（秒）

    -- 装备属性（如果是装备）
    is_equippable BOOLEAN DEFAULT false,
    equipment_slot VARCHAR(20), -- head, chest, weapon, etc.
    stat_modifiers JSONB DEFAULT '{}', -- 属性修正

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 事件专门表
CREATE TABLE events (
    entity_id UUID PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
    event_category VARCHAR(50) NOT NULL, -- combat, social, exploration, crafting, environmental
    event_subcategory VARCHAR(50), -- battle, negotiation, discovery, etc.

    -- 事件机制
    trigger_conditions JSONB DEFAULT '[]', -- 触发条件
    duration_minutes INTEGER DEFAULT 0, -- 持续时间（游戏内分钟）
    max_participants INTEGER DEFAULT 1, -- 最大参与者数量
    min_participants INTEGER DEFAULT 1, -- 最小参与者数量

    -- 成功失败条件
    success_conditions JSONB DEFAULT '[]', -- 成功条件
    failure_conditions JSONB DEFAULT '[]', -- 失败条件
    partial_success_conditions JSONB DEFAULT '[]', -- 部分成功条件

    -- 奖励和惩罚
    success_rewards JSONB DEFAULT '[]', -- 成功奖励
    failure_penalties JSONB DEFAULT '[]', -- 失败惩罚

    -- 事件状态
    is_repeatable BOOLEAN DEFAULT true, -- 是否可重复
    cooldown_minutes INTEGER DEFAULT 0, -- 冷却时间
    last_triggered TIMESTAMP WITH TIME ZONE, -- 最后触发时间

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 目标专门表
CREATE TABLE goals (
    entity_id UUID PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
    goal_type VARCHAR(50) NOT NULL, -- world, personal, faction, quest, achievement
    goal_scope VARCHAR(20) DEFAULT 'personal', -- personal, group, world, global

    -- 目标属性
    priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 10),
    difficulty VARCHAR(20) DEFAULT 'normal', -- easy, normal, hard, extreme
    estimated_duration INTEGER, -- 预计完成时间（分钟）

    -- 时间限制
    deadline TIMESTAMP WITH TIME ZONE, -- 截止时间
    is_time_sensitive BOOLEAN DEFAULT false,

    -- 完成条件
    completion_criteria JSONB NOT NULL, -- 完成标准
    progress_tracking JSONB DEFAULT '{}', -- 进度跟踪
    current_progress FLOAT DEFAULT 0.0 CHECK (current_progress >= 0 AND current_progress <= 1),

    -- 奖励和前置条件
    rewards JSONB DEFAULT '[]', -- 完成奖励
    prerequisites JSONB DEFAULT '[]', -- 前置条件

    -- 状态
    goal_status VARCHAR(20) DEFAULT 'active', -- active, completed, failed, abandoned, paused
    completion_date TIMESTAMP WITH TIME ZONE, -- 完成时间

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 游戏事件日志表 (优化后)
CREATE TABLE game_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL, -- action, evolution, system, heartbeat
    event_subtype VARCHAR(50), -- 事件子类型

    -- 参与者信息
    actor_id UUID, -- 主要触发者ID(角色或系统)
    target_id UUID, -- 主要目标ID
    scene_id UUID REFERENCES scenes(id),
    participating_characters JSONB DEFAULT '[]', -- 所有参与角色

    -- 事件数据
    event_data JSONB NOT NULL, -- 事件详细数据
    narrative_text TEXT, -- 叙事文本

    -- 时间信息
    game_time BIGINT NOT NULL, -- 事件发生的游戏时间
    real_duration_ms INTEGER, -- 现实处理时间

    -- 事件结果
    event_outcome VARCHAR(20), -- success, failure, partial, cancelled
    impact_score FLOAT DEFAULT 0.0, -- 影响评分

    -- 元数据
    batch_id UUID, -- 批处理ID
    sequence_number INTEGER, -- 在批次中的序号

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 事件处理结果表 (新增 - 详细的事件处理结果存储)
CREATE TABLE event_processing_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES entities(id), -- 关联到事件实体
    world_id UUID NOT NULL REFERENCES worlds(id),
    processing_batch_id UUID NOT NULL, -- 批处理ID

    -- 处理状态信息
    processing_status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed, timeout
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    processing_duration_ms INTEGER, -- 实际处理耗时
    estimated_duration_ms INTEGER, -- 预计处理耗时

    -- 处理结果信息
    success_status VARCHAR(20) NOT NULL DEFAULT 'unknown', -- success, partial_success, failure, cancelled
    result_data JSONB DEFAULT '{}', -- 处理结果详细数据
    narrative_output TEXT, -- AI生成的叙事文本
    state_changes JSONB DEFAULT '[]', -- 状态变更指令列表

    -- 错误和异常信息
    error_code VARCHAR(50), -- 错误代码
    error_message TEXT, -- 错误消息
    error_details JSONB DEFAULT '{}', -- 详细错误信息
    retry_count INTEGER DEFAULT 0, -- 重试次数
    max_retries INTEGER DEFAULT 3, -- 最大重试次数

    -- AI交互信息
    ai_request_id UUID, -- 关联AI交互记录
    ai_token_usage INTEGER DEFAULT 0, -- AI Token消耗
    ai_response_time_ms INTEGER, -- AI响应时间

    -- 参与者和影响范围
    participating_characters JSONB DEFAULT '[]', -- 参与角色ID列表
    affected_scenes JSONB DEFAULT '[]', -- 受影响场景ID列表
    affected_entities JSONB DEFAULT '[]', -- 受影响实体ID列表

    -- 元数据
    processing_metadata JSONB DEFAULT '{}', -- 处理过程元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 事件处理步骤详细记录表 (新增)
CREATE TABLE event_processing_steps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    result_id UUID NOT NULL REFERENCES event_processing_results(id) ON DELETE CASCADE,
    step_order INTEGER NOT NULL, -- 步骤顺序
    step_type VARCHAR(50) NOT NULL, -- validation, ai_call, state_update, notification, etc.
    step_name VARCHAR(100) NOT NULL, -- 步骤名称
    step_status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, running, completed, failed, skipped

    -- 步骤执行信息
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    execution_time_ms INTEGER,

    -- 步骤输入输出
    input_data JSONB DEFAULT '{}',
    output_data JSONB DEFAULT '{}',

    -- 错误信息
    error_message TEXT,
    error_details JSONB DEFAULT '{}',

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 事件耗时统计表 (新增)
CREATE TABLE event_timing_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(50) NOT NULL, -- 事件类型
    world_id UUID NOT NULL REFERENCES worlds(id),

    -- 时间统计
    game_time_start BIGINT NOT NULL, -- 游戏内开始时间
    game_time_end BIGINT, -- 游戏内结束时间
    game_time_duration BIGINT, -- 游戏内持续时间
    real_time_duration_ms INTEGER, -- 现实时间处理耗时

    -- 预期时间信息
    estimated_game_duration BIGINT, -- 预计游戏内耗时
    estimated_real_duration_ms INTEGER, -- 预计现实处理耗时

    -- 统计信息
    participant_count INTEGER DEFAULT 0, -- 参与者数量
    complexity_score FLOAT DEFAULT 1.0, -- 复杂度评分

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户角色会话表 (新增 - 支持多角色用户管理)
CREATE TABLE user_character_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    active_character_id UUID REFERENCES characters(id), -- 当前活跃角色

    -- 会话信息
    last_character_switch TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 偏好设置
    character_preferences JSONB DEFAULT '{}', -- 角色相关偏好

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 约束：每个用户在每个世界只能有一个会话
    UNIQUE(user_id, world_id)
);

-- AI交互日志表
CREATE TABLE ai_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID REFERENCES worlds(id),
    user_id UUID REFERENCES users(id),
    interaction_type VARCHAR(50) NOT NULL, -- world_gen, action, evolution, etc.
    prompt_text TEXT NOT NULL,
    response_text TEXT,
    token_usage INTEGER,
    response_time INTEGER, -- 响应时间(毫秒)
    status VARCHAR(20) DEFAULT 'success', -- success, error, timeout
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 内容安全审核表
CREATE TABLE content_audits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_type VARCHAR(50) NOT NULL, -- user_input, ai_output, system_message
    content_source VARCHAR(100), -- 内容来源标识
    original_content TEXT NOT NULL,
    processed_content TEXT, -- 处理后的内容
    audit_result JSONB NOT NULL, -- 审核结果详情
    safety_score FLOAT, -- 安全评分 0-1
    consistency_score FLOAT, -- 一致性评分 0-1
    quality_score FLOAT, -- 质量评分 0-1
    violations JSONB DEFAULT '[]', -- 违规项列表
    action_taken VARCHAR(50), -- block, warn, approve, modify
    reviewer_id UUID REFERENCES users(id), -- 人工审核员ID
    review_notes TEXT, -- 审核备注
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE
);

-- 世界规模框架表 (多玩家世界空间管理)
-- 用途：为多玩家世界提供概念性的空间框架，支持玩家分散初始化和自然汇聚
CREATE TABLE world_frameworks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    framework_data JSONB NOT NULL, -- 世界规模框架数据
    regions JSONB DEFAULT '[]', -- 主要地理区域
    landmarks JSONB DEFAULT '[]', -- 重要地标
    distances JSONB DEFAULT '{}', -- 距离关系
    boundaries JSONB DEFAULT '{}', -- 世界边界
    player_capacity INTEGER DEFAULT 10, -- 玩家容量
    convergence_points JSONB DEFAULT '[]', -- 汇聚点设计
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 玩家初始化分配表 (多玩家分散初始化管理)
-- 用途：管理多玩家世界中的玩家分配，确保合理的初始分布和汇聚路径
CREATE TABLE player_allocations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    allocated_region VARCHAR(100) NOT NULL, -- 分配的区域
    start_position JSONB NOT NULL, -- 起始位置坐标
    distance_to_others JSONB DEFAULT '{}', -- 与其他玩家的距离
    convergence_path JSONB DEFAULT '[]', -- 预设的汇聚路径
    allocation_strategy VARCHAR(50), -- 分配策略
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2.2. 索引设计 (优化后 - 基于查询模式分析)

```sql
-- 基础性能优化索引
CREATE INDEX idx_worlds_creator ON worlds(creator_id);
CREATE INDEX idx_worlds_status_public ON worlds(status, is_public) WHERE status = 'active';
CREATE INDEX idx_scenes_world ON scenes(world_id);
CREATE INDEX idx_scenes_world_type ON scenes(world_id, scene_type);

-- 角色相关索引 (支持多角色用户)
CREATE INDEX idx_characters_world ON characters(world_id);
CREATE INDEX idx_characters_user ON characters(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_characters_user_world ON characters(user_id, world_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_characters_scene ON characters(current_scene_id) WHERE current_scene_id IS NOT NULL;
CREATE INDEX idx_characters_status ON characters(status) WHERE status = 'active';
CREATE INDEX idx_characters_primary ON characters(user_id, world_id, is_primary) WHERE is_primary = true;

-- 实体相关索引 (优化后的位置查询)
CREATE INDEX idx_entities_world ON entities(world_id);
CREATE INDEX idx_entities_world_type ON entities(world_id, entity_type);
CREATE INDEX idx_entities_scene ON entities(current_scene_id) WHERE current_scene_id IS NOT NULL;
CREATE INDEX idx_entities_owner ON entities(owner_character_id) WHERE owner_character_id IS NOT NULL;
CREATE INDEX idx_entities_container ON entities(container_entity_id) WHERE container_entity_id IS NOT NULL;
CREATE INDEX idx_entities_status ON entities(status) WHERE status = 'active';

-- 专门实体表索引
CREATE INDEX idx_items_category ON items(item_category);
CREATE INDEX idx_items_rarity ON items(rarity);
CREATE INDEX idx_items_craftable ON items(is_craftable) WHERE is_craftable = true;
CREATE INDEX idx_events_category ON events(event_category);
CREATE INDEX idx_events_repeatable ON events(is_repeatable) WHERE is_repeatable = true;
CREATE INDEX idx_goals_type_status ON goals(goal_type, goal_status);
CREATE INDEX idx_goals_deadline ON goals(deadline) WHERE deadline IS NOT NULL;

-- 记忆和阅历系统索引 (优化后)
CREATE INDEX idx_character_memories_character ON character_memories(character_id);
CREATE INDEX idx_character_memories_type_importance ON character_memories(memory_type, importance_score DESC);
CREATE INDEX idx_character_memories_strength ON character_memories(current_strength DESC) WHERE current_strength > 0.1;
CREATE INDEX idx_character_memories_accessed ON character_memories(last_accessed DESC);

CREATE INDEX idx_character_experiences_character ON character_experiences(character_id);
CREATE INDEX idx_character_experiences_category_level ON character_experiences(experience_category, proficiency_level DESC);
CREATE INDEX idx_character_experiences_success_rate ON character_experiences(success_rate DESC) WHERE total_attempts > 0;
CREATE INDEX idx_character_experiences_recent ON character_experiences(last_used DESC);

-- 事件处理结果索引 (新增)
CREATE INDEX idx_event_results_event ON event_processing_results(event_id);
CREATE INDEX idx_event_results_world_status ON event_processing_results(world_id, processing_status);
CREATE INDEX idx_event_results_batch ON event_processing_results(processing_batch_id);
CREATE INDEX idx_event_results_time_range ON event_processing_results(started_at, completed_at);
CREATE INDEX idx_event_steps_result_order ON event_processing_steps(result_id, step_order);
CREATE INDEX idx_timing_stats_world_type ON event_timing_stats(world_id, event_type);

-- 游戏事件日志索引 (优化后)
CREATE INDEX idx_game_events_world_time ON game_events(world_id, game_time DESC);
CREATE INDEX idx_game_events_actor ON game_events(actor_id) WHERE actor_id IS NOT NULL;
CREATE INDEX idx_game_events_scene_time ON game_events(scene_id, game_time DESC) WHERE scene_id IS NOT NULL;
CREATE INDEX idx_game_events_batch ON game_events(batch_id) WHERE batch_id IS NOT NULL;

-- 用户会话索引 (新增)
CREATE INDEX idx_user_sessions_user_world ON user_character_sessions(user_id, world_id);
CREATE INDEX idx_user_sessions_character ON user_character_sessions(active_character_id) WHERE active_character_id IS NOT NULL;
CREATE INDEX idx_user_sessions_activity ON user_character_sessions(last_activity DESC);

-- AI交互索引
CREATE INDEX idx_ai_interactions_world ON ai_interactions(world_id) WHERE world_id IS NOT NULL;
CREATE INDEX idx_ai_interactions_user ON ai_interactions(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_ai_interactions_type_time ON ai_interactions(interaction_type, created_at DESC);
CREATE INDEX idx_ai_interactions_status ON ai_interactions(status) WHERE status != 'success';

-- 内容安全索引
CREATE INDEX idx_content_audits_type_time ON content_audits(content_type, created_at DESC);
CREATE INDEX idx_content_audits_safety_score ON content_audits(safety_score) WHERE safety_score < 0.8;
CREATE INDEX idx_content_audits_action ON content_audits(action_taken) WHERE action_taken != 'approve';

-- 高性能复合索引 (基于常见查询模式)
CREATE INDEX idx_characters_world_user_active ON characters(world_id, user_id, status) WHERE status = 'active' AND user_id IS NOT NULL;
CREATE INDEX idx_entities_world_type_scene ON entities(world_id, entity_type, current_scene_id) WHERE current_scene_id IS NOT NULL;
CREATE INDEX idx_memories_character_type_strength ON character_memories(character_id, memory_type, current_strength DESC) WHERE current_strength > 0.1;
CREATE INDEX idx_experiences_character_category_level ON character_experiences(character_id, experience_category, proficiency_level DESC);
CREATE INDEX idx_game_events_world_type_time ON game_events(world_id, event_type, game_time DESC);
```

## 2.3. 场景连接关系详细设计

### 2.3.1 connections字段JSON结构规范 (增强版)

基于需求文档中的"方向性与入口特性"要求，connections字段采用以下标准化JSON结构。
本版本在原有基础上增加了动态连接状态管理、智能路径建议和增强的叙事系统：

```json
{
  "connections": [
    {
      "connection_id": "uuid",
      "target_scene_id": "scene_uuid",
      "connection_type": "bidirectional|unidirectional|conditional|temporary",
      "direction": {
        "from_direction": "north|south|east|west|up|down|portal|custom",
        "to_direction": "south|north|west|east|down|up|portal|custom",
        "entrance_description": "从北方山路进入村庄，你看到炊烟袅袅",
        "exit_description": "向南离开村庄前往蜿蜒的山路"
      },
      "accessibility": {
        "is_accessible": true,
        "required_conditions": [
          {
            "type": "item_required|trait_required|time_condition|event_condition|character_level",
            "condition_description": "需要持有村长的通行证",
            "condition_value": "village_pass_item_id",
            "operator": "equals|contains|greater_than|less_than"
          }
        ],
        "travel_time": 30,
        "travel_difficulty": "easy|normal|hard|extreme",
        "travel_cost": {
          "energy": 10,
          "resources": [
            {"resource_type": "stamina", "amount": 5}
          ]
        }
      },
      "connection_properties": {
        "is_hidden": false,
        "discovery_requirements": [
          {
            "type": "exploration_skill|specific_action|random_chance",
            "requirement": "仔细搜索墙壁",
            "success_rate": 0.7
          }
        ],
        "connection_state": "open|closed|locked|destroyed|under_construction",
        "state_change_conditions": [
          {
            "trigger": "time_of_day|weather|event_completion",
            "new_state": "closed",
            "condition": "夜晚时分自动关闭"
          }
        ]
      },
      "environmental_effects": {
        "weather_impact": true,
        "time_of_day_impact": true,
        "seasonal_changes": [
          {
            "season": "winter",
            "effect": "travel_time_multiplier",
            "value": 1.5,
            "description": "冬季雪深路滑，行进缓慢"
          }
        ]
      },
      "narrative_variations": {
        "first_time_description": "你第一次踏上这条通往村庄的小径",
        "repeat_visit_description": "你再次走上熟悉的村庄小径",
        "weather_descriptions": {
          "rain": "雨水让小径变得泥泞不堪",
          "snow": "雪花覆盖了通往村庄的道路"
        },

        // 新增：基于角色特质的个性化描述
        "trait_based_descriptions": {
          "勇敢的": "你毫不犹豫地踏上这条看似危险的道路",
          "谨慎的": "你小心翼翼地观察着道路两旁，确认安全后才前进",
          "好奇的": "你兴奋地探索着这条充满未知的道路"
        },

        // 新增：基于时间和历史的动态描述
        "contextual_descriptions": {
          "after_battle": "战斗的痕迹仍然清晰可见，道路上散落着武器碎片",
          "during_festival": "道路两旁装饰着彩带，空气中弥漫着节日的欢乐气氛",
          "after_disaster": "曾经熟悉的道路现在变得陌生，到处都是灾难留下的痕迹"
        },

        // 新增：群体旅行的特殊描述
        "group_travel_descriptions": {
          "solo": "你独自一人走在寂静的道路上",
          "small_group": "你和同伴们一起踏上旅程",
          "large_group": "一行人浩浩荡荡地行进在道路上"
        }
      },

      // 新增：动态连接状态管理
      "dynamic_properties": {
        "is_temporary": false,
        "creation_time": "2024-01-08T10:00:00Z",
        "expiry_time": null,
        "created_by_event": "bridge_construction_event_id",
        "can_be_destroyed": true,
        "destruction_conditions": [
          {
            "type": "time_limit|event_trigger|character_action",
            "condition": "地震事件发生时桥梁坍塌",
            "probability": 0.3
          }
        ]
      },

      // 新增：智能路径建议
      "pathfinding_metadata": {
        "preferred_route": false,
        "scenic_route": true,
        "danger_level": "low|medium|high|extreme",
        "recommended_for": ["exploration", "trade", "escape"],
        "avoid_for": ["stealth", "heavy_cargo"],
        "alternative_routes": ["connection_id_1", "connection_id_2"]
      }
    }
  ]
}
```

### 2.3.2 连接类型定义

1. **双向连接 (bidirectional)**:
   - A和B可以相互到达
   - 两个方向可能有不同的条件和描述

2. **单向连接 (unidirectional)**:
   - 只能从A到B，不能从B回到A
   - 适用于悬崖跳跃、传送门等场景

3. **条件连接 (conditional)**:
   - 需要满足特定条件才能通过
   - 支持复杂的条件组合逻辑

### 2.3.3 方向系统

- **基础方向**: north, south, east, west, up, down
- **特殊方向**: portal（传送门）, custom（自定义方向）
- **入口特性**: 每个方向都有独特的entrance_description和exit_description

### 2.3.4 查询用例示例

```sql
-- 查询从指定场景可以到达的所有场景
SELECT
    s2.id, s2.name,
    conn->>'entrance_description' as entrance_desc,
    (conn->'accessibility'->>'travel_time')::integer as travel_time
FROM scenes s1
CROSS JOIN LATERAL jsonb_array_elements(s1.connections) AS conn
JOIN scenes s2 ON s2.id = (conn->>'target_scene_id')::uuid
WHERE s1.id = $1
  AND (conn->'accessibility'->>'is_accessible')::boolean = true;

-- 查询需要特定物品才能通过的连接
SELECT
    s1.name as from_scene,
    s2.name as to_scene,
    cond->>'condition_description' as requirement
FROM scenes s1
CROSS JOIN LATERAL jsonb_array_elements(s1.connections) AS conn
CROSS JOIN LATERAL jsonb_array_elements(conn->'accessibility'->'required_conditions') AS cond
JOIN scenes s2 ON s2.id = (conn->>'target_scene_id')::uuid
WHERE cond->>'type' = 'item_required';
```

## 2.4. 核心JSONB字段结构规范

### 2.4.1 用户相关JSONB字段

**users.preferences (用户偏好设置)**
```json
{
  "ui_settings": {
    "theme": "dark|light|auto",
    "font_size": "small|medium|large",
    "animation_enabled": true,
    "sound_enabled": true,
    "notification_settings": {
      "game_updates": true,
      "system_messages": true,
      "social_interactions": false
    }
  },
  "game_preferences": {
    "auto_save_interval": 300,
    "preferred_language": "zh-CN|en-US",
    "content_filter_level": "none|mild|strict",
    "ai_response_speed": "fast|balanced|detailed"
  },
  "privacy_settings": {
    "profile_visibility": "public|friends|private",
    "world_sharing_default": "private|friends|public",
    "activity_tracking": true
  }
}
```

**users.idp_claims (身份提供商声明)**
```json
{
  "provider_user_id": "external_user_123",
  "email_verified": true,
  "profile_picture": "https://example.com/avatar.jpg",
  "locale": "zh-CN",
  "timezone": "Asia/Shanghai",
  "subscription_tier": "free|premium|enterprise",
  "provider_metadata": {
    "account_created": "2024-01-01T00:00:00Z",
    "last_login": "2024-01-08T10:30:00Z"
  }
}
```

### 2.4.2 世界相关JSONB字段

**worlds.world_config (世界配置)**
```json
{
  "time_settings": {
    "time_multiplier": 1.0,
    "pause_when_empty": true,
    "day_night_cycle": true,
    "season_length_days": 30
  },
  "world_rules": {
    "magic_enabled": true,
    "technology_level": "medieval|renaissance|modern",
    "death_consequences": "temporary|permanent|respawn",
    "pvp_enabled": false
  },
  "ai_settings": {
    "creativity_level": "conservative|balanced|creative",
    "narrative_style": "serious|humorous|dramatic",
    "content_rating": "family|teen|mature"
  },
  "world_theme": {
    "genre": "fantasy|sci-fi|historical|modern",
    "mood": "light|dark|neutral",
    "complexity": "simple|medium|complex"
  }
}
```

**worlds.world_state (世界状态)**
```json
{
  "current_time": {
    "game_minutes": 1440,
    "day": 1,
    "season": "spring",
    "weather": "sunny",
    "time_of_day": "morning"
  },
  "global_events": [
    {
      "event_id": "uuid",
      "name": "王国庆典",
      "status": "active|pending|completed",
      "start_time": 1000,
      "end_time": 1500,
      "affected_regions": ["中央平原", "北方山脉"]
    }
  ],
  "world_statistics": {
    "total_characters": 150,
    "active_players": 5,
    "completed_quests": 23,
    "major_events": 8
  },
  "environmental_state": {
    "global_weather_pattern": "stable|changing|extreme",
    "resource_availability": {
      "food": "abundant|normal|scarce",
      "materials": "abundant|normal|scarce"
    }
  }
}
```

### 2.4.3 场景相关JSONB字段

**scenes.properties (场景属性)**
```json
{
  "physical_properties": {
    "size": "tiny|small|medium|large|huge",
    "terrain": "plains|forest|mountain|desert|urban|dungeon",
    "elevation": 100,
    "climate": "temperate|cold|hot|humid|arid"
  },
  "accessibility": {
    "light_level": "bright|dim|dark|pitch_black",
    "noise_level": "silent|quiet|normal|loud|deafening",
    "safety_level": "safe|caution|dangerous|deadly"
  },
  "special_features": [
    {
      "feature_type": "landmark|resource|hazard|secret",
      "name": "古老的石碑",
      "description": "刻满了古代文字的神秘石碑",
      "interaction_available": true
    }
  ],
  "scene_modifiers": {
    "movement_speed": 1.0,
    "visibility_range": "normal|limited|enhanced",
    "magic_influence": "none|weak|strong|chaotic"
  }
}
```

**scenes.environment (环境信息)**
```json
{
  "weather": {
    "condition": "sunny|cloudy|rainy|stormy|snowy|foggy",
    "temperature": 20,
    "humidity": 60,
    "wind_speed": "calm|light|moderate|strong|gale"
  },
  "time_effects": {
    "dawn_description": "晨光透过树叶洒下斑驳的光影",
    "day_description": "阳光明媚，鸟儿在枝头歌唱",
    "dusk_description": "夕阳西下，天空染成金黄色",
    "night_description": "月光如水，星辰满天"
  },
  "seasonal_variations": {
    "spring": "万物复苏，花朵盛开",
    "summer": "绿意盎然，生机勃勃",
    "autumn": "叶子变黄，果实累累",
    "winter": "雪花纷飞，银装素裹"
  },
  "ambient_effects": [
    {
      "type": "sound|smell|visual|tactile",
      "description": "远处传来潺潺的流水声",
      "intensity": "subtle|noticeable|prominent|overwhelming"
    }
  ]
}
```

## 3. 缓存层数据设计

### 3.1. Redis数据结构设计

#### 会话管理
```
# 用户会话
user:session:{user_id} -> {
    "token": "jwt_token",
    "refresh_token": "refresh_token", 
    "expires_at": timestamp,
    "current_world_id": "world_uuid"
}

# 在线用户集合
online_users -> Set[user_id]
```

#### 游戏状态缓存
```
# 世界实时状态
world:state:{world_id} -> {
    "current_time": game_time,
    "active_players": [user_ids],
    "pending_actions": [action_objects],
    "last_evolution": timestamp
}

# 场景快照
scene:snapshot:{scene_id} -> {
    "entities": [entity_ids],
    "characters": [character_ids],
    "last_updated": timestamp
}

# 角色状态缓存
character:state:{character_id} -> {
    "current_scene": scene_id,
    "recent_actions": [actions],
    "active_memories": [memories]
}
```

#### 队列系统
```
# AI处理队列
queue:ai_requests -> List[{
    "request_id": uuid,
    "world_id": uuid,
    "type": "action|evolution|generation",
    "priority": number,
    "data": object,
    "created_at": timestamp
}]

# 通知队列
queue:notifications -> List[{
    "user_id": uuid,
    "type": "game_update|system",
    "message": string,
    "data": object
}]
```

### 3.2. 缓存策略

#### 缓存更新策略
- **Write-Through**: 用户信息、世界配置等重要数据
- **Write-Behind**: 游戏事件日志、统计数据
- **Cache-Aside**: 场景数据、角色状态等频繁变化的数据

#### 缓存失效策略
- **TTL**: 临时状态数据(5-30分钟)
- **主动失效**: 关键数据变更时主动清除相关缓存
- **版本控制**: 使用版本号避免缓存不一致

## 4. 传输层数据设计

### 4.1. API数据格式

#### 标准响应格式
```json
{
    "success": true,
    "data": {},
    "message": "操作成功",
    "code": 200,
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid"
}
```

#### 错误响应格式
```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "输入数据验证失败",
        "details": {
            "field": "username",
            "reason": "用户名已存在"
        }
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid"
}
```

### 4.2. WebSocket消息格式

#### 游戏状态更新
```json
{
    "type": "game_update",
    "world_id": "uuid",
    "data": {
        "narrative": "你看到一个神秘的陌生人走向你...",
        "scene_changes": {},
        "character_changes": {},
        "new_entities": []
    },
    "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 实时通知
```json
{
    "type": "notification",
    "category": "system|game|social",
    "title": "系统通知",
    "message": "你的世界有新的访客",
    "data": {},
    "timestamp": "2024-01-01T00:00:00Z"
}
```

## 5. 界面层数据设计

### 5.1. 前端状态管理

#### 全局状态结构
```typescript
interface AppState {
    user: UserState;
    game: GameState;
    ui: UIState;
    cache: CacheState;
}

interface UserState {
    profile: UserProfile | null;
    preferences: UserPreferences;
    isAuthenticated: boolean;
    permissions: string[];
}

interface GameState {
    currentWorld: WorldState | null;
    character: CharacterState | null;
    scene: SceneState | null;
    inventory: InventoryState;
    history: GameEvent[];
}

interface UIState {
    loading: boolean;
    activePanel: string;
    notifications: Notification[];
    modals: ModalState[];
}
```

### 5.2. 组件数据模型

#### 游戏主界面数据
```typescript
interface GameMainProps {
    narrative: NarrativeData;
    character: CharacterData;
    scene: SceneData;
    availableActions: ActionData[];
    onAction: (action: string) => void;
}

interface NarrativeData {
    text: string;
    timestamp: string;
    type: 'story' | 'action' | 'system';
    entities: EntityReference[];
}
```

## 6. 数据安全与隐私

### 6.1. 敏感数据处理
- **密码**: 使用bcrypt加密存储
- **个人信息**: 支持数据脱敏和匿名化
- **游戏内容**: 内容审核和过滤机制

### 6.2. 数据备份策略
- **实时备份**: 关键数据的实时同步备份
- **定期备份**: 每日全量备份和增量备份
- **异地备份**: 多地域数据备份保障

### 6.3. 数据访问控制
- **行级安全**: 基于用户权限的数据访问控制
- **字段级加密**: 敏感字段的透明加密
- **审计日志**: 完整的数据访问和修改日志

## 7. 数据库迁移系统设计

### 7.1. 迁移表结构
```sql
-- 数据库迁移版本管理表
CREATE TABLE schema_migrations (
    version BIGINT PRIMARY KEY,           -- 迁移版本号 (时间戳格式: 20240101120000)
    filename VARCHAR(255) NOT NULL,       -- 迁移文件名
    checksum VARCHAR(64) NOT NULL,        -- 文件校验和
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    execution_time INTEGER,               -- 执行时间(毫秒)
    success BOOLEAN DEFAULT TRUE,         -- 执行是否成功
    error_message TEXT                    -- 错误信息
);

-- 迁移锁表 (防止并发执行)
CREATE TABLE migration_lock (
    id INTEGER PRIMARY KEY DEFAULT 1,
    locked BOOLEAN DEFAULT FALSE,
    locked_at TIMESTAMP WITH TIME ZONE,
    locked_by VARCHAR(255),              -- 执行迁移的实例ID
    CONSTRAINT single_lock CHECK (id = 1)
);
```

### 7.2. Go语言迁移工具设计
```go
// 迁移接口定义
type Migration interface {
    Up() error      // 执行迁移
    Down() error    // 回滚迁移
    Version() int64 // 获取版本号
    Description() string // 获取描述
}

// 迁移管理器
type MigrationManager struct {
    db          *sql.DB
    migrations  []Migration
    lockTimeout time.Duration
}

// 迁移执行结果
type MigrationResult struct {
    Version       int64         `json:"version"`
    Filename      string        `json:"filename"`
    ExecutionTime time.Duration `json:"execution_time"`
    Success       bool          `json:"success"`
    Error         string        `json:"error,omitempty"`
}
```

### 7.3. 迁移脚本示例
```go
// migrations/20240101120000_add_external_auth.go
package migrations

import (
    "database/sql"
    "fmt"
)

type Migration20240101120000 struct{}

func (m *Migration20240101120000) Version() int64 {
    return 20240101120000
}

func (m *Migration20240101120000) Description() string {
    return "添加外部认证支持"
}

func (m *Migration20240101120000) Up() error {
    queries := []string{
        `ALTER TABLE users ADD COLUMN external_id VARCHAR(255)`,
        `ALTER TABLE users ADD COLUMN external_provider VARCHAR(50)`,
        `CREATE UNIQUE INDEX idx_users_external ON users(external_id, external_provider)`,
    }

    for _, query := range queries {
        if _, err := db.Exec(query); err != nil {
            return fmt.Errorf("执行迁移失败: %w", err)
        }
    }
    return nil
}

func (m *Migration20240101120000) Down() error {
    queries := []string{
        `DROP INDEX IF EXISTS idx_users_external`,
        `ALTER TABLE users DROP COLUMN IF EXISTS external_provider`,
        `ALTER TABLE users DROP COLUMN IF EXISTS external_id`,
    }

    for _, query := range queries {
        if _, err := db.Exec(query); err != nil {
            return fmt.Errorf("回滚迁移失败: %w", err)
        }
    }
    return nil
}
```

### 7.4. 迁移执行流程
1. **获取迁移锁**: 防止并发执行
2. **检查当前版本**: 从schema_migrations表获取最新版本
3. **收集待执行迁移**: 找出所有未执行的迁移
4. **验证迁移文件**: 检查文件完整性和依赖关系
5. **执行迁移**: 按版本顺序执行迁移
6. **记录执行结果**: 更新schema_migrations表
7. **释放迁移锁**: 完成后释放锁

### 7.5. 迁移最佳实践
- **版本命名**: 使用时间戳格式确保唯一性和顺序性
- **原子性**: 每个迁移在事务中执行，确保原子性
- **幂等性**: 迁移脚本应该是幂等的，可重复执行
- **向后兼容**: 尽量保持向后兼容，避免破坏性变更
- **测试验证**: 在测试环境充分验证后再应用到生产环境

## 8. Go语言数据结构设计

### 8.1. 核心数据模型
```go
// 用户模型
type User struct {
    ID               string            `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
    ExternalID       string            `json:"external_id" gorm:"uniqueIndex:idx_external_user"`
    ExternalProvider string            `json:"external_provider" gorm:"uniqueIndex:idx_external_user"`
    Email            string            `json:"email" gorm:"not null"`
    DisplayName      string            `json:"display_name"`
    AvatarURL        string            `json:"avatar_url"`
    GameRoles        pq.StringArray    `json:"game_roles" gorm:"type:text[]"`
    Status           string            `json:"status" gorm:"default:active"`
    Preferences      datatypes.JSON    `json:"preferences" gorm:"type:jsonb"`
    IDPClaims        datatypes.JSON    `json:"idp_claims" gorm:"type:jsonb"`
    CreatedAt        time.Time         `json:"created_at"`
    UpdatedAt        time.Time         `json:"updated_at"`
    LastLoginAt      *time.Time        `json:"last_login_at"`
}

// 游戏世界模型
type World struct {
    ID          string         `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
    Name        string         `json:"name" gorm:"not null"`
    Description string         `json:"description"`
    CreatorID   string         `json:"creator_id" gorm:"not null"`
    Settings    datatypes.JSON `json:"settings" gorm:"type:jsonb"`
    Status      string         `json:"status" gorm:"default:active"`
    IsPublic    bool           `json:"is_public" gorm:"default:false"`
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`

    // 关联关系
    Creator     User           `json:"creator" gorm:"foreignKey:CreatorID"`
    Scenes      []Scene        `json:"scenes,omitempty"`
    Characters  []Character    `json:"characters,omitempty"`
}
```

## 9. 核心游戏场景数据库查询用例

### 9.1 世界创建场景查询

```sql
-- 创建新世界时的数据插入
INSERT INTO worlds (name, description, creator_id, world_config, world_state)
VALUES ($1, $2, $3, $4, $5)
RETURNING id;

-- 为新世界创建初始场景
INSERT INTO scenes (world_id, name, description, scene_type, properties, connections)
VALUES ($1, $2, $3, 'starting', $4, $5)
RETURNING id;

-- 查询用户创建的世界列表
SELECT w.*, u.display_name as creator_name,
       COUNT(c.id) as character_count,
       COUNT(DISTINCT c.user_id) as player_count
FROM worlds w
JOIN users u ON w.creator_id = u.id
LEFT JOIN characters c ON w.id = c.world_id AND c.status = 'active'
WHERE w.creator_id = $1 OR w.is_public = true
GROUP BY w.id, u.display_name
ORDER BY w.updated_at DESC;
```

### 9.2 角色管理和多角色支持查询

```sql
-- 查询用户在指定世界的所有角色
SELECT c.*, s.name as current_scene_name,
       ucs.active_character_id = c.id as is_active
FROM characters c
LEFT JOIN scenes s ON c.current_scene_id = s.id
LEFT JOIN user_character_sessions ucs ON ucs.user_id = c.user_id AND ucs.world_id = c.world_id
WHERE c.user_id = $1 AND c.world_id = $2 AND c.status = 'active'
ORDER BY c.is_primary DESC, c.display_order ASC;

-- 切换活跃角色
UPDATE user_character_sessions
SET active_character_id = $3, last_character_switch = NOW()
WHERE user_id = $1 AND world_id = $2;

-- 创建新角色时检查名称唯一性
SELECT COUNT(*) FROM characters
WHERE world_id = $1 AND name = $2 AND deleted_at IS NULL;
```

### 9.3 场景探索和连接查询

```sql
-- 获取当前场景的详细信息和可用连接
WITH scene_connections AS (
    SELECT
        conn->>'target_scene_id' as target_id,
        conn->'direction'->>'from_direction' as direction,
        conn->'accessibility'->>'travel_time' as travel_time,
        conn->'accessibility'->>'is_accessible' as is_accessible,
        conn->>'entrance_description' as description
    FROM scenes s
    CROSS JOIN LATERAL jsonb_array_elements(s.connections) AS conn
    WHERE s.id = $1
)
SELECT s.*, sc.direction, sc.travel_time, sc.description
FROM scene_connections sc
JOIN scenes s ON s.id = sc.target_id::uuid
WHERE sc.is_accessible::boolean = true;

-- 查询场景中的所有实体（物品、事件等）
SELECT e.*,
       CASE
           WHEN i.entity_id IS NOT NULL THEN 'item'
           WHEN ev.entity_id IS NOT NULL THEN 'event'
           WHEN g.entity_id IS NOT NULL THEN 'goal'
           ELSE e.entity_type
       END as specific_type,
       COALESCE(i.item_category, ev.event_category, g.goal_type) as category
FROM entities e
LEFT JOIN items i ON e.id = i.entity_id
LEFT JOIN events ev ON e.id = ev.entity_id
LEFT JOIN goals g ON e.id = g.entity_id
WHERE e.current_scene_id = $1 AND e.status = 'active'
ORDER BY e.entity_type, e.name;
```

### 9.4 记忆和阅历系统查询

```sql
-- 获取角色的重要记忆（用于AI上下文）
SELECT cm.*,
       EXTRACT(EPOCH FROM (NOW() - cm.last_accessed))/3600 as hours_since_access
FROM character_memories cm
WHERE cm.character_id = $1
  AND cm.current_strength > 0.3
ORDER BY cm.importance_score DESC, cm.last_accessed DESC
LIMIT 20;

-- 更新记忆强度（遗忘机制）
UPDATE character_memories
SET current_strength = GREATEST(0, current_strength - (decay_rate * $2)),
    updated_at = NOW()
WHERE character_id = $1
  AND last_accessed < NOW() - INTERVAL '1 hour';

-- 查询相关阅历（用于事件处理）
SELECT ce.*,
       CASE
           WHEN ce.total_attempts > 0 THEN ce.success_count::FLOAT / ce.total_attempts
           ELSE 0
       END as success_rate
FROM character_experiences ce
WHERE ce.character_id = $1
  AND ce.experience_category = $2
  AND ce.proficiency_level > 0
ORDER BY ce.proficiency_level DESC, ce.last_used DESC;
```

### 9.5 事件处理结果查询

```sql
-- 创建事件处理结果记录
INSERT INTO event_processing_results (
    event_id, world_id, processing_batch_id,
    participating_characters, estimated_duration_ms
) VALUES ($1, $2, $3, $4, $5)
RETURNING id;

-- 更新事件处理结果
UPDATE event_processing_results
SET processing_status = $2,
    completed_at = NOW(),
    processing_duration_ms = EXTRACT(EPOCH FROM (NOW() - started_at)) * 1000,
    success_status = $3,
    result_data = $4,
    narrative_output = $5,
    state_changes = $6
WHERE id = $1;

-- 查询批处理中的所有事件结果
SELECT epr.*, e.name as event_name, e.entity_type
FROM event_processing_results epr
JOIN entities e ON epr.event_id = e.id
WHERE epr.processing_batch_id = $1
ORDER BY epr.started_at;

-- 统计事件处理性能
SELECT
    event_type,
    COUNT(*) as total_events,
    AVG(processing_duration_ms) as avg_duration,
    AVG(ai_response_time_ms) as avg_ai_time,
    SUM(ai_token_usage) as total_tokens
FROM event_processing_results epr
JOIN entities e ON epr.event_id = e.id
WHERE epr.completed_at >= NOW() - INTERVAL '24 hours'
GROUP BY event_type
ORDER BY total_events DESC;
```

### 9.6 AI交互优化查询

```sql
-- 记录AI交互
INSERT INTO ai_interactions (
    world_id, user_id, interaction_type,
    prompt_text, response_text, token_usage, response_time
) VALUES ($1, $2, $3, $4, $5, $6, $7);

-- 查询AI交互统计（用于成本控制）
SELECT
    interaction_type,
    COUNT(*) as request_count,
    AVG(token_usage) as avg_tokens,
    AVG(response_time) as avg_response_time,
    SUM(token_usage) as total_tokens
FROM ai_interactions
WHERE created_at >= NOW() - INTERVAL '1 day'
GROUP BY interaction_type
ORDER BY total_tokens DESC;
```

## 10. 性能优化建议

### 10.1 查询优化策略

1. **分页查询**: 对于大数据量查询使用LIMIT和OFFSET
2. **索引利用**: 确保WHERE条件字段都有适当的索引
3. **JSON查询优化**: 使用GIN索引优化JSONB字段查询
4. **连接优化**: 避免不必要的JOIN，使用EXISTS替代IN子查询

### 10.2 缓存策略

1. **场景数据缓存**: 缓存热门场景的基础信息
2. **角色状态缓存**: 缓存活跃角色的核心状态
3. **连接关系缓存**: 缓存场景间的连接关系
4. **AI结果缓存**: 缓存相似AI请求的结果

### 10.3 数据库维护

1. **定期清理**: 清理过期的事件处理结果和AI交互日志
2. **统计信息更新**: 定期更新表统计信息以优化查询计划
3. **索引维护**: 监控索引使用情况，删除未使用的索引
4. **分区策略**: 对大表考虑按时间分区

## 11. 核心数据模型关系图和数据流设计

### 11.1 实体关系图 (ERD)

```mermaid
erDiagram
    USERS ||--o{ WORLDS : creates
    USERS ||--o{ CHARACTERS : owns
    USERS ||--o{ USER_CHARACTER_SESSIONS : has

    WORLDS ||--o{ SCENES : contains
    WORLDS ||--o{ CHARACTERS : hosts
    WORLDS ||--o{ ENTITIES : contains
    WORLDS ||--o{ GAME_EVENTS : logs
    WORLDS ||--o{ EVENT_PROCESSING_RESULTS : processes

    SCENES ||--o{ CHARACTERS : current_location
    SCENES ||--o{ ENTITIES : current_location

    CHARACTERS ||--o{ CHARACTER_MEMORIES : remembers
    CHARACTERS ||--o{ CHARACTER_EXPERIENCES : gains
    CHARACTERS ||--o{ ENTITIES : owns
    CHARACTERS ||--o{ GAME_EVENTS : participates

    ENTITIES ||--o{ ITEMS : extends
    ENTITIES ||--o{ EVENTS : extends
    ENTITIES ||--o{ GOALS : extends
    ENTITIES ||--o{ EVENT_PROCESSING_RESULTS : triggers

    EVENT_PROCESSING_RESULTS ||--o{ EVENT_PROCESSING_STEPS : contains

    USERS {
        uuid id PK
        string external_id
        string external_provider
        string email
        string display_name
        string status
        jsonb preferences
        timestamp created_at
    }

    WORLDS {
        uuid id PK
        string name
        text description
        uuid creator_id FK
        jsonb world_config
        jsonb world_state
        string status
        boolean is_public
        timestamp created_at
    }

    SCENES {
        uuid id PK
        uuid world_id FK
        string name
        text description
        string scene_type
        jsonb connections
        jsonb properties
        jsonb environment
        timestamp created_at
    }

    CHARACTERS {
        uuid id PK
        uuid world_id FK
        uuid user_id FK
        string name
        string character_type
        uuid current_scene_id FK
        jsonb traits
        string status
        boolean is_primary
        timestamp created_at
    }

    ENTITIES {
        uuid id PK
        uuid world_id FK
        string entity_type
        string name
        jsonb base_properties
        uuid current_scene_id FK
        uuid owner_character_id FK
        string status
        timestamp created_at
    }
```

### 11.2 数据流架构图

```mermaid
flowchart TD
    A[用户请求] --> B[API网关]
    B --> C[认证中间件]
    C --> D[业务逻辑层]

    D --> E{请求类型}
    E -->|游戏行动| F[游戏服务]
    E -->|AI生成| G[AI服务]
    E -->|用户管理| H[用户服务]

    F --> I[事件队列]
    I --> J[批处理器]
    J --> K[AI接口调用]
    K --> L[结果处理器]
    L --> M[状态更新器]

    G --> N[Prompt构建器]
    N --> O[AI接口]
    O --> P[响应解析器]
    P --> Q[结果验证器]

    M --> R[(PostgreSQL)]
    Q --> R
    H --> R

    R --> S[缓存更新]
    S --> T[(Redis缓存)]

    L --> U[通知服务]
    U --> V[WebSocket]
    V --> W[前端更新]

    subgraph "数据存储层"
        R
        T
        X[(文件存储)]
    end

    subgraph "外部服务"
        O
        Y[身份提供商]
    end

    C --> Y
```

### 11.3 核心业务数据流

#### 11.3.1 世界创建数据流
```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API服务
    participant WS as 世界服务
    participant AI as AI服务
    participant DB as 数据库

    U->>API: 创建世界请求
    API->>WS: 验证并处理请求
    WS->>AI: 生成世界框架
    AI-->>WS: 返回世界数据
    WS->>DB: 保存世界信息
    WS->>DB: 创建初始场景
    WS->>DB: 创建玩家角色
    DB-->>WS: 确认创建成功
    WS-->>API: 返回世界ID
    API-->>U: 创建成功响应
```

#### 11.3.2 事件处理数据流
```mermaid
sequenceDiagram
    participant C as 角色
    participant ES as 事件服务
    participant Q as 处理队列
    participant AI as AI服务
    participant DB as 数据库
    participant N as 通知服务

    C->>ES: 触发事件
    ES->>Q: 加入处理队列
    Q->>DB: 创建处理结果记录
    Q->>AI: 发送处理请求
    AI-->>Q: 返回处理结果
    Q->>DB: 更新处理结果
    Q->>DB: 应用状态变更
    Q->>N: 发送通知
    N-->>C: 推送结果
```

本数据设计确保了系统的数据一致性、性能和安全性，同时支持复杂的游戏逻辑、AI集成需求和数据库版本管理。通过优化后的表结构、索引设计和查询用例，系统能够高效支持AI文本游戏的各种核心场景。

## 12. 数据库表详细中文说明

### 12.1 用户相关表说明

#### users (用户基础信息表)
**表用途**：存储通过外部身份提供商（IDP）认证的用户基本信息，作为游戏内用户身份的核心标识

**主要字段含义**：
- `external_id` + `external_provider`：外部身份提供商的用户唯一标识，支持多种IDP（如Google、GitHub等）
- `game_roles`：用户在游戏内的角色权限，支持普通用户、高级用户、管理员等多级权限
- `preferences`：用户个性化设置，包括界面主题、通知偏好、游戏设置等
- `idp_claims`：从外部IDP获取的额外用户信息，如头像、邮箱验证状态等

**业务场景**：用户注册登录、权限验证、个性化设置管理、跨平台身份同步

#### user_stats (用户统计信息表)
**表用途**：记录用户的游戏活动统计数据，用于成就系统、等级系统和用户行为分析

**主要字段含义**：
- `total_play_time`：累计游戏时长，用于活跃度统计和成就解锁
- `worlds_created/joined`：创建和加入的世界数量，反映用户的参与度
- `achievements`：已获得的成就列表，采用JSON格式存储成就ID和获得时间
- `level` + `experience`：用户等级系统，基于游戏活动自动计算

**业务场景**：用户成长体系、活跃度分析、个性化推荐、社区排行榜

### 12.2 游戏世界相关表说明

#### worlds (游戏世界表)
**表用途**：存储每个独立游戏世界的基本信息和配置，是整个游戏内容的顶层容器

**主要字段含义**：
- `world_config`：世界规则配置，包括时间倍率、魔法设定、死亡机制等核心游戏规则
- `world_state`：当前世界状态，包括游戏时间、全局事件、环境状态等动态信息
- `is_public` + `max_players`：世界的开放性设置，控制其他玩家是否可以加入
- `game_time`：世界内部的游戏时间，独立于现实时间流逝

**业务场景**：世界创建和管理、多人游戏协调、世界分享、游戏进度保存

#### scenes (场景表)
**表用途**：游戏世界的基本空间单位，每个场景都是一个独立的游戏区域容器

**主要字段含义**：
- `connections`：场景间的连接关系，支持复杂的移动逻辑和条件判断
- `properties`：场景的物理和逻辑属性，如大小、地形、特殊功能等
- `environment`：环境信息，包括天气、光照、季节变化等影响游戏体验的因素
- `access_level` + `scene_state`：访问控制和场景状态，支持动态的场景开放性管理

**业务场景**：世界探索、场景生成、环境互动、空间导航

#### characters (角色表)
**表用途**：存储所有游戏角色（玩家角色和NPC）的基本信息，实现统一的角色管理

**主要字段含义**：
- `user_id`：关联的用户ID，NULL表示NPC，支持一个用户拥有多个角色
- `character_type`：角色类型，区分玩家角色、NPC、集体实体等不同类型
- `traits`：角色特质列表，使用自然语言描述角色的属性和能力
- `is_primary`：是否为用户在该世界的主角色，支持多角色切换

**业务场景**：角色创建和管理、多角色支持、NPC行为控制、角色成长系统

### 12.3 记忆和阅历系统表说明

#### character_memories (角色记忆详细表)
**表用途**：实现动态记忆系统，模拟角色对重要事件和信息的记忆与遗忘过程

**主要字段含义**：
- `memory_type`：记忆类型分类，如事件记忆、人物记忆、地点记忆等
- `importance_score` + `emotional_impact`：记忆的重要性和情感影响，决定记忆的保持时间
- `clarity_level` + `current_strength`：记忆的清晰度和当前强度，支持渐进式遗忘
- `decay_rate`：遗忘速率，不同类型记忆有不同的衰减规律

**业务场景**：AI上下文构建、角色行为决策、记忆回溯、情感系统

#### character_experiences (角色阅历详细表)
**表用途**：记录角色在各个领域的经验积累，影响角色在相关情况下的表现

**主要字段含义**：
- `experience_category` + `experience_type`：阅历的分类体系，如战斗、社交、探索等
- `proficiency_level`：熟练度等级，反映角色在该领域的专业程度
- `success_rate`：成功率统计，基于历史表现计算的能力指标
- `is_teachable` + `learned_from_character_id`：支持阅历传承和教学系统
- `reputation_impact`：对不同群体声誉的影响，支持社交阅历系统

**业务场景**：技能系统、成功率判定、角色成长、AI决策支持、知识传承

### 12.4 实体系统表说明

#### entities (实体表)
**表用途**：统一管理游戏世界中的所有非角色对象，提供一致的实体操作接口

**主要字段含义**：
- `entity_type`：实体类型，如物品、事件、目标等，支持扩展新类型
- `base_properties` + `traits`：实体的基础属性和特质，使用灵活的JSON结构
- 位置字段组合：支持实体在场景中、角色身上或容器内的复杂位置关系
- `version`：版本号，支持实体的演化和变更追踪

**业务场景**：物品管理、事件触发、目标追踪、位置查询

#### items (物品专门表)
**表用途**：扩展实体表，专门处理物品类实体的详细属性和行为

**主要字段含义**：
- `item_category` + `item_subcategory`：物品分类体系，支持精细的物品管理
- `durability` + `stack_size`：耐久度和堆叠系统，模拟真实的物品使用
- `crafting_recipe` + `usage_effects`：制作配方和使用效果，支持复杂的制作系统
- `stat_modifiers`：属性修正，用于装备系统的数值影响

**业务场景**：物品制作、装备系统、物品交易、库存管理

#### events (事件专门表)
**表用途**：管理游戏中的各种事件，包括玩家触发和系统生成的事件

**主要字段含义**：
- `trigger_conditions` + `success_conditions`：事件的触发和成功条件，支持复杂逻辑
- `duration_minutes` + `max_participants`：事件的时间和参与者限制
- `success_rewards` + `failure_penalties`：成功奖励和失败惩罚机制
- `is_repeatable` + `cooldown_minutes`：重复性和冷却时间控制

**业务场景**：事件系统、任务管理、奖励机制、时间控制

#### goals (目标专门表)
**表用途**：管理各种类型的目标和任务，为游戏提供方向性和挑战性

**主要字段含义**：
- `goal_type` + `goal_scope`：目标类型和范围，支持个人、团队、世界级目标
- `completion_criteria` + `progress_tracking`：完成标准和进度跟踪机制
- `deadline` + `is_time_sensitive`：时间限制和紧急程度设置
- `prerequisites` + `rewards`：前置条件和完成奖励系统

**业务场景**：任务系统、成就管理、进度追踪、奖励发放

### 12.5 事件处理系统表说明

#### game_events (游戏事件日志表)
**表用途**：记录游戏中发生的所有事件，提供完整的游戏历史追踪

**主要字段含义**：
- `event_type` + `event_subtype`：事件分类，如玩家行动、系统演化、心跳事件等
- `participating_characters`：参与角色列表，支持多角色事件记录
- `narrative_text`：叙事文本，AI生成的事件描述
- `game_time` + `real_duration_ms`：游戏时间和现实处理时间记录

**业务场景**：事件历史、调试分析、性能监控、叙事回顾

#### event_processing_results (事件处理结果表)
**表用途**：详细记录AI处理事件的结果和状态变更，支持复杂的事件处理流程

**主要字段含义**：
- `processing_status` + `success_status`：处理状态和成功状态的双重跟踪
- `result_data` + `state_changes`：AI返回的结果数据和具体的状态变更指令
- `ai_token_usage` + `ai_response_time_ms`：AI调用的成本和性能统计
- `retry_count` + `max_retries`：重试机制的控制和统计

**业务场景**：AI结果处理、错误重试、性能分析、成本控制

#### event_processing_steps (事件处理步骤详细记录表)
**表用途**：记录事件处理过程中的每个详细步骤，用于调试和性能优化

**主要字段含义**：
- `step_order` + `step_type`：步骤顺序和类型，如验证、AI调用、状态更新等
- `execution_time_ms`：每个步骤的执行时间，用于性能瓶颈分析
- `input_data` + `output_data`：步骤的输入输出数据，支持详细的调试分析

**业务场景**：性能调优、错误诊断、流程优化、调试分析

#### event_timing_stats (事件耗时统计表)
**表用途**：统计不同类型事件的处理时间，支持性能监控和容量规划

**主要字段含义**：
- `game_time_duration` vs `real_time_duration_ms`：游戏时间和现实时间的对比
- `participant_count` + `complexity_score`：参与者数量和复杂度评分
- `estimated_*` vs 实际时间：预期时间和实际时间的对比分析

**业务场景**：性能监控、容量规划、成本预测、系统优化

### 12.6 特殊功能表说明

#### world_frameworks (世界规模框架表)
**表用途**：为多玩家世界提供概念性的空间框架，支持玩家分散初始化和自然汇聚

**主要字段含义**：
- `regions` + `landmarks`：主要地理区域和重要地标的概念性定义
- `distances` + `boundaries`：区域间距离关系和世界边界设定
- `convergence_points`：汇聚点设计，为玩家后期相遇提供自然场所

**业务场景**：多人世界初始化、区域分配、距离计算、汇聚设计

**业务流程示例**：
```sql
-- 世界创建时生成框架
INSERT INTO world_frameworks (world_id, regions, landmarks, distances)
VALUES ($world_id,
    '[{"name": "北方山脉", "type": "mountainous"}, {"name": "中央平原", "type": "plains"}]',
    '[{"name": "王都", "region": "中央平原", "importance": "high"}]',
    '{"北方山脉-中央平原": {"travel_days": 3, "difficulty": "medium"}}'
);
```

#### player_allocations (玩家初始化分配表)
**表用途**：管理多玩家世界中的玩家分配，确保合理的初始分布和汇聚路径

**主要字段含义**：
- `allocated_region` + `start_position`：分配的区域和具体起始位置
- `distance_to_others`：与其他玩家的距离关系，确保适当的分散度
- `convergence_path`：预设的汇聚路径，引导玩家自然相遇

**业务场景**：玩家分配、距离保证、路径规划、多人协调

**分配算法示例**：
```sql
-- 查询可用区域并分配新玩家
WITH available_regions AS (
    SELECT r.name, COALESCE(COUNT(pa.id), 0) as current_players
    FROM (SELECT jsonb_array_elements_text(regions->'name') as name
          FROM world_frameworks WHERE world_id = $world_id) r
    LEFT JOIN player_allocations pa ON r.name = pa.allocated_region
    WHERE COALESCE(COUNT(pa.id), 0) < 3
    GROUP BY r.name
)
SELECT name FROM available_regions ORDER BY current_players LIMIT 1;
```

#### user_character_sessions (用户角色会话表)
**表用途**：管理用户的多角色会话状态，支持在同一世界中的角色切换

**主要字段含义**：
- `active_character_id`：当前活跃的角色ID，支持实时角色切换
- `last_character_switch`：最后一次角色切换时间，用于会话管理
- `character_preferences`：角色相关的偏好设置

**业务场景**：多角色管理、会话控制、角色切换、偏好设置

## 13. 数据库表关联关系图

### 13.1 核心关联关系图

```mermaid
erDiagram
    %% 用户系统核心
    USERS ||--o{ USER_STATS : "一对一统计"
    USERS ||--o{ WORLDS : "创建世界"
    USERS ||--o{ CHARACTERS : "拥有角色"
    USERS ||--o{ USER_CHARACTER_SESSIONS : "会话管理"
    USERS ||--o{ AI_INTERACTIONS : "AI交互记录"
    USERS ||--o{ CONTENT_AUDITS : "内容审核"

    %% 世界系统核心
    WORLDS ||--o{ SCENES : "包含场景"
    WORLDS ||--o{ CHARACTERS : "承载角色"
    WORLDS ||--o{ ENTITIES : "包含实体"
    WORLDS ||--o{ GAME_EVENTS : "事件日志"
    WORLDS ||--o{ EVENT_PROCESSING_RESULTS : "处理结果"
    WORLDS ||--o{ EVENT_TIMING_STATS : "性能统计"
    WORLDS ||--o{ WORLD_FRAMEWORKS : "规模框架"
    WORLDS ||--o{ PLAYER_ALLOCATIONS : "玩家分配"

    %% 场景系统
    SCENES ||--o{ CHARACTERS : "当前位置"
    SCENES ||--o{ ENTITIES : "场景内容"
    SCENES ||--o{ GAME_EVENTS : "发生地点"

    %% 角色系统
    CHARACTERS ||--o{ CHARACTER_MEMORIES : "记忆系统"
    CHARACTERS ||--o{ CHARACTER_EXPERIENCES : "阅历系统"
    CHARACTERS ||--o{ ENTITIES : "拥有物品"
    CHARACTERS ||--o{ GAME_EVENTS : "参与事件"
    CHARACTERS ||--o{ PLAYER_ALLOCATIONS : "分配记录"
    CHARACTERS ||--o{ USER_CHARACTER_SESSIONS : "活跃角色"

    %% 实体系统
    ENTITIES ||--o{ ITEMS : "物品扩展"
    ENTITIES ||--o{ EVENTS : "事件扩展"
    ENTITIES ||--o{ GOALS : "目标扩展"
    ENTITIES ||--o{ ENTITIES : "容器关系"
    ENTITIES ||--o{ EVENT_PROCESSING_RESULTS : "触发处理"

    %% 事件处理系统
    EVENT_PROCESSING_RESULTS ||--o{ EVENT_PROCESSING_STEPS : "处理步骤"
    AI_INTERACTIONS ||--o{ EVENT_PROCESSING_RESULTS : "AI调用关联"

    USERS {
        uuid id PK
        string external_id
        string external_provider
        string email
        string display_name
        string status
        jsonb preferences
        timestamp created_at
    }

    WORLDS {
        uuid id PK
        string name
        text description
        uuid creator_id FK
        jsonb world_config
        jsonb world_state
        string status
        boolean is_public
        timestamp created_at
    }

    SCENES {
        uuid id PK
        uuid world_id FK
        string name
        text description
        string scene_type
        jsonb connections
        jsonb properties
        jsonb environment
        timestamp created_at
    }

    CHARACTERS {
        uuid id PK
        uuid world_id FK
        uuid user_id FK
        string name
        string character_type
        uuid current_scene_id FK
        jsonb traits
        string status
        boolean is_primary
        timestamp created_at
    }

    ENTITIES {
        uuid id PK
        uuid world_id FK
        string entity_type
        string name
        jsonb base_properties
        uuid current_scene_id FK
        uuid owner_character_id FK
        string status
        timestamp created_at
    }
```

### 13.2 主外键关系详细说明

#### 用户相关关联
```sql
-- 用户统计 (一对一)
user_stats.user_id → users.id (CASCADE DELETE)

-- 用户创建的世界 (一对多)
worlds.creator_id → users.id (RESTRICT DELETE)

-- 用户拥有的角色 (一对多，支持NULL for NPCs)
characters.user_id → users.id (SET NULL)

-- 用户会话管理 (一对多)
user_character_sessions.user_id → users.id (CASCADE DELETE)
user_character_sessions.active_character_id → characters.id (SET NULL)
```

#### 世界相关关联
```sql
-- 世界包含的场景 (一对多)
scenes.world_id → worlds.id (CASCADE DELETE)

-- 世界中的角色 (一对多)
characters.world_id → worlds.id (CASCADE DELETE)

-- 世界中的实体 (一对多)
entities.world_id → worlds.id (CASCADE DELETE)

-- 世界的框架和分配 (一对一/一对多)
world_frameworks.world_id → worlds.id (CASCADE DELETE)
player_allocations.world_id → worlds.id (CASCADE DELETE)
```

#### 位置关联
```sql
-- 角色当前位置
characters.current_scene_id → scenes.id (SET NULL)

-- 实体位置 (三选一约束)
entities.current_scene_id → scenes.id (SET NULL)
entities.owner_character_id → characters.id (SET NULL)
entities.container_entity_id → entities.id (SET NULL)
```

### 13.3 业务逻辑关联

#### 记忆和阅历系统
```sql
-- 角色记忆关联
character_memories.character_id → characters.id (CASCADE DELETE)
character_memories.associated_entities → entities.id[] (JSON数组)

-- 角色阅历关联
character_experiences.character_id → characters.id (CASCADE DELETE)
character_experiences.learned_from_character_id → characters.id (SET NULL)
```

#### 事件处理链
```sql
-- 事件处理结果
event_processing_results.event_id → entities.id (事件实体)
event_processing_results.world_id → worlds.id

-- 处理步骤
event_processing_steps.result_id → event_processing_results.id (CASCADE DELETE)

-- AI交互关联
event_processing_results.ai_request_id → ai_interactions.id
```

#### 实体扩展关系
```sql
-- 专门表扩展通用实体表
items.entity_id → entities.id (CASCADE DELETE)
events.entity_id → entities.id (CASCADE DELETE)
goals.entity_id → entities.id (CASCADE DELETE)
```

### 13.4 数据流向分析

#### 用户注册到游戏流程
```
用户注册 → users表
    ↓
创建统计 → user_stats表
    ↓
创建世界 → worlds表
    ↓
生成框架 → world_frameworks表
    ↓
创建场景 → scenes表
    ↓
创建角色 → characters表
    ↓
分配位置 → player_allocations表
```

#### 游戏事件处理流程
```
玩家行动 → game_events表
    ↓
AI处理 → ai_interactions表
    ↓
处理结果 → event_processing_results表
    ↓
处理步骤 → event_processing_steps表
    ↓
性能统计 → event_timing_stats表
    ↓
状态更新 → characters/entities/scenes表
    ↓
记忆更新 → character_memories表
    ↓
阅历更新 → character_experiences表
```

#### 多玩家世界初始化流程
```
世界创建 → worlds表
    ↓
框架生成 → world_frameworks表
    ↓
玩家加入 → characters表
    ↓
区域分配 → player_allocations表
    ↓
会话创建 → user_character_sessions表
```

### 13.5 数据一致性约束

#### 业务规则约束
```sql
-- 实体位置唯一性约束
CONSTRAINT entities_single_location CHECK (
    (current_scene_id IS NOT NULL)::INTEGER +
    (owner_character_id IS NOT NULL)::INTEGER +
    (container_entity_id IS NOT NULL)::INTEGER <= 1
);

-- 用户主角色唯一性约束
CONSTRAINT characters_primary_unique
UNIQUE (user_id, world_id, is_primary)
DEFERRABLE INITIALLY DEFERRED;

-- 用户世界会话唯一性约束
CONSTRAINT user_world_session_unique
UNIQUE(user_id, world_id);
```

#### 跨表查询优化索引
```sql
-- 世界-角色-场景关联查询
CREATE INDEX idx_world_character_scene
ON characters(world_id, current_scene_id, status);

-- 实体位置查询优化
CREATE INDEX idx_entity_location
ON entities(world_id, current_scene_id, owner_character_id)
WHERE status = 'active';

-- 事件处理链查询
CREATE INDEX idx_event_processing_chain
ON event_processing_results(world_id, processing_batch_id, started_at);
```

## 14. 总结与建议

### 14.1 设计优势总结

1. **统一的实体模型**：通过保留通用实体表并结合专门表的混合设计，既保证了灵活性又优化了性能
2. **完善的阅历系统**：增加了传承、声誉、教学等功能，支持复杂的角色成长和社交系统
3. **合理的事件处理拆分**：事件日志、处理结果、处理步骤、性能统计的分离设计提供了良好的可维护性
4. **智能的多玩家支持**：世界框架和玩家分配表确保了多玩家世界的合理初始化和自然汇聚
5. **丰富的JSONB结构**：详细的JSON规范为AI生成内容提供了标准化的数据格式
6. **增强的场景连接**：动态连接状态和智能路径功能支持更复杂的世界探索体验

### 14.2 实施建议

1. **分阶段实施**：建议按照核心表→扩展表→优化功能的顺序逐步实施
2. **性能监控**：重点关注JSONB字段的查询性能，必要时添加GIN索引
3. **数据迁移**：为所有表结构变更准备详细的迁移脚本
4. **测试验证**：特别关注多玩家场景下的数据一致性测试
5. **文档维护**：随着业务发展持续更新JSONB字段的结构规范

这个数据库设计为AI文本游戏提供了坚实的数据基础，支持复杂的游戏逻辑、高效的AI集成和良好的用户体验。
