# AI文本游戏世界生成Schema文档

## 概述

本目录包含AI文本游戏世界生成功能的JSON Schema定义文件，用于规范和验证AI生成的世界配置数据结构。

## Schema文件

### world-generation.json

定义了AI生成世界配置的完整数据结构，包含以下主要字段：

#### 必填字段

- **worldDescription** (string): 世界的详细描述，长度50-2000字符
  - 包含世界的整体氛围、主要特征和背景设定
  - 是用户了解世界的第一印象

#### 可选字段

##### 1. worldRules (array)
定义世界运行的基本规则和限制，每个规则包含：
- `name`: 规则名称
- `description`: 规则详细说明
- `category`: 规则类别（magic, combat, social, economic, environmental, character, other）
- `severity`: 违规严重性（low, medium, high, critical）
- `enforcement`: 执行方式（automatic, manual, community, none）

##### 2. environment (object)
世界的自然环境和物理特征：

**climate** - 气候设定：
- `type`: 气候类型（tropical, temperate, arctic, desert, mediterranean, continental, mixed）
- `seasons`: 季节设定（spring, summer, autumn, winter）
- `weatherPatterns`: 天气模式（sunny, cloudy, rainy, stormy, snowy, foggy, windy, magical）

**terrain** - 地形特征：
- `primaryTerrain`: 主要地形类型
- `landmarks`: 重要地标和建筑列表

**resources** - 资源分布：
- `commonResources`: 常见资源类型
- `rareResources`: 稀有资源及其稀有度

##### 3. culture (object)
世界的文化、社会结构和价值观念：

**societies** - 社会群体：
- 各种族和群体的详细信息
- 群体间的关系（盟友、敌对、中立）

**languages** - 语言系统：
- 世界中使用的语言
- 语言的使用者和文字系统

**traditions** - 传统习俗：
- 重要的传统和习俗
- 参与者和举行频率

##### 4. history (object)
世界的历史事件和时间线：

**eras** - 历史时代：
- 主要时代划分
- 每个时代的关键事件

**legends** - 传说故事：
- 流传的传说和神话
- 传说的真实程度

##### 5. geography (object)
世界的地理结构和空间布局：

**基本属性**：
- `worldType`: 世界类型（planet, continent, island_chain等）
- `size`: 世界规模（small, medium, large, vast, infinite）

**regions** - 地理区域：
- 主要地理区域信息
- 区域的气候、人口、治理方式

**connections** - 区域连接：
- 区域间的连接和交通方式
- 通行难度和描述

## 使用说明

### 1. 数据验证
使用此Schema可以验证AI生成的世界配置数据是否符合预期格式：

```javascript
const Ajv = require('ajv');
const ajv = new Ajv();
const schema = require('./world-generation.json');
const validate = ajv.compile(schema);

const isValid = validate(worldData);
if (!isValid) {
  console.log(validate.errors);
}
```

### 2. AI提示词生成
基于Schema结构生成AI提示词，确保AI输出符合预期格式：

```
请根据以下JSON Schema生成一个游戏世界配置：
- 必须包含worldDescription字段
- 可选包含worldRules、environment、culture、history、geography字段
- 确保所有字段都符合Schema定义的类型和约束
```

### 3. 前端表单生成
可以基于Schema自动生成前端表单字段和验证规则：

```typescript
// 根据Schema生成表单字段
const formFields = generateFormFields(schema);

// 根据Schema生成验证规则
const validationRules = generateValidationRules(schema);
```

## 扩展说明

### 添加新字段
如需添加新的世界配置字段，请：

1. 在Schema中添加字段定义
2. 设置适当的类型、约束和示例
3. 更新本文档说明
4. 更新相关的AI提示词模板
5. 更新前端表单组件

### 版本控制
Schema文件应该进行版本控制，重大变更时需要：

1. 更新`$id`字段中的版本号
2. 保持向后兼容性
3. 提供迁移指南

## 示例数据

完整的示例世界配置数据请参考 `examples/` 目录中的示例文件。

## 相关文件

- `/internal/ai/world_generator.go` - 后端世界生成服务
- `/web/frontend/src/store/api/worldApi.ts` - 前端API接口
- `/web/frontend/src/pages/WorldCreatePage.tsx` - 世界创建页面
