{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://ai-text-game.com/schemas/world-generation.json", "title": "AI文本游戏世界生成配置Schema", "description": "定义AI生成的游戏世界配置数据结构，包含世界描述、规则、环境、文化、历史和地理等信息", "type": "object", "required": ["worldDescription"], "properties": {"worldDescription": {"type": "string", "title": "世界描述", "description": "对游戏世界的详细描述，包括整体氛围、主要特征和背景设定", "minLength": 50, "maxLength": 2000, "examples": ["这是一个充满魔法与奇迹的幻想世界，古老的龙族统治着天空，而地面上的各个种族为了争夺稀有的魔法水晶而展开激烈的竞争。在这个世界中，魔法是日常生活的一部分，每个人都拥有独特的魔法天赋。"]}, "worldRules": {"type": "array", "title": "世界规则", "description": "定义世界运行的基本规则和限制", "items": {"type": "object", "required": ["name", "description"], "properties": {"name": {"type": "string", "title": "规则名称", "description": "规则的简短名称", "maxLength": 100}, "description": {"type": "string", "title": "规则描述", "description": "规则的详细说明", "maxLength": 500}, "category": {"type": "string", "title": "规则类别", "description": "规则所属的类别", "enum": ["magic", "combat", "social", "economic", "environmental", "character", "other"], "default": "other"}, "severity": {"type": "string", "title": "违规严重性", "description": "违反此规则的严重程度", "enum": ["low", "medium", "high", "critical"], "default": "medium"}, "enforcement": {"type": "string", "title": "执行方式", "description": "规则的执行和监管方式", "enum": ["automatic", "manual", "community", "none"], "default": "automatic"}}}, "maxItems": 20, "examples": [[{"name": "魔法消耗", "description": "使用魔法会消耗角色的魔力值，魔力值耗尽后需要休息恢复", "category": "magic", "severity": "medium", "enforcement": "automatic"}, {"name": "死亡惩罚", "description": "角色死亡后会丢失部分经验值和装备，需要在复活点重生", "category": "character", "severity": "high", "enforcement": "automatic"}]]}, "environment": {"type": "object", "title": "环境设定", "description": "世界的自然环境和物理特征", "properties": {"climate": {"type": "object", "title": "气候设定", "description": "世界的气候特征", "properties": {"type": {"type": "string", "title": "气候类型", "description": "主要气候类型", "enum": ["tropical", "temperate", "arctic", "desert", "mediterranean", "continental", "mixed"], "default": "temperate"}, "seasons": {"type": "array", "title": "季节设定", "description": "世界中存在的季节", "items": {"type": "string", "enum": ["spring", "summer", "autumn", "winter"]}, "uniqueItems": true, "default": ["spring", "summer", "autumn", "winter"]}, "weatherPatterns": {"type": "array", "title": "天气模式", "description": "可能出现的天气类型", "items": {"type": "string", "enum": ["sunny", "cloudy", "rainy", "stormy", "snowy", "foggy", "windy", "magical"]}, "uniqueItems": true, "default": ["sunny", "cloudy", "rainy"]}}}, "terrain": {"type": "object", "title": "地形特征", "description": "世界的地形和地貌特征", "properties": {"primaryTerrain": {"type": "string", "title": "主要地形", "description": "世界的主导地形类型", "enum": ["plains", "mountains", "forests", "deserts", "islands", "underground", "floating", "mixed"], "default": "mixed"}, "landmarks": {"type": "array", "title": "地标建筑", "description": "世界中的重要地标和建筑", "items": {"type": "object", "required": ["name", "type"], "properties": {"name": {"type": "string", "title": "地标名称", "maxLength": 100}, "type": {"type": "string", "title": "地标类型", "enum": ["city", "castle", "temple", "tower", "ruins", "natural", "magical", "other"]}, "description": {"type": "string", "title": "地标描述", "maxLength": 300}}}, "maxItems": 10}}}, "resources": {"type": "object", "title": "资源分布", "description": "世界中的自然资源和稀有材料", "properties": {"commonResources": {"type": "array", "title": "常见资源", "description": "世界中常见的资源类型", "items": {"type": "string", "enum": ["wood", "stone", "metal", "water", "food", "herbs", "gems", "energy"]}, "uniqueItems": true}, "rareResources": {"type": "array", "title": "稀有资源", "description": "世界中稀有的特殊资源", "items": {"type": "object", "required": ["name", "rarity"], "properties": {"name": {"type": "string", "title": "资源名称", "maxLength": 50}, "rarity": {"type": "string", "title": "稀有度", "enum": ["uncommon", "rare", "epic", "legendary", "mythical"]}, "description": {"type": "string", "title": "资源描述", "maxLength": 200}}}, "maxItems": 5}}}}}, "culture": {"type": "object", "title": "文化背景", "description": "世界中的文化、社会结构和价值观念", "properties": {"societies": {"type": "array", "title": "社会群体", "description": "世界中存在的主要社会群体或种族", "items": {"type": "object", "required": ["name", "type"], "properties": {"name": {"type": "string", "title": "群体名称", "maxLength": 100}, "type": {"type": "string", "title": "群体类型", "enum": ["human", "elf", "dwarf", "orc", "dragon", "spirit", "construct", "hybrid", "other"], "default": "human"}, "description": {"type": "string", "title": "群体描述", "maxLength": 500}, "traits": {"type": "array", "title": "群体特征", "description": "该群体的主要特征和能力", "items": {"type": "string", "maxLength": 100}, "maxItems": 5}, "relations": {"type": "object", "title": "群体关系", "description": "与其他群体的关系", "properties": {"allies": {"type": "array", "title": "盟友", "items": {"type": "string"}}, "enemies": {"type": "array", "title": "敌对", "items": {"type": "string"}}, "neutral": {"type": "array", "title": "中立", "items": {"type": "string"}}}}}}, "maxItems": 8}, "languages": {"type": "array", "title": "语言系统", "description": "世界中使用的语言", "items": {"type": "object", "required": ["name", "speakers"], "properties": {"name": {"type": "string", "title": "语言名称", "maxLength": 50}, "speakers": {"type": "array", "title": "使用者", "description": "使用该语言的群体", "items": {"type": "string"}}, "script": {"type": "string", "title": "文字系统", "description": "该语言的文字形式", "enum": ["alphabetic", "logographic", "syllabic", "runic", "magical", "none"]}}}, "maxItems": 5}, "traditions": {"type": "array", "title": "传统习俗", "description": "世界中的重要传统和习俗", "items": {"type": "object", "required": ["name", "description"], "properties": {"name": {"type": "string", "title": "传统名称", "maxLength": 100}, "description": {"type": "string", "title": "传统描述", "maxLength": 300}, "participants": {"type": "array", "title": "参与者", "description": "参与该传统的群体", "items": {"type": "string"}}, "frequency": {"type": "string", "title": "举行频率", "enum": ["daily", "weekly", "monthly", "seasonal", "yearly", "rare", "once"]}}}, "maxItems": 10}}}, "history": {"type": "object", "title": "历史背景", "description": "世界的历史事件和时间线", "properties": {"eras": {"type": "array", "title": "历史时代", "description": "世界历史的主要时代划分", "items": {"type": "object", "required": ["name", "description"], "properties": {"name": {"type": "string", "title": "时代名称", "maxLength": 100}, "description": {"type": "string", "title": "时代描述", "maxLength": 500}, "duration": {"type": "string", "title": "持续时间", "description": "该时代的大致持续时间", "maxLength": 100}, "keyEvents": {"type": "array", "title": "关键事件", "description": "该时代的重要历史事件", "items": {"type": "string", "maxLength": 200}, "maxItems": 5}}}, "maxItems": 6}, "legends": {"type": "array", "title": "传说故事", "description": "世界中流传的传说和神话", "items": {"type": "object", "required": ["title", "summary"], "properties": {"title": {"type": "string", "title": "传说标题", "maxLength": 100}, "summary": {"type": "string", "title": "传说概要", "maxLength": 500}, "characters": {"type": "array", "title": "传说人物", "description": "传说中的重要人物", "items": {"type": "string", "maxLength": 50}, "maxItems": 5}, "truthLevel": {"type": "string", "title": "真实程度", "description": "传说的真实性程度", "enum": ["myth", "legend", "historical", "recent", "ongoing"]}}}, "maxItems": 8}}}, "geography": {"type": "object", "title": "地理位置", "description": "世界的地理结构和空间布局", "properties": {"worldType": {"type": "string", "title": "世界类型", "description": "世界的基本地理结构", "enum": ["planet", "continent", "island_chain", "floating_islands", "underground", "pocket_dimension", "multiverse", "other"], "default": "continent"}, "size": {"type": "string", "title": "世界规模", "description": "世界的大致规模", "enum": ["small", "medium", "large", "vast", "infinite"], "default": "medium"}, "regions": {"type": "array", "title": "地理区域", "description": "世界中的主要地理区域", "items": {"type": "object", "required": ["name", "type"], "properties": {"name": {"type": "string", "title": "区域名称", "maxLength": 100}, "type": {"type": "string", "title": "区域类型", "enum": ["kingdom", "empire", "city_state", "wilderness", "wasteland", "magical_zone", "neutral_territory", "other"]}, "description": {"type": "string", "title": "区域描述", "maxLength": 400}, "climate": {"type": "string", "title": "区域气候", "enum": ["tropical", "temperate", "arctic", "desert", "mediterranean", "continental", "magical"]}, "population": {"type": "string", "title": "人口密度", "enum": ["uninhabited", "sparse", "moderate", "dense", "overcrowded"]}, "governance": {"type": "string", "title": "治理方式", "enum": ["monarchy", "republic", "democracy", "theocracy", "anarchy", "tribal", "magical", "none"]}}}, "maxItems": 12}, "connections": {"type": "array", "title": "区域连接", "description": "区域之间的连接和交通方式", "items": {"type": "object", "required": ["from", "to", "method"], "properties": {"from": {"type": "string", "title": "起始区域", "maxLength": 100}, "to": {"type": "string", "title": "目标区域", "maxLength": 100}, "method": {"type": "string", "title": "连接方式", "enum": ["road", "sea", "air", "teleport", "portal", "bridge", "tunnel", "magical"]}, "difficulty": {"type": "string", "title": "通行难度", "enum": ["easy", "moderate", "hard", "dangerous", "impossible"], "default": "moderate"}, "description": {"type": "string", "title": "连接描述", "maxLength": 200}}}, "maxItems": 20}}}}}