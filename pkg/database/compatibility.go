package database

import (
	"fmt"
	"strings"

	"gorm.io/gorm"
)

// DatabaseType 数据库类型枚举
type DatabaseType string

const (
	PostgreSQL DatabaseType = "postgres"
	SQLite     DatabaseType = "sqlite"
)

// GetDatabaseType 根据数据库连接获取数据库类型
func GetDatabaseType(db *gorm.DB) DatabaseType {
	switch db.Dialector.Name() {
	case "postgres":
		return PostgreSQL
	case "sqlite":
		return SQLite
	default:
		return SQLite // 默认使用SQLite
	}
}

// CompatibilityConfig 数据库兼容性配置
type CompatibilityConfig struct {
	DBType DatabaseType
}

// NewCompatibilityConfig 创建兼容性配置
func NewCompatibilityConfig(db *gorm.DB) *CompatibilityConfig {
	return &CompatibilityConfig{
		DBType: GetDatabaseType(db),
	}
}

// GetUUIDType 获取UUID字段类型定义
func (c *CompatibilityConfig) GetUUIDType() string {
	switch c.DBType {
	case PostgreSQL:
		return "type:uuid;primary_key;default:gen_random_uuid()"
	case SQLite:
		return "type:text;primary_key" // SQLite使用TEXT存储UUID
	default:
		return "type:text;primary_key"
	}
}

// GetJSONType 获取JSON字段类型定义
func (c *CompatibilityConfig) GetJSONType() string {
	switch c.DBType {
	case PostgreSQL:
		return "type:jsonb"
	case SQLite:
		return "type:text" // SQLite使用TEXT存储JSON
	default:
		return "type:text"
	}
}

// GetJSONTypeWithDefault 获取带默认值的JSON字段类型定义
func (c *CompatibilityConfig) GetJSONTypeWithDefault(defaultValue string) string {
	switch c.DBType {
	case PostgreSQL:
		return fmt.Sprintf("type:jsonb;default:'%s'", defaultValue)
	case SQLite:
		return fmt.Sprintf("type:text;default:'%s'", defaultValue)
	default:
		return fmt.Sprintf("type:text;default:'%s'", defaultValue)
	}
}

// GetTimestampType 获取时间戳字段类型定义
func (c *CompatibilityConfig) GetTimestampType() string {
	switch c.DBType {
	case PostgreSQL:
		return "type:timestamp with time zone"
	case SQLite:
		return "type:datetime"
	default:
		return "type:datetime"
	}
}

// GetBooleanType 获取布尔字段类型定义
func (c *CompatibilityConfig) GetBooleanType() string {
	switch c.DBType {
	case PostgreSQL:
		return "type:boolean"
	case SQLite:
		return "type:boolean"
	default:
		return "type:boolean"
	}
}

// GetTextType 获取文本字段类型定义
func (c *CompatibilityConfig) GetTextType() string {
	switch c.DBType {
	case PostgreSQL:
		return "type:text"
	case SQLite:
		return "type:text"
	default:
		return "type:text"
	}
}

// GetVarcharType 获取变长字符串字段类型定义
func (c *CompatibilityConfig) GetVarcharType(length int) string {
	switch c.DBType {
	case PostgreSQL:
		return fmt.Sprintf("type:varchar(%d)", length)
	case SQLite:
		return fmt.Sprintf("type:varchar(%d)", length)
	default:
		return fmt.Sprintf("type:varchar(%d)", length)
	}
}

// GetIntegerType 获取整数字段类型定义
func (c *CompatibilityConfig) GetIntegerType() string {
	switch c.DBType {
	case PostgreSQL:
		return "type:integer"
	case SQLite:
		return "type:integer"
	default:
		return "type:integer"
	}
}

// TransformSQL 转换SQL语句以适配不同数据库
func (c *CompatibilityConfig) TransformSQL(sql string) string {
	if c.DBType == SQLite {
		// 将PostgreSQL特有的语法转换为SQLite兼容的语法
		sql = strings.ReplaceAll(sql, "gen_random_uuid()", "lower(hex(randomblob(16)))")
		sql = strings.ReplaceAll(sql, "JSONB", "TEXT")
		sql = strings.ReplaceAll(sql, "TIMESTAMP WITH TIME ZONE", "DATETIME")
		sql = strings.ReplaceAll(sql, "NOW()", "datetime('now')")
		sql = strings.ReplaceAll(sql, "UUID", "TEXT")
		
		// 移除PostgreSQL特有的函数和触发器
		if strings.Contains(sql, "CREATE OR REPLACE FUNCTION") {
			return "-- SQLite不支持存储过程，跳过此语句"
		}
		if strings.Contains(sql, "CREATE TRIGGER") && strings.Contains(sql, "EXECUTE FUNCTION") {
			return "-- SQLite触发器语法不同，需要单独处理"
		}
	}
	return sql
}

// CreateUUID 生成UUID
func (c *CompatibilityConfig) CreateUUID(db *gorm.DB) (string, error) {
	switch c.DBType {
	case PostgreSQL:
		var uuid string
		err := db.Raw("SELECT gen_random_uuid()").Scan(&uuid).Error
		return uuid, err
	case SQLite:
		var uuid string
		err := db.Raw("SELECT lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(2)) || '-' || hex(randomblob(6)))").Scan(&uuid).Error
		return uuid, err
	default:
		// 使用Go生成UUID作为后备方案
		return generateGoUUID(), nil
	}
}

// generateGoUUID 使用Go生成UUID（后备方案）
func generateGoUUID() string {
	// 这里可以使用github.com/google/uuid包
	// 为了简化，这里使用一个简单的实现
	return "00000000-0000-0000-0000-000000000000" // 占位符，实际应该生成真正的UUID
}

// SupportsJSONB 检查数据库是否支持JSONB
func (c *CompatibilityConfig) SupportsJSONB() bool {
	return c.DBType == PostgreSQL
}

// SupportsUUIDType 检查数据库是否原生支持UUID类型
func (c *CompatibilityConfig) SupportsUUIDType() bool {
	return c.DBType == PostgreSQL
}

// SupportsTriggers 检查数据库是否支持触发器
func (c *CompatibilityConfig) SupportsTriggers() bool {
	return true // 两种数据库都支持触发器，但语法不同
}

// GetTriggerSQL 获取更新时间触发器SQL
// 根据表名智能选择正确的主键字段
func (c *CompatibilityConfig) GetTriggerSQL(tableName string) string {
	switch c.DBType {
	case PostgreSQL:
		return fmt.Sprintf(`
CREATE TRIGGER update_%s_updated_at
    BEFORE UPDATE ON %s
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();`, tableName, tableName)
	case SQLite:
		// 根据表名确定正确的主键字段
		primaryKeyField := c.getPrimaryKeyField(tableName)
		return fmt.Sprintf(`
CREATE TRIGGER update_%s_updated_at
    AFTER UPDATE ON %s
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
    BEGIN
        UPDATE %s SET updated_at = datetime('now') WHERE %s = NEW.%s;
    END;`, tableName, tableName, tableName, primaryKeyField, primaryKeyField)
	default:
		return ""
	}
}

// getPrimaryKeyField 根据表名返回正确的主键字段名
// 这个方法确保触发器使用正确的主键字段进行更新，解决SQLite迁移过程中的列引用错误
//
// 背景：不同表的主键字段名可能不同：
// - user_stats 表使用 user_id 作为主键（外键关联到 users.id）
// - 其他大多数表使用 id 作为主键
//
// 在SQLite的GORM AutoMigrate过程中，会创建临时表并重命名，
// 如果触发器引用了错误的主键字段，会导致 "no such column" 错误
func (c *CompatibilityConfig) getPrimaryKeyField(tableName string) string {
	switch tableName {
	case "user_stats":
		// user_stats 表的主键是 user_id，不是 id
		// 这是因为它是一个关联表，使用外键作为主键
		return "user_id"
	case "users", "worlds", "characters", "scenes", "entities", "game_events":
		// 大多数主表使用 id 作为主键
		return "id"
	default:
		// 默认使用 id，但在生产环境中应该记录警告
		// 如果遇到新表，需要在这里添加相应的映射
		return "id"
	}
}

// GetUpdateTimestampFunctionSQL 获取更新时间戳函数SQL
func (c *CompatibilityConfig) GetUpdateTimestampFunctionSQL() string {
	switch c.DBType {
	case PostgreSQL:
		return `
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';`
	case SQLite:
		return "-- SQLite使用触发器直接更新，不需要单独的函数"
	default:
		return ""
	}
}

// ValidateCompatibility 验证数据库兼容性
func (c *CompatibilityConfig) ValidateCompatibility() error {
	switch c.DBType {
	case PostgreSQL, SQLite:
		return nil
	default:
		return fmt.Errorf("不支持的数据库类型: %s", c.DBType)
	}
}
