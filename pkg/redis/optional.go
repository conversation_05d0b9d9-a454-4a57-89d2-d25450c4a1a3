package redis

import (
	"context"
	"fmt"
	"log"
	"time"

	"ai-text-game-iam-npc/internal/config"

	"github.com/redis/go-redis/v9"
)

// OptionalClient 可选的Redis客户端
// 当Redis不可用时，提供优雅降级功能
type OptionalClient struct {
	client    *redis.Client
	enabled   bool
	connected bool
}

// NewOptionalClient 创建可选的Redis客户端
func NewOptionalClient(cfg *config.RedisConfig) *OptionalClient {
	oc := &OptionalClient{
		enabled: true, // 默认启用
	}

	// 检查是否明确禁用Redis
	if cfg.Host == "" || cfg.Port == 0 {
		log.Println("Redis配置为空，禁用Redis功能")
		oc.enabled = false
		return oc
	}

	// 尝试连接Redis
	client, err := New(cfg)
	if err != nil {
		log.Printf("Redis连接失败，将在无Redis模式下运行: %v", err)
		oc.enabled = false
		oc.connected = false
		return oc
	}

	oc.client = client
	oc.connected = true
	log.Println("Redis连接成功")
	return oc
}

// IsEnabled 检查Redis是否启用
func (oc *OptionalClient) IsEnabled() bool {
	return oc.enabled && oc.connected
}

// IsConnected 检查Redis是否连接
func (oc *OptionalClient) IsConnected() bool {
	return oc.connected
}

// GetClient 获取Redis客户端（如果可用）
func (oc *OptionalClient) GetClient() *redis.Client {
	if !oc.IsEnabled() {
		return nil
	}
	return oc.client
}

// Set 设置键值对（带降级处理）
func (oc *OptionalClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	if !oc.IsEnabled() {
		// Redis不可用时的降级处理：记录日志但不报错
		log.Printf("Redis不可用，跳过缓存设置: key=%s", key)
		return nil
	}
	return oc.client.Set(ctx, key, value, expiration).Err()
}

// Get 获取值（带降级处理）
func (oc *OptionalClient) Get(ctx context.Context, key string) (string, bool) {
	if !oc.IsEnabled() {
		// Redis不可用时返回空值
		return "", false
	}
	
	val, err := oc.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", false // 键不存在
		}
		log.Printf("Redis获取失败: %v", err)
		return "", false
	}
	return val, true
}

// Del 删除键（带降级处理）
func (oc *OptionalClient) Del(ctx context.Context, keys ...string) error {
	if !oc.IsEnabled() {
		log.Printf("Redis不可用，跳过缓存删除: keys=%v", keys)
		return nil
	}
	return oc.client.Del(ctx, keys...).Err()
}

// Exists 检查键是否存在（带降级处理）
func (oc *OptionalClient) Exists(ctx context.Context, keys ...string) (int64, error) {
	if !oc.IsEnabled() {
		return 0, nil // Redis不可用时返回0
	}
	return oc.client.Exists(ctx, keys...).Result()
}

// Lock 分布式锁（带降级处理）
func (oc *OptionalClient) Lock(ctx context.Context, key string, expiration time.Duration) (bool, error) {
	if !oc.IsEnabled() {
		// Redis不可用时，总是返回获取锁成功
		// 注意：这在单实例部署时是安全的，但在多实例部署时需要其他锁机制
		log.Printf("Redis不可用，跳过分布式锁: key=%s", key)
		return true, nil
	}
	return oc.client.SetNX(ctx, key, "locked", expiration).Result()
}

// Unlock 释放分布式锁（带降级处理）
func (oc *OptionalClient) Unlock(ctx context.Context, key string) error {
	if !oc.IsEnabled() {
		log.Printf("Redis不可用，跳过锁释放: key=%s", key)
		return nil
	}
	return oc.client.Del(ctx, key).Err()
}

// Publish 发布消息（带降级处理）
func (oc *OptionalClient) Publish(ctx context.Context, channel string, message interface{}) error {
	if !oc.IsEnabled() {
		log.Printf("Redis不可用，跳过消息发布: channel=%s", channel)
		return nil
	}
	return oc.client.Publish(ctx, channel, message).Err()
}

// Subscribe 订阅频道（带降级处理）
func (oc *OptionalClient) Subscribe(ctx context.Context, channels ...string) *redis.PubSub {
	if !oc.IsEnabled() {
		log.Printf("Redis不可用，无法订阅频道: channels=%v", channels)
		return nil
	}
	return oc.client.Subscribe(ctx, channels...)
}

// Close 关闭连接
func (oc *OptionalClient) Close() error {
	if oc.client != nil {
		return oc.client.Close()
	}
	return nil
}

// HealthCheck 健康检查
func (oc *OptionalClient) HealthCheck(ctx context.Context) map[string]interface{} {
	status := map[string]interface{}{
		"enabled":   oc.enabled,
		"connected": oc.connected,
	}

	if !oc.IsEnabled() {
		status["status"] = "disabled"
		status["message"] = "Redis功能已禁用或连接失败"
		return status
	}

	// 测试连接
	err := oc.client.Ping(ctx).Err()
	if err != nil {
		status["status"] = "error"
		status["error"] = err.Error()
		oc.connected = false
	} else {
		status["status"] = "ok"
		status["ping"] = "pong"
	}

	return status
}

// GetStats 获取Redis统计信息
func (oc *OptionalClient) GetStats(ctx context.Context) map[string]interface{} {
	stats := map[string]interface{}{
		"enabled":   oc.enabled,
		"connected": oc.connected,
	}

	if !oc.IsEnabled() {
		return stats
	}

	// 获取Redis信息
	info, err := oc.client.Info(ctx).Result()
	if err != nil {
		stats["error"] = err.Error()
		return stats
	}

	// 解析基本信息
	stats["info"] = info
	
	// 获取数据库大小
	dbSize, err := oc.client.DBSize(ctx).Result()
	if err == nil {
		stats["db_size"] = dbSize
	}

	return stats
}

// 全局可选客户端实例
var OptionalRedis *OptionalClient

// InitOptionalRedis 初始化可选Redis客户端
func InitOptionalRedis(cfg *config.RedisConfig) {
	OptionalRedis = NewOptionalClient(cfg)
}

// GetOptionalClient 获取全局可选Redis客户端
func GetOptionalClient() *OptionalClient {
	return OptionalRedis
}

// IsRedisAvailable 检查Redis是否可用
func IsRedisAvailable() bool {
	if OptionalRedis == nil {
		return false
	}
	return OptionalRedis.IsEnabled()
}

// SafeSet 安全的缓存设置（全局函数）
func SafeSet(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	if OptionalRedis == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}
	return OptionalRedis.Set(ctx, key, value, expiration)
}

// SafeGet 安全的缓存获取（全局函数）
func SafeGet(ctx context.Context, key string) (string, bool) {
	if OptionalRedis == nil {
		return "", false
	}
	return OptionalRedis.Get(ctx, key)
}

// SafeDel 安全的缓存删除（全局函数）
func SafeDel(ctx context.Context, keys ...string) error {
	if OptionalRedis == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}
	return OptionalRedis.Del(ctx, keys...)
}
