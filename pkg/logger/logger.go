package logger

import (
	"fmt"
	"log"
	"os"
)

// Logger 日志接口
type Logger interface {
	Debug(msg string, args ...interface{})
	Info(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Error(msg string, args ...interface{})
	Fatal(msg string, args ...interface{})
}

// standardLogger 基于标准log的日志实现
type standardLogger struct {
	logger *log.Logger
	level  string
}

// New 创建新的日志实例
func New(level string) Logger {
	logger := log.New(os.Stdout, "", log.LstdFlags|log.Lshortfile)

	return &standardLogger{
		logger: logger,
		level:  level,
	}
}

// shouldLog 检查是否应该记录此级别的日志
func (l *standardLogger) shouldLog(level string) bool {
	levels := map[string]int{
		"debug": 0,
		"info":  1,
		"warn":  2,
		"error": 3,
	}

	currentLevel, exists := levels[l.level]
	if !exists {
		currentLevel = 1 // 默认info级别
	}

	logLevel, exists := levels[level]
	if !exists {
		return true
	}

	return logLevel >= currentLevel
}

// formatArgs 格式化参数
func (l *standardLogger) formatArgs(args ...interface{}) string {
	if len(args) == 0 {
		return ""
	}

	result := ""
	for i := 0; i < len(args); i += 2 {
		if i+1 < len(args) {
			result += fmt.Sprintf(" %v=%v", args[i], args[i+1])
		} else {
			result += fmt.Sprintf(" %v", args[i])
		}
	}
	return result
}

// Debug 调试级别日志
func (l *standardLogger) Debug(msg string, args ...interface{}) {
	if l.shouldLog("debug") {
		l.logger.Printf("[DEBUG] %s%s", msg, l.formatArgs(args...))
	}
}

// Info 信息级别日志
func (l *standardLogger) Info(msg string, args ...interface{}) {
	if l.shouldLog("info") {
		l.logger.Printf("[INFO] %s%s", msg, l.formatArgs(args...))
	}
}

// Warn 警告级别日志
func (l *standardLogger) Warn(msg string, args ...interface{}) {
	if l.shouldLog("warn") {
		l.logger.Printf("[WARN] %s%s", msg, l.formatArgs(args...))
	}
}

// Error 错误级别日志
func (l *standardLogger) Error(msg string, args ...interface{}) {
	if l.shouldLog("error") {
		l.logger.Printf("[ERROR] %s%s", msg, l.formatArgs(args...))
	}
}

// Fatal 致命错误级别日志（会退出程序）
func (l *standardLogger) Fatal(msg string, args ...interface{}) {
	l.logger.Printf("[FATAL] %s%s", msg, l.formatArgs(args...))
	os.Exit(1)
}
