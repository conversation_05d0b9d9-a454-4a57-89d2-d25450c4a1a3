#!/bin/bash

# AI文本游戏前后端集成测试脚本
# 测试后端API端点是否正常响应

BASE_URL="http://localhost:8082/api/v1"

echo "🚀 开始AI文本游戏前后端集成测试..."
echo "后端服务器地址: $BASE_URL"
echo "前端服务器地址: http://localhost:3000"
echo ""

# 测试健康检查
echo "1. 测试健康检查..."
curl -s -o /dev/null -w "状态码: %{http_code}\n" "$BASE_URL/health" || echo "健康检查端点不存在"
echo ""

# 测试OAuth认证端点
echo "2. 测试OAuth认证端点..."
echo "Google OAuth URL:"
curl -s -o /dev/null -w "状态码: %{http_code}\n" "$BASE_URL/auth/google/url" || echo "Google OAuth端点不存在"

echo "GitHub OAuth URL:"
curl -s -o /dev/null -w "状态码: %{http_code}\n" "$BASE_URL/auth/github/url" || echo "GitHub OAuth端点不存在"
echo ""

# 测试世界管理API（需要认证，预期401）
echo "3. 测试世界管理API（未认证，预期401）..."
curl -s -o /dev/null -w "状态码: %{http_code}\n" "$BASE_URL/game/my-worlds"
echo ""

# 测试角色管理API（需要认证，预期401）
echo "4. 测试角色管理API（未认证，预期401）..."
curl -s -o /dev/null -w "状态码: %{http_code}\n" "$BASE_URL/game/my-characters"
echo ""

# 测试AI内容生成API（需要认证，预期401）
echo "5. 测试AI内容生成API（未认证，预期401）..."
curl -s -o /dev/null -w "状态码: %{http_code}\n" -X POST "$BASE_URL/ai/generate/scene" \
  -H "Content-Type: application/json" \
  -d '{"world_id":"test","scene_name":"测试场景"}'
echo ""

# 测试静态文件服务
echo "6. 测试静态文件服务..."
curl -s -o /dev/null -w "状态码: %{http_code}\n" "http://localhost:8082/static/"
echo ""

echo "✅ 集成测试完成！"
echo ""
echo "📋 测试结果说明:"
echo "- 状态码 200: 成功"
echo "- 状态码 401: 未认证（正常，需要登录）"
echo "- 状态码 404: 端点不存在"
echo "- 状态码 500: 服务器错误"
echo ""
echo "🌐 请在浏览器中访问 http://localhost:3000 进行完整的用户界面测试"
echo "🔐 使用Google或GitHub OAuth进行登录测试"
