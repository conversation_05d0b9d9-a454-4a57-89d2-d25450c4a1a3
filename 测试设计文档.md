# AI文本游戏测试设计文档

## 1. 测试策略概述

### 1.1. 测试目标
- **功能正确性**: 确保所有功能按需求正确实现
- **性能达标**: 验证系统性能满足设计指标
- **安全可靠**: 确保系统安全性和数据保护
- **用户体验**: 验证用户交互流程的流畅性
- **稳定性**: 确保系统在各种条件下稳定运行

### 1.2. 测试金字塔
```
                    E2E测试 (10%)
                 ┌─────────────────┐
                 │  端到端测试     │
                 │  用户场景测试   │
                 └─────────────────┘
              集成测试 (20%)
         ┌─────────────────────────────┐
         │     API集成测试             │
         │     服务间集成测试          │
         │     数据库集成测试          │
         └─────────────────────────────┘
    单元测试 (70%)
┌─────────────────────────────────────────┐
│           业务逻辑单元测试              │
│           工具函数单元测试              │
│           数据访问层单元测试            │
└─────────────────────────────────────────┘
```

### 1.3. 测试环境
- **开发环境**: 本地开发测试
- **测试环境**: CI/CD自动化测试
- **预发环境**: 生产前验证测试
- **生产环境**: 生产监控和健康检查

## 2. 单元测试设计

### 2.1. Go语言单元测试框架

#### 测试工具选择
```go
// 主要测试框架和工具
import (
    "testing"
    "github.com/stretchr/testify/assert"     // 断言库
    "github.com/stretchr/testify/mock"       // Mock框架
    "github.com/stretchr/testify/suite"      // 测试套件
    "github.com/DATA-DOG/go-sqlmock"         // 数据库Mock
    "github.com/golang/mock/gomock"          // 接口Mock生成
    "github.com/onsi/ginkgo/v2"             // BDD测试框架
    "github.com/onsi/gomega"                 // 匹配器库
)
```

#### 业务逻辑单元测试
```go
// 世界创建逻辑测试
func TestWorldService_CreateWorld(t *testing.T) {
    tests := []struct {
        name        string
        input       *CreateWorldRequest
        mockSetup   func(*MockWorldRepository, *MockAIService)
        expected    *World
        expectedErr error
    }{
        {
            name: "成功创建世界",
            input: &CreateWorldRequest{
                Name:        "测试世界",
                Description: "一个测试用的魔法世界",
                CreatorID:   "user-123",
            },
            mockSetup: func(repo *MockWorldRepository, ai *MockAIService) {
                ai.EXPECT().GenerateWorld(gomock.Any()).Return(&AIWorldResponse{
                    WorldConcept: "魔法世界概念",
                    Regions:      []string{"北方山脉", "中央平原"},
                    Landmarks:    []string{"魔法塔", "古老城堡"},
                }, nil)
                
                repo.EXPECT().Create(gomock.Any()).Return(nil)
            },
            expected: &World{
                Name:        "测试世界",
                Description: "一个测试用的魔法世界",
                Status:      WorldStatusActive,
            },
            expectedErr: nil,
        },
        {
            name: "AI服务失败",
            input: &CreateWorldRequest{
                Name:        "测试世界",
                Description: "描述",
                CreatorID:   "user-123",
            },
            mockSetup: func(repo *MockWorldRepository, ai *MockAIService) {
                ai.EXPECT().GenerateWorld(gomock.Any()).Return(nil, 
                    errors.New("AI service unavailable"))
            },
            expected:    nil,
            expectedErr: ErrAIServiceUnavailable,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ctrl := gomock.NewController(t)
            defer ctrl.Finish()

            mockRepo := NewMockWorldRepository(ctrl)
            mockAI := NewMockAIService(ctrl)
            
            if tt.mockSetup != nil {
                tt.mockSetup(mockRepo, mockAI)
            }

            service := NewWorldService(mockRepo, mockAI)
            result, err := service.CreateWorld(context.Background(), tt.input)

            if tt.expectedErr != nil {
                assert.Error(t, err)
                assert.Equal(t, tt.expectedErr, err)
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tt.expected.Name, result.Name)
                assert.Equal(t, tt.expected.Status, result.Status)
            }
        })
    }
}

// 角色交互逻辑测试
func TestCharacterService_ProcessAction(t *testing.T) {
    suite.Run(t, new(CharacterServiceTestSuite))
}

type CharacterServiceTestSuite struct {
    suite.Suite
    service    *CharacterService
    mockRepo   *MockCharacterRepository
    mockAI     *MockAIService
    mockCache  *MockCacheManager
    ctrl       *gomock.Controller
}

func (s *CharacterServiceTestSuite) SetupTest() {
    s.ctrl = gomock.NewController(s.T())
    s.mockRepo = NewMockCharacterRepository(s.ctrl)
    s.mockAI = NewMockAIService(s.ctrl)
    s.mockCache = NewMockCacheManager(s.ctrl)
    
    s.service = NewCharacterService(s.mockRepo, s.mockAI, s.mockCache)
}

func (s *CharacterServiceTestSuite) TearDownTest() {
    s.ctrl.Finish()
}

func (s *CharacterServiceTestSuite) TestProcessAction_Observe() {
    // 测试观察动作
    character := &Character{
        ID:      "char-123",
        Name:    "测试角色",
        SceneID: "scene-456",
    }
    
    action := &ActionRequest{
        Type:        ActionTypeObserve,
        CharacterID: character.ID,
    }
    
    s.mockRepo.EXPECT().GetByID(character.ID).Return(character, nil)
    s.mockAI.EXPECT().ProcessAction(gomock.Any()).Return(&AIResponse{
        Narrative: "你环顾四周，看到一个神秘的房间。",
        StateChanges: []StateChange{
            {Type: "add_memory", Data: map[string]interface{}{
                "content": "观察了房间",
                "type":    "observation",
            }},
        },
    }, nil)
    s.mockRepo.EXPECT().Update(gomock.Any()).Return(nil)
    
    result, err := s.service.ProcessAction(context.Background(), action)
    
    s.NoError(err)
    s.Contains(result.Narrative, "你环顾四周")
    s.Len(result.StateChanges, 1)
}
```

#### 数据访问层测试
```go
func TestWorldRepository_Create(t *testing.T) {
    db, mock, err := sqlmock.New()
    require.NoError(t, err)
    defer db.Close()

    repo := NewWorldRepository(db)
    
    world := &World{
        ID:          "world-123",
        Name:        "测试世界",
        Description: "测试描述",
        CreatorID:   "user-456",
        Status:      WorldStatusActive,
    }

    mock.ExpectBegin()
    mock.ExpectExec("INSERT INTO worlds").
        WithArgs(world.ID, world.Name, world.Description, 
                world.CreatorID, world.Status, sqlmock.AnyArg()).
        WillReturnResult(sqlmock.NewResult(1, 1))
    mock.ExpectCommit()

    err = repo.Create(context.Background(), world)
    
    assert.NoError(t, err)
    assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWorldRepository_GetByID(t *testing.T) {
    db, mock, err := sqlmock.New()
    require.NoError(t, err)
    defer db.Close()

    repo := NewWorldRepository(db)
    worldID := "world-123"

    rows := sqlmock.NewRows([]string{"id", "name", "description", "creator_id", "status", "created_at"}).
        AddRow("world-123", "测试世界", "测试描述", "user-456", "active", time.Now())

    mock.ExpectQuery("SELECT (.+) FROM worlds WHERE id = \\$1").
        WithArgs(worldID).
        WillReturnRows(rows)

    world, err := repo.GetByID(context.Background(), worldID)
    
    assert.NoError(t, err)
    assert.Equal(t, worldID, world.ID)
    assert.Equal(t, "测试世界", world.Name)
    assert.NoError(t, mock.ExpectationsWereMet())
}
```

### 2.2. 前端单元测试

#### React组件测试
```typescript
// 使用Jest + React Testing Library
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import WorldCreationForm from '../components/WorldCreationForm';
import { worldSlice } from '../store/worldSlice';

describe('WorldCreationForm', () => {
  let store: any;
  
  beforeEach(() => {
    store = configureStore({
      reducer: {
        world: worldSlice.reducer,
      },
    });
  });

  test('应该渲染世界创建表单', () => {
    render(
      <Provider store={store}>
        <WorldCreationForm />
      </Provider>
    );

    expect(screen.getByLabelText('世界名称')).toBeInTheDocument();
    expect(screen.getByLabelText('世界描述')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '创建世界' })).toBeInTheDocument();
  });

  test('应该验证输入并显示错误信息', async () => {
    render(
      <Provider store={store}>
        <WorldCreationForm />
      </Provider>
    );

    const submitButton = screen.getByRole('button', { name: '创建世界' });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('世界名称不能为空')).toBeInTheDocument();
      expect(screen.getByText('世界描述不能为空')).toBeInTheDocument();
    });
  });

  test('应该成功提交表单', async () => {
    const mockCreateWorld = jest.fn();
    
    render(
      <Provider store={store}>
        <WorldCreationForm onSubmit={mockCreateWorld} />
      </Provider>
    );

    fireEvent.change(screen.getByLabelText('世界名称'), {
      target: { value: '测试世界' }
    });
    fireEvent.change(screen.getByLabelText('世界描述'), {
      target: { value: '一个测试用的魔法世界' }
    });

    fireEvent.click(screen.getByRole('button', { name: '创建世界' }));

    await waitFor(() => {
      expect(mockCreateWorld).toHaveBeenCalledWith({
        name: '测试世界',
        description: '一个测试用的魔法世界',
      });
    });
  });
});

// Redux状态管理测试
describe('worldSlice', () => {
  test('应该处理创建世界请求', () => {
    const initialState = {
      worlds: [],
      loading: false,
      error: null,
    };

    const action = worldSlice.actions.createWorldRequest({
      name: '测试世界',
      description: '测试描述',
    });

    const newState = worldSlice.reducer(initialState, action);

    expect(newState.loading).toBe(true);
    expect(newState.error).toBe(null);
  });

  test('应该处理创建世界成功', () => {
    const initialState = {
      worlds: [],
      loading: true,
      error: null,
    };

    const world = {
      id: 'world-123',
      name: '测试世界',
      description: '测试描述',
    };

    const action = worldSlice.actions.createWorldSuccess(world);
    const newState = worldSlice.reducer(initialState, action);

    expect(newState.loading).toBe(false);
    expect(newState.worlds).toContain(world);
    expect(newState.error).toBe(null);
  });
});
```

## 3. 集成测试设计

### 3.1. API集成测试

#### HTTP API测试
```go
func TestWorldAPI_Integration(t *testing.T) {
    // 设置测试数据库
    testDB := setupTestDatabase(t)
    defer cleanupTestDatabase(testDB)
    
    // 设置测试服务器
    server := setupTestServer(testDB)
    defer server.Close()
    
    client := &http.Client{Timeout: time.Second * 10}
    
    t.Run("创建世界API", func(t *testing.T) {
        requestBody := map[string]interface{}{
            "name":        "集成测试世界",
            "description": "用于集成测试的世界",
        }
        
        body, _ := json.Marshal(requestBody)
        req, _ := http.NewRequest("POST", server.URL+"/api/v1/worlds", 
            bytes.NewBuffer(body))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer "+getTestToken())
        
        resp, err := client.Do(req)
        require.NoError(t, err)
        defer resp.Body.Close()
        
        assert.Equal(t, http.StatusCreated, resp.StatusCode)
        
        var response map[string]interface{}
        json.NewDecoder(resp.Body).Decode(&response)
        
        data := response["data"].(map[string]interface{})
        assert.Equal(t, "集成测试世界", data["name"])
        assert.NotEmpty(t, data["id"])
    })
    
    t.Run("获取世界列表API", func(t *testing.T) {
        req, _ := http.NewRequest("GET", server.URL+"/api/v1/worlds", nil)
        req.Header.Set("Authorization", "Bearer "+getTestToken())
        
        resp, err := client.Do(req)
        require.NoError(t, err)
        defer resp.Body.Close()
        
        assert.Equal(t, http.StatusOK, resp.StatusCode)
        
        var response map[string]interface{}
        json.NewDecoder(resp.Body).Decode(&response)
        
        data := response["data"].([]interface{})
        assert.GreaterOrEqual(t, len(data), 1)
    })
}

// WebSocket集成测试
func TestGameWebSocket_Integration(t *testing.T) {
    server := setupTestWebSocketServer(t)
    defer server.Close()
    
    // 连接WebSocket
    wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws"
    conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
    require.NoError(t, err)
    defer conn.Close()
    
    // 发送认证消息
    authMsg := map[string]interface{}{
        "type": "auth",
        "token": getTestToken(),
    }
    err = conn.WriteJSON(authMsg)
    require.NoError(t, err)
    
    // 接收认证响应
    var authResp map[string]interface{}
    err = conn.ReadJSON(&authResp)
    require.NoError(t, err)
    assert.Equal(t, "auth_success", authResp["type"])
    
    // 发送游戏动作
    actionMsg := map[string]interface{}{
        "type": "game_action",
        "action": map[string]interface{}{
            "type": "observe",
            "character_id": "test-character-id",
        },
    }
    err = conn.WriteJSON(actionMsg)
    require.NoError(t, err)
    
    // 接收游戏响应
    var gameResp map[string]interface{}
    err = conn.ReadJSON(&gameResp)
    require.NoError(t, err)
    assert.Equal(t, "game_response", gameResp["type"])
    assert.NotEmpty(t, gameResp["narrative"])
}
```

### 3.2. 数据库集成测试

#### 数据库事务测试
```go
func TestDatabaseTransactions(t *testing.T) {
    db := setupTestDatabase(t)
    defer cleanupTestDatabase(db)
    
    t.Run("世界创建事务", func(t *testing.T) {
        tx, err := db.Begin()
        require.NoError(t, err)
        
        // 创建世界
        world := &World{
            ID:          uuid.New().String(),
            Name:        "事务测试世界",
            Description: "测试事务的世界",
            CreatorID:   "test-user",
        }
        
        _, err = tx.Exec(`
            INSERT INTO worlds (id, name, description, creator_id, status, created_at)
            VALUES ($1, $2, $3, $4, $5, $6)
        `, world.ID, world.Name, world.Description, world.CreatorID, 
           "active", time.Now())
        require.NoError(t, err)
        
        // 创建初始场景
        scene := &Scene{
            ID:          uuid.New().String(),
            WorldID:     world.ID,
            Name:        "起始场景",
            Description: "世界的起始场景",
        }
        
        _, err = tx.Exec(`
            INSERT INTO scenes (id, world_id, name, description, created_at)
            VALUES ($1, $2, $3, $4, $5)
        `, scene.ID, scene.WorldID, scene.Name, scene.Description, time.Now())
        require.NoError(t, err)
        
        // 提交事务
        err = tx.Commit()
        require.NoError(t, err)
        
        // 验证数据已保存
        var count int
        err = db.QueryRow("SELECT COUNT(*) FROM worlds WHERE id = $1", world.ID).Scan(&count)
        require.NoError(t, err)
        assert.Equal(t, 1, count)
        
        err = db.QueryRow("SELECT COUNT(*) FROM scenes WHERE world_id = $1", world.ID).Scan(&count)
        require.NoError(t, err)
        assert.Equal(t, 1, count)
    })
    
    t.Run("事务回滚测试", func(t *testing.T) {
        tx, err := db.Begin()
        require.NoError(t, err)
        
        worldID := uuid.New().String()
        
        // 插入世界数据
        _, err = tx.Exec(`
            INSERT INTO worlds (id, name, description, creator_id, status, created_at)
            VALUES ($1, $2, $3, $4, $5, $6)
        `, worldID, "回滚测试世界", "测试回滚", "test-user", "active", time.Now())
        require.NoError(t, err)
        
        // 故意插入无效数据触发错误
        _, err = tx.Exec(`
            INSERT INTO scenes (id, world_id, name, description, created_at)
            VALUES ($1, $2, $3, $4, $5)
        `, "invalid-uuid", worldID, "场景", "描述", time.Now())
        
        // 回滚事务
        tx.Rollback()
        
        // 验证数据未保存
        var count int
        err = db.QueryRow("SELECT COUNT(*) FROM worlds WHERE id = $1", worldID).Scan(&count)
        require.NoError(t, err)
        assert.Equal(t, 0, count)
    })
}
```

### 3.3. 外部服务集成测试

#### AI服务集成测试
```go
func TestAIServiceIntegration(t *testing.T) {
    if testing.Short() {
        t.Skip("跳过AI服务集成测试")
    }
    
    aiService := NewAIService(getTestAIConfig())
    
    t.Run("世界生成测试", func(t *testing.T) {
        request := &AIWorldGenerationRequest{
            Description: "一个充满魔法的中世纪世界",
            Style:       "fantasy",
            Complexity:  "medium",
        }
        
        response, err := aiService.GenerateWorld(context.Background(), request)
        require.NoError(t, err)
        
        assert.NotEmpty(t, response.WorldConcept)
        assert.GreaterOrEqual(t, len(response.Regions), 2)
        assert.GreaterOrEqual(t, len(response.Landmarks), 1)
        assert.NotEmpty(t, response.InitialScene)
    })
    
    t.Run("角色动作处理测试", func(t *testing.T) {
        request := &AIActionRequest{
            Action:      "观察周围环境",
            Context: map[string]interface{}{
                "scene": "一个神秘的洞穴",
                "character": map[string]interface{}{
                    "name": "冒险者",
                    "level": 1,
                },
                "history": []string{
                    "你进入了洞穴",
                },
            },
        }
        
        response, err := aiService.ProcessAction(context.Background(), request)
        require.NoError(t, err)
        
        assert.NotEmpty(t, response.Narrative)
        assert.GreaterOrEqual(t, len(response.StateChanges), 0)
    })
    
    t.Run("AI服务错误处理", func(t *testing.T) {
        // 测试无效请求
        request := &AIActionRequest{
            Action: "", // 空动作
            Context: nil,
        }
        
        _, err := aiService.ProcessAction(context.Background(), request)
        assert.Error(t, err)
        assert.Contains(t, err.Error(), "invalid request")
    })
}

// 外部IDP集成测试
func TestIDPIntegration(t *testing.T) {
    if testing.Short() {
        t.Skip("跳过IDP集成测试")
    }
    
    idpClient := NewIDPClient(getTestIDPConfig())
    
    t.Run("OAuth令牌验证", func(t *testing.T) {
        // 使用测试令牌
        testToken := getTestOAuthToken()
        
        userInfo, err := idpClient.ValidateToken(context.Background(), testToken)
        require.NoError(t, err)
        
        assert.NotEmpty(t, userInfo.ExternalID)
        assert.NotEmpty(t, userInfo.Email)
        assert.Equal(t, "test-provider", userInfo.Provider)
    })
    
    t.Run("用户信息获取", func(t *testing.T) {
        testToken := getTestOAuthToken()
        
        userInfo, err := idpClient.GetUserInfo(context.Background(), testToken)
        require.NoError(t, err)
        
        assert.NotEmpty(t, userInfo.Name)
        assert.NotEmpty(t, userInfo.Email)
        assert.NotEmpty(t, userInfo.ExternalID)
    })
}
```

## 4. 端到端测试设计

### 4.1. 用户场景测试

#### Playwright E2E测试
```typescript
// 使用Playwright进行端到端测试
import { test, expect } from '@playwright/test';

test.describe('AI文本游戏用户流程', () => {
  test.beforeEach(async ({ page }) => {
    // 设置测试环境
    await page.goto('http://localhost:3000');
  });

  test('完整的游戏创建和游玩流程', async ({ page }) => {
    // 1. 用户登录
    await page.click('text=登录');
    await page.fill('[data-testid=email]', '<EMAIL>');
    await page.fill('[data-testid=password]', 'testpassword');
    await page.click('[data-testid=login-button]');
    
    await expect(page.locator('text=欢迎回来')).toBeVisible();

    // 2. 创建新世界
    await page.click('text=创建新世界');
    await page.fill('[data-testid=world-name]', 'E2E测试世界');
    await page.fill('[data-testid=world-description]', '一个用于端到端测试的魔法世界');
    await page.click('[data-testid=create-world-button]');
    
    // 等待AI生成世界
    await expect(page.locator('text=正在创建世界')).toBeVisible();
    await expect(page.locator('text=世界创建成功')).toBeVisible({ timeout: 30000 });

    // 3. 进入游戏
    await page.click('[data-testid=enter-game-button]');
    await expect(page.locator('[data-testid=game-interface]')).toBeVisible();

    // 4. 执行游戏动作
    await page.click('[data-testid=observe-button]');
    await expect(page.locator('[data-testid=narrative-text]')).toContainText('你环顾四周');

    // 5. 文本输入交互
    await page.fill('[data-testid=action-input]', '检查背包');
    await page.press('[data-testid=action-input]', 'Enter');
    await expect(page.locator('[data-testid=narrative-text]')).toContainText('背包');

    // 6. 查看角色状态
    await page.click('[data-testid=character-panel-toggle]');
    await expect(page.locator('[data-testid=character-name]')).toBeVisible();
    await expect(page.locator('[data-testid=character-memories]')).toBeVisible();

    // 7. 保存并退出
    await page.click('[data-testid=save-game-button]');
    await expect(page.locator('text=游戏已保存')).toBeVisible();
    
    await page.click('[data-testid=exit-game-button]');
    await expect(page.locator('[data-testid=world-list]')).toBeVisible();
  });

  test('多玩家世界加入流程', async ({ page, context }) => {
    // 创建第二个浏览器上下文模拟第二个用户
    const secondPage = await context.newPage();
    
    // 第一个用户创建世界并分享
    await page.goto('http://localhost:3000');
    await loginUser(page, '<EMAIL>');
    const shareCode = await createAndShareWorld(page, '多玩家测试世界');

    // 第二个用户加入世界
    await secondPage.goto('http://localhost:3000');
    await loginUser(secondPage, '<EMAIL>');
    await joinWorldWithCode(secondPage, shareCode);

    // 验证两个用户都在同一世界但不同位置
    const user1Location = await page.locator('[data-testid=current-scene]').textContent();
    const user2Location = await secondPage.locator('[data-testid=current-scene]').textContent();
    
    expect(user1Location).not.toBe(user2Location); // 确保分散初始化
  });

  test('内容安全过滤测试', async ({ page }) => {
    await page.goto('http://localhost:3000');
    await loginUser(page, '<EMAIL>');
    await enterExistingWorld(page);

    // 尝试输入不当内容
    await page.fill('[data-testid=action-input]', '微信号：123456，加我购买装备');
    await page.press('[data-testid=action-input]', 'Enter');
    
    // 验证内容被过滤
    await expect(page.locator('text=输入内容包含不当信息')).toBeVisible();
    
    // 尝试输入正常内容
    await page.fill('[data-testid=action-input]', '我想探索这个房间');
    await page.press('[data-testid=action-input]', 'Enter');
    
    // 验证正常内容被处理
    await expect(page.locator('[data-testid=narrative-text]')).toContainText('探索');
  });
});

// 辅助函数
async function loginUser(page: Page, email: string) {
  await page.click('text=登录');
  await page.fill('[data-testid=email]', email);
  await page.fill('[data-testid=password]', 'testpassword');
  await page.click('[data-testid=login-button]');
  await expect(page.locator('text=欢迎回来')).toBeVisible();
}

async function createAndShareWorld(page: Page, worldName: string): Promise<string> {
  await page.click('text=创建新世界');
  await page.fill('[data-testid=world-name]', worldName);
  await page.fill('[data-testid=world-description]', '多玩家测试世界');
  await page.click('[data-testid=create-world-button]');
  
  await expect(page.locator('text=世界创建成功')).toBeVisible({ timeout: 30000 });
  
  await page.click('[data-testid=share-world-button]');
  const shareCode = await page.locator('[data-testid=share-code]').textContent();
  
  return shareCode || '';
}

async function joinWorldWithCode(page: Page, shareCode: string) {
  await page.click('text=加入世界');
  await page.fill('[data-testid=share-code-input]', shareCode);
  await page.click('[data-testid=join-world-button]');
  await expect(page.locator('[data-testid=game-interface]')).toBeVisible();
}
```

## 5. 性能测试设计

### 5.1. 负载测试

#### K6性能测试脚本
```javascript
// 使用K6进行负载测试
import http from 'k6/http';
import ws from 'k6/ws';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// 自定义指标
const errorRate = new Rate('errors');

export let options = {
  stages: [
    { duration: '2m', target: 100 },   // 2分钟内增加到100用户
    { duration: '5m', target: 100 },   // 保持100用户5分钟
    { duration: '2m', target: 200 },   // 2分钟内增加到200用户
    { duration: '5m', target: 200 },   // 保持200用户5分钟
    { duration: '2m', target: 0 },     // 2分钟内减少到0用户
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'],  // 95%的请求响应时间小于500ms
    http_req_failed: ['rate<0.1'],     // 错误率小于10%
    errors: ['rate<0.1'],              // 自定义错误率小于10%
  },
};

const BASE_URL = 'http://localhost:8080';

export function setup() {
  // 创建测试用户和世界
  const loginResp = http.post(`${BASE_URL}/api/v1/auth/login`, {
    email: '<EMAIL>',
    password: 'testpassword',
  });
  
  const token = loginResp.json('data.token');
  
  const worldResp = http.post(`${BASE_URL}/api/v1/worlds`, {
    name: '负载测试世界',
    description: '用于负载测试的世界',
  }, {
    headers: { Authorization: `Bearer ${token}` },
  });
  
  return {
    token: token,
    worldId: worldResp.json('data.id'),
  };
}

export default function(data) {
  const headers = { Authorization: `Bearer ${data.token}` };
  
  // 测试API响应性能
  testAPIPerformance(headers, data.worldId);
  
  // 测试WebSocket性能
  testWebSocketPerformance(data.token, data.worldId);
  
  sleep(1);
}

function testAPIPerformance(headers, worldId) {
  // 获取世界列表
  let resp = http.get(`${BASE_URL}/api/v1/worlds`, { headers });
  check(resp, {
    '获取世界列表状态为200': (r) => r.status === 200,
    '获取世界列表响应时间<200ms': (r) => r.timings.duration < 200,
  }) || errorRate.add(1);
  
  // 获取世界详情
  resp = http.get(`${BASE_URL}/api/v1/worlds/${worldId}`, { headers });
  check(resp, {
    '获取世界详情状态为200': (r) => r.status === 200,
    '获取世界详情响应时间<100ms': (r) => r.timings.duration < 100,
  }) || errorRate.add(1);
  
  // 创建角色
  resp = http.post(`${BASE_URL}/api/v1/worlds/${worldId}/characters`, {
    name: `负载测试角色${__VU}`,
  }, { headers });
  check(resp, {
    '创建角色状态为201': (r) => r.status === 201,
    '创建角色响应时间<300ms': (r) => r.timings.duration < 300,
  }) || errorRate.add(1);
}

function testWebSocketPerformance(token, worldId) {
  const wsUrl = `ws://localhost:8080/ws?token=${token}`;
  
  const response = ws.connect(wsUrl, {}, function (socket) {
    socket.on('open', function () {
      // 发送认证消息
      socket.send(JSON.stringify({
        type: 'auth',
        token: token,
      }));
    });
    
    socket.on('message', function (message) {
      const data = JSON.parse(message);
      check(data, {
        'WebSocket消息格式正确': (d) => d.type !== undefined,
      }) || errorRate.add(1);
    });
    
    // 发送游戏动作
    socket.send(JSON.stringify({
      type: 'game_action',
      action: {
        type: 'observe',
        character_id: 'test-character',
      },
    }));
    
    sleep(2);
  });
  
  check(response, {
    'WebSocket连接成功': (r) => r && r.status === 101,
  }) || errorRate.add(1);
}

export function teardown(data) {
  // 清理测试数据
  const headers = { Authorization: `Bearer ${data.token}` };
  http.del(`${BASE_URL}/api/v1/worlds/${data.worldId}`, null, { headers });
}
```

### 5.2. 压力测试

#### AI接口压力测试
```go
func TestAIServiceStress(t *testing.T) {
    if testing.Short() {
        t.Skip("跳过压力测试")
    }
    
    aiService := NewAIService(getTestAIConfig())
    
    // 并发压力测试
    concurrency := 50
    requestsPerWorker := 20
    
    var wg sync.WaitGroup
    var successCount int64
    var errorCount int64
    
    start := time.Now()
    
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            
            for j := 0; j < requestsPerWorker; j++ {
                request := &AIActionRequest{
                    Action: fmt.Sprintf("Worker %d Action %d", workerID, j),
                    Context: map[string]interface{}{
                        "scene": "测试场景",
                        "character": map[string]interface{}{
                            "name": fmt.Sprintf("Worker%d", workerID),
                        },
                    },
                }
                
                _, err := aiService.ProcessAction(context.Background(), request)
                if err != nil {
                    atomic.AddInt64(&errorCount, 1)
                    t.Logf("Worker %d Request %d failed: %v", workerID, j, err)
                } else {
                    atomic.AddInt64(&successCount, 1)
                }
            }
        }(i)
    }
    
    wg.Wait()
    duration := time.Since(start)
    
    totalRequests := int64(concurrency * requestsPerWorker)
    successRate := float64(successCount) / float64(totalRequests) * 100
    qps := float64(totalRequests) / duration.Seconds()
    
    t.Logf("压力测试结果:")
    t.Logf("总请求数: %d", totalRequests)
    t.Logf("成功请求: %d", successCount)
    t.Logf("失败请求: %d", errorCount)
    t.Logf("成功率: %.2f%%", successRate)
    t.Logf("QPS: %.2f", qps)
    t.Logf("总耗时: %v", duration)
    
    // 断言性能指标
    assert.GreaterOrEqual(t, successRate, 95.0, "成功率应该大于95%")
    assert.GreaterOrEqual(t, qps, 10.0, "QPS应该大于10")
}
```

## 6. 安全测试设计

### 6.1. 安全漏洞测试

#### SQL注入测试
```go
func TestSQLInjectionPrevention(t *testing.T) {
    db := setupTestDatabase(t)
    defer cleanupTestDatabase(db)
    
    repo := NewWorldRepository(db)
    
    // 测试SQL注入攻击
    maliciousInputs := []string{
        "'; DROP TABLE worlds; --",
        "' OR '1'='1",
        "'; INSERT INTO worlds (name) VALUES ('hacked'); --",
        "' UNION SELECT * FROM users --",
    }
    
    for _, input := range maliciousInputs {
        t.Run(fmt.Sprintf("SQL注入测试: %s", input), func(t *testing.T) {
            // 尝试使用恶意输入创建世界
            world := &World{
                ID:          uuid.New().String(),
                Name:        input,
                Description: "测试描述",
                CreatorID:   "test-user",
            }
            
            err := repo.Create(context.Background(), world)
            
            // 应该成功创建（因为使用了参数化查询）
            assert.NoError(t, err)
            
            // 验证数据库结构未被破坏
            var tableCount int
            err = db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'worlds'").Scan(&tableCount)
            assert.NoError(t, err)
            assert.Equal(t, 1, tableCount, "worlds表应该仍然存在")
            
            // 验证恶意输入被正确存储为字符串
            var storedName string
            err = db.QueryRow("SELECT name FROM worlds WHERE id = $1", world.ID).Scan(&storedName)
            assert.NoError(t, err)
            assert.Equal(t, input, storedName, "恶意输入应该被正确存储为字符串")
        })
    }
}

// XSS防护测试
func TestXSSPrevention(t *testing.T) {
    server := setupTestServer(setupTestDatabase(t))
    defer server.Close()
    
    client := &http.Client{Timeout: time.Second * 10}
    
    xssPayloads := []string{
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "<img src=x onerror=alert('xss')>",
        "';alert('xss');//",
    }
    
    for _, payload := range xssPayloads {
        t.Run(fmt.Sprintf("XSS测试: %s", payload), func(t *testing.T) {
            requestBody := map[string]interface{}{
                "name":        payload,
                "description": "正常描述",
            }
            
            body, _ := json.Marshal(requestBody)
            req, _ := http.NewRequest("POST", server.URL+"/api/v1/worlds", 
                bytes.NewBuffer(body))
            req.Header.Set("Content-Type", "application/json")
            req.Header.Set("Authorization", "Bearer "+getTestToken())
            
            resp, err := client.Do(req)
            require.NoError(t, err)
            defer resp.Body.Close()
            
            // 应该成功创建（输入验证通过）
            assert.Equal(t, http.StatusCreated, resp.StatusCode)
            
            // 获取创建的世界
            var createResp map[string]interface{}
            json.NewDecoder(resp.Body).Decode(&createResp)
            worldID := createResp["data"].(map[string]interface{})["id"].(string)
            
            // 获取世界详情
            getReq, _ := http.NewRequest("GET", server.URL+"/api/v1/worlds/"+worldID, nil)
            getReq.Header.Set("Authorization", "Bearer "+getTestToken())
            
            getResp, err := client.Do(getReq)
            require.NoError(t, err)
            defer getResp.Body.Close()
            
            var worldData map[string]interface{}
            json.NewDecoder(getResp.Body).Decode(&worldData)
            
            // 验证XSS payload被正确转义或过滤
            name := worldData["data"].(map[string]interface{})["name"].(string)
            assert.NotContains(t, name, "<script>", "脚本标签应该被过滤")
            assert.NotContains(t, name, "javascript:", "JavaScript协议应该被过滤")
        })
    }
}
```

### 6.2. 认证授权测试

#### JWT安全测试
```go
func TestJWTSecurity(t *testing.T) {
    server := setupTestServer(setupTestDatabase(t))
    defer server.Close()
    
    client := &http.Client{Timeout: time.Second * 10}
    
    t.Run("无效JWT令牌", func(t *testing.T) {
        invalidTokens := []string{
            "invalid.jwt.token",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature",
            "",
            "Bearer ",
        }
        
        for _, token := range invalidTokens {
            req, _ := http.NewRequest("GET", server.URL+"/api/v1/worlds", nil)
            if token != "" {
                req.Header.Set("Authorization", token)
            }
            
            resp, err := client.Do(req)
            require.NoError(t, err)
            defer resp.Body.Close()
            
            assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
        }
    })
    
    t.Run("过期JWT令牌", func(t *testing.T) {
        // 创建过期令牌
        expiredToken := createExpiredJWT()
        
        req, _ := http.NewRequest("GET", server.URL+"/api/v1/worlds", nil)
        req.Header.Set("Authorization", "Bearer "+expiredToken)
        
        resp, err := client.Do(req)
        require.NoError(t, err)
        defer resp.Body.Close()
        
        assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
    })
    
    t.Run("权限不足", func(t *testing.T) {
        // 创建只有读权限的令牌
        readOnlyToken := createReadOnlyJWT()
        
        // 尝试创建世界（需要写权限）
        requestBody := map[string]interface{}{
            "name":        "权限测试世界",
            "description": "测试权限的世界",
        }
        
        body, _ := json.Marshal(requestBody)
        req, _ := http.NewRequest("POST", server.URL+"/api/v1/worlds", 
            bytes.NewBuffer(body))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer "+readOnlyToken)
        
        resp, err := client.Do(req)
        require.NoError(t, err)
        defer resp.Body.Close()
        
        assert.Equal(t, http.StatusForbidden, resp.StatusCode)
    })
}
```

## 7. 测试自动化与CI/CD

### 7.1. GitHub Actions配置

#### 测试流水线
```yaml
# .github/workflows/test.yml
name: 测试流水线

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: 设置Go环境
      uses: actions/setup-go@v3
      with:
        go-version: 1.21
    
    - name: 安装依赖
      run: go mod download
    
    - name: 运行单元测试
      run: |
        go test -v -race -coverprofile=coverage.out ./...
        go tool cover -html=coverage.out -o coverage.html
      env:
        DATABASE_URL: postgres://postgres:testpassword@localhost:5432/testdb?sslmode=disable
        REDIS_URL: redis://localhost:6379
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
    
    - name: 静态代码分析
      run: |
        go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
        gosec ./...

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_DB: testdb
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: 设置Go环境
      uses: actions/setup-go@v3
      with:
        go-version: 1.21
    
    - name: 运行集成测试
      run: go test -v -tags=integration ./tests/integration/...
      env:
        DATABASE_URL: postgres://postgres:testpassword@localhost:5432/testdb?sslmode=disable
        REDIS_URL: redis://localhost:6379

  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v3
      with:
        node-version: 18
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 安装前端依赖
      run: |
        cd frontend
        npm ci
    
    - name: 运行前端单元测试
      run: |
        cd frontend
        npm run test:coverage
    
    - name: 运行前端集成测试
      run: |
        cd frontend
        npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, frontend-tests]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: 设置测试环境
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30  # 等待服务启动
    
    - name: 运行E2E测试
      run: |
        cd frontend
        npm ci
        npx playwright install
        npm run test:e2e
    
    - name: 清理测试环境
      if: always()
      run: docker-compose -f docker-compose.test.yml down

  security-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: 运行安全扫描
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'security-scan-results.sarif'
    
    - name: 依赖安全检查
      run: |
        go install github.com/sonatypecommunity/nancy@latest
        go list -json -m all | nancy sleuth
```

这份测试设计文档为AI文本游戏项目提供了全面的测试策略，涵盖了从单元测试到端到端测试的完整测试体系，确保系统的质量、性能和安全性。
